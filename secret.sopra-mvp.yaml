global:
    licenseKey: ENC[AES256_GCM,data:H8bRRx0zhZLWG3GVyeW0w6FHVU8aNCnhULkERhEdhkeIUcXTAA==,iv:W1UQKIimMWMc3MD+yyFi6biOkkbUxIoNizHgNTcaAJo=,tag:h7HXEutXq01Et4ZPzoAJ7Q==,type:str]
    keycloak:
        username: <PERSON><PERSON>[AES256_GCM,data:brlVyhg=,iv:+hmlPq9jX2Px38ghMIYwdQyodYt4wGamgbmhBRBnVG4=,tag:OH4JjgI4VB8BhnUHo9DYTg==,type:str]
        password: ENC[AES256_GCM,data:L3BtJVYBQR8G7BMnoM+MKyBejnO9,iv:k+MPsPSgPDzqV+kwAREfatCnpu1MuSp6ktbvlB9Jtpw=,tag:xNDtIERqtLRI+z8WMInB8A==,type:str]
    postgresql:
        username: ENC[AES256_GCM,data:Gp/DM+M=,iv:pCXLi/WE7xtr++eT/bYmn5lF6oJQwpDEXPpdD8f+DoM=,tag:GtQOwpnnUwKwb4F77vkrrQ==,type:str]
        password: ENC[AES256_GCM,data:XDzcYg2ntPQLStIRY04a0g==,iv:lS+d7O0PbdFP6FQdzKCe5lpV2IFVOwlEH7gryPVMGns=,tag:nTrdg6G4pz9AYu1Ch6FSPA==,type:str]
sops:
    kms:
        - arn: arn:aws:kms:eu-central-1:739275467562:key/mrk-d3288e018d7846ec9a07ddcc9c6f30df
          created_at: "2025-02-21T07:40:18Z"
          enc: AQICAHgOToWtMvBkTLJRFGXMktK0A82XiNzezydcSTPOj+A3VgEXGIw1ZApZ9Hkwq+Tf8O4AAAAAfjB8BgkqhkiG9w0BBwagbzBtAgEAMGgGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMFAFVLNX4QBLoRsI1AgEQgDsKQtlBlS4OixA5heLejoRDVaoRYhcJv3qiVbMeC+5BmrHCErzaKQp4Tl+sSTCmNvFUHIR5Cb78pAgK1Q==
          aws_profile: ""
    gcp_kms: []
    azure_kv: []
    hc_vault: []
    age: []
    lastmodified: "2025-06-30T15:21:54Z"
    mac: ENC[AES256_GCM,data:7F9IhDiskB5Py5uIK/z6+Tmq+vS7289FJb6eJhn5CWHxRL1YSGPDn4Sg6ggrTsahVKqty/7V7PZY5NM6Citajhv2MxaOIJd1raQVWgAKDPT4Ke5wOOL6n9kQjVf+uDNlDR3hrI/O1V7wxAAL68Xgma2wC7IvpKCwOlKLHwicZQA=,iv:an+hYX6e8t88sdfQH3eC107jrMcDnFgoZe1WCCv3cVk=,tag:0DDXQ9vPlJ2txwCJxUGf9g==,type:str]
    pgp: []
    unencrypted_suffix: _unencrypted
    version: 3.9.4
