pluginManagement {
	repositories {
		maven { url 'https://repo.spring.io/milestone' }
		maven { url 'https://plugins.gradle.org/m2/' }
		gradlePluginPortal()
	}
}
rootProject.name = 'fix-api-server'

ext {
	repository_username = System.env.NEXUS_DEPLOY_USERNAME
	repository_password = System.env.NEXUS_DEPLOY_PASSWORD
}

include 'fix-api-domain'
include 'fix-api-common'
include 'fix-api-custom-ohlc'
include 'fix-api-drop-copy'
include 'fix-api-market-data'
include 'fix-api-trading'

dependencyResolutionManagement {
	repositories {
		mavenLocal()
		maven {
			name 'nexus-snapshots'
			url 'https://repo.wyden.io/nexus/repository/snapshots/'
			credentials {
				username repository_username
				password repository_password
			}
		}
		mavenCentral()
	}
	versionCatalogs {
		create("dependencyCatalog") {
			from("io.wyden:dependency-catalog:0.1.33-SNAPSHOT")
		}
	}
}
