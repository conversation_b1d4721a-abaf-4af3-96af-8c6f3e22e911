rootProject.name = 'reference-data'

ext {
    repository_username = System.env.NEXUS_DEPLOY_USERNAME
    repository_password = System.env.NEXUS_DEPLOY_PASSWORD
}

include 'reference-data-server'
include 'reference-data-client'
include 'reference-data-domain'

dependencyResolutionManagement {
    repositories {
        mavenLocal()
        maven {
            name 'nexus-snapshots'
            url 'https://repo.wyden.io/nexus/repository/snapshots/'
            credentials {
                username repository_username
                password repository_password
            }
        }
        mavenCentral()
    }
    versionCatalogs {
        create("dependencyCatalog") {
            from("io.wyden:dependency-catalog:0.1.34-SNAPSHOT")
        }
    }
}
