pluginManagement {
    repositories {
        maven { url 'https://repo.spring.io/milestone' }
        gradlePluginPortal()
    }
}

rootProject.name = 'rate-service'

include 'rate-service'
include 'rate-service-client'
include 'rate-service-domain'

ext {
    repository_username = System.env.NEXUS_DEPLOY_USERNAME
    repository_password = System.env.NEXUS_DEPLOY_PASSWORD
}

dependencyResolutionManagement {
    repositories {
        mavenLocal()
        maven {
            name 'nexus-snapshots'
            url 'https://repo.wyden.io/nexus/repository/snapshots/'
            credentials {
                username repository_username
                password repository_password
            }
        }
        mavenCentral()
    }
    versionCatalogs {
        create("dependencyCatalog") {
            from("io.wyden:dependency-catalog:0.1.33-SNAPSHOT")
        }
    }
}
