pluginManagement {
	repositories {
		maven { url 'https://repo.spring.io/milestone' }
		maven { url 'https://plugins.gradle.org/m2/' }
		gradlePluginPortal()
	}
}
rootProject.name = 'connector-wrapper'

include 'connector-wrapper-base'
include 'connector-wrapper-binance-spot'
include 'connector-wrapper-bitfinex'
include 'connector-wrapper-bitmex'
include 'connector-wrapper-bitstamp'
include 'connector-wrapper-coinapi'
include 'connector-wrapper-coinbase'
include 'connector-wrapper-mock'
include 'connector-wrapper-generic-outbound'
include 'connector-wrapper-kraken'
include 'connector-wrapper-bit2me'
include 'connector-wrapper-goldman-sachs'
include 'connector-wrapper-lmax-global'
include 'connector-wrapper-360t'
include 'connector-wrapper-b2c2'
include 'connector-wrapper-wintermute'
include 'connector-wrapper-scrypt'
include 'connector-wrapper-fireblocks'
include 'connector-wrapper-bullish'

ext {
	repository_username = System.env.NEXUS_DEPLOY_USERNAME
	repository_password = System.env.NEXUS_DEPLOY_PASSWORD
}

dependencyResolutionManagement {
	repositories {
		mavenLocal()
		maven {
			name 'nexus-main'
			url 'https://repo.wyden.io/nexus/repository/main/'
			credentials {
				username repository_username
				password repository_password
			}
		}
		maven {
			name 'nexus-snapshot'
			url 'https://repo.wyden.io/nexus/repository/snapshots/'
			credentials {
				username repository_username
				password repository_password
			}
		}
		mavenCentral()
	}
	versionCatalogs {
		create("dependencyCatalog") {
////      When changing version number or adding new connector - see target-registry/README.md for additional steps
			from("io.wyden:dependency-catalog:0.1.33-SNAPSHOT")
		}
	}
}
