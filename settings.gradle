rootProject.name = 'rest-management'

ext {
    repository_username = System.env.NEXUS_DEPLOY_USERNAME
    repository_password = System.env.NEXUS_DEPLOY_PASSWORD
}

include 'rest-management-domain'
include 'rest-management-server'

dependencyResolutionManagement {
    repositories {
        mavenLocal()
        maven {
            name 'nexus-snapshots'
            url 'https://repo.wyden.io/nexus/repository/snapshots/'
            credentials {
                username repository_username
                password repository_password
            }
        }
        mavenCentral()
    }
    versionCatalogs {
        create("dependencyCatalog") {
            from("io.wyden:dependency-catalog:0.2.1-SNAPSHOT")
        }
    }
}
