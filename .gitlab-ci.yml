include:
  - project: infrastructure/components
    ref: main
    file: components.yml
  - template: Security/SAST.gitlab-ci.yml

image: 739275467562.dkr.ecr.eu-central-1.amazonaws.com/dockerhub/library/maven:3.8.4-eclipse-temurin-17

variables:
  IMAGE_NAME: exchange-simulator
  # Used in the deploy stage (see the components repo)
  RELEASE_NAME: exchange-simulator
  #For submodule
  GIT_SUBMODULE_STRATEGY: recursive
  GIT_SUBMODULE_UPDATE_FLAGS: --remote
  GIT_SUBMODULE_DEPTH: 0

stages:
  - version
  - test
  - build
  - publish
  - scan
  - deploy
  - tag
  - qa

version-verify:
  extends: .pipeline-verification

changes-verify:
  extends: .verify-locked-files

semgrep-sast:
  rules:
    - if: $CI_COMMIT_TAG
      when: never

sonarqube-check:
  extends: .sonar-gradle

unit-test:
  extends: .unit_test

integration-test:
  extends: .integration_test

build:
  extends: .build-gradle

publish:
  extends: .publish_image

container-scan:
  extends: .docker_scan

retag:
  extends: .retag_image
  rules:
    - if: $CI_COMMIT_TAG =~ /^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$/
      when: always

release_image:
  extends: .retag_image
  script:
    - crane auth login -u ${NEXUS_USER} -p ${NEXUS_PASSWORD} ${DOCKER_IMAGE_REPOSITORY}
    - crane cp ${DOCKER_IMAGE_REPOSITORY}/${DEV_IMAGE_PATH}/${IMAGE_NAME}:${CI_COMMIT_SHORT_SHA} ${DOCKER_IMAGE_REPOSITORY}/${RELEASE_IMAGE_PATH}/${IMAGE_NAME}:${CI_COMMIT_TAG}
    - crane cp ${DOCKER_IMAGE_REPOSITORY}/${DEV_IMAGE_PATH}/${IMAGE_NAME}:${CI_COMMIT_SHORT_SHA} ${DOCKER_IMAGE_REPOSITORY}/${RELEASE_IMAGE_PATH}/${IMAGE_NAME}:latest
  rules:
    - if: $CI_COMMIT_TAG =~ /^[0-9]+\.[0-9]+\.[0-9]+$/
      when: always
    - when: never

.deploy_exchange_helm:
  extends: .deploy_helm
  script:
    - helm secrets upgrade --install --namespace ${NAMESPACE} ${RELEASE_NAME} ./env-values -f ./env-values/secret.${ENVIRONMENT}-aws.yaml -f ./env-values/values-${ENVIRONMENT}-aws.yaml --set image.tag=${IMAGE_TAG} --timeout 20m

deploy_qa:
  variables:
    ENVIRONMENT: "qa"
  extends: .deploy_exchange_helm

deploy_performance:
  variables:
    ENVIRONMENT: "performance"
  extends: .deploy_exchange_helm
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule" || $CI_COMMIT_TAG != null
      when: never
    - if: $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH
      when: manual

deploy_og:
  extends: .deploy_helm
  allow_failure: true
  script:
    - helm repo add --username $NEXUS_USER --password $NEXUS_PASSWORD wyden https://repo.wyden.io/nexus/repository/wyden/
    - helm repo update
    - helm secrets upgrade --install exchange-simulator wyden/wyden-exchange-simulator -f ./env-values/secret.prod-aws.yaml -f ./env-values/values-prod-aws.yaml --set image.tag=${CI_COMMIT_TAG} --namespace wyden-og --timeout 20m
  rules:
    - if: $CI_COMMIT_TAG =~ /^[0-9]+\.[0-9]+\.[0-9]+$/
      when: on_success
    - when: never

tag_rc:
  extends: .tag_repository

tag_rc_scheduled:
  extends: .tag_repository_scheduled
