include:
  - project: infrastructure/components
    ref: main
    file: components.yml
  - template: Security/SAST.gitlab-ci.yml

image: 739275467562.dkr.ecr.eu-central-1.amazonaws.com/dockerhub/library/maven:3.8.4-eclipse-temurin-17

stages:
  - version
  - docs
  - test
  - build
  - publish
  - scan
  - deploy
  - tag
  - qa

version-verify:
  extends: .pipeline-verification

changes-verify:
  extends: .verify-locked-files

generate-fix-spec:
  stage: docs
  interruptible: true
  image: python:3.7
  script:
    - cd inbound-fix-spec && ./generate_fix_spec.sh
  allow_failure: true
  artifacts:
    when: always
    paths:
      - inbound-fix-spec/inbound-fix
    expire_in: 7 days

semgrep-sast:
  rules:
    - if: $CI_COMMIT_TAG
      when: never

sonarqube-check:
  variables:
    JAVA_OPTS: "-Xmx4096m -XX:MaxMetaspaceSize=2048m"
  extends: .sonar-gradle

unit-test:
  extends: .unit_test

integration-test:
  extends: .integration_test

build:
  extends: .build-gradle

publish: # -trading (it can't be renamed because job definitions from infrastructure/components depend on standard naming)
  variables:
    IMAGE_NAME: fix-api
    DOCKER_BUILD_CONTEXT: fix-api-trading/.
    DOCKERFILE_PATH: fix-api-trading/Dockerfile
  extends: .publish_image

publish-market-data:
  variables:
    IMAGE_NAME: fix-api-market-data
    DOCKER_BUILD_CONTEXT: fix-api-market-data/.
    DOCKERFILE_PATH: fix-api-market-data/Dockerfile
  extends: .publish_image

publish-drop-copy:
  variables:
    IMAGE_NAME: fix-api-drop-copy
    DOCKER_BUILD_CONTEXT: fix-api-drop-copy/.
    DOCKERFILE_PATH: fix-api-drop-copy/Dockerfile
  extends: .publish_image

publish-custom-ohlc:
  variables:
    IMAGE_NAME: fix-api-custom-ohlc
    DOCKER_BUILD_CONTEXT: fix-api-custom-ohlc/.
    DOCKERFILE_PATH: fix-api-custom-ohlc/Dockerfile
  extends: .publish_image

publish-domain-lib:
  extends: .publish-lib

container-scan:
  extends: .docker_scan
  script:
    - snyk auth $SNYK_TOKEN
    - |
      for app in fix-api-drop-copy fix-api-market-data fix-api fix-api-custom-ohlc
      do
        snyk container test --username=$NEXUS_USER --password=$NEXUS_PASSWORD docker.wyden.io/${IMAGE_PATH}/$app:${CI_COMMIT_SHORT_SHA} --json | snyk-to-html -o results-container_$app.html
      done
  artifacts:
    when: always
    paths:
      - ./results-container*.html
    expire_in: 1 days

retag:
  extends: .retag_image
  script:
    - crane auth login -u ${NEXUS_USER} -p ${NEXUS_PASSWORD} ${DOCKER_IMAGE_REPOSITORY}
    - |
      for app in fix-api-drop-copy fix-api-market-data fix-api fix-api-custom-ohlc
      do
        echo "Tagging docker image for server $app as ${CI_COMMIT_TAG}"
        crane cp ${DOCKER_IMAGE_REPOSITORY}/${IMAGE_PATH}/${app}:${CI_COMMIT_SHORT_SHA} ${DOCKER_IMAGE_REPOSITORY}/${IMAGE_PATH}/${app}:${CI_COMMIT_TAG}
        echo "DONE with ${app}:${CI_COMMIT_TAG}"
      done

audit:
  variables:
    AUDIT_EXTRA_PARAMS: "-c fix-api,fix-api-market-data,fix-api-drop-copy,fix-api-custom-ohlc"
  extends: .audit_trails

deploy-dev-trading:
  variables:
    RELEASE_NAME: fix-api
  extends: .deploy_helm

deploy-dev-market-data:
  variables:
    RELEASE_NAME: fix-api-market-data
  extends: .deploy_helm

deploy-dev-drop-copy:
  variables:
    RELEASE_NAME: fix-api-drop-copy
  extends: .deploy_helm

deploy-dev-custom-ohlc:
  variables:
    RELEASE_NAME: fix-api-custom-ohlc
  extends: .deploy_helm

tag_rc:
  extends: .tag_repository

tag_rc_scheduled:
  extends: .tag_repository_scheduled
