include:
  - project: infrastructure/components
    ref: main
    file: components.yml
  - template: Security/SAST.gitlab-ci.yml

image: 739275467562.dkr.ecr.eu-central-1.amazonaws.com/dockerhub/library/maven:3.8.4-eclipse-temurin-17

variables:
  IMAGE_NAME: target-registry
  # Used in the deploy stage (see the components repo)
  RELEASE_NAME: target-registry

stages:
  - version
  - test
  - build
  - publish
  - deploy
  - tag
  - qa

version-verify:
  extends: .pipeline-verification

changes-verify:
  extends: .verify-locked-files

semgrep-sast:
  rules:
    - if: $CI_COMMIT_TAG
      when: never

version-check-domain:
  variables:
    VERSION_FILE: "target-registry-domain/version.properties"
    SOURCE_FILES: "target-registry-domain/src/**/*"
  extends: .lib-version-check

sonarqube-check:
  extends: .sonar-gradle

unit-test:
  extends: .unit_test

integration-test:
  extends: .integration_test

build:
  extends: .build-gradle

publish:
  variables:
    DOCKERFILE_PATH: target-registry-server/Dockerfile
    DOCKER_BUILD_CONTEXT: target-registry-server/.
  extends: .publish_image

publish-lib:
  extends: .publish-lib

retag:
  extends: .retag_image

audit:
  extends: .audit_trails

deploy-dev:
  extends: .deploy_helm

tag_rc:
  extends: .tag_repository

tag_rc_scheduled:
  extends: .tag_repository_scheduled
