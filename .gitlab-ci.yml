include:
  - project: infrastructure/components
    ref: main
    file: components.yml
  - template: Security/SAST.gitlab-ci.yml

image: 739275467562.dkr.ecr.eu-central-1.amazonaws.com/dockerhub/library/maven:3.8.4-eclipse-temurin-17

stages:
  - version
  - test
  - build
  - publish
  - scan
  - deploy
  - tag
  - qa

version-verify:
  extends: .pipeline-verification

semgrep-sast:
  rules:
    - if: $CI_COMMIT_TAG
      when: never

spotbugs-sast:
  rules:
    - if: $CI_COMMIT_TAG
      when: never

sonarqube-check:
  extends: .sonar-gradle

unit-test:
  extends: .unit_test

integration-test:
  extends: .integration_test

build:
  extends: .build-gradle
  needs:
    - job: unit-test
      optional: true
    - job: integration-test
      optional: true

publish:
  extends: .publish_image
  script:
    # Fix short name issue
    - sed -i 's/short-name-mode="enforcing"/short-name-mode="permissive"/g' /etc/containers/registries.conf
    - buildah login -u "${NEXUS_USER}" -p "${NEXUS_PASSWORD}" docker.wyden.io
    - |
      for connector in connector-wrapper-*
      do
        if [ -f "$connector/Dockerfile" ]; then
          echo "Building docker image for connector $connector:${CI_COMMIT_SHORT_SHA}"
          buildah bud --manifest docker.wyden.io/${IMAGE_PATH}/$connector:${CI_COMMIT_SHORT_SHA} --platform=linux/arm64/v8,linux/amd64 -f ./$connector/Dockerfile ./$connector
          echo "Building docker image for connector $connector:${CI_COMMIT_SHORT_SHA}"
          buildah manifest push docker.wyden.io/${IMAGE_PATH}/$connector:${CI_COMMIT_SHORT_SHA} docker://docker.wyden.io/${IMAGE_PATH}/$connector:${CI_COMMIT_SHORT_SHA} --all --format v2s2
          buildah manifest push docker.wyden.io/${IMAGE_PATH}/$connector:${CI_COMMIT_SHORT_SHA} docker://docker.wyden.io/${IMAGE_PATH}/$connector:dev --all --format v2s2
          echo "DONE with $connector:${CI_COMMIT_SHORT_SHA}"
        fi
      done
    - echo "DONE BUILDING ALL IMAGES"
  needs:
    - job: build
      optional: true

container-scan:
  extends: .docker_scan
  script:
    - snyk auth $SNYK_TOKEN
    - |
      for connector in connector-wrapper-*
      do
        snyk container test --username=$NEXUS_USER --password=$NEXUS_PASSWORD docker.wyden.io/${IMAGE_PATH}/$connector:${CI_COMMIT_SHORT_SHA} --json | snyk-to-html -o results-container_$connector.html
      done
  artifacts:
    when: always
    paths:
      - ./results-container*.html
    expire_in: 1 days
  needs:
    - job: publish
      optional: true

retag:
  extends: .retag_image
  script:
    - crane auth login -u ${NEXUS_USER} -p ${NEXUS_PASSWORD} ${DOCKER_IMAGE_REPOSITORY}
    - |
      for connector in connector-wrapper-*
      do
        if [ -f "$connector/Dockerfile" ]; then
          if [[ "$CI_COMMIT_TAG" =~ ^[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
              echo "Check if the docker image exists"
              if ! crane digest ${DOCKER_IMAGE_REPOSITORY}/${RELEASE_IMAGE_PATH}/${connector}:${CI_COMMIT_TAG}; then
                echo "Image for $connector is not available... retagging $CI_COMMIT_SHORT_SHA as $CI_COMMIT_TAG"
                crane cp ${DOCKER_IMAGE_REPOSITORY}/${DEV_IMAGE_PATH}/${connector}:${CI_COMMIT_SHORT_SHA} ${DOCKER_IMAGE_REPOSITORY}/${RELEASE_IMAGE_PATH}/${connector}:${CI_COMMIT_TAG}
                crane cp ${DOCKER_IMAGE_REPOSITORY}/${DEV_IMAGE_PATH}/${connector}:${CI_COMMIT_SHORT_SHA} ${DOCKER_IMAGE_REPOSITORY}/${DEV_IMAGE_PATH}/${connector}:${CI_COMMIT_TAG}
                crane cp ${DOCKER_IMAGE_REPOSITORY}/${DEV_IMAGE_PATH}/${connector}:${CI_COMMIT_SHORT_SHA} ${DOCKER_IMAGE_REPOSITORY}/${RELEASE_IMAGE_PATH}/${connector}:latest
                echo "Done with $connector:$CI_COMMIT_TAG"
              else
                echo "Image $connector:${CI_COMMIT_TAG} is available"
              fi
          else
              echo "Tagging docker image for connector $connector as ${CI_COMMIT_TAG}"
              crane cp ${DOCKER_IMAGE_REPOSITORY}/${DEV_IMAGE_PATH}/${connector}:${CI_COMMIT_SHORT_SHA} ${DOCKER_IMAGE_REPOSITORY}/${DEV_IMAGE_PATH}/${connector}:${CI_COMMIT_TAG}
              echo "DONE with $connector:${CI_COMMIT_TAG}"
          fi
        fi
      done

audit:
  variables:
    AUDIT_EXTRA_PARAMS: "-c connectors"
  extends: .audit_trails

deploy-mock-dev:
  extends: .deploy_helm
  variables:
    RELEASE_NAME: connector-wrapper-mock
  script:
    - helm repo add --username $NEXUS_USER --password $NEXUS_PASSWORD wyden https://repo.wyden.io/nexus/repository/wyden-dev/
    - helm repo update
    - helm pull wyden/wyden-mock --untar
    - helm upgrade --install --namespace ${NAMESPACE} ${RELEASE_NAME} ./wyden-mock -f ./wyden-mock/values-${STAGE}-aws.yaml --set image.tag=${IMAGE_TAG} --timeout 20m

tag_rc:
  extends: .tag_repository
  needs:
    - job: publish
      optional: true

tag_rc_scheduled:
  extends: .tag_repository_scheduled
