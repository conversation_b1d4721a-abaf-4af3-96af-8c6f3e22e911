include:
  - project: infrastructure/components
    ref: main
    file: components.yml
  - template: Security/SAST.gitlab-ci.yml

image: 739275467562.dkr.ecr.eu-central-1.amazonaws.com/dockerhub/library/maven:3.8.4-eclipse-temurin-17

variables:
  IMAGE_NAME: quoting-engine
  # Used in the deploy stage (see the components repo)
  RELEASE_NAME: quoting-engine

stages:
  - version
  - test
  - build
  - publish
  - scan
  - deploy
  - tag
  - qa

version-verify:
  extends: .pipeline-verification

changes-verify:
  extends: .verify-locked-files

semgrep-sast:
  rules:
    - if: $CI_COMMIT_TAG
      when: never

sonarqube-check:
  extends: .sonar-gradle

version-check-domain:
  variables:
    VERSION_FILE: "quoting-engine-domain/version.properties"
    SOURCE_FILES: "quoting-engine-domain/src/**/*"
  extends: .lib-version-check

unit-test:
  extends: .unit_test

integration-test:
  extends: .integration_test

build:
  extends: .build-gradle

publish-lib:
  extends: .publish-lib

publish:
  variables:
    DOCKER_BUILD_CONTEXT: quoting-engine-service/.
    DOCKERFILE_PATH: quoting-engine-service/Dockerfile
  extends: .publish_image

container-scan:
  extends: .docker_scan

retag:
  extends: .retag_image

audit:
  extends: .audit_trails

deploy-dev:
  extends: .deploy_helm

tag_rc:
  extends: .tag_repository

tag_rc_scheduled:
  extends: .tag_repository_scheduled
