include:
  - project: infrastructure/components
    ref: main
    file: components.yml

image: 739275467562.dkr.ecr.eu-central-1.amazonaws.com/dockerhub/library/maven:3.8.4-eclipse-temurin-17

stages:
  - version
  - test
  - build
  - publish
  - scan
  - deploy
  - tag
  - qa

variables:
  IMAGE_NAME: storage
  # Used in the deploy stage (see the components repo)
  RELEASE_NAME: storage

version-verify:
  extends: .pipeline-verification

changes-verify:
  extends: .verify-locked-files

sonarqube-check:
  extends: .sonar-gradle

integration-test:
  extends: .integration_test

build:
  extends: .build-gradle

publish:
  extends: .publish_image

container-scan:
  extends: .docker_scan

retag:
  extends: .retag_image

audit:
  extends: .audit_trails

deploy-dev:
  extends: .deploy_helm

tag_rc:
  extends: .tag_repository

tag_rc_scheduled:
  extends: .tag_repository_scheduled
