include:
  - template: Security/SAST.gitlab-ci.yml

image: maven:3.8.********************

stages:
  - version
  - test
  - build
  - publish

default:
  retry:
    max: 2
    when: runner_system_failure


version-check-rabbitmq:
  stage: version
  interruptible: true
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $CI_DEFAULT_BRANCH
      changes:
        - cloud-utils-rabbitmq/version.properties
      when: never
      allow_failure: false
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $CI_DEFAULT_BRANCH
      changes:
        - cloud-utils-rabbitmq/src/**/*
      when: always
      allow_failure: false
    - when: never
  script:
    - echo "cloud-utils-rabbitmq - Version not updated. Please update version.properties file"
    - exit 1
  tags:
    - kubernetes

version-check-destinations:
  stage: version
  interruptible: true
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $CI_DEFAULT_BRANCH
      changes:
        - cloud-utils-rabbitmq-destinations/version.properties
      when: never
      allow_failure: false
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $CI_DEFAULT_BRANCH
      changes:
        - cloud-utils-rabbitmq-destinations/src/**/*
      when: always
      allow_failure: false
    - when: never
  script:
    - echo "cloud-utils-rabbitmq-destinations - Version not updated. Please update version.properties file"
    - exit 1
  tags:
    - kubernetes

version-check-hazelcast:
  stage: version
  interruptible: true
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $CI_DEFAULT_BRANCH
      changes:
        - cloud-utils-hazelcast/version.properties
      when: never
      allow_failure: false
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $CI_DEFAULT_BRANCH
      changes:
        - cloud-utils-hazelcast/src/**/*
      when: always
      allow_failure: false
    - when: never
  script:
    - echo "cloud-utils-hazelcast - Version not updated. Please update version.properties file"
    - exit 1
  tags:
    - kubernetes

version-check-tools:
  stage: version
  interruptible: true
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $CI_DEFAULT_BRANCH
      changes:
        - cloud-utils-tools/version.properties
      when: never
      allow_failure: false
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $CI_DEFAULT_BRANCH
      changes:
        - cloud-utils-tools/src/**/*
      when: always
      allow_failure: false
    - when: never
  script:
    - echo "cloud-utils-tools - Version not updated. Please update version.properties file"
    - exit 1
  tags:
    - kubernetes

version-check-rest:
  stage: version
  interruptible: true
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $CI_DEFAULT_BRANCH
      changes:
        - cloud-utils-rest/version.properties
      when: never
      allow_failure: false
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $CI_DEFAULT_BRANCH
      changes:
        - cloud-utils-rest/src/**/*
      when: always
      allow_failure: false
    - when: never
  script:
    - echo "cloud-utils-rest - Version not updated. Please update version.properties file"
    - exit 1
  tags:
    - kubernetes

version-check-spring:
  stage: version
  interruptible: true
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $CI_DEFAULT_BRANCH
      changes:
        - cloud-utils-spring/version.properties
      when: never
      allow_failure: false
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $CI_DEFAULT_BRANCH
      changes:
        - cloud-utils-spring/src/**/*
      when: always
      allow_failure: false
    - when: never
  script:
    - echo "cloud-utils-spring - Version not updated. Please update version.properties file"
    - exit 1
  tags:
    - kubernetes

version-check-test:
  stage: version
  interruptible: true
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $CI_DEFAULT_BRANCH
      changes:
        - cloud-utils-test/version.properties
      when: never
      allow_failure: false
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $CI_DEFAULT_BRANCH
      changes:
        - cloud-utils-test/src/**/*
      when: always
      allow_failure: false
    - when: never
  script:
    - echo "cloud-utils-test - Version not updated. Please update version.properties file"
    - exit 1
  tags:
    - kubernetes

version-check-test-client:
  stage: version
  interruptible: true
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $CI_DEFAULT_BRANCH
      changes:
        - cloud-utils-test-client/version.properties
      when: never
      allow_failure: false
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $CI_DEFAULT_BRANCH
      changes:
        - cloud-utils-test-client/src/**/*
      when: always
      allow_failure: false
    - when: never
  script:
    - echo "cloud-utils-test-client - Version not updated. Please update version.properties file"
    - exit 1
  tags:
    - kubernetes

version-check-telemetry:
  stage: version
  interruptible: true
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $CI_DEFAULT_BRANCH
      changes:
        - cloud-utils-telemetry/version.properties
      when: never
      allow_failure: false
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $CI_DEFAULT_BRANCH
      changes:
        - cloud-utils-telemetry/src/**/*
      when: always
      allow_failure: false
    - when: never
  script:
    - echo "cloud-utils-telemetry - Version not updated. Please update version.properties file"
    - exit 1
  tags:
    - kubernetes

unit-test:
  stage: test
  interruptible: true
  services:
    - name: docker:20.10-dind
      command: [ "--tls=false" ]
  variables:
    DOCKER_HOST: tcp://docker:2375
    DOCKER_TLS_CERTDIR: ""
    DOCKER_DRIVER: overlay2
    TESTCONTAINERS_RYUK_DISABLED: "true"
  script:
    - ./gradlew test
  artifacts:
    when: always
    reports:
      junit:
        - build/test-results/test/**/TEST-*.xml
    expire_in: 1 days
  tags:
    - kubernetes

integration-test:
  stage: test
  interruptible: true
  services:
    - name: docker:20.10-dind
      command: ["--tls=false"]
  variables:
    DOCKER_HOST: tcp://docker:2375
    DOCKER_TLS_CERTDIR: ""
    DOCKER_DRIVER: overlay2
    TESTCONTAINERS_RYUK_DISABLED: "true"
  rules:
    - changes:
        - cloud-utils-rabbitmq/src/**/*
      when: always
      allow_failure: false
    - when: never
  script:
    - ./gradlew integrationTest
  artifacts:
    when: always
    reports:
      junit:
        - build/test-results/integrationTest/**/TEST-*.xml
    expire_in: 1 days
  tags:
    - kubernetes

sonarqube-check:
  stage: test
  interruptible: true
  variables:
    # SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar" # Defines the location of the analysis task cache
    # GIT_DEPTH: "0" # Tells git to fetch all the branches of the project, required by the analysis task
    MAVEN_OPTS: "-Xmx2048m -XX:MaxPermSize=1024m"
  # cache:
  #   key: "${CI_JOB_NAME}"
  #   paths:
  #     - .sonar/cache
  script:
    - ./gradlew sonarqube --stacktrace
  allow_failure: true
  only:
    - main
  tags:
    - kubernetes

build:
  stage: build
  interruptible: true
  script:
    - ./gradlew assemble
  cache: &artifact_cache
    key: "$CI_COMMIT_SHA"
    paths:
      - build/libs/
    policy: push
  tags:
    - kubernetes

publish-lib:
  stage: publish
  interruptible: true
  only:
    - main
  script:
    - ./gradlew publish
  dependencies:
    - build
  cache:
    <<: *artifact_cache
    policy: pull
  tags:
    - kubernetes
