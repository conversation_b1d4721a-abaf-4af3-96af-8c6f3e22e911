plugins {
    id 'java-library'
    id 'maven-publish'
    alias dependencyCatalog.plugins.protobuf
    alias dependencyCatalog.plugins.sonarqube
    alias dependencyCatalog.plugins.jacocoToCobertura
}

def versionPropsFile = file('version.properties')
if (versionPropsFile.canRead()) {
    Properties versionProps = new Properties()
    versionProps.load(new FileInputStream(versionPropsFile))
    def ver = versionProps['VERSION'].toString()
    project.version = ver
} else {
    throw new GradleException("Could not read version.properties!")
}

publishing {
    publications {
        mavenJava(MavenPublication) {
            repositories {
                maven {
                    name 'nexus-snapshots'
                    url 'https://repo.wyden.io/nexus/repository/snapshots/'
                    credentials {
                        username repository_username
                        password repository_password
                    }
                }
            }
            from components.java
        }
    }
}

dependencies {
    implementation dependencyCatalog.published.language.oems
    implementation dependencyCatalog.protobuf.java
    implementation dependencyCatalog.hazelcast
    implementation dependencyCatalog.cloud.utils.hazelcast
    implementation dependencyCatalog.cloud.utils.telemetry
    implementation dependencyCatalog.jakarta.annotation
}

protobuf {
    protoc {
        // The artifact spec for the Protobuf Compiler
        artifact = dependencyCatalog.protobuf.protoc.get()
    }
}

sonarqube {
    properties {
        property "sonar.projectKey", "rest-api-domain"
        property "sonar.projectName", "REST API Domain"
    }
}
