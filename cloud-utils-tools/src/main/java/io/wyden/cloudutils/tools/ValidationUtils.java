package io.wyden.cloudutils.tools;

import io.wyden.cloudutils.exceptions.ValidationException;

import java.math.BigDecimal;
import java.util.Objects;

import static java.math.BigDecimal.ZERO;

public class ValidationUtils {

    private ValidationUtils() {
        // Empty
    }

    /**
     * Validates that a given value is a positive number and
     * a multiple of a specified increment. If the validation fails,
     * a ValidationException is thrown.
     *
     * @param valueToCheck the value to be checked for validation, must not be null*/
    public static void validateStepIncrement(BigDecimal valueToCheck, BigDecimal increment, String valueName) throws ValidationException {
        Objects.requireNonNull(valueToCheck);
        Objects.requireNonNull(increment);

        if (valueToCheck.compareTo(BigDecimal.ZERO) <= 0 || increment.compareTo(BigDecimal.ZERO) <= 0) {
            throw new ValidationException("%s - incorrect multiplication of step increment - values must be positive numbers".formatted(valueName));
        }

        if (valueToCheck.remainder(increment).compareTo(ZERO) != 0) {
            throw new ValidationException("%s - incorrect multiplication of step increment = %s".formatted(valueName, increment.toPlainString()));
        }
    }
}
