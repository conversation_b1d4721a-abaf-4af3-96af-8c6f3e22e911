package io.wyden.cloudutils.tools;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Timestamp;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.chrono.ChronoZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.format.DateTimeParseException;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;

import static org.apache.commons.lang3.StringUtils.EMPTY;

/**
 * Utility class for date and time conversions between various formats commonly used in financial trading systems.
 * 
 * This class provides comprehensive conversion capabilities between:
 * - FIX protocol timestamps (FIX 4.4 compliant)
 * - ISO 8601 UTC timestamps with microsecond precision
 * - Epoch time representations (milliseconds, microseconds, nanoseconds)
 * - SQL Timestamps and Java time objects
 * 
 * All methods are null-safe and return appropriate null values or empty strings when inputs are null or invalid.
 * Parsing errors are logged and handled gracefully without throwing exceptions.
 * 
 * <AUTHOR> Platform
 * @since 1.0
 */
public final class DateUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(DateUtils.class);

    /**
     * UTC timezone constant used throughout the utility for consistent timezone handling.
     */
    public static final ZoneId UTC = ZoneId.of("UTC");

    /**
     * FIX protocol timestamp formatter with millisecond precision.
     * Format: "yyyyMMdd-HH:mm:ss.SSS" in UTC timezone.
     * Example: "20231215-14:30:25.123"
     */
    public static final DateTimeFormatter FIX_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd-HH:mm:ss.SSS").withZone(UTC);
    
    /**
     * FIX protocol timestamp formatter without millisecond precision.
     * Format: "yyyyMMdd-HH:mm:ss" in UTC timezone.
     * Example: "20231215-14:30:25"
     */
    public static final DateTimeFormatter FIX_TIME_FORMATTER_SHORT = DateTimeFormatter.ofPattern("yyyyMMdd-HH:mm:ss").withZone(UTC);

    /**
     * ISO8601 formatter in UTC timezone with microsecond precision.
     * Precision is aligned with PostgreSQL timestamp precision requirements.
     * Example: "2023-12-15T14:30:25.123456Z"
     */
    public static final DateTimeFormatter ISO_UTC_FORMATTER;
    static {
        ISO_UTC_FORMATTER = new DateTimeFormatterBuilder()
            .parseCaseInsensitive()
            .appendInstant(6)
            .toFormatter();
    }

    /**
     * Private constructor to prevent instantiation of this utility class.
     */
    private DateUtils() {
        // Empty
    }

    /**
     * Converts the current date/time to FIX protocol UTC timestamp format.
     * 
     * @return current timestamp in FIX format (yyyyMMdd-HH:mm:ss.SSS)
     * @see #toFixUtcTime(ZonedDateTime)
     */
    @Nonnull
    public static String toFixUtcTime() {
        return toFixUtcTime(ZonedDateTime.now());
    }

    /**
     * Converts a ZonedDateTime to FIX protocol UTC timestamp format.
     * 
     * @param dateTime the ZonedDateTime to convert, can be null
     * @return FIX formatted timestamp string, or empty string if input is null
     * 
     * <pre>{@code
     * ZonedDateTime dt = ZonedDateTime.of(2023, 12, 15, 14, 30, 25, 123000000, ZoneId.of("America/New_York"));
     * String result = DateUtils.toFixUtcTime(dt); // Returns "20231215-19:30:25.123"
     * }</pre>
     */
    @Nonnull
    public static String toFixUtcTime(@Nullable ZonedDateTime dateTime) {
        return Objects.nonNull(dateTime) ? dateTime.withZoneSameInstant(UTC).format(FIX_TIME_FORMATTER) : EMPTY;
    }

    /**
     * Parses a FIX protocol UTC timestamp string to ZonedDateTime.
     * Supports both long format (with milliseconds) and short format (without milliseconds).
     * 
     * @param fixUtcTime FIX formatted timestamp string, can be null or blank
     * @return parsed ZonedDateTime in UTC, or null if input is null/blank/invalid
     * 
     * <pre>{@code
     * ZonedDateTime result1 = DateUtils.fromFixUtcTime("20231215-14:30:25.123"); // With milliseconds
     * ZonedDateTime result2 = DateUtils.fromFixUtcTime("20231215-14:30:25"); // Without milliseconds
     * ZonedDateTime result3 = DateUtils.fromFixUtcTime(null); // Returns null
     * }</pre>
     */
    @Nullable
    public static ZonedDateTime fromFixUtcTime(@Nullable String fixUtcTime) {
        if (StringUtils.isBlank(fixUtcTime)) {
            return null;
        }
        return fromFixUtcTimeLong(fixUtcTime);
    }

    private static ZonedDateTime fromFixUtcTimeLong(@Nonnull String fixUtcTime) {
        try {
            return ZonedDateTime.parse(fixUtcTime, FIX_TIME_FORMATTER);
        } catch (DateTimeParseException ex) {
            return fromFixUtcTimeShort(fixUtcTime);
        }
    }

    private static ZonedDateTime fromFixUtcTimeShort(@Nonnull String fixUtcTime) {
        try {
            return ZonedDateTime.parse(fixUtcTime, FIX_TIME_FORMATTER_SHORT);
        } catch (DateTimeParseException ex) {
            LOGGER.warn("Exception when parsing FIX UTCTimestamp: {} - {}", fixUtcTime, ex.getMessage());
            return null;
        }
    }

    /**
     * Converts the current date/time to ISO 8601 UTC timestamp format.
     * 
     * @return current timestamp in ISO UTC format with microsecond precision
     * @see #toIsoUtcTime(ZonedDateTime)
     */
    @Nonnull
    public static String toIsoUtcTime() {
        return toIsoUtcTime(ZonedDateTime.now());
    }

    /**
     * Converts a ZonedDateTime to ISO 8601 UTC timestamp format.
     * 
     * @param dateTime the ZonedDateTime to convert, can be null
     * @return ISO UTC formatted timestamp string, or empty string if input is null
     * 
     * <pre>{@code
     * ZonedDateTime dt = ZonedDateTime.of(2023, 12, 15, 14, 30, 25, 123456000, ZoneId.of("Europe/London"));
     * String result = DateUtils.toIsoUtcTime(dt); // Returns "2023-12-15T14:30:25.123456Z"
     * }</pre>
     */
    @Nonnull
    public static String toIsoUtcTime(@Nullable ZonedDateTime dateTime) {
        return Objects.nonNull(dateTime) ? toIsoUtcTime(dateTime.toInstant()) : EMPTY;
    }

    /**
     * Converts a SQL Timestamp to ISO 8601 UTC timestamp format.
     * 
     * @param timestamp the SQL Timestamp to convert, can be null
     * @return ISO UTC formatted timestamp string, or empty string if input is null
     * 
     * <pre>{@code
     * Timestamp ts = Timestamp.valueOf("2023-12-15 14:30:25.123456");
     * String result = DateUtils.toIsoUtcTime(ts); // Returns "2023-12-15T14:30:25.123456Z"
     * }</pre>
     */
    @Nonnull
    public static String toIsoUtcTime(@Nullable Timestamp timestamp) {
        return Objects.nonNull(timestamp)? toIsoUtcTime(timestamp.toInstant()) : EMPTY;
    }

    /**
     * Converts an Instant to ISO 8601 UTC timestamp format.
     * 
     * @param instant the Instant to convert, can be null
     * @return ISO UTC formatted timestamp string, or empty string if input is null
     * 
     * <pre>{@code
     * Instant instant = Instant.ofEpochMilli(1702647025123L);
     * String result = DateUtils.toIsoUtcTime(instant); // Returns "2023-12-15T14:30:25.123000Z"
     * }</pre>
     */
    @Nonnull
    public static String toIsoUtcTime(@Nullable Instant instant) {
        return Objects.nonNull(instant) ? instant.atZone(UTC).format(ISO_UTC_FORMATTER) : EMPTY;
    }

    /**
     * Parses an ISO 8601 UTC timestamp string to ZonedDateTime.
     * 
     * @param isoUtcTime ISO UTC formatted timestamp string
     * @return parsed ZonedDateTime in UTC, or null if parsing fails
     * 
     * <pre>{@code
     * ZonedDateTime result = DateUtils.isoUtcTimeToZonedDateTime("2023-12-15T14:30:25.123456Z");
     * // Returns ZonedDateTime representing 2023-12-15 14:30:25.123456 UTC
     * }</pre>
     */
    @Nullable
    public static ZonedDateTime isoUtcTimeToZonedDateTime(@Nonnull String isoUtcTime) {
        return Optional.of(isoUtcTime)
            .map(DateUtils::isoUtcTimeToInstant)
            .map(i -> i.atZone(UTC))
            .orElse(null);
    }

    /**
     * Parses an ISO 8601 UTC timestamp string to SQL Timestamp.
     * 
     * @param isoUtcTime ISO UTC formatted timestamp string
     * @return parsed SQL Timestamp, or null if parsing fails
     * 
     * <pre>{@code
     * Timestamp result = DateUtils.isoUtcTimeToTimestamp("2023-12-15T14:30:25.123456Z");
     * // Returns Timestamp representing 2023-12-15 14:30:25.123456
     * }</pre>
     */
    @Nullable
    public static Timestamp isoUtcTimeToTimestamp(@Nonnull String isoUtcTime) {
        return Optional.of(isoUtcTime)
            .map(DateUtils::isoUtcTimeToInstant)
            .map(Timestamp::from)
            .orElse(null);
    }

    /**
     * Parses an ISO 8601 UTC timestamp string to Instant.
     * 
     * @param instantStr ISO UTC formatted timestamp string
     * @return parsed Instant, or null if input is blank or parsing fails
     * 
     * <pre>{@code
     * Instant result = DateUtils.isoUtcTimeToInstant("2023-12-15T14:30:25.123456Z");
     * // Returns Instant representing 2023-12-15 14:30:25.123456 UTC
     * }</pre>
     */
    @Nullable
    public static Instant isoUtcTimeToInstant(@Nonnull String instantStr) {
        try {
            return StringUtils.isNotBlank(instantStr) ? Instant.parse(instantStr) : null;
        } catch (Exception ex) {
            LOGGER.warn("Unable to parse ISO date time: '{}'", instantStr);
            return null;
        }
    }

    /**
     * Converts an ISO 8601 UTC timestamp string to epoch milliseconds.
     * 
     * @param isoString ISO UTC formatted timestamp string
     * @return epoch milliseconds as string, or null if parsing fails
     * 
     * <pre>{@code
     * String result = DateUtils.isoUtcTimeToEpochMillis("2023-12-15T14:30:25.123Z");
     * // Returns "1702647025123"
     * }</pre>
     */
    @Nullable
    public static String isoUtcTimeToEpochMillis(@Nonnull String isoString) {
        return Optional.of(isoString)
            .map(DateUtils::isoUtcTimeToInstant)
            .map(Instant::toEpochMilli)
            .map(String::valueOf)
            .orElse(null);
    }

    /**
     * Converts epoch milliseconds string to ISO 8601 UTC timestamp format.
     * 
     * @param epochMillis epoch milliseconds as string, can be null
     * @return ISO UTC formatted timestamp string, or empty string if input is null/invalid
     * 
     * <pre>{@code
     * String result = DateUtils.epochMillisToIsoUtcTime("1702647025123");
     * // Returns "2023-12-15T14:30:25.123000Z"
     * }</pre>
     */
    @Nonnull
    public static String epochMillisToIsoUtcTime(@Nullable String epochMillis) {
        return Optional.ofNullable(epochMillis)
            .map(DateUtils::epochMillisToInstant)
            .map(DateUtils::toIsoUtcTime)
            .orElse(EMPTY);
    }

    /**
     * Converts a ZonedDateTime to epoch milliseconds string.
     * 
     * @param zonedDateTime the ZonedDateTime to convert, can be null
     * @return epoch milliseconds as string, or null if input is null
     * 
     * <pre>{@code
     * ZonedDateTime dt = ZonedDateTime.of(2023, 12, 15, 14, 30, 25, 123000000, ZoneId.systemDefault());
     * String result = DateUtils.zonedDateTimeToEpochMillis(dt); // Returns "1702647025123"
     * }</pre>
     */
    @Nullable
    public static String zonedDateTimeToEpochMillis(@Nullable ZonedDateTime zonedDateTime) {
        return Optional.ofNullable(zonedDateTime)
            .map(ChronoZonedDateTime::toInstant)
            .map(DateUtils::instantToEpochMillis)
            .orElse(null);
    }

    /**
     * Converts a ZonedDateTime to epoch microseconds string.
     * 
     * @param zonedDateTime the ZonedDateTime to convert, can be null
     * @return epoch microseconds as string, or null if input is null
     * 
     * <pre>{@code
     * ZonedDateTime dt = ZonedDateTime.of(2023, 12, 15, 14, 30, 25, 123456000, ZoneId.systemDefault());
     * String result = DateUtils.zonedDateTimeToEpochMicros(dt); // Returns "1702647025123456"
     * }</pre>
     */
    @Nullable
    public static String zonedDateTimeToEpochMicros(@Nullable ZonedDateTime zonedDateTime) {
        return Optional.ofNullable(zonedDateTime)
            .map(ChronoZonedDateTime::toInstant)
            .map(DateUtils::instantToEpochMicros)
            .orElse(null);
    }

    /**
     * Converts epoch milliseconds string to ZonedDateTime in UTC.
     * 
     * @param epochMillis epoch milliseconds as string, can be null
     * @return ZonedDateTime in UTC, or null if input is null/invalid
     * 
     * <pre>{@code
     * ZonedDateTime result = DateUtils.epochMillisToZonedDateTime("1702647025123");
     * // Returns ZonedDateTime representing 2023-12-15 14:30:25.123 UTC
     * }</pre>
     */
    @Nullable
    public static ZonedDateTime epochMillisToZonedDateTime(@Nullable String epochMillis) {
        return Optional.ofNullable(epochMillis)
            .map(DateUtils::epochMillisToInstant)
            .map(i -> i.atZone(UTC))
            .orElse(null);
    }

    /**
     * Converts epoch microseconds string to ZonedDateTime in UTC.
     * 
     * @param epochMicros epoch microseconds as string, can be null
     * @return ZonedDateTime in UTC, or null if input is null/invalid
     * 
     * <pre>{@code
     * ZonedDateTime result = DateUtils.epochMicrosToZonedDateTime("1702647025123456");
     * // Returns ZonedDateTime representing 2023-12-15 14:30:25.123456 UTC
     * }</pre>
     */
    @Nullable
    public static ZonedDateTime epochMicrosToZonedDateTime(@Nullable String epochMicros) {
        return Optional.ofNullable(epochMicros)
            .map(DateUtils::epochMicrosToInstant)
            .map(i -> i.atZone(UTC))
            .orElse(null);
    }

    /**
     * Converts a SQL Timestamp to epoch milliseconds string.
     * 
     * @param timestamp the SQL Timestamp to convert, can be null
     * @return epoch milliseconds as string, or null if input is null
     * 
     * <pre>{@code
     * Timestamp ts = Timestamp.valueOf("2023-12-15 14:30:25.123");
     * String result = DateUtils.sqlTimestampToEpochMillis(ts); // Returns "1702647025123"
     * }</pre>
     */
    @Nullable
    public static String sqlTimestampToEpochMillis(@Nullable Timestamp timestamp) {
        return Objects.nonNull(timestamp) ? instantToEpochMillis(timestamp.toInstant()) : null;
    }

    /**
     * Converts epoch milliseconds string to SQL Timestamp.
     * 
     * @param epochMillis epoch milliseconds as string, can be null
     * @return SQL Timestamp, or null if input is null/invalid
     * 
     * <pre>{@code
     * Timestamp result = DateUtils.epochMillisToSqlTimestamp("1702647025123");
     * // Returns Timestamp representing 2023-12-15 14:30:25.123
     * }</pre>
     */
    @Nullable
    public static Timestamp epochMillisToSqlTimestamp(@Nullable String epochMillis) {
        Instant instant = epochMillisToInstant(epochMillis);
        return instant != null ? Timestamp.from(instant) : null;
    }

    /**
     * Converts epoch microseconds string to SQL Timestamp.
     * 
     * @param epochMicros epoch microseconds as string, can be null
     * @return SQL Timestamp, or null if input is null/invalid
     * 
     * <pre>{@code
     * Timestamp result = DateUtils.epochMicrosToSqlTimestamp("1702647025123456");
     * // Returns Timestamp representing 2023-12-15 14:30:25.123456
     * }</pre>
     */
    @Nullable
    public static Timestamp epochMicrosToSqlTimestamp(@Nullable String epochMicros) {
        Instant instant = epochMicrosToInstant(epochMicros);
        return instant != null ? Timestamp.from(instant) : null;
    }

    /**
     * Gets the current time as epoch milliseconds string.
     * 
     * @return current time in epoch milliseconds as string
     * 
     * <pre>{@code
     * String result = DateUtils.toEpochMillis(); // Returns current time like "1702647025123"
     * }</pre>
     */
    @Nullable
    public static String toEpochMillis() {
        return instantToEpochMillis(Instant.now());
    }

    /**
     * Converts an Instant to epoch milliseconds string.
     * 
     * @param instant the Instant to convert, can be null
     * @return epoch milliseconds as string, or null if input is null
     * 
     * <pre>{@code
     * Instant instant = Instant.ofEpochMilli(1702647025123L);
     * String result = DateUtils.instantToEpochMillis(instant); // Returns "1702647025123"
     * }</pre>
     */
    @Nullable
    public static String instantToEpochMillis(@Nullable Instant instant) {
        return Objects.nonNull(instant) ? String.valueOf(instant.toEpochMilli()) : null;
    }

    /**
     * Gets the current time as epoch microseconds string.
     * 
     * @return current time in epoch microseconds as string
     * 
     * <pre>{@code
     * String result = DateUtils.toEpochMicros(); // Returns current time like "1702647025123456"
     * }</pre>
     */
    @Nullable
    public static String toEpochMicros() {
        return instantToEpochMicros(Instant.now());
    }

    /**
     * Converts an Instant to epoch microseconds string.
     * Calculates total microseconds from epoch by combining seconds and nanoseconds.
     * 
     * @param instant the Instant to convert, can be null
     * @return epoch microseconds as string, or null if input is null
     * 
     * <pre>{@code
     * Instant instant = Instant.ofEpochSecond(1702647025, 123456000);
     * String result = DateUtils.instantToEpochMicros(instant); // Returns "1702647025123456"
     * }</pre>
     */
    @Nullable
    public static String instantToEpochMicros(@Nullable Instant instant) {
        if (Objects.nonNull(instant)) {
            long epochSeconds = instant.getEpochSecond();
            int nanoAdjustment = instant.getNano();
            long totalMicroseconds = epochSeconds * 1_000_000 + nanoAdjustment / 1_000;
            return String.valueOf(totalMicroseconds);
        } else {
            return null;
        }
    }

    /**
     * Converts epoch milliseconds string to Instant.
     * 
     * @param epochMillis epoch milliseconds as string, can be null or blank
     * @return parsed Instant, or null if input is null/blank/invalid
     * 
     * <pre>{@code
     * Instant result = DateUtils.epochMillisToInstant("1702647025123");
     * // Returns Instant representing 2023-12-15 14:30:25.123 UTC
     * }</pre>
     */
    @Nullable
    public static Instant epochMillisToInstant(@Nullable String epochMillis) {
        try {
            return StringUtils.isNotBlank(epochMillis) ? Instant.ofEpochMilli(Long.parseLong(epochMillis)) : null;
        } catch (Exception ex) {
            LOGGER.warn("Unable to convert epoch milliseconds: '{}'", epochMillis);
            return null;
        }
    }

    /**
     * Converts epoch microseconds string to Instant.
     * Splits microseconds into seconds and nanosecond components for precise conversion.
     * 
     * @param epochMicros epoch microseconds as string, can be null or blank
     * @return parsed Instant, or null if input is null/blank/invalid
     * 
     * <pre>{@code
     * Instant result = DateUtils.epochMicrosToInstant("1702647025123456");
     * // Returns Instant representing 2023-12-15 14:30:25.123456 UTC
     * }</pre>
     */
    @Nullable
    public static Instant epochMicrosToInstant(@Nullable String epochMicros) {
        try {
            if (StringUtils.isNotBlank(epochMicros)) {
                long micros = Long.parseLong(epochMicros);
                long seconds = micros / 1_000_000;
                long microsRemainder = micros % 1_000_000;
                long nanos = microsRemainder * 1_000;
                return Instant.ofEpochSecond(seconds, nanos);
            } else {
                return null;
            }
        } catch (Exception ex) {
            LOGGER.warn("Unable to convert epoch microseconds: '{}'", epochMicros);
            return null;
        }
    }

    /**
     * Converts epoch nanoseconds string to Instant.
     * 
     * @param epochNanos epoch nanoseconds as string, can be null or blank
     * @return parsed Instant, or null if input is null/blank/invalid
     * 
     * <pre>{@code
     * Instant result = DateUtils.epochNanosToInstant("1702647025123456789");
     * // Returns Instant representing 2023-12-15 14:30:25.123456789 UTC
     * }</pre>
     */
    @Nullable
    public static Instant epochNanosToInstant(@Nullable String epochNanos) {
        try {
            return StringUtils.isNotBlank(epochNanos) ? Instant.ofEpochSecond(0L, TimeUnit.NANOSECONDS.toNanos(Long.parseLong(epochNanos))) : null;
        } catch (Exception ex) {
            LOGGER.warn("Unable to convert epoch nanoseconds: '{}'", epochNanos);
            return null;
        }
    }

    /**
     * Converts an ISO 8601 UTC timestamp string to epoch nanoseconds.
     * 
     * @param timestamp ISO UTC formatted timestamp string, can be null
     * @return epoch nanoseconds as string, or null if input is null/invalid
     * 
     * <pre>{@code
     * String result = DateUtils.isoUtcTimeToEpochNano("2023-12-15T14:30:25.123456Z");
     * // Returns "1702647025123456000"
     * }</pre>
     */
    @Nullable
    public static String isoUtcTimeToEpochNano(@Nullable String timestamp) {
        return Optional.ofNullable(timestamp)
            .map(DateUtils::isoUtcTimeToInstant)
            .map(instant -> TimeUnit.SECONDS.toNanos(instant.getEpochSecond()) + TimeUnit.NANOSECONDS.toNanos(instant.getNano()))
            .map(String::valueOf)
            .orElse(null);
    }

    /**
     * Converts epoch nanoseconds string to ISO 8601 UTC timestamp format.
     * 
     * @param epochNanos epoch nanoseconds as string, can be null
     * @return ISO UTC formatted timestamp string, or empty string if input is null/invalid
     * 
     * <pre>{@code
     * String result = DateUtils.epochNanosToIsoUtcTime("1702647025123456789");
     * // Returns "2023-12-15T14:30:25.123456789Z"
     * }</pre>
     */
    @Nonnull
    public static String epochNanosToIsoUtcTime(@Nullable String epochNanos) {
        return Optional.ofNullable(epochNanos)
            .map(DateUtils::epochNanosToInstant)
            .map(DateUtils::toIsoUtcTime)
            .orElse(EMPTY);
    }
}
