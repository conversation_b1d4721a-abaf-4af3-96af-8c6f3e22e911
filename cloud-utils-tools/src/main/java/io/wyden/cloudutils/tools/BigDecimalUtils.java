package io.wyden.cloudutils.tools;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import javax.annotation.Nullable;

import static java.math.BigDecimal.ZERO;
import static org.apache.commons.lang3.StringUtils.isBlank;

/**
 * Utility class providing convenient methods for working with {@link BigDecimal} objects.
 * This class contains static methods for creation, validation, comparison, and formatting
 * of BigDecimal values commonly used in financial calculations.
 */
public final class BigDecimalUtils {

    private BigDecimalUtils() {
        // Empty
    }

    /**
     * Creates a BigDecimal from a string value.
     * 
     * @param val the string representation of the decimal value
     * @return a new BigDecimal instance, or null if the input string is blank
     * @throws NumberFormatException if the string is not a valid representation of a BigDecimal
     * 
     * Examples:
     * BigDecimalUtils.bd("123.45") returns BigDecimal representing 123.45
     * BigDecimalUtils.bd("0.001") returns BigDecimal representing 0.001
     * BigDecimalUtils.bd("") returns null
     * BigDecimalUtils.bd("   ") returns null
     * BigDecimalUtils.bd("invalid") throws NumberFormatException
     */
    @Nullable
    public static BigDecimal bd(String val) {
        if (isBlank(val)) {
            return null;
        }

        return new BigDecimal(val);
    }

    /**
     * Creates a BigDecimal from an integer value.
     * 
     * @param val the integer value
     * @return a new BigDecimal instance representing the integer value
     */
    public static BigDecimal bd(int val) {
        return new BigDecimal(val);
    }

    /**
     * Creates a BigDecimal from a double value.
     * Uses {@link BigDecimal#valueOf(double)} to avoid precision issues.
     * 
     * @param val the double value
     * @return a new BigDecimal instance representing the double value
     */
    public static BigDecimal bd(double val) {
        return BigDecimal.valueOf(val);
    }

    /**
     * Checks if a BigDecimal is null or zero.
     * 
     * @param bigDecimal the BigDecimal to check
     * @return true if the BigDecimal is null or equals zero, false otherwise
     * 
     * Examples:
     * BigDecimalUtils.isNullOrZero(null) returns true
     * BigDecimalUtils.isNullOrZero(BigDecimal.ZERO) returns true
     * BigDecimalUtils.isNullOrZero(new BigDecimal("0.00")) returns true
     * BigDecimalUtils.isNullOrZero(new BigDecimal("1.5")) returns false
     */
    public static boolean isNullOrZero(BigDecimal bigDecimal) {
        if (bigDecimal == null) {
            return true;
        }

        return isZero(bigDecimal);
    }

    /**
     * Checks if a BigDecimal equals zero.
     * 
     * @param bigDecimal the BigDecimal to check
     * @return true if the BigDecimal equals zero, false if null or non-zero
     * 
     * Examples:
     * BigDecimalUtils.isZero(BigDecimal.ZERO) returns true
     * BigDecimalUtils.isZero(new BigDecimal("0.000")) returns true
     * BigDecimalUtils.isZero(new BigDecimal("0.1")) returns false
     * BigDecimalUtils.isZero(null) returns false
     */
    public static boolean isZero(BigDecimal bigDecimal) {
        if (bigDecimal == null) {
            return false;
        }

        return bigDecimal.compareTo(BigDecimal.ZERO) == 0;
    }

    /**
     * Checks if a BigDecimal is non-zero.
     * 
     * @param bigDecimal the BigDecimal to check
     * @return true if the BigDecimal is not null and not zero, false otherwise
     * 
     * Examples:
     * BigDecimalUtils.isNonZero(new BigDecimal("5.0")) returns true
     * BigDecimalUtils.isNonZero(new BigDecimal("-0.1")) returns true
     * BigDecimalUtils.isNonZero(BigDecimal.ZERO) returns false
     * BigDecimalUtils.isNonZero(null) returns false
     */
    public static boolean isNonZero(BigDecimal bigDecimal) {
        if (bigDecimal == null) {
            return false;
        }

        return !isZero(bigDecimal);
    }

    /**
     * Checks if a BigDecimal is positive (greater than zero).
     * 
     * @param bigDecimal the BigDecimal to check
     * @return true if the BigDecimal is greater than zero, false if null, zero, or negative
     * 
     * Examples:
     * BigDecimalUtils.isPositive(new BigDecimal("10.5")) returns true
     * BigDecimalUtils.isPositive(new BigDecimal("0.001")) returns true
     * BigDecimalUtils.isPositive(BigDecimal.ZERO) returns false
     * BigDecimalUtils.isPositive(new BigDecimal("-5")) returns false
     * BigDecimalUtils.isPositive(null) returns false
     */
    public static boolean isPositive(BigDecimal bigDecimal) {
        if (bigDecimal == null) {
            return false;
        }

        return bigDecimal.compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * Checks if a BigDecimal is negative (less than zero).
     * 
     * @param bigDecimal the BigDecimal to check
     * @return true if the BigDecimal is less than zero, false if null, zero, or positive
     * 
     * Examples:
     * BigDecimalUtils.isNegative(new BigDecimal("-10.5")) returns true
     * BigDecimalUtils.isNegative(new BigDecimal("-0.001")) returns true
     * BigDecimalUtils.isNegative(BigDecimal.ZERO) returns false
     * BigDecimalUtils.isNegative(new BigDecimal("5")) returns false
     * BigDecimalUtils.isNegative(null) returns false
     */
    public static boolean isNegative(BigDecimal bigDecimal) {
        if (bigDecimal == null) {
            return false;
        }

        return bigDecimal.compareTo(BigDecimal.ZERO) < 0;
    }

    /**
     * Returns the minimum value from the provided BigDecimal values.
     * 
     * @param decimals the BigDecimal values to compare
     * @return the minimum BigDecimal value
     * @throws IllegalArgumentException if no arguments are provided
     * @throws NullPointerException if any of the decimals is null
     * 
     * Examples:
     * BigDecimalUtils.min(bd("10"), bd("5"), bd("20")) returns BigDecimal representing 5
     * BigDecimalUtils.min(bd("-5"), bd("0"), bd("3")) returns BigDecimal representing -5
     * BigDecimalUtils.min(bd("1.5")) returns BigDecimal representing 1.5
     */
    public static BigDecimal min(BigDecimal... decimals) {
        return Collections.min(List.of(decimals));
    }

    /**
     * Returns the maximum value from the provided BigDecimal values.
     * 
     * @param decimals the BigDecimal values to compare
     * @return the maximum BigDecimal value
     * @throws IllegalArgumentException if no arguments are provided
     * @throws NullPointerException if any of the decimals is null
     * 
     * Examples:
     * BigDecimalUtils.max(bd("10"), bd("5"), bd("20")) returns BigDecimal representing 20
     * BigDecimalUtils.max(bd("-5"), bd("0"), bd("3")) returns BigDecimal representing 3
     * BigDecimalUtils.max(bd("1.5")) returns BigDecimal representing 1.5
     */
    public static BigDecimal max(BigDecimal... decimals) {
        return Collections.max(List.of(decimals));
    }

    /**
     * Checks if a string can be used to create a valid BigDecimal.
     * 
     * @param val the string to validate
     * @return true if the string can be converted to a BigDecimal, false otherwise
     * 
     * Examples:
     * BigDecimalUtils.isCreatable("123.45") returns true
     * BigDecimalUtils.isCreatable("-99.9") returns true
     * BigDecimalUtils.isCreatable("0") returns true
     * BigDecimalUtils.isCreatable("abc") returns false
     * BigDecimalUtils.isCreatable(null) returns false
     * BigDecimalUtils.isCreatable("") returns false
     */
    public static boolean isCreatable(String val) {
        try {
            new BigDecimal(val);
            return true;
        } catch (NumberFormatException | NullPointerException e) {
            return false;
        }
    }

    /**
     * Converts a BigDecimal to a string representation.
     * Returns "null" for null values, "-" for zero values, and the plain string
     * representation with trailing zeros stripped for non-zero values.
     * 
     * @param bigDecimal the BigDecimal to convert
     * @return string representation of the BigDecimal
     * 
     * Examples:
     * BigDecimalUtils.toString(null) returns "null"
     * BigDecimalUtils.toString(BigDecimal.ZERO) returns "-"
     * BigDecimalUtils.toString(new BigDecimal("0.00")) returns "-"
     * BigDecimalUtils.toString(new BigDecimal("123.450")) returns "123.45"
     * BigDecimalUtils.toString(new BigDecimal("-99.9")) returns "-99.9"
     */
    public static String toString(BigDecimal bigDecimal) {
        if (bigDecimal == null) {
            return "null";
        }

        if (bigDecimal.compareTo(ZERO) == 0) {
            return "-";
        }

        return bigDecimal.stripTrailingZeros().toPlainString();
    }
}
