package io.wyden.cloudutils.tools;

import java.math.BigDecimal;
import java.math.MathContext;
import java.util.function.Function;
import java.util.stream.Collector;

/**
 * Utility class providing mathematical operations and helper methods.
 * This class is not instantiable.
 */
public class MathUtils {

    private MathUtils() {
        // Empty
    }

    /**
     * Divides the specified value by the specified divisor using a precision of DECIMAL128.
     *
     * @param value the dividend, represented as a {@link BigDecimal}
     * @param divisor the divisor, represented as a {@link BigDecimal}
     * @return the result of the division as a {@link BigDecimal}
     * @throws ArithmeticException if division by zero occurs
     */
    public static BigDecimal divide(BigDecimal value, BigDecimal divisor) {
        return value.divide(divisor, MathContext.DECIMAL128);
    }

    /**
     * Creates a {@link Collector} that computes a weighted average of values extracted from the input elements.
     * The weights and values are specified by the provided functions.
     *
     * @param <T> the type of input elements for the collector
     * @param valueFunction a function to extract the value to be averaged from an element
     * @param weightFunction a function to extract the weight associated with an element
     * @return a Collector that computes the weighted average as a {@link BigDecimal}
     */
    public static <T> Collector<T, ?, BigDecimal> averagingWeighted(Function<T, String> valueFunction, Function<T, String> weightFunction) {
        class Box {
            BigDecimal num = BigDecimal.ZERO;
            BigDecimal denom = BigDecimal.ZERO;
        }
        return Collector.of(
            Box::new,
            (b, e) -> {
                b.num = b.num.add(new BigDecimal(valueFunction.apply(e)).multiply(new BigDecimal(weightFunction.apply(e))));
                b.denom = b.denom.add(new BigDecimal(weightFunction.apply(e)));
            },
            (b1, b2) -> { b1.num = b1.num.add(b2.num); b1.denom = b1.denom.add(b2.denom); return b1; },
            b -> divide(b.num, b.denom)
        );
    }

}
