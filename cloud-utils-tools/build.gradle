 plugins {
    id 'java-library'
    id 'idea'
    alias dependencyCatalog.plugins.sonarqube
    id 'maven-publish'
}

group 'io.wyden'

def versionPropsFile = file('version.properties')
if (versionPropsFile.canRead()) {
    Properties versionProps = new Properties()
    versionProps.load(new FileInputStream(versionPropsFile))
    def ver = versionProps['VERSION'].toString()
    project.version = ver
} else {
    throw new GradleException("Could not read version.properties!")
}

ext {
    repository_username = System.env.NEXUS_DEPLOY_USERNAME
    repository_password = System.env.NEXUS_DEPLOY_PASSWORD
}

dependencies {
    api dependencyCatalog.slf4j.api

    api dependencyCatalog.guava
    api dependencyCatalog.commons.lang3
    api dependencyCatalog.commons.collections4
    api dependencyCatalog.protobuf.java.util
    implementation dependencyCatalog.jakarta.annotation

    testImplementation dependencyCatalog.junit.jupiter.api
    testImplementation dependencyCatalog.assertj.core
    testImplementation dependencyCatalog.awaitility
    testImplementation dependencyCatalog.mockito.core
    testRuntimeOnly dependencyCatalog.junit.jupiter.engine
}

sonarqube {
    properties {
        property "sonar.projectKey", "cloud-utils-telemetry"
        property "sonar.projectName", "Cloud Utils Telemetry"
    }
}

testing {
    suites {
        test {
            useJUnitJupiter()
        }

        integrationTest(JvmTestSuite) {
            dependencies {
                implementation project(':cloud-utils-telemetry')
                implementation project(':cloud-utils-test')
                implementation dependencyCatalog.testcontainers
                implementation dependencyCatalog.testcontainers.junit.jupiter
                implementation dependencyCatalog.assertj.core
                implementation dependencyCatalog.junit.jupiter.api
                implementation dependencyCatalog.awaitility
                implementation dependencyCatalog.junit.jupiter.engine
            }

            targets {
                all {
                    testTask.configure {
                        shouldRunAfter(test)
                    }
                }
            }
        }
    }
}

tasks.named('check') {
    dependsOn(testing.suites.integrationTest)
}

 java {
     withSourcesJar()
     withJavadocJar()
 }

publishing {
    publications {
        mavenJava(MavenPublication) {
            from components.java
        }
    }
    repositories {
        maven {
            name 'nexus-snapshots'
            url 'https://repo.wyden.io/nexus/repository/snapshots/'
            credentials {
                username repository_username
                password repository_password
            }
        }
    }
}
