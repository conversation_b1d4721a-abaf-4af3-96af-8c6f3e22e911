package io.wyden.booking.testcontainers;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.web.client.RestTemplate;
import org.testcontainers.containers.GenericContainer;
import org.testcontainers.containers.Network;
import org.testcontainers.containers.RabbitMQContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;
import org.testcontainers.utility.DockerImageName;

@Testcontainers
@DirtiesContext
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
                properties = {
                    "tracing.collector.endpoint=disabled",
                    "coinapi.enabled=false",
                    "logging.level.root=info",
                    "logging.level.io.wyden=debug",
                    "logging.level.io.wyden.cloudutils.telemetry=warn",
                    "logging.level.org.testcontainers.containers=debug",
                    "spring.main.allow-bean-definition-overriding=true",
                    "cqrs.persisting.strategy=none",
                    "deduplication.processedmessages.enabled=true",
                    "cache.transaction.enabled=true",
                    "booking-engine.publisher.command-result.enabled=true",
                    "booking-engine.publisher.booking-completed.enabled=true",
                    "booking-engine.publisher.transaction-created.enabled=true"
                })
@ContextConfiguration(classes = TestContainersConfiguration.class)
public class TestContainersIntegrationBase {

    public static final Network SHARED_NETWORK = Network.newNetwork();

    @Container
    protected static RabbitMQContainer rabbitmq = new RabbitMQContainer(DockerImageName.parse("docker.wyden.io/mirror/rabbitmq:3.12-management")
        .asCompatibleSubstituteFor("rabbitmq:management"))
        .withExposedPorts(5672)
        .withNetwork(SHARED_NETWORK);

    @Container
    protected static GenericContainer<?> hazelcast = new GenericContainer<>(DockerImageName.parse("docker.wyden.io/mirror/hazelcast/hazelcast:5.2.0"))
        .withExposedPorts(5701)
        .withNetwork(SHARED_NETWORK);

    @LocalServerPort
    protected int port;

    @Autowired
    protected RestTemplate restTemplate;

    @DynamicPropertySource
    static void registerDynamicProperties(DynamicPropertyRegistry registry) {
        registry.add("rabbitmq.host", rabbitmq::getHost);
        registry.add("rabbitmq.port", () -> rabbitmq.getMappedPort(5672));
        registry.add("rabbitmq.username", rabbitmq::getAdminUsername);
        registry.add("rabbitmq.password", rabbitmq::getAdminPassword);

        registry.add("hz.addressList", () -> hazelcast.getHost() + ":" + hazelcast.getFirstMappedPort());
    }
}
