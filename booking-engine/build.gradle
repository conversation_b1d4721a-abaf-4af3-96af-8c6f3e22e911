plugins {
	id 'java'
	id 'idea'
	id 'jaco<PERSON>'
	alias dependencyCatalog.plugins.spring.boot
	alias dependencyCatalog.plugins.dependency.management
	alias dependencyCatalog.plugins.sonarqube
	alias dependencyCatalog.plugins.jacocoToCobertura
}

group = 'io.wyden'
version = '0.0.1-SNAPSHOT'
sourceCompatibility = '17'

ext {
	repository_username = System.env.NEXUS_DEPLOY_USERNAME
	repository_password = System.env.NEXUS_DEPLOY_PASSWORD
}

repositories {
	mavenLocal()
	maven {
		name 'nexus-releases'
		url 'https://repo.wyden.io/nexus/repository/releases/'
		credentials {
			username repository_username
			password repository_password
		}
	}
	maven {
		name 'nexus-snapshot'
		url 'https://repo.wyden.io/nexus/repository/snapshots/'
		credentials {
			username repository_username
			password repository_password
		}
	}
	mavenCentral()
}

dependencies {
	annotationProcessor(dependencyCatalog.hibernate.jpamodelgen)

	// wyden
	implementation project(':booking-engine-domain')
	implementation dependencyCatalog.cloud.utils.hazelcast
	implementation dependencyCatalog.cloud.utils.rabbitmq
	implementation dependencyCatalog.cloud.utils.rabbitmq.destinations
	implementation dependencyCatalog.cloud.utils.spring
	implementation dependencyCatalog.cloud.utils.rest
	implementation dependencyCatalog.cloud.utils.telemetry
	implementation dependencyCatalog.cloud.utils.tools
	implementation dependencyCatalog.published.language.oems
	implementation dependencyCatalog.access.gateway.client
	implementation dependencyCatalog.rate.service.client
	implementation dependencyCatalog.reference.data.client

	// spring
	implementation dependencyCatalog.spring.boot.starter.actuator
	implementation dependencyCatalog.spring.boot.starter.webflux // deprecated
	implementation dependencyCatalog.spring.boot.starter.web
	implementation dependencyCatalog.spring.boot.starter.data.jpa
	implementation dependencyCatalog.spring.retry

	// booking
	implementation dependencyCatalog.flyway.core
	implementation dependencyCatalog.postgresql
	implementation dependencyCatalog.oracle
	implementation dependencyCatalog.commons.math
	implementation dependencyCatalog.hazelcast
	implementation dependencyCatalog.hazelcast.jet.protobuf
	implementation dependencyCatalog.hazelcast.sql

	// cannot use dependencyCatalog with version classifiers (:@zip)
	// the only option is to use
	// 	testImplementation(dependencyCatalog.jbehave.site) {
	//		artifact {
	//			classifier = "@zip"
	//		}
	//	}
	// but it <NAME_EMAIL> ('-' instead of ':')
	testImplementation 'org.jbehave.site:jbehave-site-resources:3.5:@zip'
	implementation dependencyCatalog.jbehave.core

	testImplementation dependencyCatalog.reactor.test
	testImplementation dependencyCatalog.awaitility
	testImplementation dependencyCatalog.assertj.core
	testImplementation dependencyCatalog.junit4
	testImplementation dependencyCatalog.h2
	testImplementation dependencyCatalog.spring.boot.starter.test
	testImplementation dependencyCatalog.cloud.utils.test
	testImplementation(dependencyCatalog.hazelcast) { artifact { classifier = 'tests'} }
}

tasks.withType(JavaCompile) {
	options.compilerArgs += ['-Ahibernate.jpamodelgen.debug=true']
}

// add test/java as test resources for JBehave to find *.story files matching the test scenarios
sourceSets.test.resources.srcDir 'src/test/java'

test {
	maxHeapSize = "1024m"
	doFirst {
		delete "build/jbehave"
		copy {
			from(zipTree(jarPath("jbehave-core"))) {
				include "style/*"
			}
			into("build/jbehave/view")

		}
		copy {
			from(zipTree(jarPath("jbehave-site-resources"))) {
				include "js/**/*"
				include "style/**/*"
				include "images/*"
			}
			into("build/jbehave/view")
		}
	}
}

testing {
	suites {
		integrationTest(JvmTestSuite) {
			dependencies {
				implementation project(':booking-engine-domain')
				implementation project

				// wyden
				implementation dependencyCatalog.cloud.utils.hazelcast
				implementation dependencyCatalog.cloud.utils.rabbitmq
				implementation dependencyCatalog.cloud.utils.rabbitmq.destinations
				implementation dependencyCatalog.cloud.utils.telemetry
				implementation dependencyCatalog.cloud.utils.tools
				implementation dependencyCatalog.published.language.oems
				implementation dependencyCatalog.rate.service.client
				implementation dependencyCatalog.reference.data.client

				// spring
				implementation dependencyCatalog.spring.boot.starter.webflux
				implementation dependencyCatalog.spring.boot.starter.data.jpa

				// booking
				implementation dependencyCatalog.flyway.core
				implementation dependencyCatalog.postgresql
				implementation dependencyCatalog.oracle
				implementation dependencyCatalog.commons.math
				implementation dependencyCatalog.hazelcast

				// test
				implementation dependencyCatalog.spring.boot.starter.test
				implementation dependencyCatalog.reactor.test
				implementation dependencyCatalog.assertj.core
				implementation dependencyCatalog.awaitility
				implementation dependencyCatalog.testcontainers
				implementation dependencyCatalog.testcontainers.junit.jupiter
				implementation dependencyCatalog.testcontainers.postgresql
				implementation dependencyCatalog.testcontainers.rabbitmq
				implementation dependencyCatalog.testcontainers.oracle
			}
			targets {
				all {
					testTask.configure {
						shouldRunAfter(test)
					}
				}
			}
		}
	}
}

def jarPath(String jarName) {
	var abc = project.configurations.testCompileClasspath.find {it.name.startsWith(jarName) }
	return abc.absolutePath
}

bootJar {
	manifest {
		attributes(
				"Implementation-Version": "${archiveVersion}"
		)
	}
}

bootRun {
	args = ["--tracing.collector.endpoint=http://localhost:4317"]
	jvmArgs = ["-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:9100"]
	environment([
			"FLUENTD_HOST": "localhost",
			"SPRING_PROFILES_ACTIVE": "dev"
	])
}

tasks.withType(JavaCompile) {
	options.generatedSourceOutputDirectory = file("src/generated/java")
}

sourceSets {
	generated {
		java {
			srcDirs = ['src/generated/java']
		}
	}
}

sonarqube {
	properties {
		property "sonar.projectKey", "booking-engine"
		property "sonar.projectName", "Booking Engine"
	}
}

tasks.named('check') {
	dependsOn(testing.suites.integrationTest)
}

test {
	finalizedBy jacocoTestReport
}

jacocoTestReport {
	reports {
		xml.enabled true
		csv.enabled true
	}

	getExecutionData().setFrom(fileTree(buildDir).include("/jacoco/*.exec"))
}

plugins.withType(JacocoPlugin) {
	tasks["test"].finalizedBy 'jacocoTestReport'
	tasks["integrationTest"].finalizedBy 'jacocoTestReport'
}

jacocoToCobertura {
	inputFile.set(file("$buildDir/reports/jacoco/test/jacocoTestReport.xml"))
	outputFile.set(file("$buildDir/reports/jacoco/test/cobertura.xml"))
}

plugins.withType(JacocoPlugin) {
	tasks["test"].finalizedBy 'jacocoTestReport'
	tasks["integrationTest"].finalizedBy 'jacocoTestReport'
	tasks["jacocoTestReport"].finalizedBy 'jacocoToCobertura'
	tasks["jacocoToCobertura"].dependsOn 'jacocoTestReport'
}

import org.gradle.api.tasks.testing.logging.TestExceptionFormat
import org.gradle.api.tasks.testing.logging.TestLogEvent

tasks.withType(Test) {
	testLogging {
		info {
			events TestLogEvent.FAILED,
					TestLogEvent.PASSED,
					TestLogEvent.SKIPPED
		}
		debug {
			events TestLogEvent.STARTED,
					TestLogEvent.FAILED,
					TestLogEvent.PASSED,
					TestLogEvent.SKIPPED,
					TestLogEvent.STANDARD_OUT,
					TestLogEvent.STANDARD_ERROR
			exceptionFormat TestExceptionFormat.FULL
			showExceptions true
			showCauses true
			showStackTraces true
			showStandardStreams true
		}

		afterSuite { desc, result ->
			if (!desc.parent) { // will match the outermost suite
				def output = "Results: ${result.resultType} (${result.testCount} tests, ${result.successfulTestCount} passed, ${result.failedTestCount} failed, ${result.skippedTestCount} skipped)"
				def startItem = '|  ', endItem = '  |'
				def repeatLength = startItem.length() + output.length() + endItem.length()
				println('\n' + ('-' * repeatLength) + '\n' + startItem + output + endItem + '\n' + ('-' * repeatLength))
			}
		}
	}
}
