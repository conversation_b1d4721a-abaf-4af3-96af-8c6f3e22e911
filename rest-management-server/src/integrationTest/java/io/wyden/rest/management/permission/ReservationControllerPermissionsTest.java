package io.wyden.rest.management.permission;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hazelcast.core.HazelcastInstance;
import io.wyden.accessgateway.client.license.LicenseService;
import io.wyden.accessgateway.client.permission.PermissionChecker;
import io.wyden.rest.management.DisableApiSignatureValidation;
import io.wyden.rest.management.WithMockCustomUser;
import io.wyden.rest.management.account.VenueAccountRepository;
import io.wyden.rest.management.booking.BookingEngineService;
import io.wyden.rest.management.domain.ReservationModel;
import io.wyden.rest.management.domain.TransactionModel;
import io.wyden.rest.management.infrastructure.web.WebSecurityConfig;
import io.wyden.rest.management.portfolio.PortfolioRepository;
import io.wyden.rest.management.reservation.ReservationController;
import io.wyden.rest.management.reservation.validator.BookReservationPermissionValidator;
import io.wyden.rest.management.reservation.validator.ReservationByRefIdPermissionValidator;
import io.wyden.rest.management.transaction.validator.TransactionRequestValidator;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;
import java.util.stream.Stream;

import static org.junit.jupiter.params.provider.Arguments.of;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(ReservationController.class)
@Import({
    WebSecurityConfig.class, BookReservationPermissionValidator.class, ReservationByRefIdPermissionValidator.class,
    DisableApiSignatureValidation.class})
@AutoConfigureMockMvc
@MockBean(classes = {PermissionChecker.class, HazelcastInstance.class, LicenseService.class, PortfolioRepository.class, VenueAccountRepository.class})
public class ReservationControllerPermissionsTest {

    private static final String PORTFOLIO_ID = "portfolioId";
    private static final String COUNTER_PORTFOLIO_ID = "counterPortfolioId";
    private static final String FEE_PORTFOLIO_ID = "feePortfolioId";
    private static final String ACCOUNT_ID = "accountId";
    private static final String TO_ACCOUNT_ID = "toAccountId";
    private static final String FEE_ACCOUNT_ID = "feeAccountId";
    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private BookingEngineService bookingEngineService;

    @MockBean
    private TransactionRequestValidator validator;

    @WithMockCustomUser(authorities = {
        "portfolio:read",
        "portfolio:trade",
        "accountId:venue.account:read",
        "accountId:venue.account:trade",
        "toAccountId:venue.account:read",
        "toAccountId:venue.account:trade",
        "feeAccountId:venue.account:read",
        "feeAccountId:venue.account:trade"
    })
    @ParameterizedTest
    @MethodSource("happyPath_bookReservationsArguments")
    public void happyPath_bookReservations(ReservationModel.ReservationRequest request) throws Exception {
        // Given
        // When
        mockMvc.perform(post("/api/v1/reservations")
                .content(objectMapper.writeValueAsString(request))
                .contentType(MediaType.APPLICATION_JSON))
            //then
            .andExpect(status().isOk());
    }

    static Stream<Arguments> happyPath_bookReservationsArguments() {
        return Stream.of(
            of(new ReservationModel.ClientCashTradeReservationRequest("mock", TransactionModel.TransactionType.CLIENT_CASH_TRADE, 123L, "mock", "mock", BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, PORTFOLIO_ID, COUNTER_PORTFOLIO_ID, List.of())),
            of(new ReservationModel.StreetCashTradeReservationRequest("mock", TransactionModel.TransactionType.STREET_CASH_TRADE, 123L, "mock", "mock", BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, PORTFOLIO_ID, ACCOUNT_ID, List.of())),
            of(new ReservationModel.DepositReservationRequest("mock", TransactionModel.TransactionType.DEPOSIT, 123L, "mock", BigDecimal.ZERO, PORTFOLIO_ID, ACCOUNT_ID, FEE_PORTFOLIO_ID, FEE_ACCOUNT_ID, List.of())),
            of(new ReservationModel.WithdrawalReservationRequest("mock", TransactionModel.TransactionType.WITHDRAWAL, 123L, "mock", BigDecimal.ZERO, PORTFOLIO_ID, ACCOUNT_ID, FEE_PORTFOLIO_ID, FEE_ACCOUNT_ID, List.of())),
            of(new ReservationModel.PortfolioCashTransferReservationRequest("mock", TransactionModel.TransactionType.PORTFOLIO_CASH_TRANSFER, 123L, "mock", BigDecimal.ZERO, PORTFOLIO_ID, COUNTER_PORTFOLIO_ID, FEE_PORTFOLIO_ID, List.of())),
            of(new ReservationModel.AccountCashTransferReservationRequest("mock", TransactionModel.TransactionType.ACCOUNT_CASH_TRANSFER, 123L, "mock", BigDecimal.ZERO, ACCOUNT_ID, TO_ACCOUNT_ID, FEE_ACCOUNT_ID, FEE_PORTFOLIO_ID, List.of())));
    }

    @WithMockCustomUser(authorities = {
        "accountId:venue.account:read",
        "accountId:venue.account:trade",
        "toAccountId:venue.account:read",
        "toAccountId:venue.account:trade",
        "feeAccountId:venue.account:read",
        "feeAccountId:venue.account:trade"
    })
    @ParameterizedTest
    @MethodSource("negativePath_bookReservationsArguments")
    public void negativePath_bookReservations(ReservationModel.ReservationRequest request) throws Exception {
        // Given
        // When
        mockMvc.perform(post("/api/v1/reservations")
                .content(objectMapper.writeValueAsString(request))
                .contentType(MediaType.APPLICATION_JSON))
            //then
            .andExpect(status().isUnauthorized());
    }

    public static Stream<Arguments> negativePath_bookReservationsArguments() {
        return Stream.of(
            of(new ReservationModel.ClientCashTradeReservationRequest("mock", TransactionModel.TransactionType.CLIENT_CASH_TRADE, 123L, "mock", "mock", BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, "random", COUNTER_PORTFOLIO_ID, List.of())),
            of(new ReservationModel.StreetCashTradeReservationRequest("mock", TransactionModel.TransactionType.STREET_CASH_TRADE, 123L, "mock", "mock", BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, "random", ACCOUNT_ID, List.of())),
            of(new ReservationModel.StreetCashTradeReservationRequest("mock", TransactionModel.TransactionType.STREET_CASH_TRADE, 123L, "mock", "mock", BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, PORTFOLIO_ID, "random", List.of())),
            of(new ReservationModel.DepositReservationRequest("mock", TransactionModel.TransactionType.WITHDRAWAL, 123L, "mock", BigDecimal.ZERO, "random", ACCOUNT_ID, FEE_PORTFOLIO_ID, FEE_ACCOUNT_ID, List.of())),
            of(new ReservationModel.DepositReservationRequest("mock", TransactionModel.TransactionType.WITHDRAWAL, 123L, "mock", BigDecimal.ZERO, PORTFOLIO_ID, "random", FEE_PORTFOLIO_ID, FEE_ACCOUNT_ID, List.of())),
            of(new ReservationModel.DepositReservationRequest("mock", TransactionModel.TransactionType.WITHDRAWAL, 123L, "mock", BigDecimal.ZERO, PORTFOLIO_ID, ACCOUNT_ID, "random", FEE_ACCOUNT_ID, List.of())),
            of(new ReservationModel.DepositReservationRequest("mock", TransactionModel.TransactionType.WITHDRAWAL, 123L, "mock", BigDecimal.ZERO, PORTFOLIO_ID, ACCOUNT_ID, FEE_PORTFOLIO_ID, "random", List.of())),
            of(new ReservationModel.WithdrawalReservationRequest("mock", TransactionModel.TransactionType.WITHDRAWAL, 123L, "mock", BigDecimal.ZERO, "random", ACCOUNT_ID, FEE_PORTFOLIO_ID, FEE_ACCOUNT_ID, null)),
            of(new ReservationModel.WithdrawalReservationRequest("mock", TransactionModel.TransactionType.WITHDRAWAL, 123L, "mock", BigDecimal.ZERO, PORTFOLIO_ID, "random", FEE_PORTFOLIO_ID, FEE_ACCOUNT_ID, null)),
            of(new ReservationModel.WithdrawalReservationRequest("mock", TransactionModel.TransactionType.WITHDRAWAL, 123L, "mock", BigDecimal.ZERO, PORTFOLIO_ID, ACCOUNT_ID, "random", FEE_ACCOUNT_ID, null)),
            of(new ReservationModel.WithdrawalReservationRequest("mock", TransactionModel.TransactionType.WITHDRAWAL, 123L, "mock", BigDecimal.ZERO, PORTFOLIO_ID, ACCOUNT_ID, FEE_PORTFOLIO_ID, "random", null)),
            of(new ReservationModel.PortfolioCashTransferReservationRequest("mock", TransactionModel.TransactionType.PORTFOLIO_CASH_TRANSFER, 123L, "mock", BigDecimal.ZERO, "random", COUNTER_PORTFOLIO_ID, FEE_PORTFOLIO_ID, null)),
            of(new ReservationModel.PortfolioCashTransferReservationRequest("mock", TransactionModel.TransactionType.PORTFOLIO_CASH_TRANSFER, 123L, "mock", BigDecimal.ZERO, PORTFOLIO_ID, "random", FEE_PORTFOLIO_ID, null)),
            of(new ReservationModel.AccountCashTransferReservationRequest("mock", TransactionModel.TransactionType.ACCOUNT_CASH_TRANSFER, 123L, "mock", BigDecimal.ZERO, "random", TO_ACCOUNT_ID, FEE_ACCOUNT_ID, FEE_PORTFOLIO_ID, null)),
            of(new ReservationModel.AccountCashTransferReservationRequest("mock", TransactionModel.TransactionType.ACCOUNT_CASH_TRANSFER, 123L, "mock", BigDecimal.ZERO, ACCOUNT_ID, "random", FEE_ACCOUNT_ID, FEE_PORTFOLIO_ID, null)),
            of(new ReservationModel.AccountCashTransferReservationRequest("mock", TransactionModel.TransactionType.ACCOUNT_CASH_TRANSFER, 123L, "mock", BigDecimal.ZERO, ACCOUNT_ID, TO_ACCOUNT_ID, "random", FEE_PORTFOLIO_ID, null)),
            of(new ReservationModel.AccountCashTransferReservationRequest("mock", TransactionModel.TransactionType.ACCOUNT_CASH_TRANSFER, 123L, "mock", BigDecimal.ZERO, ACCOUNT_ID, TO_ACCOUNT_ID, FEE_ACCOUNT_ID, "random", null)));
    }

    @ParameterizedTest
    @MethodSource("getReservationByRefId_args")
    @WithMockCustomUser(authorities = {
        "portfolio:read",
        "portfolio:trade",
        "accountId:venue.account:read",
        "accountId:venue.account:trade",
        "toAccountId:venue.account:read",
        "toAccountId:venue.account:trade",
        "feeAccountId:venue.account:read",
        "feeAccountId:venue.account:trade"
    })
    void happyPath_getReservationByRefId(ReservationModel.Reservation reservation) throws Exception {
        //given
        String reservationRef = "reservationRef";
        //when
        when(bookingEngineService.findReservationByReference(reservationRef)).thenReturn(reservation);
        //then
        mockMvc.perform(get("/api/v1/reservations/" + reservationRef)
                .content("")
                .contentType(MediaType.APPLICATION_JSON))
            //then
            .andExpect(status().isOk());
    }

    @ParameterizedTest
    @MethodSource("getReservationByRefId_args")
    @WithMockCustomUser
    void negativePath_getReservationByRefId(ReservationModel.Reservation reservation) throws Exception {
        //given
        String reservationRef = "reservationRef";
        //when
        when(bookingEngineService.findReservationByReference(reservationRef)).thenReturn(reservation);
        //then
        mockMvc.perform(get("/api/v1/reservations/" + reservationRef)
                .content("")
                .contentType(MediaType.APPLICATION_JSON))
            //then
            .andExpect(status().isUnauthorized());
    }

    public static Stream<Arguments> getReservationByRefId_args() {
        return Stream.of(
            of(new ReservationModel.DepositReservation("ref", TransactionModel.TransactionType.DEPOSIT, 123L, "CUR", BigDecimal.TEN, "portfolioId", "accountId", "feePortfolioId", "feeAccountId", Set.of())),
            of(new ReservationModel.AccountCashTransferReservation("ref", TransactionModel.TransactionType.DEPOSIT, 123L, "CUR", BigDecimal.TEN, "portfolioId", "accountId", "feePortfolioId", "feeAccountId", Set.of())),
            of(new ReservationModel.WithdrawalReservation("ref", TransactionModel.TransactionType.DEPOSIT, 123L, "CUR", BigDecimal.TEN, "portfolioId", "accountId", "feePortfolioId", "feeAccountId", Set.of())),
            of(new ReservationModel.ClientCashTradeReservation("ref", TransactionModel.TransactionType.DEPOSIT, 123L, "CUR", "CUR", BigDecimal.TEN, BigDecimal.TEN, BigDecimal.ZERO,"portfolioId", "accountId", Set.of())),
            of(new ReservationModel.StreetCashTradeReservation("ref", TransactionModel.TransactionType.DEPOSIT, 123L, "CUR", "CUR", BigDecimal.TEN, BigDecimal.TEN, BigDecimal.ZERO,"portfolioId", "accountId", Set.of())),
            of(new ReservationModel.PortfolioCashTransferReservation("ref", TransactionModel.TransactionType.DEPOSIT, 123L, "CUR", BigDecimal.TEN, "portfolioId", "accountId", FEE_PORTFOLIO_ID, Set.of())));
    }
}
