package io.wyden.rest.management.permission;

import com.hazelcast.map.IMap;
import io.wyden.accessgateway.client.permission.PermissionCache;
import io.wyden.published.security.Permission;
import io.wyden.published.security.PermissionListSnapshot;
import io.wyden.rest.management.TestcontainersIntegrationTest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Set;

import static io.wyden.accessgateway.client.permission.Permission.Resources.PORTFOLIO;
import static io.wyden.accessgateway.client.permission.Permission.Scopes.READ;
import static io.wyden.rest.management.security.authentication.AuthenticationService.calculateSignature;
import static io.wyden.rest.management.security.authentication.Headers.API_KEY;
import static io.wyden.rest.management.security.authentication.Headers.API_NONCE;
import static io.wyden.rest.management.security.authentication.Headers.API_SIGNATURE;
import static org.hamcrest.Matchers.greaterThan;
import static org.hamcrest.Matchers.startsWith;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
import static org.springframework.security.test.web.servlet.setup.SecurityMockMvcConfigurers.springSecurity;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class PermissionControllerTest extends TestcontainersIntegrationTest {

    @Autowired
    private WebApplicationContext context;

    @MockBean
    PermissionCache permissionCache;

    @Autowired
    private IMap<String, Long> noncePerApiKeyMap;

    PermissionListSnapshot mockedPermissions = PermissionListSnapshot.newBuilder()
        .addPermissions(Permission.newBuilder().setResource(PORTFOLIO).setScope(READ).setResourceId("resourceId"))
        .build();

    private MockMvc mvc;

    private long timestamp;
    private String apiSignature;

    @BeforeEach
    public void setup() {
        timestamp = Instant.now().toEpochMilli();
        String nonce = Long.toString(timestamp);
        String path = "/api/v1/permissions";
        String method = "GET";

        apiSignature = calculateSignature(nonce, apiSecret, method, path, "");

        this.mvc = MockMvcBuilders.webAppContextSetup(context)
            .apply(springSecurity())
            .build();

        when(permissionCache.getPermittedResourceIds(any(), any(), any(), any(), anyInt())).thenReturn(Set.of());
        when(permissionCache.getPermittedResourceIds(any(), any(), eq("portfolio"), eq("read"), anyInt())).thenReturn(Set.of("resourceId"));

        noncePerApiKeyMap.clear();
    }

    @Test
    public void whenMissingApiKeysHeaders() throws Exception {
        // missing api-nonce
        mvc.perform(get("/api/v1/permissions")
                .header(API_KEY, apiKey)
                .header(API_SIGNATURE, apiSignature))
            .andExpect(status().is(400))
            .andExpect(content().string("Missing X-API-NONCE header"));
        // missing api-key
        mvc.perform(get("/api/v1/permissions")
                .header(API_NONCE, timestamp)
                .header(API_SIGNATURE, apiSignature))
            .andExpect(status().is(400))
            .andExpect(content().string("Missing X-API-KEY header"));
        // missing api-signature
        mvc.perform(get("/api/v1/permissions")
                .header(API_KEY, apiKey)
                .header(API_NONCE, timestamp))
            .andExpect(status().is(400))
            .andExpect(content().string("Missing X-API-SIGNATURE header"));
    }

    @Test
    public void whenNonceIsNotANumber() throws Exception {
        mvc.perform(
                get("/api/v1/permissions")
                    .header(API_KEY, apiKey)
                    .header(API_NONCE, "wrong")
                    .header(API_SIGNATURE, apiSignature)
            )
            .andExpect(status().is(400))
            .andExpect(content().string("Invalid api-nonce"));
    }

    @Test
    public void whenNonceIsTooOld() throws Exception {
        long timestamp = Instant.now().minus(1, ChronoUnit.HOURS).toEpochMilli();
        mvc.perform(
                get("/api/v1/permissions")
                    .header(API_KEY, apiKey)
                    .header(API_NONCE, timestamp)
                    .header(API_SIGNATURE, apiSignature)
            )
            .andExpect(status().is(400))
            .andExpect(content().string("Invalid api-nonce"));
    }

    @Test
    public void whenNonceAlreadyRegistered() throws Exception {
        long timestamp = Instant.now().toEpochMilli();
        noncePerApiKeyMap.put(apiKey, timestamp);
        mvc.perform(
                get("/api/v1/permissions")
                    .header(API_KEY, apiKey)
                    .header(API_NONCE, timestamp)
                    .header(API_SIGNATURE, "hash")
            )
            .andExpect(status().is(400))
            .andExpect(content().string("Invalid api-nonce"));
    }

    @Test
    public void whenHavingBadCredentials() throws Exception {
        long timestamp = Instant.now().toEpochMilli();
        mvc.perform(get("/api/v1/permissions")
                .header(API_KEY, "wrong")
                .header(API_NONCE, timestamp)
                .header(API_SIGNATURE, "hash"))
            .andExpect(status().is(401))
            .andExpect(content().string(startsWith("Invalid api-key")));
    }

    @Test
    public void whenCredentialsAreCorrect() throws Exception {
        mvc.perform(get("/api/v1/permissions")
                .header(API_KEY, apiKey)
                .header(API_NONCE, timestamp)
                .header(API_SIGNATURE, apiSignature))
            .andDo(print())
            .andExpect(status().is(200))
            // Assert that the output is a JSON array
            .andExpect(jsonPath("$").isArray())
            // Assert that the JSON array has more than one element.
            .andExpect(jsonPath("$.length()", greaterThan(1)));

    }
}
