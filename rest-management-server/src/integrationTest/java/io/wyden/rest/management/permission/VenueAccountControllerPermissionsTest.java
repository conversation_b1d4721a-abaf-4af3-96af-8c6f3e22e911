package io.wyden.rest.management.permission;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hazelcast.core.HazelcastInstance;
import io.wyden.accessgateway.client.license.LicenseService;
import io.wyden.accessgateway.client.permission.PermissionChecker;
import io.wyden.rest.management.DisableApiSignatureValidation;
import io.wyden.rest.management.WithMockCustomUser;
import io.wyden.rest.management.account.VenueAccountRepository;
import io.wyden.rest.management.infrastructure.web.WebSecurityConfig;
import io.wyden.rest.management.portfolio.PortfolioRepository;
import io.wyden.rest.management.referencedata.VenueAccountController;
import io.wyden.rest.management.referencedata.VenueAccountService;
import io.wyden.rest.management.security.permission.DefaultPermissionValidator;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(controllers = VenueAccountController.class)
@Import({WebSecurityConfig.class, DisableApiSignatureValidation.class, DefaultPermissionValidator.class})
@MockBean(classes = { PermissionChecker.class, HazelcastInstance.class, LicenseService.class, PortfolioRepository.class, VenueAccountRepository.class} )
@AutoConfigureMockMvc
public class VenueAccountControllerPermissionsTest {
    @Autowired
    private MockMvc mockMvc;
    @MockBean
    VenueAccountService venueAccountService;
    @Autowired
    private ObjectMapper objectMapper;

    @WithMockCustomUser(authorities = {
        "accountId:venue.account:manage",
        "venue.account:create",
    })
    @Test
    void venueAccountActivateAndDeactivate() throws Exception {
        mockMvc.perform(
                MockMvcRequestBuilders.post("/api/v1/connectors/accountId/activate"))
            .andExpect(status().isOk());
        mockMvc.perform(
                MockMvcRequestBuilders.post("/api/v1/connectors/accountId/deactivate"))
            .andExpect(status().isOk());
    }


}
