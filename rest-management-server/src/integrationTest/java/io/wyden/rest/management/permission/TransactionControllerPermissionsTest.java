package io.wyden.rest.management.permission;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hazelcast.core.HazelcastInstance;
import io.wyden.accessgateway.client.license.LicenseService;
import io.wyden.accessgateway.client.permission.PermissionChecker;
import io.wyden.rest.management.DisableApiSignatureValidation;
import io.wyden.rest.management.WithMockCustomUser;
import io.wyden.rest.management.account.VenueAccountRepository;
import io.wyden.rest.management.booking.BookingEngineService;
import io.wyden.rest.management.domain.TransactionModel;
import io.wyden.rest.management.infrastructure.web.WebSecurityConfig;
import io.wyden.rest.management.portfolio.PortfolioRepository;
import io.wyden.rest.management.transaction.TransactionController;
import io.wyden.rest.management.transaction.validator.BookTransactionPermissionsValidator;
import io.wyden.rest.management.transaction.validator.TransactionRequestValidator;
import io.wyden.rest.management.transaction.validator.TransferByTransferIdPermissionValidator;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.util.LinkedMultiValueMap;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Stream;

import static org.junit.jupiter.params.provider.Arguments.of;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(TransactionController.class)
@Import({
    WebSecurityConfig.class, BookTransactionPermissionsValidator.class, TransferByTransferIdPermissionValidator.class,
    DisableApiSignatureValidation.class})
@MockBean(classes = {PermissionChecker.class, HazelcastInstance.class, LicenseService.class, PortfolioRepository.class, VenueAccountRepository.class})
@AutoConfigureMockMvc
public class TransactionControllerPermissionsTest {

    private static final String PORTFOLIO_ID = "portfolioId";
    private static final String COUNTER_PORTFOLIO_ID = "counterPortfolioId";
    private static final String FEE_PORTFOLIO_ID = "feePortfolioId";
    private static final String ACCOUNT_ID = "accountId";
    private static final String TO_ACCOUNT_ID = "toAccountId";
    private static final String FEE_ACCOUNT_ID = "feeAccountId";

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private BookingEngineService bookingEngineService;

    @MockBean
    private TransactionRequestValidator validator;

    @WithMockCustomUser(authorities = {
        "portfolio:read",
        "portfolio:trade",
        "accountId:venue.account:read",
        "accountId:venue.account:trade",
        "toAccountId:venue.account:read",
        "toAccountId:venue.account:trade",
        "feeAccountId:venue.account:read",
        "feeAccountId:venue.account:trade"
    })
    @ParameterizedTest
    @MethodSource("bookTransactions_happyPath_arg")
    public void bookTransactions_happyPath(TransactionModel.TransactionRequest transactionRequest) throws Exception {
        // Given
        // When
        mockMvc.perform(post("/api/v1/transactions")
                .content(objectMapper.writeValueAsString(transactionRequest))
                .contentType(MediaType.APPLICATION_JSON))
            //then
            .andExpect(status().isOk());
    }

    static Stream<Arguments> bookTransactions_happyPath_arg() {
        return Stream.of(
            of(new TransactionModel.ClientCashTradeRequest("mock", 123L, "orderId", "mock", "mock", "mock", "mock", "mock", "mock", "mock", "mock", TransactionModel.TransactionType.CLIENT_CASH_TRADE, "mock", "mock", BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, PORTFOLIO_ID, COUNTER_PORTFOLIO_ID, null, null, null, null)),
            of(new TransactionModel.StreetCashTradeRequest("mock", 123L, "orderId", "mock", "mock", "mock", "mock", "mock", "mock", "mock", "mock", TransactionModel.TransactionType.STREET_CASH_TRADE, "mock", "mock", BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, PORTFOLIO_ID, ACCOUNT_ID, null, null, null, null)),
            of(new TransactionModel.DepositRequest("mock", 123L, "orderId", "mock", TransactionModel.TransactionType.DEPOSIT, "mock", BigDecimal.ONE, PORTFOLIO_ID, ACCOUNT_ID, FEE_PORTFOLIO_ID, FEE_ACCOUNT_ID, null, null)),
            of(new TransactionModel.WithdrawalRequest("mock", 123L, "orderId", "mock", TransactionModel.TransactionType.WITHDRAWAL, "mock", BigDecimal.ONE, PORTFOLIO_ID, ACCOUNT_ID, FEE_PORTFOLIO_ID, FEE_ACCOUNT_ID, null, null)),
            of(new TransactionModel.PortfolioCashTransferRequest("mock", 123L, "orderId", "mock", TransactionModel.TransactionType.PORTFOLIO_CASH_TRANSFER, "mock", BigDecimal.ZERO, "mock", PORTFOLIO_ID, COUNTER_PORTFOLIO_ID, FEE_PORTFOLIO_ID, null)),
            of(new TransactionModel.AccountCashTransferRequest("mock", 123L, "orderId", "mock", TransactionModel.TransactionType.ACCOUNT_CASH_TRANSFER, "mock", BigDecimal.ZERO, "mock", ACCOUNT_ID, TO_ACCOUNT_ID, FEE_PORTFOLIO_ID, FEE_ACCOUNT_ID, List.of())));
    }

    @WithMockCustomUser(authorities = {
        "accountId:venue.account:read",
        "accountId:venue.account:trade",
        "toAccountId:venue.account:read",
        "toAccountId:venue.account:trade",
        "feeAccountId:venue.account:read",
        "feeAccountId:venue.account:trade"
    })
    @ParameterizedTest
    @MethodSource("bookTransactions_negativePath_arg")
    public void bookTransactions_negativePath(TransactionModel.TransactionRequest transactionRequest) throws Exception {
        // Given
        // When
        mockMvc.perform(post("/api/v1/transactions")
                .content(objectMapper.writeValueAsString(transactionRequest))
                .contentType(MediaType.APPLICATION_JSON))
            //then
            .andExpect(status().isUnauthorized());
    }

    static Stream<Arguments> bookTransactions_negativePath_arg() {
        return Stream.of(
            of(new TransactionModel.ClientCashTradeRequest("mock", 123L, "orderId", "mock", "mock", "mock", "mock", "mock", "mock", "mock", "mock", TransactionModel.TransactionType.CLIENT_CASH_TRADE, "mock", "mock", BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, "random", COUNTER_PORTFOLIO_ID, null, null, null, null)),
            of(new TransactionModel.StreetCashTradeRequest("mock", 123L, "orderId", "mock", "mock", "mock", "mock", "mock", "mock", "mock", "mock", TransactionModel.TransactionType.STREET_CASH_TRADE, "mock", "mock", BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, "random", ACCOUNT_ID, null, null, null, null)),
            of(new TransactionModel.StreetCashTradeRequest("mock", 123L, "orderId", "mock", "mock", "mock", "mock", "mock", "mock", "mock", "mock", TransactionModel.TransactionType.STREET_CASH_TRADE, "mock", "mock", BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, PORTFOLIO_ID, "random", null, null, null, null)),
            of(new TransactionModel.DepositRequest("mock", 123L, "orderId", "mock", TransactionModel.TransactionType.DEPOSIT, "mock", BigDecimal.ONE, "random", ACCOUNT_ID, FEE_PORTFOLIO_ID, FEE_ACCOUNT_ID, null, null)),
            of(new TransactionModel.DepositRequest("mock", 123L, "orderId", "mock", TransactionModel.TransactionType.DEPOSIT, "mock", BigDecimal.ONE, PORTFOLIO_ID, "random", FEE_PORTFOLIO_ID, FEE_ACCOUNT_ID, null, null)),
            of(new TransactionModel.DepositRequest("mock", 123L, "orderId", "mock", TransactionModel.TransactionType.DEPOSIT, "mock", BigDecimal.ONE, PORTFOLIO_ID, ACCOUNT_ID, "random", FEE_ACCOUNT_ID, null, null)),
            of(new TransactionModel.DepositRequest("mock", 123L, "orderId", "mock", TransactionModel.TransactionType.DEPOSIT, "mock", BigDecimal.ONE, PORTFOLIO_ID, ACCOUNT_ID, FEE_PORTFOLIO_ID, "random", null, null)),
            of(new TransactionModel.WithdrawalRequest("mock", 123L, "orderId", "mock", TransactionModel.TransactionType.WITHDRAWAL, "mock", BigDecimal.ONE, "random", ACCOUNT_ID, FEE_PORTFOLIO_ID, FEE_ACCOUNT_ID, null, null)),
            of(new TransactionModel.WithdrawalRequest("mock", 123L, "orderId", "mock", TransactionModel.TransactionType.WITHDRAWAL, "mock", BigDecimal.ONE, PORTFOLIO_ID, "random", FEE_PORTFOLIO_ID, FEE_ACCOUNT_ID, null, null)),
            of(new TransactionModel.WithdrawalRequest("mock", 123L, "orderId", "mock", TransactionModel.TransactionType.WITHDRAWAL, "mock", BigDecimal.ONE, PORTFOLIO_ID, ACCOUNT_ID, "random", FEE_ACCOUNT_ID, null, null)),
            of(new TransactionModel.WithdrawalRequest("mock", 123L, "orderId", "mock", TransactionModel.TransactionType.WITHDRAWAL, "mock", BigDecimal.ONE, PORTFOLIO_ID, ACCOUNT_ID, FEE_PORTFOLIO_ID, "random", null, null)),
            of(new TransactionModel.PortfolioCashTransferRequest("mock", 123L, "orderId", "mock", TransactionModel.TransactionType.PORTFOLIO_CASH_TRANSFER, "mock", BigDecimal.ZERO, "mock", "random", COUNTER_PORTFOLIO_ID, FEE_PORTFOLIO_ID, null)),
            of(new TransactionModel.PortfolioCashTransferRequest("mock", 123L, "orderId", "mock", TransactionModel.TransactionType.PORTFOLIO_CASH_TRANSFER, "mock", BigDecimal.ZERO, "mock", PORTFOLIO_ID, "random", FEE_PORTFOLIO_ID, null)),
            of(new TransactionModel.AccountCashTransferRequest("mock", 123L, "orderId", "mock", TransactionModel.TransactionType.ACCOUNT_CASH_TRANSFER, "mock", BigDecimal.ZERO, "mock", "random", TO_ACCOUNT_ID, FEE_PORTFOLIO_ID, FEE_ACCOUNT_ID, null)),
            of(new TransactionModel.AccountCashTransferRequest("mock", 123L, "orderId", "mock", TransactionModel.TransactionType.ACCOUNT_CASH_TRANSFER, "mock", BigDecimal.ZERO, "mock", ACCOUNT_ID, "random", FEE_PORTFOLIO_ID, FEE_ACCOUNT_ID, null)),
            of(new TransactionModel.AccountCashTransferRequest("mock", 123L, "orderId", "mock", TransactionModel.TransactionType.ACCOUNT_CASH_TRANSFER, "mock", BigDecimal.ZERO, "mock", ACCOUNT_ID, TO_ACCOUNT_ID, "random", FEE_ACCOUNT_ID, null)),
            of(new TransactionModel.AccountCashTransferRequest("mock", 123L, "orderId", "mock", TransactionModel.TransactionType.ACCOUNT_CASH_TRANSFER, "mock", BigDecimal.ZERO, "mock", ACCOUNT_ID, TO_ACCOUNT_ID, FEE_PORTFOLIO_ID, "random", null)));
    }

    @WithMockCustomUser(authorities = {"portfolio:read", "accountId:venue.account:read"})
    @ParameterizedTest
    @MethodSource("getTransactionsArguments_happyPath_arg")
    public void getTransactionsArguments_happyPath(Map<String, String> params) throws Exception {
        // Given
        LinkedMultiValueMap<String, String> mvcParams = new LinkedMultiValueMap<>();
        params.forEach(mvcParams::add);
        // When
        mockMvc.perform(get("/api/v1/transactions")
                .params(mvcParams)
                .contentType(MediaType.APPLICATION_JSON))
            //then
            .andExpect(status().isOk());
    }

    static Stream<Arguments> getTransactionsArguments_happyPath_arg() {
        return Stream.of(
            of(Map.of("portfolioId", "portfolioId")),
            of(Map.of("accountId", "accountId")),
            of(Map.of("portfolioId", "random"))
        );
    }

    @WithMockCustomUser
    @ParameterizedTest
    @MethodSource("getTransactionsArguments_negativePath_arg")
    public void getTransactionsArguments_negativePath(Map<String, String> params) throws Exception {
        // Given
        LinkedMultiValueMap<String, String> mvcParams = new LinkedMultiValueMap<>();
        params.forEach(mvcParams::add);
        // When
        mockMvc.perform(get("/api/v1/transactions")
                .params(mvcParams)
                .contentType(MediaType.APPLICATION_JSON))
            //then
            .andExpect(status().isUnauthorized());
    }

    static Stream<Arguments> getTransactionsArguments_negativePath_arg() {
        return Stream.of(
            of(Map.of("accountId", "random"))
        );
    }

    @ParameterizedTest
    @MethodSource("getTransactionByTransactionId_args")
    @WithMockCustomUser(authorities = {
        "portfolio:read",
        "portfolio:trade",
        "accountId:venue.account:read",
        "accountId:venue.account:trade",
        "toAccountId:venue.account:read",
        "toAccountId:venue.account:trade",
        "feeAccountId:venue.account:read",
        "feeAccountId:venue.account:trade"
    })
    void happyPath_getTransactionsByTransactionId(TransactionModel.Transaction transaction) throws Exception {
        //given
        String transactionId = "transactionId";
        //when
        when(bookingEngineService.findTransactionById(transactionId)).thenReturn(transaction);
        //then
        mockMvc.perform(get("/api/v1/transactions/" + transactionId)
                .content("")
                .contentType(MediaType.APPLICATION_JSON))
            //then
            .andExpect(status().isOk());
    }

    @ParameterizedTest
    @MethodSource("getTransactionByTransactionId_args")
    @WithMockCustomUser
    @Disabled
    void negativePath_getTransactionsByTransactionId(TransactionModel.Transaction transaction) throws Exception {
        //given
        String transactionId = "transactionId";
        //when
        when(bookingEngineService.findTransactionById(transactionId)).thenReturn(transaction);
        //then
        mockMvc.perform(get("/api/v1/transactions/" + transactionId)
                .content("")
                .contentType(MediaType.APPLICATION_JSON))
            //then
            .andExpect(status().isUnauthorized());
    }

    public static Stream<Arguments> getTransactionByTransactionId_args() {
        return Stream.of(
            of(new TransactionModel.Deposit("ref", "ref", 123L, "", "", TransactionModel.TransactionType.DEPOSIT, true, 123L, "CUR", BigDecimal.TEN, "portfolioId", "accountId", "feePortfolioId", "feeAccountId", "", Set.of())),
            of(new TransactionModel.AccountCashTransfer("ref", "ref", 123L, "", "", TransactionModel.TransactionType.ACCOUNT_CASH_TRANSFER, true, 123L, "CUR", BigDecimal.TEN, "portfolioId", "accountId", "feePortfolioId", "feeAccountId", "xyz", Set.of())),
            of(new TransactionModel.Withdrawal("ref", "", 123L, "", "", TransactionModel.TransactionType.WITHDRAWAL, true, 123L, "CUR", BigDecimal.TEN, "portfolioId", "accountId", "feePortfolioId", "feeAccountId", "", Set.of())),
            of(new TransactionModel.ClientCashTrade("ref", "", 123L, "", "", "", "", "", "", "", "", "", TransactionModel.TransactionType.CLIENT_CASH_TRADE, true, 123L, "123", "123", "CUR", "CUR", BigDecimal.TEN, BigDecimal.TEN, BigDecimal.TEN, "portfolioId", "accountId", "feePortfolioId", Set.of())),
            of(new TransactionModel.StreetCashTrade("ref", "", 123L, "", "", "", "", "", "", "", "", "", TransactionModel.TransactionType.STREET_CASH_TRADE, true, 123L, "123", "123", "CUR", "CUR", BigDecimal.TEN, BigDecimal.TEN, BigDecimal.TEN, "portfolioId", "accountId", "feePortfolioId", Set.of())),
            of(new TransactionModel.PortfolioCashTransfer("ref", "", 123L, "", "", TransactionModel.TransactionType.PORTFOLIO_CASH_TRANSFER, true, 123L, "CUR", BigDecimal.TEN, "portfolioId", "accountId", FEE_PORTFOLIO_ID, FEE_PORTFOLIO_ID, Set.of())));
    }
}
