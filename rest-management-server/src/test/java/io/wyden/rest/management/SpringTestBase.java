package io.wyden.rest.management;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.protobuf.Message;
import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import com.hazelcast.test.TestHazelcastInstanceFactory;
import com.rabbitmq.client.Channel;
import io.wyden.accessgateway.client.apikey.ApiKeyWebClient;
import io.wyden.accessgateway.client.apikey.dto.AuthResponseDto;
import io.wyden.accessgateway.client.permission.PermissionCache;
import io.wyden.accessgateway.client.permission.PermissionChecker;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.referencedata.AccountType;
import io.wyden.published.referencedata.Currency;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.published.referencedata.PortfolioChangeEvent;
import io.wyden.published.referencedata.PortfolioChangeRequest;
import io.wyden.published.referencedata.PortfolioOnboardingAccessGrantedEvent;
import io.wyden.published.referencedata.PortfolioOnboardingRequest;
import io.wyden.published.referencedata.PortfolioType;
import io.wyden.published.referencedata.VenueAccount;
import io.wyden.published.referencedata.VenueAccountCreateRequest;
import io.wyden.published.referencedata.WalletType;
import io.wyden.published.referencedata.account.AccountOnboardingRequest;
import io.wyden.referencedata.domain.PortfolioMapConfig;
import io.wyden.referencedata.domain.VenueAccountMapConfig;
import io.wyden.rest.management.account.VenueAccountRepository;
import io.wyden.rest.management.booking.BookingEngineHttpClient;
import io.wyden.rest.management.booking.BookingEngineRabbitClient;
import io.wyden.rest.management.common.Identifiers;
import io.wyden.rest.management.contract.ApiClient;
import io.wyden.rest.management.portfolio.PortfolioRepository;
import io.wyden.rest.management.referencedata.CurrencyService;
import io.wyden.rest.management.security.apikey.ApiKeyRepository;
import io.wyden.rest.management.security.apikey.VaultApiKey;
import io.wyden.rest.management.security.authentication.AuthenticationService;
import io.wyden.rest.management.security.authentication.Headers;
import io.wyden.rest.management.security.permission.DefaultPermissionValidator;

import org.junit.jupiter.api.BeforeEach;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.test.web.servlet.MockMvc;
import reactor.core.publisher.Mono;

import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.Optional;
import java.util.Set;

import static io.wyden.accessgateway.client.permission.Permission.PORTFOLIO_MANAGE;
import static io.wyden.accessgateway.client.permission.Permission.PORTFOLIO_READ;
import static io.wyden.accessgateway.client.permission.Permission.PORTFOLIO_TRADE;
import static io.wyden.accessgateway.client.permission.Permission.Resources.PORTFOLIO;
import static io.wyden.accessgateway.client.permission.Permission.Resources.RISK;
import static io.wyden.accessgateway.client.permission.Permission.Resources.VENUE_ACCOUNT;
import static io.wyden.accessgateway.client.permission.Permission.Resources.WALLET;
import static io.wyden.accessgateway.client.permission.Permission.Resources.WALLET_NOSTRO;
import static io.wyden.accessgateway.client.permission.Permission.Resources.WALLET_VOSTRO;
import static io.wyden.accessgateway.client.permission.Permission.Scopes.CREATE;
import static io.wyden.accessgateway.client.permission.Permission.Scopes.MANAGE;
import static io.wyden.accessgateway.client.permission.Permission.Scopes.READ;
import static io.wyden.accessgateway.client.permission.Permission.Scopes.TRADE;
import static io.wyden.accessgateway.client.permission.Permission.VENUE_ACCOUNT_READ;
import static io.wyden.accessgateway.client.permission.Permission.VENUE_ACCOUNT_TRADE;
import static io.wyden.rest.management.common.Identifiers.randomSuffix;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
                properties = {"logging.level.root=debug", "spring.main.allow-bean-definition-overriding=true"},
                classes = {SpringTestBase.SpringTestConfiguration.class})
@AutoConfigureMockMvc
public abstract class SpringTestBase {

    public static final String AUTHORIZED_PORTFOLIO_ID = "ClientA";
    public static final String UNAUTHORIZED_PORTFOLIO_ID = "ClientB";
    public static final String AUTHORIZED_ACCOUNT_ID = "AccountA";

    public VenueAccount authorizedAccount;
    public Portfolio authorizedPortfolio;

    public String apiKey;
    public String apiSecret;
    public String nonce;
    public String username;

    @LocalServerPort
    protected int port;

    @Autowired
    protected MockMvc mockMvc;

    @Autowired
    protected ObjectMapper objectMapper;

    @Autowired
    protected IMap<String, VenueAccount> venueAccountsMap;

    @Autowired
    protected IMap<String, Portfolio> portfoliosMap;

    @MockBean
    protected ApiKeyRepository apiKeyRepository;

    @MockBean
    protected BookingEngineHttpClient bookingEngineHttpClient;

    @MockBean
    protected BookingEngineRabbitClient bookingEngineRabbitClient;

    @MockBean
    protected ApiKeyWebClient apiKeyWebClient;

    @MockBean
    protected PermissionCache permissionCache;

    @MockBean
    protected PermissionChecker permissionChecker;

    @MockBean
    protected DefaultPermissionValidator defaultPermissionValidator;

    @Autowired
    protected PortfolioRepository portfolioRepository;

    @Autowired
    protected VenueAccountRepository venueAccountRepository;

//    @MockBean
//    protected PortfolioChangeEventConsumer portfolioChangeEventConsumer;

    @MockBean
    protected CurrencyService currencyService;

    @BeforeEach
    protected void setUp() {
        // setup keys
        apiKey = Identifiers.randomIdentifier();
        apiSecret = Identifiers.randomIdentifier();
        nonce = String.valueOf(Instant.now().toEpochMilli());
        username = "admin";

        // setup permissions
        when(apiKeyWebClient.authenticate(any(), any())).thenReturn(Mono.just(new AuthResponseDto(
            username,
            username,
            Set.of(),
            Set.of()
        )));
        when(permissionCache.getPermittedResourceIds(any(), any(), any(), any(), anyInt())).thenReturn(Set.of(AUTHORIZED_PORTFOLIO_ID, AUTHORIZED_ACCOUNT_ID));
        when(permissionChecker.hasPermission(any(), any(), any(), eq(PORTFOLIO), eq(READ))).thenReturn(true);
        when(permissionChecker.hasPermission(any(), any(), any(), eq(PORTFOLIO), eq(CREATE))).thenReturn(true);
        when(permissionChecker.hasPermission(any(), any(), any(), eq(PORTFOLIO), eq(READ), eq(AUTHORIZED_PORTFOLIO_ID))).thenReturn(true);
        when(permissionChecker.hasPermission(any(), any(), any(), eq(PORTFOLIO), eq(MANAGE), eq(AUTHORIZED_PORTFOLIO_ID))).thenReturn(true);
        when(permissionChecker.hasPermission(any(), any(), any(), eq(PORTFOLIO), eq(TRADE), eq(AUTHORIZED_PORTFOLIO_ID))).thenReturn(true);
        when(permissionChecker.hasPermission(any(), any(), any(), eq(PORTFOLIO_READ), eq(AUTHORIZED_PORTFOLIO_ID))).thenReturn(true);
        when(permissionChecker.hasPermission(any(), any(), any(), eq(PORTFOLIO_MANAGE), eq(AUTHORIZED_PORTFOLIO_ID))).thenReturn(true);
        when(permissionChecker.hasPermission(any(), any(), any(), eq(PORTFOLIO_READ))).thenReturn(true);
        when(permissionChecker.hasPermission(any(), any(), any(), eq(PORTFOLIO_MANAGE))).thenReturn(true);
        when(permissionChecker.hasPermission(any(), any(), any(), eq(PORTFOLIO_TRADE))).thenReturn(true);
        when(permissionChecker.hasPermission(any(), any(), any(), eq(VENUE_ACCOUNT), eq(READ), eq(AUTHORIZED_ACCOUNT_ID))).thenReturn(true);
        when(permissionChecker.hasPermission(any(), any(), any(), eq(VENUE_ACCOUNT), eq(TRADE), eq(AUTHORIZED_ACCOUNT_ID))).thenReturn(true);
        when(permissionChecker.hasPermission(any(), any(), any(), eq(VENUE_ACCOUNT), eq(MANAGE), eq(AUTHORIZED_ACCOUNT_ID))).thenReturn(true);
        when(permissionChecker.hasPermission(any(), any(), any(), eq(VENUE_ACCOUNT_READ), eq(AUTHORIZED_ACCOUNT_ID))).thenReturn(true);
        when(permissionChecker.hasPermission(any(), any(), any(), eq(VENUE_ACCOUNT_READ))).thenReturn(true);
        when(permissionChecker.hasPermission(any(), any(), any(), eq(VENUE_ACCOUNT_TRADE))).thenReturn(true);
        when(permissionChecker.hasPermission(any(), any(), any(), eq(RISK), eq(READ))).thenReturn(true);
        when(permissionChecker.hasPermission(any(), any(), any(), eq(RISK), eq(MANAGE))).thenReturn(true);
        when(permissionChecker.hasPermission(any(), any(), any(), eq(WALLET), eq(READ))).thenReturn(true);
        when(permissionChecker.hasPermission(any(), any(), any(), eq(WALLET), eq(CREATE))).thenReturn(true);
        when(permissionChecker.hasPermission(any(), any(), any(), eq(WALLET), eq(TRADE))).thenReturn(true);
        when(permissionChecker.hasPermission(any(), any(), any(), eq(WALLET_VOSTRO), eq(TRADE))).thenReturn(true);
        when(permissionChecker.hasPermission(any(), any(), any(), eq(WALLET_VOSTRO), eq(READ))).thenReturn(true);
        when(permissionChecker.hasPermission(any(), any(), any(), eq(WALLET_NOSTRO), eq(TRADE))).thenReturn(true);
        when(permissionChecker.hasPermission(any(), any(), any(), eq(WALLET_NOSTRO), eq(READ))).thenReturn(true);

        when(defaultPermissionValidator.hasPermission(any(), any(), any(), any())).thenReturn(true);
        when(defaultPermissionValidator.hasPermission(any(), any(), any(), any(), any())).thenReturn(true);

        // setup vault
        VaultApiKey vaultData = new VaultApiKey(apiKey, apiSecret, username, username);
        when(apiKeyRepository.getApiKeyById(apiKey)).thenReturn(vaultData);

        when(currencyService.find(anyString())).thenAnswer(invocation -> {
            String currency = invocation.getArgument(0, String.class);
            return Optional.of(Currency.newBuilder().setSymbol(currency).build());
        });

        authorizedAccount = createAccount(AUTHORIZED_ACCOUNT_ID);
        authorizedPortfolio = createPortfolio(AUTHORIZED_PORTFOLIO_ID);
    }

    protected ApiClient configureApi(String path, String method, String body) {
        String signature = AuthenticationService.calculateSignature(nonce, apiSecret, method, path, body);

        ApiClient apiClient = new ApiClient();
        apiClient.setBasePath("http://localhost:%d/api/v1".formatted(port));
        apiClient.addDefaultHeader(Headers.API_KEY, apiKey);
        apiClient.addDefaultHeader(Headers.API_NONCE, nonce);
        apiClient.addDefaultHeader(Headers.API_SIGNATURE, signature);

        return apiClient;
    }

    protected VenueAccount createAccount() {
        return createAccount(randomSuffix("account"));
    }

    protected VenueAccount createAccount(String accountId) {
        ZonedDateTime dateTime = ZonedDateTime.now();
        String createdAt = DateUtils.toIsoUtcTime(dateTime);

        VenueAccount account = VenueAccount.newBuilder()
            .setId(accountId)
            .setVenueAccountName(randomSuffix("name"))
            .setOwnerUsername(randomSuffix("owner"))
            .setVenueName(randomSuffix("venue"))
            .setCreatedAt(createdAt)
            .setAccountType(AccountType.EXCHANGE)
            .setWalletType(WalletType.WALLET_NOSTRO)
            .build();

        venueAccountsMap.put(account.getId(), account);

        return account;
    }

    protected Portfolio createPortfolio() {
        return createPortfolio(AUTHORIZED_PORTFOLIO_ID);
    }

    protected Portfolio createPortfolio(String portfolioId) {
        ZonedDateTime dateTime = ZonedDateTime.now();
        String createdAt = DateUtils.toIsoUtcTime(dateTime);

        Portfolio portfolio = Portfolio.newBuilder()
            .setId(portfolioId)
            .setName(randomSuffix("name"))
            .setCreatedAt(createdAt)
            .setPortfolioCurrency("EUR")
            .putTags("portfolioGroup", "crypto_users")
            .setPortfolioType(PortfolioType.VOSTRO)
            .build();

        portfoliosMap.put(portfolio.getId(), portfolio);

        return portfolio;
    }

    @TestConfiguration
    static class SpringTestConfiguration {

        @Bean("hazelcast")
        @Primary
        HazelcastInstance createHz() {
            return new TestHazelcastInstanceFactory().newHazelcastInstance();
        }

        @Bean
        IMap<String, Portfolio> portfoliosMap(HazelcastInstance hazelcast) {
            return PortfolioMapConfig.getMap(hazelcast);
        }

        @Bean
        IMap<String, VenueAccount> venueAccountsMap(HazelcastInstance hazelcast) {
            return VenueAccountMapConfig.getMap(hazelcast);
        }

        @Bean
        RabbitIntegrator rabbitIntegrator() {
            RabbitIntegrator rabbitIntegrator = Mockito.mock(RabbitIntegrator.class);
            Channel channel = Mockito.mock(Channel.class);
            when(rabbitIntegrator.getDeclarationAndPublishChannel()).thenReturn(channel);
            when(rabbitIntegrator.getConsumptionChannel()).thenReturn(channel);
            return rabbitIntegrator;
        }

        @Bean
        RabbitExchange<PortfolioChangeRequest> portfolioChangeRequestExchange() {
            return createExchangeMock();
        }

        @Bean
        RabbitExchange<PortfolioChangeEvent> portfolioChangeEventExchange() {
            return createExchangeMock();
        }

        @Bean
        RabbitExchange<PortfolioOnboardingRequest> portfolioOnboardingRequestExchange() {
            return createExchangeMock();
        }

        @Bean
        RabbitExchange<PortfolioOnboardingAccessGrantedEvent> portfolioOnboardingAccessGrantedEventExchange() {
            return createExchangeMock();
        }

        @Bean
        RabbitExchange<AccountOnboardingRequest> accountOnboardingRequestExchange() {
            return createExchangeMock();
        }

        @Bean
        RabbitExchange<VenueAccountCreateRequest> accountCreateRequestExchange() {
            return createExchangeMock();
        }

        @Bean
        RabbitExchange<Message> venueAccountDeactivateRequestExchange() {
            return createExchangeMock();
        }

        @Bean
        RabbitExchange<Message> venueAccountActivateRequestExchange() {
            return createExchangeMock();
        }

        private static RabbitExchange createExchangeMock() {
            RabbitExchange exchange = Mockito.mock(RabbitExchange.class);
            when(exchange.isBindableWithRoutingKey()).thenReturn(true);
            when(exchange.isBindableWithHeaders()).thenReturn(true);
            return exchange;
        }
    }
}
