package io.wyden.rest.management.validation;

import io.wyden.published.booking.PortfolioCashTransferSnapshot;
import io.wyden.published.booking.TransactionRequest;
import io.wyden.published.booking.TransactionSnapshot;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.rest.management.domain.TransactionModel;
import org.junit.jupiter.api.Test;

import static io.wyden.rest.management.common.Identifiers.randomSuffix;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

public class PortfolioCashTransferValidationTest extends TransactionValidationTest {

    @Test
    void shouldNotCreateTransferWhenSourceDoesNotExist() throws Exception {

        // source and target do not exist in the system
        TransactionModel.PortfolioCashTransferRequest request = createPortfolioCashTransferRequest();

        postTransactionAndExpectValidationError(request, "Portfolio does not exist: " + request.sourcePortfolioId());
    }

    @Test
    void shouldNotCreateTransferWhenTargetDoesNotExist() throws Exception {

        Portfolio portfolio = createPortfolio();

        // with existing source
        TransactionModel.PortfolioCashTransferRequest request = createPortfolioCashTransferRequest()
            .withSourcePortfolioId(portfolio.getId());

        postTransactionAndExpectValidationError(request, "Portfolio does not exist: " + request.targetPortfolioId());
    }

    @Test
    void shouldCreateTransfer() throws Exception {

        Portfolio portfolio = createPortfolio();

        // with existing source and target
        TransactionModel.PortfolioCashTransferRequest request = createPortfolioCashTransferRequest()
            .withSourcePortfolioId(portfolio.getId())
            .withTargetPortfolioId(portfolio.getId())
            .withFeePortfolioId(portfolio.getId());

        when(bookingEngineRabbitClient.requestTransaction(any(TransactionRequest.class)))
            .thenReturn(TransactionSnapshot.newBuilder()
                .setPortfolioCashTransfer(
                    PortfolioCashTransferSnapshot.newBuilder()
                        .setUuid(randomSuffix("portfolio-cash-transfer"))
                        .setReservationRef(request.reservationRef())
                        // ... other fields
                        .build())
                .build());

        postTransactionAndExpectSuccess(request);
    }
}
