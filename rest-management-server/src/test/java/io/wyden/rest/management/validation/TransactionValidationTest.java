package io.wyden.rest.management.validation;

import io.wyden.rest.management.SpringTestBase;
import io.wyden.rest.management.domain.ReservationModel;
import io.wyden.rest.management.domain.TransactionModel;
import io.wyden.rest.management.reservation.validator.BookReservationPermissionValidator;
import io.wyden.rest.management.security.authentication.AuthenticationService;
import io.wyden.rest.management.security.authentication.Headers;
import io.wyden.rest.management.transaction.validator.BookTransactionPermissionsValidator;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.access.PermissionEvaluator;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;

import java.time.ZonedDateTime;
import java.util.List;

import static io.wyden.cloud.utils.test.TestUtils.bd;
import static io.wyden.rest.management.common.Identifiers.randomSuffix;
import static org.hamcrest.Matchers.blankOrNullString;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.not;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

public abstract class TransactionValidationTest extends SpringTestBase {

    @MockBean
    PermissionEvaluator permissionEvaluator;

    @MockBean
    BookReservationPermissionValidator bookReservationPermissionValidator;

    @MockBean
    BookTransactionPermissionsValidator bookTransactionPermissionsValidator;

    @BeforeEach
    protected void setUp() {
        super.setUp();

        // clear hz maps
        portfoliosMap.clear();
        venueAccountsMap.clear();
        when(bookTransactionPermissionsValidator.check(any(TransactionModel.AccountCashTransferRequest.class), any(), any())).thenReturn(true);
        when(bookTransactionPermissionsValidator.check(any(TransactionModel.DepositRequest.class), any(), any())).thenReturn(true);
        when(bookTransactionPermissionsValidator.check(any(TransactionModel.WithdrawalRequest.class), any(), any())).thenReturn(true);
        when(bookTransactionPermissionsValidator.check(any(TransactionModel.StreetCashTradeRequest.class), any(), any())).thenReturn(true);
        when(bookTransactionPermissionsValidator.check(any(TransactionModel.ClientCashTradeRequest.class), any(), any())).thenReturn(true);
        when(bookTransactionPermissionsValidator.check(any(TransactionModel.PortfolioCashTransferRequest.class), any(), any())).thenReturn(true);
        when(bookReservationPermissionValidator.check(any(ReservationModel.AccountCashTransferReservationRequest.class), any(), any())).thenReturn(true);
        when(bookReservationPermissionValidator.check(any(ReservationModel.DepositReservationRequest.class), any(), any())).thenReturn(true);
        when(bookReservationPermissionValidator.check(any(ReservationModel.WithdrawalReservationRequest.class), any(), any())).thenReturn(true);
        when(bookReservationPermissionValidator.check(any(ReservationModel.StreetCashTradeReservationRequest.class), any(), any())).thenReturn(true);
        when(bookReservationPermissionValidator.check(any(ReservationModel.ClientCashTradeReservationRequest.class), any(), any())).thenReturn(true);
        when(bookReservationPermissionValidator.check(any(ReservationModel.PortfolioCashTransferReservationRequest.class), any(), any())).thenReturn(true);
        when(permissionEvaluator.hasPermission(any(), any(), any())).thenReturn(true);
        when(permissionEvaluator.hasPermission(any(), any(), any(), any())).thenReturn(true);
    }

    protected void postTransactionAndExpectValidationError(TransactionModel.TransactionRequest request, String reason) throws Exception {
        String body = objectMapper.writeValueAsString(request);

        String path = "/api/v1/transactions";
        String signature = AuthenticationService.calculateSignature(nonce, apiSecret, "POST", path, body);

        this.mockMvc
            .perform(post(path)
                .header(Headers.API_KEY, apiKey)
                .header(Headers.API_NONCE, nonce)
                .header(Headers.API_SIGNATURE, signature)
                .content(body)
                .contentType(MediaType.APPLICATION_JSON))
            .andDo(MockMvcResultHandlers.print())
            .andExpect(status().is4xxClientError())
            .andExpect(jsonPath("$.timestamp").value(not(blankOrNullString())))
            .andExpect(jsonPath("$.status").value(HttpStatus.BAD_REQUEST.value()))
            .andExpect(jsonPath("$.error").value(HttpStatus.BAD_REQUEST.getReasonPhrase()))
            .andExpect(jsonPath("$.message").value(Matchers.containsString(reason)))
            .andExpect(jsonPath("$.path").value(path))
            .andExpect(jsonPath("$.exception").isEmpty())
            .andExpect(jsonPath("$.stackTrace").value(is(blankOrNullString())));
    }

    protected void postTransactionAndExpectSuccess(TransactionModel.TransactionRequest request) throws Exception {
        String body = objectMapper.writeValueAsString(request);

        String path = "/api/v1/transactions";
        String signature = AuthenticationService.calculateSignature(nonce, apiSecret, "POST", path, body);

        this.mockMvc
            .perform(post(path)
                .header(Headers.API_KEY, apiKey)
                .header(Headers.API_NONCE, nonce)
                .header(Headers.API_SIGNATURE, signature)
                .content(body)
                .contentType(MediaType.APPLICATION_JSON))
            .andDo(MockMvcResultHandlers.print())
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.id").value(not(blankOrNullString())))
            .andExpect(jsonPath("$.reservationRef").value(request.reservationRef()));
    }

    protected static TransactionModel.DepositRequest createDepositRequest() {

        long transactionDateTime = ZonedDateTime.now()
            .toInstant()
            .toEpochMilli();

        TransactionModel.TransactionFee exchangeFee = new TransactionModel.TransactionFee(
            bd("10.2"),
            "USD",
            TransactionModel.FeeType.EXCHANGE_FEE
        );

        List<TransactionModel.TransactionFee> fees = List.of(exchangeFee);

        return new TransactionModel.DepositRequest(
            randomSuffix("reservation-ref"),
            transactionDateTime,
            randomSuffix("execution"),
            randomSuffix("venue-execution"),
            TransactionModel.TransactionType.DEPOSIT,
            "BTC",
            bd("1.005"),
            randomSuffix("portfolio"),
            randomSuffix("account"),
            randomSuffix("fee-portfolio"),
            randomSuffix("fee-account"),
            randomSuffix("desc"),
            fees);
    }

    protected static TransactionModel.WithdrawalRequest createWithdrawalRequest() {

        long transactionDateTime = ZonedDateTime.now()
            .toInstant()
            .toEpochMilli();

        TransactionModel.TransactionFee exchangeFee = new TransactionModel.TransactionFee(
            bd("10.2"),
            "USD",
            TransactionModel.FeeType.EXCHANGE_FEE
        );

        List<TransactionModel.TransactionFee> fees = List.of(exchangeFee);

        return new TransactionModel.WithdrawalRequest(
            randomSuffix("reservation-ref"),
            transactionDateTime,
            randomSuffix("execution"),
            randomSuffix("venue-execution"),
            TransactionModel.TransactionType.WITHDRAWAL,
            "BTC",
            bd("1.005"),
            randomSuffix("portfolio"),
            randomSuffix("account"),
            randomSuffix("fee-portfolio"),
            randomSuffix("fee-account"),
            randomSuffix("desc"),
            fees);
    }

    protected static TransactionModel.ClientCashTradeRequest createClientCashTradeRequest() {

        long transactionDateTime = ZonedDateTime.now()
            .toInstant()
            .toEpochMilli();

        TransactionModel.TransactionFee exchangeFee = new TransactionModel.TransactionFee(
            bd("10.2"),
            "USD",
            TransactionModel.FeeType.EXCHANGE_FEE
        );

        List<TransactionModel.TransactionFee> fees = List.of(exchangeFee);

        return new TransactionModel.ClientCashTradeRequest(
            randomSuffix("reservation-ref"),
            transactionDateTime,
            randomSuffix("order"),
            randomSuffix("parent-order"),
            randomSuffix("root-order"),
            randomSuffix("client-root-order"),
            randomSuffix("execution"),
            randomSuffix("venue-execution"),
            randomSuffix("underlying-execution"),
            randomSuffix("root-execution"),
            randomSuffix("ext-order-id"),
            TransactionModel.TransactionType.CLIENT_CASH_TRADE,
            "BTC",
            "USD",
            bd("1.005"),
            bd("0"),
            bd("62000"),
            randomSuffix("portfolio"),
            randomSuffix("counter-portfolio"),
            randomSuffix("desc"),
            fees,
            null,
            null);
    }

    protected static TransactionModel.StreetCashTradeRequest createStreetCashTradeRequest() {

        long transactionDateTime = ZonedDateTime.now()
            .toInstant()
            .toEpochMilli();

        TransactionModel.TransactionFee exchangeFee = new TransactionModel.TransactionFee(
            bd("10.2"),
            "USD",
            TransactionModel.FeeType.EXCHANGE_FEE
        );

        List<TransactionModel.TransactionFee> fees = List.of(exchangeFee);

        return new TransactionModel.StreetCashTradeRequest(
            randomSuffix("reservation-ref"),
            transactionDateTime,
            randomSuffix("order"),
            randomSuffix("parent-order"),
            randomSuffix("root-order"),
            randomSuffix("client-root-order"),
            randomSuffix("execution"),
            randomSuffix("venue-execution"),
            randomSuffix("underlying-execution"),
            randomSuffix("root-execution"),
            randomSuffix("ext-order-id"),
            TransactionModel.TransactionType.STREET_CASH_TRADE,
            "BTC",
            "USD",
            bd("1.005"),
            bd("0"),
            bd("62000"),
            randomSuffix("portfolio"),
            randomSuffix("account"),
            randomSuffix("desc"),
            fees,
            null,
            null);
    }

    protected static TransactionModel.AccountCashTransferRequest createAccountCashTransferRequest() {

        long transactionDateTime = ZonedDateTime.now()
            .toInstant()
            .toEpochMilli();

        TransactionModel.TransactionFee exchangeFee = new TransactionModel.TransactionFee(
            bd("10.2"),
            "USD",
            TransactionModel.FeeType.EXCHANGE_FEE
        );

        List<TransactionModel.TransactionFee> fees = List.of(exchangeFee);

        return new TransactionModel.AccountCashTransferRequest(
            randomSuffix("reservation-ref"),
            transactionDateTime,
            randomSuffix("execution"),
            randomSuffix("venue-execution"),
            TransactionModel.TransactionType.ACCOUNT_CASH_TRANSFER,
            "BTC",
            bd("1.005"),
            randomSuffix("desc"),
            randomSuffix("source"),
            randomSuffix("target"),
            randomSuffix("fee-portfolio"),
            randomSuffix("fee-account"),
            fees);
    }

    protected static TransactionModel.PortfolioCashTransferRequest createPortfolioCashTransferRequest() {

        long transactionDateTime = ZonedDateTime.now()
            .toInstant()
            .toEpochMilli();

        TransactionModel.TransactionFee exchangeFee = new TransactionModel.TransactionFee(
            bd("10.2"),
            "USD",
            TransactionModel.FeeType.EXCHANGE_FEE
        );

        List<TransactionModel.TransactionFee> fees = List.of(exchangeFee);

        return new TransactionModel.PortfolioCashTransferRequest(
            randomSuffix("reservation-ref"),
            transactionDateTime,
            randomSuffix("execution"),
            randomSuffix("venue-execution"),
            TransactionModel.TransactionType.ACCOUNT_CASH_TRANSFER,
            "BTC",
            bd("1.005"),
            randomSuffix("desc"),
            randomSuffix("source"),
            randomSuffix("target"),
            randomSuffix("feePortfolioId"),
            fees);
    }
}
