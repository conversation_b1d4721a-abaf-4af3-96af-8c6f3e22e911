package io.wyden.rest.management.contract;

import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.rest.management.SpringTestBase;
import io.wyden.rest.management.contract.client.TargetRegistryApi;
import io.wyden.rest.management.contract.model.AggregatedTargetState;
import io.wyden.rest.management.targetregistry.TargetRegistryHttpClient;

import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Set;

import static io.wyden.rest.management.targetregistry.AggregatedTargetState.CapabilityState;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anySet;
import static org.mockito.Mockito.when;

@Tag("contract-tests")
public class TargetRegistryContractTest extends SpringTestBase {

    @MockBean
    private TargetRegistryHttpClient targetRegistryHttpClient;

    @Test
    void testActivateConnector() {
        String venueAccountId = AUTHORIZED_ACCOUNT_ID;

        String path = "/api/v1/connectors/%s/activate".formatted(venueAccountId);
        TargetRegistryApi targetRegistryApi = new TargetRegistryApi(configureApi(path, "POST", ""));

        targetRegistryApi.activateConnector(venueAccountId);
    }

    @Test
    void testDeactivateConnector() {
        String venueAccountId = AUTHORIZED_ACCOUNT_ID;

        String path = "/api/v1/connectors/%s/deactivate".formatted(venueAccountId);
        TargetRegistryApi targetRegistryApi = new TargetRegistryApi(configureApi(path, "POST", ""));

        targetRegistryApi.deactivateAConnector(venueAccountId);
    }

    @Test
    void testGetConnectorStates() {
        String path = "/api/v1/connectors/states";
        TargetRegistryApi targetRegistryApi = new TargetRegistryApi(configureApi(path, "GET", ""));

        when(targetRegistryHttpClient.targetStatesSnapshot(anySet()))
            .thenAnswer(invocation -> {
                CapabilityState capabilityState = new CapabilityState("trading", "healthy", "OK", DateUtils.zonedDateTimeToEpochMillis(ZonedDateTime.now()));
                io.wyden.rest.management.targetregistry.AggregatedTargetState targetState = new io.wyden.rest.management.targetregistry.AggregatedTargetState("bitmex", Set.of(capabilityState));
                return List.of(targetState);
            });

        List<AggregatedTargetState> connectorStates = targetRegistryApi.retrieveConnectorStatesWithHttpInfo().getBody();
        assertThat(connectorStates).hasSize(1);
        assertThat(connectorStates.get(0).getTarget()).isNotBlank();
        assertThat(connectorStates.get(0).getCapabilities()).hasSize(1);
        assertThat(connectorStates.get(0).getCapabilities().get(0).getCapability()).isEqualTo("trading");
    }
}
