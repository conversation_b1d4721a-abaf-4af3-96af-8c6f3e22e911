package io.wyden.rest.management.contract.reservation;

import com.fasterxml.jackson.core.JsonProcessingException;
import io.wyden.published.booking.AccountCashTransferReservationSnapshot;
import io.wyden.published.booking.ReservationRequest;
import io.wyden.published.booking.ReservationSnapshot;
import io.wyden.rest.management.SpringTestBase;
import io.wyden.rest.management.contract.client.TransactionReservationApi;
import io.wyden.rest.management.contract.model.AccountCashTransferReservation;
import io.wyden.rest.management.contract.model.AccountCashTransferReservationRequest;
import io.wyden.rest.management.contract.model.ReservationBase;
import io.wyden.rest.management.contract.model.TransactionType;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;

import static io.wyden.rest.management.contract.reservation.ReservationRequestContractFactory.createAccountCashTransferReservationRequest;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.slf4j.LoggerFactory.getLogger;

@Tag("contract-tests")
public class AccountCashTransferReservationContractTest extends SpringTestBase {

    private static final Logger LOGGER = getLogger(AccountCashTransferReservationContractTest.class);

    /**
     * Request Reservation for incoming transaction
     */
    @Test
    public void reservationsPostTest() throws JsonProcessingException {
        AccountCashTransferReservationRequest reservationRequestBase = createAccountCashTransferReservationRequest();

        String path = "/api/v1/reservations";
        TransactionReservationApi reservationApi = new TransactionReservationApi(configureApi(path, "POST", objectMapper.writeValueAsString(reservationRequestBase)));

        when(bookingEngineRabbitClient.requestReservation(any()))
            .thenAnswer(invocation -> {
                ReservationRequest request = invocation.getArgument(0, ReservationRequest.class);
                io.wyden.published.booking.AccountCashTransferReservationRequest reservationRequest = request.getAccountCashTransferReservationRequest();

                AccountCashTransferReservationSnapshot.Builder reservationSnapshot = AccountCashTransferReservationSnapshot.newBuilder()
                    .setReservationRef(reservationRequest.getReservationRef())
                    .setTransactionType(reservationRequest.getTransactionType())
                    .setDateTime(reservationRequest.getDateTime())
                    .setCurrency(reservationRequest.getCurrency())
                    .setQuantity(reservationRequest.getQuantity())
                    .setFromAccountId(reservationRequest.getFromAccountId())
                    .setToAccountId(reservationRequest.getToAccountId())
                    .setFeeAccountId(reservationRequest.getFeeAccountId())
                    .setFeePortfolioId(reservationRequest.getFeePortfolioId())
                    .addAllReservationFee(reservationRequest.getReservationFeeList());

                return ReservationSnapshot.newBuilder()
                    .setAccountCashTransferReservation(reservationSnapshot)
                    .build();
            });

        ReservationBase response = reservationApi.requestAReservationForAnIncomingTransaction(reservationRequestBase);
        LOGGER.info("Response: {}", response);

        assertThat(response).isNotNull();
        assertThat(response).isInstanceOf(AccountCashTransferReservation.class);

        verifyReservation((AccountCashTransferReservation) response, reservationRequestBase);
    }

    public static void verifyReservation(AccountCashTransferReservation reservationResponse, AccountCashTransferReservationRequest reservationRequest) {
        assertThat(reservationResponse.getReservationRef()).isEqualTo(reservationRequest.getReservationRef());
        assertThat(reservationResponse.getTransactionType()).isEqualTo(TransactionType.ACCOUNT_CASH_TRANSFER);
        assertThat(reservationResponse.getDateTime()).isEqualTo(reservationRequest.getDateTime());
        assertThat(reservationResponse.getCurrency()).isEqualTo(reservationRequest.getCurrency());
        assertThat(reservationResponse.getQuantity()).isEqualTo(reservationRequest.getQuantity());
        assertThat(reservationResponse.getSourceAccountId()).isEqualTo(reservationRequest.getSourceAccountId());
        assertThat(reservationResponse.getTargetAccountId()).isEqualTo(reservationRequest.getTargetAccountId());
        assertThat(reservationResponse.getFeeAccountId()).isEqualTo(reservationRequest.getFeeAccountId());
        assertThat(reservationResponse.getFeePortfolioId()).isEqualTo(reservationRequest.getFeePortfolioId());
        assertThat(reservationResponse.getFees()).hasSize(1);
        assertThat(reservationResponse.getFees().get(0).getAmount()).isEqualTo(reservationRequest.getFees().get(0).getAmount());
        assertThat(reservationResponse.getFees().get(0).getCurrency()).isEqualTo(reservationRequest.getFees().get(0).getCurrency());
        assertThat(reservationResponse.getFees().get(0).getFeeType()).isEqualTo(reservationRequest.getFees().get(0).getFeeType());
    }
}