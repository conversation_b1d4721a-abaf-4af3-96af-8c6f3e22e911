spring.application.name=rest-management

spring.cloud.vault.host=localhost
spring.cloud.vault.token=my-token
spring.cloud.vault.scheme=http
spring.cloud.vault.kv.application-name=rest-management
spring.cloud.vault.reactive.enabled=false

server.port=8083

# This sets a custom prefix to all endpoint.
# All @RestControllers have to use @RequestMapping(path = "${rest.management.context-path}") annotation additionally.
# This solution is better than setting server.servlet.context-path=/api/v1
# or spring.mvc.servlet.path=/api/v1
# because it does not affect readiness and liveness probes (no changes in devops required).
# Entire application is available under / path so error handling works also for example for /api/v2
rest.management.context-path=/api/v1

# How long to wait for onboarding process to finish before error status is returned
rest.management.portfolio-onboarding-timeout-seconds=120
rest.management.account-onboarding-timeout-seconds=120
rest.management.portfolio-create-timeout-seconds=20
rest.management.query-limits.portfolio=100

# Maximum number of portfolios / accounts in one onboarding request
rest.management.onboarding.max-items=200

server.error.whitelabel.enabled=false
spring.mvc.throw-exception-if-no-handler-found=true
spring.web.resources.add-mappings=false

# comma-separated list of hz member hosts
hz.addressList=localhost
hz.outboundPortDefinition=

rabbitmq.username=rest-management
rabbitmq.password=password
rabbitmq.virtualHost=/
rabbitmq.host=localhost
rabbitmq.port=5672
# specify a valid protocol name, e.g. "TLSv1.2" . leave empty for non-TLS connection
rabbitmq.tls =

management.endpoints.web.exposure.include=health,prometheus,metrics,loggers
management.endpoint.health.show-details=always
management.endpoint.health.probes.enabled=true
management.endpoint.loggers.enabled=true
management.endpoint.health.group.liveness.include=livenessState,rabbit,clusterRunning,diskSpace,hazelcast
management.endpoint.health.group.readiness.include=readinessState
management.health.livenessState.enabled=true
management.health.readinessState.enabled=true
management.metrics.tags.wyden_service=rest-management

access.gateway.host=http://localhost:8089
broker.config.service.host=http://localhost:8049
booking.engine.host=http://localhost:8100
booking.reporting.host=http://localhost:8045
booking.reporting.balances.enabled=false
risk.engine.host=http://localhost:8300
order.history.host=http://localhost:8040
tracing.collector.endpoint=http://localhost:4317
target.registry.host=http://localhost:8066
target.registry.target.states.url=${target.registry.host}/targets/states

nonce.validation.window=1000s

logging.level.io.wyden.rest.management.security=debug
rabbitmq.reference-data-rest-management-queue.portfolio-change-event=rest-management-queue.reference-data.%s.v1.PORTFOLIO-CHANGE-EVENT
rabbitmq.booking-engine-rest-management-queue.portfolio-onboarding-event=rest-management-queue.booking-engine.%s.v1.PORTFOLIO-ONBOARDING-EVENT
rabbitmq.booking-engine-rest-management-queue.account-onboarding-event=rest-management-queue.booking-engine.%s.v1.ACCOUNT-ONBOARDING-EVENT
rabbitmq.booking-engine-rest-api-queue.command-result=rest-management-queue.booking.%s.RESULT

booking.command.response.timeoutInSeconds = 30

server.error.include-stacktrace=never

entitlement.wyden-exchange=PLATFORM_WYDEN_EXCHANGE
