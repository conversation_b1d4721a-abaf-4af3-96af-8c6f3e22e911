package io.wyden.rest.management.referencedata;

import io.wyden.rest.management.security.authentication.WydenAuthenticationToken;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.ServletWebRequest;


@RestController
@RequestMapping(path = "/api/v1")
public class VenueAccountController {

    private static final Logger LOGGER = LoggerFactory.getLogger(VenueAccountController.class);

    private final VenueAccountService venueAccountService;

    public VenueAccountController(VenueAccountService venueAccountService) {
        this.venueAccountService = venueAccountService;
    }

    @PostMapping("/connectors/{venueAccountId}/activate")
    @PreAuthorize("@defaultPermissionValidator.hasPermission(#venueAccountId,'venue.account', 'manage', #wydenAuthenticationToken, #servletRequest)")
    public ResponseEntity<Void> activate(@PathVariable("venueAccountId") String venueAccountId, WydenAuthenticationToken wydenAuthenticationToken, ServletWebRequest servletRequest) {
        LOGGER.info("Activating connector: {}", venueAccountId);
        venueAccountService.activate(wydenAuthenticationToken.getClientId(), venueAccountId);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/connectors/{venueAccountId}/deactivate")
    @PreAuthorize("@defaultPermissionValidator.hasPermission(#venueAccountId,'venue.account', 'manage', #wydenAuthenticationToken, #servletRequest)")
    public ResponseEntity<Void> deactivate(@PathVariable("venueAccountId") String venueAccountId, WydenAuthenticationToken wydenAuthenticationToken, ServletWebRequest servletRequest) {
        LOGGER.info("Deactivating connector: {}", venueAccountId);
        venueAccountService.deactivate(wydenAuthenticationToken.getClientId(), venueAccountId);
        return ResponseEntity.ok().build();
    }
}
