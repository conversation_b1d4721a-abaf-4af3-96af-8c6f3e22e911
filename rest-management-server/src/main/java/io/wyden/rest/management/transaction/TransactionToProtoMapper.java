package io.wyden.rest.management.transaction;

import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.booking.AccountCashTransferRequest;
import io.wyden.published.booking.ClientCashTradeRequest;
import io.wyden.published.booking.DepositRequest;
import io.wyden.published.booking.Fee;
import io.wyden.published.booking.FeeType;
import io.wyden.published.booking.PortfolioCashTransferRequest;
import io.wyden.published.booking.StreetCashTradeRequest;
import io.wyden.published.booking.TransactionRequest;
import io.wyden.published.booking.TransactionSearch;
import io.wyden.published.booking.TransactionType;
import io.wyden.published.booking.WithdrawalRequest;
import io.wyden.published.common.Metadata;
import io.wyden.published.common.SortingOrder;
import io.wyden.published.common.TernaryBool;
import io.wyden.rest.management.common.StringUtils;
import io.wyden.rest.management.domain.TransactionModel;

import java.util.Collection;
import java.util.UUID;

import static io.wyden.rest.management.common.ProtoMappingUtils.createMetadata;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

public final class TransactionToProtoMapper {

    private TransactionToProtoMapper() {
    }

    public static TransactionRequest map(TransactionModel.TransactionRequest transactionRequest) {

        Metadata metadata = createMetadata();
        TransactionRequest.Builder builder = TransactionRequest.newBuilder();
        builder.setUuid(UUID.randomUUID().toString());

        if (transactionRequest instanceof TransactionModel.DepositRequest depositRequest) {
            DepositRequest.Builder request = DepositRequest.newBuilder()
                .setMetadata(metadata)
                .setDateTime(DateUtils.epochMillisToIsoUtcTime(String.valueOf(depositRequest.dateTime())));

            if (isNotBlank(depositRequest.reservationRef())) {
                request.setReservationRef(depositRequest.reservationRef());
            }

            if (isNotBlank(depositRequest.executionId())) {
                request.setExecutionId(depositRequest.executionId());
            }

            if (isNotBlank(depositRequest.venueExecutionId())) {
                request.setVenueExecutionId(depositRequest.venueExecutionId());
            }

            if (isNotBlank(depositRequest.currency())) {
                request.setCurrency(depositRequest.currency());
            }

            if (isNotBlank(depositRequest.description())) {
                request.setDescription(depositRequest.description());
            }

            if (depositRequest.quantity() != null) {
                request.setQuantity(StringUtils.toProtoString(depositRequest.quantity()));
            }

            if (isNotBlank(depositRequest.portfolioId())) {
                request.setPortfolioId(depositRequest.portfolioId());
            }

            if (isNotBlank(depositRequest.accountId())) {
                request.setAccountId(depositRequest.accountId());
            }

            if (isNotBlank(depositRequest.feePortfolioId())) {
                request.setFeePortfolioId(depositRequest.feePortfolioId());
            }

            if (isNotBlank(depositRequest.feeAccountId())) {
                request.setFeeAccountId(depositRequest.feeAccountId());
            }

            if (map(depositRequest.fees()) != null) {
                request.addAllTransactionFee(map(depositRequest.fees()));
            }

            return builder
                .setDepositRequest(request)
                .build();
        }

        if (transactionRequest instanceof TransactionModel.WithdrawalRequest withdrawalRequest) {
            WithdrawalRequest.Builder request = WithdrawalRequest.newBuilder()
                .setMetadata(metadata)
                .setDateTime(DateUtils.epochMillisToIsoUtcTime(String.valueOf(withdrawalRequest.dateTime())));

            if (isNotBlank(withdrawalRequest.reservationRef())) {
                request.setReservationRef(withdrawalRequest.reservationRef());
            }

            if (isNotBlank(withdrawalRequest.executionId())) {
                request.setExecutionId(withdrawalRequest.executionId());
            }

            if (isNotBlank(withdrawalRequest.venueExecutionId())) {
                request.setVenueExecutionId(withdrawalRequest.venueExecutionId());
            }

            if (isNotBlank(withdrawalRequest.currency())) {
                request.setCurrency(withdrawalRequest.currency());
            }

            if (isNotBlank(withdrawalRequest.description())) {
                request.setDescription(withdrawalRequest.description());
            }

            if (withdrawalRequest.quantity() != null) {
                request.setQuantity(StringUtils.toProtoString(withdrawalRequest.quantity()));
            }

            if (isNotBlank(withdrawalRequest.portfolioId())) {
                request.setPortfolioId(withdrawalRequest.portfolioId());
            }

            if (isNotBlank(withdrawalRequest.accountId())) {
                request.setAccountId(withdrawalRequest.accountId());
            }

            if (isNotBlank(withdrawalRequest.feePortfolioId())) {
                request.setFeePortfolioId(withdrawalRequest.feePortfolioId());
            }

            if (isNotBlank(withdrawalRequest.feeAccountId())) {
                request.setFeeAccountId(withdrawalRequest.feeAccountId());
            }

            if (map(withdrawalRequest.fees()) != null) {
                request.addAllTransactionFee(map(withdrawalRequest.fees()));
            }

            return builder
                .setWithdrawalRequest(request)
                .build();
        }

        if (transactionRequest instanceof TransactionModel.ClientCashTradeRequest tradeRequest) {
            ClientCashTradeRequest.Builder request = ClientCashTradeRequest.newBuilder()
                .setMetadata(metadata)
                .setDateTime(DateUtils.epochMillisToIsoUtcTime(String.valueOf(tradeRequest.dateTime())));

            if (isNotBlank(tradeRequest.reservationRef())) {
                request.setReservationRef(tradeRequest.reservationRef());
            }

            if (isNotBlank(tradeRequest.orderId())) {
                request.setOrderId(tradeRequest.orderId());
            }

            if (isNotBlank(tradeRequest.parentOrderId())) {
                request.setParentOrderId(tradeRequest.parentOrderId());
            }

            if (isNotBlank(tradeRequest.rootOrderId())) {
                request.setRootOrderId(tradeRequest.rootOrderId());
            }

            if (isNotBlank(tradeRequest.clientRootOrderId())) {
                request.setClientRootOrderId(tradeRequest.clientRootOrderId());
            }

            if (isNotBlank(tradeRequest.extOrderId())) {
                request.setExtOrderId(tradeRequest.extOrderId());
            }

            if (isNotBlank(tradeRequest.executionId())) {
                request.setExecutionId(tradeRequest.executionId());
            }

            if (isNotBlank(tradeRequest.venueExecutionId())) {
                request.setVenueExecutionId(tradeRequest.venueExecutionId());
            }

            if (isNotBlank(tradeRequest.rootExecutionId())) {
                request.setRootExecutionId(tradeRequest.rootExecutionId());
            }

            if (isNotBlank(tradeRequest.underlyingExecutionId())) {
                request.setUnderlyingExecutionId(tradeRequest.underlyingExecutionId());
            }

            if (isNotBlank(tradeRequest.baseCurrency())) {
                request.setBaseCurrency(tradeRequest.baseCurrency());
            }

            if (isNotBlank(tradeRequest.currency())) {
                request.setCurrency(tradeRequest.currency());
            }

            if (isNotBlank(tradeRequest.description())) {
                request.setDescription(tradeRequest.description());
            }

            if (tradeRequest.quantity() != null) {
                request.setQuantity(StringUtils.toProtoString(tradeRequest.quantity()));
            }

            if (tradeRequest.leavesQuantity() != null) {
                request.setLeavesQuantity(StringUtils.toProtoString(tradeRequest.leavesQuantity()));
            }

            if (tradeRequest.price() != null) {
                request.setPrice(StringUtils.toProtoString(tradeRequest.price()));
            }

            if (isNotBlank(tradeRequest.portfolioId())) {
                request.setPortfolioId(tradeRequest.portfolioId());
            }

            if (isNotBlank(tradeRequest.counterPortfolioId())) {
                request.setCounterPortfolioId(tradeRequest.counterPortfolioId());
            }

            if (map(tradeRequest.fees()) != null) {
                request.addAllTransactionFee(map(tradeRequest.fees()));
            }

            request.setSettled(tradeRequest.settled() == null || tradeRequest.settled());
            if (tradeRequest.settledDateTime() != null) {
                request.setSettledDateTime(DateUtils.epochMillisToIsoUtcTime(String.valueOf(tradeRequest.settledDateTime())));
            }

            return builder
                .setClientCashTradeRequest(request)
                .build();
        }

        if (transactionRequest instanceof TransactionModel.StreetCashTradeRequest tradeRequest) {
            StreetCashTradeRequest.Builder request = StreetCashTradeRequest.newBuilder()
                .setMetadata(metadata)
                .setDateTime(DateUtils.epochMillisToIsoUtcTime(String.valueOf(tradeRequest.dateTime())));

            if (isNotBlank(tradeRequest.reservationRef())) {
                request.setReservationRef(tradeRequest.reservationRef());
            }

            if (isNotBlank(tradeRequest.orderId())) {
                request.setOrderId(tradeRequest.orderId());
            }

            if (isNotBlank(tradeRequest.parentOrderId())) {
                request.setParentOrderId(tradeRequest.parentOrderId());
            }

            if (isNotBlank(tradeRequest.rootOrderId())) {
                request.setRootOrderId(tradeRequest.rootOrderId());
            }

            if (isNotBlank(tradeRequest.executionId())) {
                request.setExecutionId(tradeRequest.executionId());
            }

            if (isNotBlank(tradeRequest.venueExecutionId())) {
                request.setVenueExecutionId(tradeRequest.venueExecutionId());
            }

            if (isNotBlank(tradeRequest.rootExecutionId())) {
                request.setRootExecutionId(tradeRequest.rootExecutionId());
            }

            if (isNotBlank(tradeRequest.clientRootOrderId())) {
                request.setClientRootOrderId(tradeRequest.clientRootOrderId());
            }

            if (isNotBlank(tradeRequest.extOrderId())) {
                request.setExtOrderId(tradeRequest.extOrderId());
            }

            if (isNotBlank(tradeRequest.underlyingExecutionId())) {
                request.setUnderlyingExecutionId(tradeRequest.underlyingExecutionId());
            }

            if (isNotBlank(tradeRequest.baseCurrency())) {
                request.setBaseCurrency(tradeRequest.baseCurrency());
            }

            if (isNotBlank(tradeRequest.currency())) {
                request.setCurrency(tradeRequest.currency());
            }

            if (isNotBlank(tradeRequest.description())) {
                request.setDescription(tradeRequest.description());
            }

            if (tradeRequest.quantity() != null) {
                request.setQuantity(StringUtils.toProtoString(tradeRequest.quantity()));
            }

            if (tradeRequest.leavesQuantity() != null) {
                request.setLeavesQuantity(StringUtils.toProtoString(tradeRequest.leavesQuantity()));
            }

            if (tradeRequest.price() != null) {
                request.setPrice(StringUtils.toProtoString(tradeRequest.price()));
            }

            if (isNotBlank(tradeRequest.portfolioId())) {
                request.setPortfolioId(tradeRequest.portfolioId());
            }

            if (isNotBlank(tradeRequest.accountId())) {
                request.setAccountId(tradeRequest.accountId());
            }

            if (map(tradeRequest.fees()) != null) {
                request.addAllTransactionFee(map(tradeRequest.fees()));
            }

            request.setSettled(tradeRequest.settled() == null || tradeRequest.settled());
            if (tradeRequest.settledDateTime() != null) {
                request.setSettledDateTime(DateUtils.epochMillisToIsoUtcTime(String.valueOf(tradeRequest.settledDateTime())));
            }

            return builder
                .setStreetCashTradeRequest(request)
                .build();
        }

        if (transactionRequest instanceof TransactionModel.PortfolioCashTransferRequest transferRequest) {
            PortfolioCashTransferRequest.Builder request = PortfolioCashTransferRequest.newBuilder()
                .setMetadata(metadata)
                .setDateTime(DateUtils.epochMillisToIsoUtcTime(String.valueOf(transferRequest.dateTime())));

            if (isNotBlank(transferRequest.reservationRef())) {
                request.setReservationRef(transferRequest.reservationRef());
            }

            if (isNotBlank(transferRequest.executionId())) {
                request.setExecutionId(transferRequest.executionId());
            }

            if (isNotBlank(transferRequest.venueExecutionId())) {
                request.setVenueExecutionId(transferRequest.venueExecutionId());
            }

            if (isNotBlank(transferRequest.currency())) {
                request.setCurrency(transferRequest.currency());
            }

            if (isNotBlank(transferRequest.description())) {
                request.setDescription(transferRequest.description());
            }

            if (transferRequest.quantity() != null) {
                request.setQuantity(StringUtils.toProtoString(transferRequest.quantity()));
            }

            if (isNotBlank(transferRequest.sourcePortfolioId())) {
                request.setFromPortfolioId(transferRequest.sourcePortfolioId());
            }

            if (isNotBlank(transferRequest.targetPortfolioId())) {
                request.setToPortfolioId(transferRequest.targetPortfolioId());
            }

            if (isNotBlank(transferRequest.feePortfolioId())) {
                request.setFeePortfolioId(transferRequest.feePortfolioId());
            }

            if (map(transferRequest.fees()) != null) {
                request.addAllTransactionFee(map(transferRequest.fees()));
            }

            return builder
                .setPortfolioCashTransferRequest(request)
                .build();
        }

        if (transactionRequest instanceof TransactionModel.AccountCashTransferRequest transferRequest) {
            AccountCashTransferRequest.Builder request = AccountCashTransferRequest.newBuilder()
                .setMetadata(metadata)
                .setDateTime(DateUtils.epochMillisToIsoUtcTime(String.valueOf(transferRequest.dateTime())));

            if (isNotBlank(transferRequest.reservationRef())) {
                request.setReservationRef(transferRequest.reservationRef());
            }

            if (isNotBlank(transferRequest.executionId())) {
                request.setExecutionId(transferRequest.executionId());
            }

            if (isNotBlank(transferRequest.venueExecutionId())) {
                request.setVenueExecutionId(transferRequest.venueExecutionId());
            }

            if (isNotBlank(transferRequest.currency())) {
                request.setCurrency(transferRequest.currency());
            }

            if (isNotBlank(transferRequest.description())) {
                request.setDescription(transferRequest.description());
            }

            if (transferRequest.quantity() != null) {
                request.setQuantity(StringUtils.toProtoString(transferRequest.quantity()));
            }

            if (isNotBlank(transferRequest.sourceAccountId())) {
                request.setFromAccountId(transferRequest.sourceAccountId());
            }

            if (isNotBlank(transferRequest.targetAccountId())) {
                request.setToAccountId(transferRequest.targetAccountId());
            }

            if (isNotBlank(transferRequest.feePortfolioId())) {
                request.setFeePortfolioId(transferRequest.feePortfolioId());
            }

            if (isNotBlank(transferRequest.feeAccountId())) {
                request.setFeeAccountId(transferRequest.feeAccountId());
            }

            if (map(transferRequest.fees()) != null) {
                request.addAllTransactionFee(map(transferRequest.fees()));
            }

            return builder
                .setAccountCashTransferRequest(request)
                .build();
        }

        return null;
    }

    public static TransactionSearch map(TransactionModel.TransactionSearch transactionSearch, String clientId) {
        TransactionSearch.Builder search = TransactionSearch.newBuilder()
            .setClientId(clientId)
            .setSortingOrder(map(transactionSearch.sortingOrder()));

        if (isNotBlank(transactionSearch.currency())) {
            search.addCurrency(transactionSearch.currency());
        }

        if (isNotBlank(transactionSearch.accountId())) {
            search.addAccountId(transactionSearch.accountId());
        }

        if (isNotBlank(transactionSearch.portfolioId())) {
            search.addPortfolioId(transactionSearch.portfolioId());
        }

        if (transactionSearch.transactionType() != null) {
            search.addTransactionType(map(transactionSearch.transactionType()));
        }

        if (isNotBlank(transactionSearch.orderId())) {
            search.setOrderId(transactionSearch.orderId());
        }

        if (isNotBlank(transactionSearch.parentOrderId())) {
            search.setParentOrderId(transactionSearch.parentOrderId());
        }

        if (isNotBlank(transactionSearch.rootOrderId())) {
            search.setRootOrderId(transactionSearch.rootOrderId());
        }

        if (isNotBlank(transactionSearch.executionId())) {
            search.setExecutionId(transactionSearch.executionId());
        }

        if (isNotBlank(transactionSearch.underlyingExecutionId())) {
            search.setUnderlyingExecutionId(transactionSearch.underlyingExecutionId());
        }

        if (isNotBlank(transactionSearch.rootExecutionId())) {
            search.setRootExecutionId(transactionSearch.rootExecutionId());
        }

        if (transactionSearch.from() != null) {
            search.setFrom(String.valueOf(transactionSearch.from()));
        }

        if (transactionSearch.to() != null) {
            search.setTo(String.valueOf(transactionSearch.to()));
        }

        if (transactionSearch.settlementFrom() != null) {
            search.setSettlementFrom(String.valueOf(transactionSearch.settlementFrom()));
        }

        if (transactionSearch.settlementTo() != null) {
            search.setSettlementTo(String.valueOf(transactionSearch.settlementTo()));
        }

        if (transactionSearch.settled() != null) {
            search.setSettled(map(transactionSearch.settled()));
        }

        if (transactionSearch.first() != null) {
            search.setFirst(transactionSearch.first());
        }

        if (isNotBlank(transactionSearch.after())) {
            search.setAfter(transactionSearch.after());
        }

        return search.build();
    }

    private static Collection<Fee> map(Collection<TransactionModel.TransactionFee> fees) {
        if (fees == null) {
            return null;
        }

        return fees.stream()
            .map(fee -> Fee.newBuilder()
                .setAmount(StringUtils.toProtoString(fee.amount()))
                .setCurrency(fee.currency())
                .setFeeType(map(fee.feeType()))
                .build())
            .toList();
    }

    private static FeeType map(TransactionModel.FeeType feeType) {
        if (feeType == null) {
            return null;
        }

        return switch (feeType) {
            case FIXED_FEE -> FeeType.FIXED_FEE;
            case TRANSACTION_FEE -> FeeType.TRANSACTION_FEE;
            case EXCHANGE_FEE -> FeeType.EXCHANGE_FEE;
            default -> FeeType.FEE_TYPE_UNSPECIFIED;
        };
    }

    public static TransactionType map(TransactionModel.TransactionType transactionType) {
        if (transactionType == null) {
            return null;
        }

        return switch (transactionType) {
            case DEPOSIT -> TransactionType.TRANSACTION_TYPE_DEPOSIT;
            case WITHDRAWAL -> TransactionType.TRANSACTION_TYPE_WITHDRAWAL;
            case CLIENT_CASH_TRADE -> TransactionType.TRANSACTION_TYPE_CLIENT_CASH_TRADE;
            case STREET_CASH_TRADE -> TransactionType.TRANSACTION_TYPE_STREET_CASH_TRADE;
            case PORTFOLIO_CASH_TRANSFER -> TransactionType.TRANSACTION_TYPE_PORTFOLIO_CASH_TRANSFER;
            case ACCOUNT_CASH_TRANSFER -> TransactionType.TRANSACTION_TYPE_ACCOUNT_CASH_TRANSFER;
            case SETTLEMENT -> TransactionType.TRANSACTION_TYPE_SETTLEMENT;
            default -> TransactionType.TRANSACTION_TYPE_UNSPECIFIED;
        };
    }

    public static SortingOrder map(TransactionModel.SortingOrder sortingOrder) {
        if (sortingOrder == null) {
            // by default all entries should be ordered by 'latest first'
            return SortingOrder.SORTING_ORDER_DESC;
        }

        return switch (sortingOrder) {
            case ASC -> SortingOrder.SORTING_ORDER_ASC;
            case DESC -> SortingOrder.SORTING_ORDER_DESC;
        };
    }

    private static TernaryBool map(Boolean bool) {
        if (bool == null) {
            return TernaryBool.TERNARY_BOOL_UNSPECIFIED;
        }

        return bool ? TernaryBool.TRUE : TernaryBool.FALSE;
    }
}
