package io.wyden.rest.management.transaction.validator;

import io.wyden.rest.management.account.AccountService;
import io.wyden.rest.management.domain.ReservationModel;
import io.wyden.rest.management.domain.TransactionModel;
import io.wyden.rest.management.portfolio.PortfolioService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Component
public class TransactionRequestValidator {

    private final PortfolioService portfolioService;
    private final AccountService accountService;

    public TransactionRequestValidator(PortfolioService portfolioService,
                                       AccountService accountService) {
        this.portfolioService = portfolioService;
        this.accountService = accountService;
    }

    public void validate(TransactionModel.TransactionRequest transactionRequest) {
        if (transactionRequest instanceof TransactionModel.DepositRequest request) {
            validatePortfolioId(request.portfolioId());
            validateAccountId(request.accountId());
            validatePortfolioId(request.feePortfolioId());
            validateAccountId(request.feeAccountId());
            validateFees(request);
        }

        if (transactionRequest instanceof TransactionModel.WithdrawalRequest request) {
            validatePortfolioId(request.portfolioId());
            validateAccountId(request.accountId());
            validatePortfolioId(request.feePortfolioId());
            validateAccountId(request.feeAccountId());
            validateFees(request);
        }

        if (transactionRequest instanceof TransactionModel.ClientCashTradeRequest request) {
            validatePortfolioId(request.portfolioId());
            validatePortfolioId(request.counterPortfolioId());
        }

        if (transactionRequest instanceof TransactionModel.StreetCashTradeRequest request) {
            validatePortfolioId(request.portfolioId());
            validateAccountId(request.accountId());
        }

        if (transactionRequest instanceof TransactionModel.AccountCashTransferRequest request) {
            validateAccountId(request.sourceAccountId());
            validateAccountId(request.targetAccountId());
            validatePortfolioId(request.feePortfolioId());
            validateAccountId(request.feeAccountId());
            validateFees(request);
        }

        if (transactionRequest instanceof TransactionModel.PortfolioCashTransferRequest request) {
            validatePortfolioId(request.sourcePortfolioId());
            validatePortfolioId(request.targetPortfolioId());
            validatePortfolioId(request.feePortfolioId());
        }
    }

    public void validate(ReservationModel.ReservationRequest reservationRequest) {
        if (reservationRequest instanceof ReservationModel.DepositReservationRequest request) {
            validatePortfolioId(request.portfolioId());
            validateAccountId(request.accountId());
            validatePortfolioId(request.feePortfolioId());
            validateAccountId(request.feeAccountId());
            validateFees(request);
        }

        if (reservationRequest instanceof ReservationModel.WithdrawalReservationRequest request) {
            validatePortfolioId(request.portfolioId());
            validateAccountId(request.accountId());
            validatePortfolioId(request.feePortfolioId());
            validateAccountId(request.feeAccountId());
            validateFees(request);
        }

        if (reservationRequest instanceof ReservationModel.ClientCashTradeReservationRequest request) {
            validatePortfolioId(request.portfolioId());
            validatePortfolioId(request.counterPortfolioId());
        }

        if (reservationRequest instanceof ReservationModel.StreetCashTradeReservationRequest request) {
            validatePortfolioId(request.portfolioId());
            validateAccountId(request.accountId());
        }

        if (reservationRequest instanceof ReservationModel.AccountCashTransferReservationRequest request) {
            validateAccountId(request.sourceAccountId());
            validateAccountId(request.targetAccountId());
            validatePortfolioId(request.feePortfolioId());
            validateAccountId(request.feeAccountId());
            validateFees(request);
        }

        if (reservationRequest instanceof ReservationModel.PortfolioCashTransferReservationRequest request) {
            validatePortfolioId(request.sourcePortfolioId());
            validatePortfolioId(request.targetPortfolioId());
            validatePortfolioId(request.feePortfolioId());
        }
    }

    private void validatePortfolioId(String portfolioId) {
        if (isNotBlank(portfolioId)) {
            portfolioService.find(portfolioId)
                .orElseThrow(() -> new IllegalArgumentException("Portfolio does not exist: " + portfolioId));
        }
    }

    private void validateAccountId(String accountId) {
        if (isNotBlank(accountId)) {
            if (!accountService.exists(accountId)) {
                throw new IllegalArgumentException("Account does not exist: " + accountId);
            }
        }
    }

    /**
     * Validates the fees in the given transaction request.
     * Checks whether the fees, feePortfolioId, and feeAccountId are consistent based on the presence of fees and
     * ensures that the required relationships between these fields are met.
     */
    private void validateFees(TransactionModel.WithAdditionalFees<? extends TransactionModel.AdditionalFees> request) {
        if (CollectionUtils.isEmpty(request.fees())) {
            // ok, because no fees, so feePortfolioId and feeAccountId are not required
            return;
        }

        if (isNotBlank(request.feePortfolioId()) && isNotBlank(request.feeAccountId())) {
            // ok, because both feePortfolioId and feeAccountId are present
            return;
        }

        boolean hasFees = request.fees().stream()
            .anyMatch(fee -> fee.amount().compareTo(BigDecimal.ZERO) != 0);

        if (hasFees) {
            // has fees, but feePortfolioId and/or feeAccountId are empty
            throw new IllegalArgumentException("feePortfolioId and feeAccountId cannot be empty, when fees are added");
        }
    }
}
