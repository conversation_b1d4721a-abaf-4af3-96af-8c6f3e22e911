package io.wyden.rest.management.transaction.validator;

import io.wyden.rest.management.domain.TransactionModel;
import io.wyden.rest.management.security.authentication.WydenAuthenticationToken;
import io.wyden.rest.management.security.authentication.WydenAuthenticationPermissionHelper;

import org.springframework.stereotype.Component;
import org.springframework.web.context.request.ServletWebRequest;

import static io.wyden.rest.management.security.authentication.WydenAuthenticationPermissionHelper.hasReadAndTradePermissionsForAccount;
import static io.wyden.rest.management.security.authentication.WydenAuthenticationPermissionHelper.hasReadAndTradePermissionsForPortfolio;
import static io.wyden.rest.management.security.authentication.WydenAuthenticationPermissionHelper.hasReadPermissionsForAccount;
import static io.wyden.rest.management.util.AccessDeniedLogger.logIfAccessDenied;

@Component("bookTransactionValidator")
public class BookTransactionPermissionsValidator implements WydenAuthenticationPermissionHelper {

    private static final String PORTFOLIO_READ_PORTFOLIO_TRADE = "portfolio.read & portfolio.trade";
    private static final String ACCOUNT_READ_ACCOUNT_TRADE = "account.read & account.trade";

    public boolean check(final TransactionModel.ClientCashTradeRequest request, WydenAuthenticationToken wydenAuthenticationToken, ServletWebRequest servletRequest) {
        return logIfAccessDenied(hasReadAndTradePermissionsForPortfolio(request.portfolioId(), wydenAuthenticationToken),
            servletRequest, wydenAuthenticationToken, PORTFOLIO_READ_PORTFOLIO_TRADE, request.portfolioId());
    }

    public boolean check(final TransactionModel.StreetCashTradeRequest request, WydenAuthenticationToken wydenAuthenticationToken, ServletWebRequest servletRequest) {
        boolean hasPortfolioPermission = hasReadAndTradePermissionsForPortfolio(request.portfolioId(), wydenAuthenticationToken);
        boolean hasAccountPermission = hasReadAndTradePermissionsForAccount(request.accountId(), wydenAuthenticationToken);

        return logIfAccessDenied(hasPortfolioPermission, servletRequest, wydenAuthenticationToken, PORTFOLIO_READ_PORTFOLIO_TRADE, request.portfolioId()) &&
            logIfAccessDenied(hasAccountPermission, servletRequest, wydenAuthenticationToken, ACCOUNT_READ_ACCOUNT_TRADE, request.portfolioId());
    }

    public boolean check(final TransactionModel.DepositRequest request, WydenAuthenticationToken wydenAuthenticationToken, ServletWebRequest servletRequest) {
        boolean hasPortfolioPermission = hasReadAndTradePermissionsForPortfolio(request.portfolioId(), wydenAuthenticationToken);
        boolean hasFeePortfolioPermission = hasReadAndTradePermissionsForPortfolio(request.feePortfolioId(), wydenAuthenticationToken);
        boolean hasAccountPermission = hasReadAndTradePermissionsForAccount(request.accountId(), wydenAuthenticationToken);
        boolean hasFeeAccountPermission = hasReadAndTradePermissionsForAccount(request.feeAccountId(), wydenAuthenticationToken);

        return logIfAccessDenied(hasPortfolioPermission, servletRequest, wydenAuthenticationToken, PORTFOLIO_READ_PORTFOLIO_TRADE, request.portfolioId()) &&
            logIfAccessDenied(hasFeePortfolioPermission, servletRequest, wydenAuthenticationToken, PORTFOLIO_READ_PORTFOLIO_TRADE, request.feePortfolioId()) &&
            logIfAccessDenied(hasAccountPermission, servletRequest, wydenAuthenticationToken, ACCOUNT_READ_ACCOUNT_TRADE, request.accountId()) &&
            logIfAccessDenied(hasFeeAccountPermission, servletRequest, wydenAuthenticationToken, ACCOUNT_READ_ACCOUNT_TRADE, request.feeAccountId());
    }

    public boolean check(final TransactionModel.WithdrawalRequest request, WydenAuthenticationToken wydenAuthenticationToken, ServletWebRequest servletRequest) {
        boolean hasPortfolioPermission = hasReadAndTradePermissionsForPortfolio(request.portfolioId(), wydenAuthenticationToken);
        boolean hasFeePortfolioPermission = hasReadAndTradePermissionsForPortfolio(request.feePortfolioId(), wydenAuthenticationToken);
        boolean hasAccountPermission = hasReadAndTradePermissionsForAccount(request.accountId(), wydenAuthenticationToken);
        boolean hasFeeAccountPermission = hasReadAndTradePermissionsForAccount(request.feeAccountId(), wydenAuthenticationToken);

        return logIfAccessDenied(hasPortfolioPermission, servletRequest, wydenAuthenticationToken, PORTFOLIO_READ_PORTFOLIO_TRADE, request.portfolioId()) &&
            logIfAccessDenied(hasFeePortfolioPermission, servletRequest, wydenAuthenticationToken, PORTFOLIO_READ_PORTFOLIO_TRADE, request.feePortfolioId()) &&
            logIfAccessDenied(hasAccountPermission, servletRequest, wydenAuthenticationToken, ACCOUNT_READ_ACCOUNT_TRADE, request.accountId()) &&
            logIfAccessDenied(hasFeeAccountPermission, servletRequest, wydenAuthenticationToken, ACCOUNT_READ_ACCOUNT_TRADE, request.feeAccountId());
    }

    public boolean check(final TransactionModel.AccountCashTransferRequest request, WydenAuthenticationToken wydenAuthenticationToken, ServletWebRequest servletRequest) {
        boolean hasSourceAccountPermission = hasReadAndTradePermissionsForAccount(request.sourceAccountId(), wydenAuthenticationToken);
        boolean hasTargetAccountPermission = hasReadAndTradePermissionsForAccount(request.targetAccountId(), wydenAuthenticationToken);
        boolean hasFeePortfolioPermission = hasReadAndTradePermissionsForPortfolio(request.feePortfolioId(), wydenAuthenticationToken);
        boolean hasFeeAccountPermission = hasReadAndTradePermissionsForAccount(request.feeAccountId(), wydenAuthenticationToken);

        return logIfAccessDenied(hasSourceAccountPermission, servletRequest, wydenAuthenticationToken, ACCOUNT_READ_ACCOUNT_TRADE, request.sourceAccountId()) &&
            logIfAccessDenied(hasTargetAccountPermission, servletRequest, wydenAuthenticationToken, ACCOUNT_READ_ACCOUNT_TRADE, request.targetAccountId()) &&
            logIfAccessDenied(hasFeePortfolioPermission, servletRequest, wydenAuthenticationToken, PORTFOLIO_READ_PORTFOLIO_TRADE, request.feePortfolioId()) &&
            logIfAccessDenied(hasFeeAccountPermission, servletRequest, wydenAuthenticationToken, ACCOUNT_READ_ACCOUNT_TRADE, request.feeAccountId());
    }

    public boolean check(final TransactionModel.PortfolioCashTransferRequest request, WydenAuthenticationToken wydenAuthenticationToken, ServletWebRequest servletRequest) {
        boolean hasSourcePortfolioPermission = hasReadAndTradePermissionsForPortfolio(request.sourcePortfolioId(), wydenAuthenticationToken);
        boolean hasTargetPortfolioPermission = hasReadAndTradePermissionsForPortfolio(request.targetPortfolioId(), wydenAuthenticationToken);

        return logIfAccessDenied(hasSourcePortfolioPermission, servletRequest, wydenAuthenticationToken, PORTFOLIO_READ_PORTFOLIO_TRADE, request.sourcePortfolioId()) &&
            logIfAccessDenied(hasTargetPortfolioPermission, servletRequest, wydenAuthenticationToken, PORTFOLIO_READ_PORTFOLIO_TRADE, request.targetPortfolioId());
    }


    public boolean check(final TransactionModel.TransactionSearch search, WydenAuthenticationToken token, ServletWebRequest servletRequest) {
        return logIfAccessDenied(hasReadPermissionsForAccount(search.accountId(), token), servletRequest, token, "account.read", search.accountId());
    }
}