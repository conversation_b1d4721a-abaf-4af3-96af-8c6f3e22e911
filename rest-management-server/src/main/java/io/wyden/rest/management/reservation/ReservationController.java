package io.wyden.rest.management.reservation;

import io.wyden.cloud.utils.rest.pagination.PaginationModel;
import io.wyden.published.booking.ReservationSearch;
import io.wyden.rest.management.booking.BookingEngineService;
import io.wyden.rest.management.domain.ReservationModel;
import io.wyden.rest.management.reservation.validator.ReservationByRefIdPermissionValidator;
import io.wyden.rest.management.security.authentication.WydenAuthenticationToken;
import io.wyden.rest.management.transaction.validator.TransactionRequestValidator;

import jakarta.validation.Valid;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PostAuthorize;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.ServletWebRequest;
import org.springframework.web.server.ResponseStatusException;

import java.util.Collection;

@RestController
@Validated
@RequestMapping(path = "${rest.management.context-path}")
public class ReservationController {

    private final TransactionRequestValidator validator;
    private final BookingEngineService bookingEngineService;
    private final ReservationByRefIdPermissionValidator reservationByRefIdPermissionValidator;

    public ReservationController(TransactionRequestValidator validator,
                                 BookingEngineService bookingEngineService,
                                 ReservationByRefIdPermissionValidator reservationByRefIdPermissionValidator) {
        this.validator = validator;
        this.bookingEngineService = bookingEngineService;
        this.reservationByRefIdPermissionValidator = reservationByRefIdPermissionValidator;
    }

    @GetMapping("/reservations")
    public PaginationModel.CursorConnection<ReservationModel.Reservation> searchReservations(ReservationModel.ReservationSearch search, WydenAuthenticationToken authentication) {
        ReservationSearch reservationSearch = ReservationToProtoMapper.map(search, authentication.getClientId());
        return bookingEngineService.searchReservations(reservationSearch);
    }

    @PostMapping("/reservations")
    @PreAuthorize("@bookReservationValidator.check(#reservation, #authentication, #servletRequest)")
    public ReservationModel.Reservation bookReservation(@Valid @RequestBody ReservationModel.ReservationRequest reservation, WydenAuthenticationToken authentication, ServletWebRequest servletRequest) {
        validator.validate(reservation);
        return bookingEngineService.createReservation(reservation);
    }

    @GetMapping("/reservations/{reservationRef}")
    @PostAuthorize("@reservationByRefIdValidator.validateAccess(returnObject, #authentication, #servletRequest)")
    public ReservationModel.Reservation getReservation(@PathVariable String reservationRef, WydenAuthenticationToken authentication, ServletWebRequest servletRequest) {
        ReservationModel.Reservation reservation = bookingEngineService.findReservationByReference(reservationRef);
        if (reservation != null) {
            return reservation;
        }

        throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Cannot find reservation with ref: " + reservationRef);
    }

    @GetMapping("/reservations/{reservationRef}/balances")
    public Collection<ReservationModel.ReservationBalance> getReservationBalances(@PathVariable String reservationRef, WydenAuthenticationToken authentication, ServletWebRequest servletRequest) {
        ReservationModel.Reservation reservation = bookingEngineService.findReservationByReference(reservationRef);
        if (reservation != null) {
            reservationByRefIdPermissionValidator.validateReservationAccess(reservation, authentication, servletRequest);
            return bookingEngineService.getReservationBalanceByReference(reservationRef);
        }

        throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Cannot find reservation with ref: " + reservationRef);
    }

    @PostMapping("/reservations/{reservationRef}/release")
    public Collection<ReservationModel.ReservationBalance> releaseReservation(@PathVariable String reservationRef, WydenAuthenticationToken authentication, ServletWebRequest servletRequest) {
        ReservationModel.Reservation reservation = bookingEngineService.findReservationByReference(reservationRef);
        if (reservation == null) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Cannot find reservation with ref: " + reservationRef);
        }

        reservationByRefIdPermissionValidator.validateReservationAccess(reservation, authentication, servletRequest);

        // TODO SPL rest-management return reservations after delete
        return bookingEngineService.releaseReservation(reservation);
    }
}
