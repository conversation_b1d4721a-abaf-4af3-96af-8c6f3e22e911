package io.wyden.rest.management.infrastructure.web;

import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import io.wyden.accessgateway.client.apikey.ApiKeyWebClient;
import io.wyden.accessgateway.client.license.LicenseService;
import io.wyden.accessgateway.client.permission.PermissionCache;
import io.wyden.accessgateway.client.permission.PermissionChecker;
import io.wyden.accessgateway.client.web.WebClientFactory;
import io.wyden.accessgateway.domain.license.LicenseMapConfig;
import io.wyden.accessgateway.domain.license.LicenseState;
import io.wyden.accessgateway.domain.permission.PermissionGroupListMapConfig;
import io.wyden.accessgateway.domain.permission.PermissionGroupMapConfig;
import io.wyden.accessgateway.domain.permission.PermissionUserListMapConfig;
import io.wyden.accessgateway.domain.permission.PermissionUserMapConfig;
import io.wyden.rest.management.security.authentication.AuthenticationFilter;
import io.wyden.rest.management.security.authentication.AuthenticationService;
import io.wyden.rest.management.security.authentication.CustomPermissionEvaluator;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.access.PermissionEvaluator;
import org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler;
import org.springframework.security.access.expression.method.MethodSecurityExpressionHandler;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.annotation.web.configurers.HeadersConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.header.writers.ReferrerPolicyHeaderWriter;
import org.springframework.web.client.RestTemplate;

import java.util.Set;
import javax.net.ssl.SSLException;

@Configuration
@EnableWebSecurity
@EnableMethodSecurity
public class WebSecurityConfig {

    private static final String PERMISSION_POLICY = "accelerometer=(), autoplay=(), camera=(), cross-origin-isolated=(), display-capture=(), " +
            "encrypted-media=(), fullscreen=(), geolocation=(), gyroscope=(), keyboard-map=(), magnetometer=(), microphone=(), midi=(), payment=(), " +
            "picture-in-picture=(), publickey-credentials-get=(), screen-wake-lock=(), sync-xhr=(self), usb=(), web-share=(), xr-spatial-tracking=(), " +
            "clipboard-read=(), clipboard-write=(), gamepad=(), hid=(), idle-detection=(), interest-cohort=(), serial=(), unload=()";

    @Bean
    static MethodSecurityExpressionHandler expressionHandler(PermissionEvaluator permissionEvaluator) {
        var expressionHandler = new DefaultMethodSecurityExpressionHandler();
        expressionHandler.setPermissionEvaluator(permissionEvaluator);
        return expressionHandler;
    }

    @Bean
    public ApiKeyWebClient apiKeyWebClient(@Value("${access.gateway.host}") String accessGatewayHost) throws SSLException {
        return new ApiKeyWebClient(WebClientFactory.create(), accessGatewayHost);
    }

    @Bean
    public PermissionEvaluator permissionEvaluator(PermissionChecker permissionChecker) {
        return new CustomPermissionEvaluator(permissionChecker);
    }

    @Bean
    PermissionChecker permissionChecker(HazelcastInstance hazelcastInstance) {
        return new PermissionChecker(hazelcastInstance);
    }

    @Bean
    public IMap<String, Set<String>> userPermissionList(HazelcastInstance hazelcast) {
        return PermissionUserListMapConfig.getMap(hazelcast);
    }

    @Bean
    public IMap<String, Set<String>> groupPermissionList(HazelcastInstance hazelcast) {
        return PermissionGroupListMapConfig.getMap(hazelcast);
    }

    @Bean
    public PermissionUserMapConfig userPermissionMapConfig() {
        return new PermissionUserMapConfig();
    }

    @Bean
    public PermissionGroupMapConfig groupPermissionMapConfig() {
        return new PermissionGroupMapConfig();
    }

    @Bean
    public PermissionCache permissionCache(IMap<String, Set<String>> userPermissionList,
                                           IMap<String, Set<String>> groupPermissionList) {
        return new PermissionCache(userPermissionList, groupPermissionList);
    }

    @Bean
    public IMap<String, LicenseState> license(HazelcastInstance hazelcast) {
        return LicenseMapConfig.getMap(hazelcast);
    }

    @Bean
    LicenseService licenseService(IMap<String, LicenseState> licenseMap, RestTemplate restTemplate, @Value("${access.gateway.host}") String accessGatewayHost) {
        return new LicenseService(licenseMap, restTemplate, accessGatewayHost);
    }

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http, AuthenticationService authenticationService) throws Exception {
        http
            .csrf(AbstractHttpConfigurer::disable)
            .authorizeHttpRequests(authorizationManagerRequestMatcherRegistry ->
                authorizationManagerRequestMatcherRegistry
                    .requestMatchers("/actuator/health/**").permitAll()
                    .requestMatchers("/**").authenticated())
            .headers(httpSecurityHeadersConfigurer ->
                    httpSecurityHeadersConfigurer
                            .referrerPolicy(customizer -> customizer.policy(ReferrerPolicyHeaderWriter.ReferrerPolicy.NO_REFERRER))
                            .xssProtection(HeadersConfigurer.XXssConfig::disable)
                            .frameOptions(HeadersConfigurer.FrameOptionsConfig::disable)
                            .contentSecurityPolicy(contentSecurityPolicyConfig ->
                                    contentSecurityPolicyConfig.policyDirectives("default-src: 'self'"))
                            .permissionsPolicy(customizer -> customizer.policy(PERMISSION_POLICY)))
            .sessionManagement(httpSecuritySessionManagementConfigurer -> httpSecuritySessionManagementConfigurer.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            .addFilterBefore(new AuthenticationFilter(authenticationService), UsernamePasswordAuthenticationFilter.class);
        return http.build();
    }
}
