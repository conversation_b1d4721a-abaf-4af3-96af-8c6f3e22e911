package io.wyden.rest.management.reservation.validator;

import io.wyden.rest.management.domain.ReservationModel;
import io.wyden.rest.management.security.authentication.WydenAuthenticationToken;
import io.wyden.rest.management.security.authentication.WydenAuthenticationPermissionHelper;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.ServletWebRequest;

import static io.wyden.rest.management.security.authentication.WydenAuthenticationPermissionHelper.hasReadAndTradePermissionsForAccount;
import static io.wyden.rest.management.security.authentication.WydenAuthenticationPermissionHelper.hasReadAndTradePermissionsForPortfolio;
import static io.wyden.rest.management.util.AccessDeniedLogger.logIfAccessDenied;

@Component("bookReservationValidator")
public class BookReservationPermissionValidator implements WydenAuthenticationPermissionHelper {

    private static final String PORTFOLIO_READ_PORTFOLIO_TRADE = "portfolio.read & portfolio.trade";
    private static final String ACCOUNT_READ_ACCOUNT_TRADE = "account.read & account.trade";

    public boolean check(final ReservationModel.ClientCashTradeReservationRequest request, WydenAuthenticationToken token, ServletWebRequest servletRequest) {
        boolean hasPortfolioPermission = hasReadAndTradePermissionsForPortfolio(request.portfolioId(), token);
        return logIfAccessDenied(hasPortfolioPermission, servletRequest, token, PORTFOLIO_READ_PORTFOLIO_TRADE, request.portfolioId());
    }

    public boolean check(final ReservationModel.StreetCashTradeReservationRequest request, WydenAuthenticationToken token, ServletWebRequest servletRequest) {
        boolean hasPortfolioPermission = hasReadAndTradePermissionsForPortfolio(request.portfolioId(), token);
        boolean hasAccountPermission = hasReadAndTradePermissionsForAccount(request.accountId(), token);

        return logIfAccessDenied(hasPortfolioPermission, servletRequest, token, PORTFOLIO_READ_PORTFOLIO_TRADE, request.portfolioId()) &&
            logIfAccessDenied(hasAccountPermission, servletRequest, token, ACCOUNT_READ_ACCOUNT_TRADE, request.accountId());

    }

    public boolean check(final ReservationModel.DepositReservationRequest request, WydenAuthenticationToken token, ServletWebRequest servletRequest) {
        boolean hasPortfolioPermission = hasReadAndTradePermissionsForPortfolio(request.portfolioId(), token);
        boolean hasFeePortfolioPermission = hasReadAndTradePermissionsForPortfolio(request.feePortfolioId(), token);
        boolean hasAccountPermission = hasReadAndTradePermissionsForAccount(request.accountId(), token);
        boolean hasFeeAccountPermission = hasReadAndTradePermissionsForAccount(request.feeAccountId(), token);

        return logIfAccessDenied(hasPortfolioPermission, servletRequest, token, PORTFOLIO_READ_PORTFOLIO_TRADE, request.portfolioId()) &&
            logIfAccessDenied(hasFeePortfolioPermission, servletRequest, token, PORTFOLIO_READ_PORTFOLIO_TRADE, request.feePortfolioId()) &&
            logIfAccessDenied(hasAccountPermission, servletRequest, token, ACCOUNT_READ_ACCOUNT_TRADE, request.accountId()) &&
            logIfAccessDenied(hasFeeAccountPermission, servletRequest, token, ACCOUNT_READ_ACCOUNT_TRADE, request.feeAccountId());

    }

    public boolean check(final ReservationModel.WithdrawalReservationRequest request, WydenAuthenticationToken token, ServletWebRequest servletRequest) {
        boolean hasPortfolioPermission = hasReadAndTradePermissionsForPortfolio(request.portfolioId(), token);
        boolean hasFeePortfolioPermission = hasReadAndTradePermissionsForPortfolio(request.feePortfolioId(), token);
        boolean hasAccountPermission = hasReadAndTradePermissionsForAccount(request.accountId(), token);
        boolean hasFeeAccountPermission = hasReadAndTradePermissionsForAccount(request.feeAccountId(), token);

        return logIfAccessDenied(hasPortfolioPermission, servletRequest, token, PORTFOLIO_READ_PORTFOLIO_TRADE, request.portfolioId()) &&
            logIfAccessDenied(hasFeePortfolioPermission, servletRequest, token, PORTFOLIO_READ_PORTFOLIO_TRADE, request.feePortfolioId()) &&
            logIfAccessDenied(hasAccountPermission, servletRequest, token, ACCOUNT_READ_ACCOUNT_TRADE, request.accountId()) &&
            logIfAccessDenied(hasFeeAccountPermission, servletRequest, token, ACCOUNT_READ_ACCOUNT_TRADE, request.feeAccountId());

    }

    public boolean check(final ReservationModel.AccountCashTransferReservationRequest request, WydenAuthenticationToken token, ServletWebRequest servletRequest) {
        boolean hasSourceAccountPermission = hasReadAndTradePermissionsForAccount(request.sourceAccountId(), token);
        boolean hasTargetAccountPermission = hasReadAndTradePermissionsForAccount(request.targetAccountId(), token);
        boolean hasFeePortfolioPermission = hasReadAndTradePermissionsForPortfolio(request.feePortfolioId(), token);
        boolean hasFeeAccountPermission = hasReadAndTradePermissionsForAccount(request.feeAccountId(), token);

        return logIfAccessDenied(hasSourceAccountPermission, servletRequest, token, ACCOUNT_READ_ACCOUNT_TRADE, request.sourceAccountId()) &&
            logIfAccessDenied(hasTargetAccountPermission, servletRequest, token, ACCOUNT_READ_ACCOUNT_TRADE, request.targetAccountId()) &&
            logIfAccessDenied(hasFeePortfolioPermission, servletRequest, token, PORTFOLIO_READ_PORTFOLIO_TRADE, request.feePortfolioId()) &&
            logIfAccessDenied(hasFeeAccountPermission, servletRequest, token, ACCOUNT_READ_ACCOUNT_TRADE, request.feeAccountId());

    }

    public boolean check(final ReservationModel.PortfolioCashTransferReservationRequest request, WydenAuthenticationToken token, ServletWebRequest servletRequest) {
        boolean hasSourcePortfolioPermission = hasReadAndTradePermissionsForPortfolio(request.sourcePortfolioId(), token);
        boolean hasTargetPortfolioPermission = hasReadAndTradePermissionsForPortfolio(request.targetPortfolioId(), token);
        return logIfAccessDenied(hasSourcePortfolioPermission, servletRequest, token, PORTFOLIO_READ_PORTFOLIO_TRADE, request.sourcePortfolioId()) &&
            logIfAccessDenied(hasTargetPortfolioPermission, servletRequest, token, PORTFOLIO_READ_PORTFOLIO_TRADE, request.targetPortfolioId());
    }

}