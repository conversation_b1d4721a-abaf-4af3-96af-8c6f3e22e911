package io.wyden.rest.management.security.authentication;

import com.google.common.hash.HashCode;
import com.google.common.hash.Hashing;
import com.google.common.io.CharStreams;
import com.hazelcast.map.IMap;
import io.wyden.accessgateway.client.apikey.ApiKeyWebClient;
import io.wyden.accessgateway.client.apikey.dto.AuthResponseDto;
import io.wyden.accessgateway.client.permission.PermissionChecker;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.rest.management.account.VenueAccountRepository;
import io.wyden.rest.management.portfolio.PortfolioRepository;
import io.wyden.rest.management.security.apikey.ApiKeyRepository;
import io.wyden.rest.management.security.apikey.VaultApiKey;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriUtils;
import reactor.core.scheduler.Schedulers;

import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.Instant;
import java.util.Objects;
import java.util.Optional;
import javax.net.ssl.SSLException;

import static java.util.Objects.isNull;
import static org.apache.commons.lang3.StringUtils.isBlank;

@Service
public class AuthenticationService {

    private final Logger LOGGER = LoggerFactory.getLogger(AuthenticationService.class);

    private final ApiKeyRepository apiKeyRepository;
    private final ApiKeyWebClient apiKeyRestClient;
    private final IMap<String, Long> noncePerApiKeyMap;
    private final Duration validationTolerance;
    private final PermissionChecker permissionChecker;
    private final PortfolioRepository portfolioRepository;
    private final VenueAccountRepository venueAccountRepository;

    public AuthenticationService(ApiKeyRepository apiKeyRepository,
                                 PermissionChecker permissionChecker,
                                 ApiKeyWebClient apiKeyRestClient,
                                 IMap<String, Long> noncePerApiKeyMap,
                                 @Value("${nonce.validation.window}")
                                 Duration validationTolerance,
                                 PortfolioRepository portfolioRepository,
                                 VenueAccountRepository venueAccountRepository) throws SSLException {
        this.apiKeyRepository = apiKeyRepository;
        this.permissionChecker = permissionChecker;
        this.apiKeyRestClient = apiKeyRestClient;
        this.noncePerApiKeyMap = noncePerApiKeyMap;
        this.validationTolerance = validationTolerance;
        this.portfolioRepository = portfolioRepository;
        this.venueAccountRepository = venueAccountRepository;
    }

    /**
     * Calculate signature. If path contains spaces, for example /api/v1/portfolios/Client 1/positions, it has to be encoded, for example
     * by using {@link UriUtils#encodePath(String, Charset)} method.
     */
    public static String calculateSignature(String apiNonce, String apiSecret, String method, String path, String body) {
        // path has to be encoded, because path can contain empty spaces, etc.
        String input = apiNonce + method + path + body;

        byte[] secretBytes = apiSecret.getBytes(StandardCharsets.UTF_8);

        HashCode hash = Hashing.hmacSha256(secretBytes)
            .hashString(input, StandardCharsets.UTF_8);

        return hash.toString();
    }

    public Authentication getAuthentication(HttpServletRequest request) {
        String apiKeyId = Optional.ofNullable(request.getHeader(Headers.API_KEY))
            .orElseThrow(() -> new AuthHeadersException("Missing X-API-KEY header"));

        String apiNonce = Optional.ofNullable(request.getHeader(Headers.API_NONCE))
            .orElseThrow(() -> new AuthHeadersException("Missing X-API-NONCE header"));

        String apiSignature = Optional.ofNullable(request.getHeader(Headers.API_SIGNATURE))
            .orElseThrow(() -> new AuthHeadersException("Missing X-API-SIGNATURE header"));

        LOGGER.debug("Using the following request auth: key=%s, nonce=%s, signature=%s"
            .formatted(apiKeyId, apiNonce, apiSignature));

        if (!NumberUtils.isParsable(apiNonce)) {
            throw new AuthHeadersException("Invalid api-nonce");
        }

        if (!validateNonceValue(apiKeyId, apiNonce)) {
            throw new AuthHeadersException("Invalid api-nonce");
        }

        VaultApiKey vaultData = apiKeyRepository.getApiKeyById(apiKeyId);

        if (isNull(vaultData)) {
            String message = String.format("Invalid api-key: %s, path: %s , query: %s", apiKeyId, request.getRequestURI(), request.getQueryString());
            LOGGER.warn(message);
            throw new WrongCredentialsException(message);
        }

        String path = getPath(request);
        String body = getRequestBody(request);
        String method = request.getMethod();
        String challengeSecret = vaultData.secret();
        String challengeSignature = calculateSignature(apiNonce, challengeSecret, method, path, body);

        LOGGER.debug("Using the following calculated auth: path=%s, method=%s, signature=%s, body=%s"
            .formatted(path, method, challengeSignature, body));

        if (!Objects.equals(challengeSignature, apiSignature)) {
            throw new AuthHeadersException("Invalid api-signature");
        }

        AuthResponseDto authResponseDto = authenticate(apiKeyId, challengeSecret);

        LOGGER.trace("Using the following auth response: {}", authResponseDto);

        noncePerApiKeyMap.put(apiKeyId, Long.parseLong(apiNonce));

        return new WydenAuthenticationToken(permissionChecker, apiKeyId, challengeSecret, authResponseDto.username(), authResponseDto.roles(), authResponseDto.groups(),
            portfolioRepository, venueAccountRepository);
    }

    private AuthResponseDto authenticate(String apiKey, String apiSecret) {
        try {
            return apiKeyRestClient.authenticate(apiKey, apiSecret)
                .subscribeOn(Schedulers.boundedElastic())
                .block(Duration.ofSeconds(10));
        } catch (Exception e) {
            throw new RuntimeException("Unable to authenticate api keys");
        }
    }

    private boolean validateNonceValue(String apiKeyId, String nonce) {
        long previousEpoch = noncePerApiKeyMap.getOrDefault(apiKeyId, 0L);

        if (previousEpoch == 0) {
            Instant nonceExpiration = DateUtils.epochMillisToInstant(nonce)
                .plus(validationTolerance);

            return nonceExpiration.isAfter(Instant.now());
        }

        long parsedNonce = Long.parseLong(nonce);
        return parsedNonce > previousEpoch;
    }

    private static String getRequestBody(HttpServletRequest request) {
        try {
            return CharStreams.toString(request.getReader());
        } catch (IOException e) {
            throw new AuthHeadersException("Unable to parse request body");
        }
    }

    private static String getPath(HttpServletRequest request) {
        String uri = request.getRequestURI();
        String queryString = request.getQueryString();

        if (isBlank(queryString)) {
            return uri;
        }

        return uri + "?" + queryString;
    }
}
