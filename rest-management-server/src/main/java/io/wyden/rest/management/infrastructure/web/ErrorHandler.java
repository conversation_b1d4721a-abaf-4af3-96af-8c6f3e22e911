package io.wyden.rest.management.infrastructure.web;

import io.wyden.cloud.utils.rest.errorhandling.Error;
import io.wyden.rest.management.portfolio.QueryLimitExceededException;
import io.wyden.rest.management.security.authentication.WydenAuthenticationToken;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.ServletException;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageConversionException;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.context.request.ServletWebRequest;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.web.servlet.NoHandlerFoundException;

import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.time.Instant;

import static io.wyden.rest.management.util.AccessDeniedLogger.ACCESS_DENIED_MESSAGE_REQUEST_ATTRIBUTE_KEY;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.slf4j.LoggerFactory.getLogger;

@ControllerAdvice
public class ErrorHandler {

    private static final Logger LOGGER = getLogger(ErrorHandler.class);

    private final ObjectMapper objectMapper;

    public ErrorHandler(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    @ExceptionHandler({MissingServletRequestParameterException.class})
    public ResponseEntity<Error> handleMissingParameterException(MissingServletRequestParameterException ex, ServletWebRequest request) {
        HttpStatus status = HttpStatus.BAD_REQUEST;
        String message = ex.getBody().getDetail();

        LOGGER.error(message, ex);

        Error error = getError(status, message, ex, request);
        return new ResponseEntity<>(error, status);
    }

    @ExceptionHandler({HttpMessageConversionException.class})
    public ResponseEntity<Error> handleMissingBodyException(HttpMessageConversionException ex, ServletWebRequest request) {
        HttpStatus status = HttpStatus.BAD_REQUEST;
        String message = ex.getMessage();

        LOGGER.error(message, ex);

        Error error = getError(status, message, ex, request);
        return new ResponseEntity<>(error, status);
    }

    @ExceptionHandler({ResponseStatusException.class})
    public ResponseEntity<Error> handleMissingBodyException(ResponseStatusException ex, ServletWebRequest request) {
        HttpStatus status = HttpStatus.resolve(ex.getBody().getStatus());
        String message = ex.getReason();

        LOGGER.error(message, ex);

        Error error = getError(status, message, ex, request);
        return new ResponseEntity<>(error, status);
    }

    @ExceptionHandler({HttpServerErrorException.class})
    public ResponseEntity<Error> handleHttpServerErrorException(HttpServerErrorException ex, ServletWebRequest request) {
        HttpStatus status = HttpStatus.INTERNAL_SERVER_ERROR;
        String message = getMessage(ex);

        Error cause = getCause(ex);
        if (cause != null) {
            message = cause.message();
            status = HttpStatus.resolve(cause.status());
        }

        LOGGER.error(message, ex);

        Error error = getError(status, message, ex, request);
        return new ResponseEntity<>(error, status);
    }

    @ExceptionHandler({HttpClientErrorException.class})
    public ResponseEntity<Error> handleHttpClientErrorException(HttpClientErrorException ex, ServletWebRequest request) {
        HttpStatus status = HttpStatus.INTERNAL_SERVER_ERROR;
        String message = getMessage(ex);

        Error cause = getCause(ex);
        if (cause != null) {
            message = cause.message();
            status = HttpStatus.resolve(cause.status());
        }

        LOGGER.error(message, ex);

        Error error = getError(status, message, ex, request);
        return new ResponseEntity<>(error, status);
    }

    @ExceptionHandler({Throwable.class})
    public ResponseEntity<Error> handleRuntimeException(Exception ex, ServletWebRequest request) {
        HttpStatus status = HttpStatus.INTERNAL_SERVER_ERROR;
        String message = getMessage(ex);

        LOGGER.error(message, ex);

        Error error = getError(status, message, ex, request);
        return new ResponseEntity<>(error, status);
    }

    @ExceptionHandler(NoHandlerFoundException.class)
    public ResponseEntity<Error> handleNoHandlerFound(NoHandlerFoundException ex, ServletWebRequest request) {
        HttpStatus status = HttpStatus.NOT_FOUND;
        String message = getMessage(ex);

        LOGGER.error(message, ex);

        Error error = getError(status, message, ex, request);
        return new ResponseEntity<>(error, status);
    }

    @ExceptionHandler({AccessDeniedException.class})
    public ResponseEntity<Error> handleAccessDenied(AccessDeniedException ex, ServletWebRequest request, WydenAuthenticationToken token) {
        HttpStatus status = HttpStatus.UNAUTHORIZED;
        String accessDeniedMessage = (String) request.getRequest().getAttribute(ACCESS_DENIED_MESSAGE_REQUEST_ATTRIBUTE_KEY);

        if(StringUtils.isNoneEmpty(accessDeniedMessage)) {
            LOGGER.warn(accessDeniedMessage);
            Error error = getErrorWithoutStackTrace(status, accessDeniedMessage, ex, request);
            return new ResponseEntity<>(error, status);
        }

        String message = getMessage(ex);
        String apiKey = request.getRequest().getHeader("x-api-key");
        LOGGER.warn("Access denied for user: {}, api-key: {}, {} on path: {} , query: {}",
            token.getClientId(),
            apiKey,
            request.getRequest().getMethod(),
            request.getRequest().getRequestURI(),
            request.getRequest().getQueryString());

        Error error = getErrorWithoutStackTrace(status, message, ex, request);
        return new ResponseEntity<>(error, status);
    }

    @ExceptionHandler({QueryLimitExceededException.class})
    public ResponseEntity<Error> handleQueryLimitException(QueryLimitExceededException ex, ServletWebRequest request) {
        HttpStatus status = HttpStatus.BAD_REQUEST;
        String message = getMessage(ex);

        LOGGER.warn(message);

        Error error = getErrorWithoutStackTrace(status, message, ex, request);
        return new ResponseEntity<>(error, status);
    }

    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<Error> handleIllegalArgument(IllegalArgumentException ex, ServletWebRequest request) {
        HttpStatus status = HttpStatus.BAD_REQUEST;
        String message = getMessage(ex);

        LOGGER.warn(message);

        Error error = getError(status, message, ex, request);
        return new ResponseEntity<>(error, status);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<Error> handleNotValidArgument(MethodArgumentNotValidException ex, ServletWebRequest request) {
        HttpStatus status = HttpStatus.BAD_REQUEST;
        String message = ex.getBindingResult().getFieldErrors().stream()
            .map(DefaultMessageSourceResolvable::getDefaultMessage)
            .findFirst()
            .orElseGet(() -> getMessage(ex));

        LOGGER.warn(message);

        Error error = getError(status, message, ex, request);
        return new ResponseEntity<>(error, status);
    }

    @NotNull
    private Error getError(HttpStatus status, String msg, Exception ex, ServletWebRequest request) {
        Instant timestamp = Instant.now();
        String reason = status.getReasonPhrase();
        String path = request.getRequest().getRequestURI();
        String exception = getException(ex);
        String stackTrace = getStackTrace(ex);
        // as a result of pentest we are hiding stacktrace from users, see AC-4691
        return new Error(timestamp, status.value(), reason, msg, path, null, null);
    }

    @NotNull
    private Error getErrorWithoutStackTrace(HttpStatus status, String msg, Exception ex, ServletWebRequest request) {
        Instant timestamp = Instant.now();
        String reason = status.getReasonPhrase();
        String path = request.getRequest().getRequestURI();
        String exception = getException(ex);
        return new Error(timestamp, status.value(), reason, msg, path, exception, StringUtils.EMPTY);
    }

    private Error getCause(HttpStatusCodeException ex) {
        try {
            return objectMapper.reader().readValue(ex.getResponseBodyAsString(), Error.class);
        } catch (IOException e) {
            return null;
        }
    }

    private static String getStackTrace(Throwable ex) {
        StringWriter stackTrace = new StringWriter();
        ex.printStackTrace(new PrintWriter(stackTrace));
        stackTrace.flush();
        return stackTrace.toString();
    }

    private static String getException(Throwable ex) {
        Throwable error = ex;
        while (true) {
            if (!(error instanceof ServletException) || error.getCause() == null) {
                return error.getClass().getName();
            }

            error = error.getCause();
        }
    }

    private static String getMessage(Throwable ex) {
        String message = ex.getMessage();
        if (isBlank(message)) {
            return "No message available";
        }

        return message;
    }
}
