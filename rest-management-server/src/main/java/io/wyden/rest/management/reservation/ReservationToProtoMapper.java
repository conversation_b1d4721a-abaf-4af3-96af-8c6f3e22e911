package io.wyden.rest.management.reservation;

import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.booking.AccountCashTransferReservationRequest;
import io.wyden.published.booking.ClientCashTradeReservationRequest;
import io.wyden.published.booking.DepositReservationRequest;
import io.wyden.published.booking.Fee;
import io.wyden.published.booking.FeeType;
import io.wyden.published.booking.PortfolioCashTransferReservationRequest;
import io.wyden.published.booking.ReservationRequest;
import io.wyden.published.booking.ReservationSearch;
import io.wyden.published.booking.StreetCashTradeReservationRequest;
import io.wyden.published.booking.TransactionType;
import io.wyden.published.booking.WithdrawalReservationRequest;
import io.wyden.published.common.Metadata;
import io.wyden.rest.management.common.Identifiers;
import io.wyden.rest.management.common.StringUtils;
import io.wyden.rest.management.domain.ReservationModel;
import io.wyden.rest.management.domain.TransactionModel;
import io.wyden.rest.management.transaction.TransactionToProtoMapper;
import org.slf4j.Logger;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.UUID;

import static io.wyden.rest.management.common.CollectionUtils.DEFAULT_PAGE_SIZE;
import static io.wyden.rest.management.common.StringUtils.toProtoString;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.slf4j.LoggerFactory.getLogger;

public final class ReservationToProtoMapper {

    private static final Logger LOGGER = getLogger(ReservationToProtoMapper.class);

    private ReservationToProtoMapper() {
    }

    public static ReservationSearch map(ReservationModel.ReservationSearch reservationSearch, String clientId) {
        ReservationSearch.Builder search = ReservationSearch.newBuilder()
            .setClientId(clientId)
            .setSortingOrder(TransactionToProtoMapper.map(reservationSearch.sortingOrder()));

        if (isNotBlank(reservationSearch.currency())) {
            search.addCurrency(reservationSearch.currency());
        }

        if (isNotBlank(reservationSearch.accountId())) {
            search.addAccountId(reservationSearch.accountId());
        }

        if (isNotBlank(reservationSearch.portfolioId())) {
            search.addPortfolioId(reservationSearch.portfolioId());
        }

        if (reservationSearch.transactionType() != null) {
            search.addTransactionType(TransactionToProtoMapper.map(reservationSearch.transactionType()));
        }

        if (isNotBlank(reservationSearch.reservationRef())) {
            search.setReservationRef(reservationSearch.reservationRef());
        }

        if (reservationSearch.from() != null) {
            search.setFrom(String.valueOf(reservationSearch.from()));
        }

        if (reservationSearch.to() != null) {
            search.setTo(String.valueOf(reservationSearch.to()));
        }

        if (reservationSearch.first() != null) {
            search.setFirst(reservationSearch.first());
        } else {
            search.setFirst(DEFAULT_PAGE_SIZE);
        }

        if (isNotBlank(reservationSearch.after())) {
            search.setAfter(reservationSearch.after());
        }

        return search.build();
    }

    public static ReservationRequest map(ReservationModel.ReservationRequest reservationRequest) {
        if (reservationRequest == null) {
            return null;
        }

        Metadata metadata = Metadata.newBuilder()
            .setCreatedAt(DateUtils.toIsoUtcTime())
            .setRequestId(Identifiers.randomIdentifier())
            .setRequesterId("rest-management")
            .build();

        ReservationRequest.Builder builder = ReservationRequest.newBuilder();
        builder.setUuid(UUID.randomUUID().toString());

        if (reservationRequest instanceof ReservationModel.DepositReservationRequest deposit) {
            DepositReservationRequest.Builder request = DepositReservationRequest.newBuilder()
                .setMetadata(metadata)
                .setTransactionType(TransactionType.TRANSACTION_TYPE_DEPOSIT);

            String reservationRef = deposit.reservationRef();
            if (isNotBlank(reservationRef)) {
                request.setReservationRef(reservationRef);
            }

            Long dateTime = deposit.dateTime();
            if (dateTime != null) {
                request.setDateTime(DateUtils.epochMillisToIsoUtcTime(String.valueOf(dateTime)));
            }

            String currency = deposit.currency();
            if (isNotBlank(currency)) {
                request.setCurrency(currency);
            }

            BigDecimal quantity = deposit.quantity();
            if (quantity != null) {
                request.setQuantity(toProtoString(quantity));
            }

            String portfolioId = deposit.portfolioId();
            if (isNotBlank(portfolioId)) {
                request.setPortfolioId(portfolioId);
            }

            String accountId = deposit.accountId();
            if (isNotBlank(accountId)) {
                request.setAccountId(accountId);
            }

            String feePortfolioId = deposit.feePortfolioId();
            if (isNotBlank(feePortfolioId)) {
                request.setFeePortfolioId(feePortfolioId);
            }

            String feeAccountId = deposit.feeAccountId();
            if (isNotBlank(feeAccountId)) {
                request.setFeeAccountId(feeAccountId);
            }

            List<Fee> fees = map(deposit.fees());
            if (fees != null) {
                request.addAllReservationFee(fees);
            }

            return builder
                .setDepositReservationRequest(request)
                .build();
        }

        if (reservationRequest instanceof ReservationModel.WithdrawalReservationRequest withdrawal) {
            WithdrawalReservationRequest.Builder request = WithdrawalReservationRequest.newBuilder()
                .setMetadata(metadata)
                .setTransactionType(TransactionType.TRANSACTION_TYPE_WITHDRAWAL);

            String reservationRef = withdrawal.reservationRef();
            if (isNotBlank(reservationRef)) {
                request.setReservationRef(reservationRef);
            }

            Long dateTime = withdrawal.dateTime();
            if (dateTime != null) {
                request.setDateTime(DateUtils.epochMillisToIsoUtcTime(String.valueOf(dateTime)));
            }

            String currency = withdrawal.currency();
            if (isNotBlank(currency)) {
                request.setCurrency(currency);
            }

            BigDecimal quantity = withdrawal.quantity();
            if (quantity != null) {
                request.setQuantity(toProtoString(quantity));
            }

            String portfolioId = withdrawal.portfolioId();
            if (isNotBlank(portfolioId)) {
                request.setPortfolioId(portfolioId);
            }

            String accountId = withdrawal.accountId();
            if (isNotBlank(accountId)) {
                request.setAccountId(accountId);
            }

            String feePortfolioId = withdrawal.feePortfolioId();
            if (isNotBlank(feePortfolioId)) {
                request.setFeePortfolioId(feePortfolioId);
            }

            String feeAccountId = withdrawal.feeAccountId();
            if (isNotBlank(feeAccountId)) {
                request.setFeeAccountId(feeAccountId);
            }

            List<Fee> fees = map(withdrawal.fees());
            if (fees != null) {
                request.addAllReservationFee(fees);
            }

            return builder
                .setWithdrawalReservationRequest(request)
                .build();
        }

        if (reservationRequest instanceof ReservationModel.ClientCashTradeReservationRequest trade) {
            ClientCashTradeReservationRequest.Builder request = ClientCashTradeReservationRequest.newBuilder()
                .setMetadata(metadata)
                .setTransactionType(TransactionType.TRANSACTION_TYPE_CLIENT_CASH_TRADE);

            String reservationRef = trade.reservationRef();
            if (isNotBlank(reservationRef)) {
                request.setReservationRef(reservationRef);
            }

            Long dateTime = trade.dateTime();
            if (dateTime != null) {
                request.setDateTime(DateUtils.epochMillisToIsoUtcTime(String.valueOf(dateTime)));
            }

            String currency = trade.currency();
            if (isNotBlank(currency)) {
                request.setCurrency(currency);
            }

            String baseCurrency = trade.baseCurrency();
            if (isNotBlank(baseCurrency)) {
                request.setBaseCurrency(baseCurrency);
            }

            BigDecimal quantity = trade.quantity();
            if (quantity != null) {
                request.setQuantity(toProtoString(quantity));
            }

            BigDecimal price = trade.price();
            if (price != null) {
                request.setPrice(toProtoString(price));
            }

            BigDecimal stopPrice = trade.stopPrice();
            if (stopPrice != null) {
                request.setStopPrice(toProtoString(stopPrice));
            }

            String portfolioId = trade.portfolioId();
            if (isNotBlank(portfolioId)) {
                request.setPortfolioId(portfolioId);
            }

            String counterPortfolioId = trade.counterPortfolioId();
            if (isNotBlank(counterPortfolioId)) {
                request.setCounterPortfolioId(counterPortfolioId);
            }

            List<Fee> fees = map(trade.fees());
            if (fees != null) {
                request.addAllReservationFee(fees);
            }

            return builder
                .setClientCashTradeReservationRequest(request)
                .build();
        }

        if (reservationRequest instanceof ReservationModel.StreetCashTradeReservationRequest trade) {
            StreetCashTradeReservationRequest.Builder request = StreetCashTradeReservationRequest.newBuilder()
                .setMetadata(metadata)
                .setTransactionType(TransactionType.TRANSACTION_TYPE_STREET_CASH_TRADE);

            String reservationRef = trade.reservationRef();
            if (isNotBlank(reservationRef)) {
                request.setReservationRef(reservationRef);
            }

            Long dateTime = trade.dateTime();
            if (dateTime != null) {
                request.setDateTime(DateUtils.epochMillisToIsoUtcTime(String.valueOf(dateTime)));
            }

            String currency = trade.currency();
            if (isNotBlank(currency)) {
                request.setCurrency(currency);
            }

            String baseCurrency = trade.baseCurrency();
            if (isNotBlank(baseCurrency)) {
                request.setBaseCurrency(baseCurrency);
            }

            BigDecimal quantity = trade.quantity();
            if (quantity != null) {
                request.setQuantity(toProtoString(quantity));
            }

            BigDecimal price = trade.price();
            if (price != null) {
                request.setPrice(toProtoString(price));
            }

            BigDecimal stopPrice = trade.stopPrice();
            if (stopPrice != null) {
                request.setStopPrice(toProtoString(stopPrice));
            }

            String portfolioId = trade.portfolioId();
            if (isNotBlank(portfolioId)) {
                request.setPortfolioId(portfolioId);
            }

            String accountId = trade.accountId();
            if (isNotBlank(accountId)) {
                request.setAccountId(accountId);
            }

            List<Fee> fees = map(trade.fees());
            if (fees != null) {
                request.addAllReservationFee(fees);
            }

            return builder
                .setStreetCashTradeReservationRequest(request)
                .build();
        }

        if (reservationRequest instanceof ReservationModel.AccountCashTransferReservationRequest transfer) {
            AccountCashTransferReservationRequest.Builder request = AccountCashTransferReservationRequest.newBuilder()
                .setMetadata(metadata)
                .setTransactionType(TransactionType.TRANSACTION_TYPE_ACCOUNT_CASH_TRANSFER);

            String reservationRef = transfer.reservationRef();
            if (isNotBlank(reservationRef)) {
                request.setReservationRef(reservationRef);
            }

            Long dateTime = transfer.dateTime();
            if (dateTime != null) {
                request.setDateTime(DateUtils.epochMillisToIsoUtcTime(String.valueOf(dateTime)));
            }

            String currency = transfer.currency();
            if (isNotBlank(currency)) {
                request.setCurrency(currency);
            }

            BigDecimal quantity = transfer.quantity();
            if (quantity != null) {
                request.setQuantity(toProtoString(quantity));
            }

            String sourceAccountId = transfer.sourceAccountId();
            if (isNotBlank(sourceAccountId)) {
                request.setFromAccountId(sourceAccountId);
            }

            String targetAccountId = transfer.targetAccountId();
            if (isNotBlank(targetAccountId)) {
                request.setToAccountId(targetAccountId);
            }

            String feeAccountId = transfer.feeAccountId();
            if (isNotBlank(feeAccountId)) {
                request.setFeeAccountId(feeAccountId);
            }

            String feePortfolioId = transfer.feePortfolioId();
            if (isNotBlank(feePortfolioId)) {
                request.setFeePortfolioId(feePortfolioId);
            }

            List<Fee> fees = map(transfer.fees());
            if (fees != null) {
                request.addAllReservationFee(fees);
            }

            return builder
                .setAccountCashTransferReservationRequest(request)
                .build();
        }

        if (reservationRequest instanceof ReservationModel.PortfolioCashTransferReservationRequest transfer) {
            PortfolioCashTransferReservationRequest.Builder request = PortfolioCashTransferReservationRequest.newBuilder()
                .setMetadata(metadata)
                .setTransactionType(TransactionType.TRANSACTION_TYPE_PORTFOLIO_CASH_TRANSFER);

            String reservationRef = transfer.reservationRef();
            if (isNotBlank(reservationRef)) {
                request.setReservationRef(reservationRef);
            }

            Long dateTime = transfer.dateTime();
            if (dateTime != null) {
                request.setDateTime(DateUtils.epochMillisToIsoUtcTime(String.valueOf(dateTime)));
            }

            String currency = transfer.currency();
            if (isNotBlank(currency)) {
                request.setCurrency(currency);
            }

            BigDecimal quantity = transfer.quantity();
            if (quantity != null) {
                request.setQuantity(toProtoString(quantity));
            }

            String sourcePortfolioId = transfer.sourcePortfolioId();
            if (isNotBlank(sourcePortfolioId)) {
                request.setFromPortfolioId(sourcePortfolioId);
            }

            String targetPortfolioId = transfer.targetPortfolioId();
            if (isNotBlank(targetPortfolioId)) {
                request.setToPortfolioId(targetPortfolioId);
            }

            String feePortfolioId = transfer.feePortfolioId();
            if (isNotBlank(feePortfolioId)) {
                request.setFeePortfolioId(feePortfolioId);
            }

            List<Fee> fees = map(transfer.fees());
            if (fees != null) {
                request.addAllReservationFee(fees);
            }

            return builder
                .setPortfolioCashTransferReservationRequest(request)
                .build();
        }

        LOGGER.warn("Cannot find mapping (model -> proto) for reservation: {}", reservationRequest);
        return null;
    }

    private static List<Fee> map(Collection<ReservationModel.ReservationFee> fees) {
        if (fees == null) {
            return null;
        }

        return fees.stream()
            .map(fee -> Fee.newBuilder()
                .setAmount(StringUtils.toProtoString(fee.amount()))
                .setCurrency(fee.currency())
                .setFeeType(map(fee.feeType()))
                .build())
            .toList();
    }

    private static FeeType map(TransactionModel.FeeType feeType) {
        if (feeType == null) {
            return FeeType.FEE_TYPE_UNSPECIFIED;
        }

        return switch (feeType) {
            case FIXED_FEE -> FeeType.FIXED_FEE;
            case TRANSACTION_FEE -> FeeType.TRANSACTION_FEE;
            case EXCHANGE_FEE -> FeeType.EXCHANGE_FEE;
            default -> FeeType.FEE_TYPE_UNSPECIFIED;
        };
    }
}
