package io.wyden.rest.management.portfolio;

import io.micrometer.common.util.StringUtils;
import io.wyden.accessgateway.client.permission.PermissionCache;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.rest.management.domain.PortfolioModel;
import io.wyden.rest.management.security.authentication.WydenAuthenticationToken;
import io.wyden.rest.management.security.permission.AccessGatewayFacade;
import io.wyden.rest.management.security.permission.User;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.ServletWebRequest;

import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static io.wyden.accessgateway.client.permission.Permission.PORTFOLIO_CREATE;
import static io.wyden.accessgateway.client.permission.Permission.PORTFOLIO_MANAGE;
import static io.wyden.accessgateway.client.permission.Permission.PORTFOLIO_NOSTRO_CREATE;
import static io.wyden.accessgateway.client.permission.Permission.PORTFOLIO_NOSTRO_MANAGE;
import static io.wyden.accessgateway.client.permission.Permission.PORTFOLIO_NOSTRO_READ;
import static io.wyden.accessgateway.client.permission.Permission.PORTFOLIO_READ;
import static io.wyden.accessgateway.client.permission.Permission.PORTFOLIO_VOSTRO_CREATE;
import static io.wyden.accessgateway.client.permission.Permission.PORTFOLIO_VOSTRO_MANAGE;
import static io.wyden.accessgateway.client.permission.Permission.PORTFOLIO_VOSTRO_READ;
import static io.wyden.accessgateway.client.permission.Permission.Resources.PORTFOLIO;
import static io.wyden.accessgateway.client.permission.Permission.Scopes.READ;
import static io.wyden.rest.management.security.permission.User.user;
import static io.wyden.rest.management.util.AccessDeniedLogger.logIfAccessDenied;

@Component("portfolioPermissionValidator")
public class PortfolioPermissionValidator {

    private final PortfolioRepository portfolioRepository;
    private final AccessGatewayFacade accessGatewayClient;
    private final PermissionCache permissionCache;
    private final int portfolioQueryLimit;

    public PortfolioPermissionValidator(PortfolioRepository portfolioRepository,
                                        AccessGatewayFacade accessGatewayClient,
                                        PermissionCache permissionCache,
                                        @Value("${rest.management.query-limits.portfolio:100}") int portfolioQueryLimit) {
        this.portfolioRepository = portfolioRepository;
        this.accessGatewayClient = accessGatewayClient;
        this.permissionCache = permissionCache;
        this.portfolioQueryLimit = portfolioQueryLimit;
    }

    public PortfolioModel.PortfolioSearch authorizeRequest(PortfolioModel.PortfolioSearch request, WydenAuthenticationToken token) {
        User user = user(token);

        if (accessGatewayClient.hasPortfolioPermission(user, READ)) {
            return request;
        }

        PortfolioModel.PortfolioSearch authorizedRequest = applyPortfolioType(request, user);

        if (authorizedRequest.portfolioIds().isEmpty() && StringUtils.isEmpty(authorizedRequest.name())) {
            if (hasAnyStaticReadPortfolioPermission(user)) {
                return authorizedRequest;
            }

            Set<String> authorizedPortfolioIds = permissionCache.getPermittedResourceIds(token.getGroups(), token.getClientId(), PORTFOLIO, READ, portfolioQueryLimit);
            return authorizedPortfolioIds.isEmpty() ? null : authorizedRequest.withPortfolioIds(authorizedPortfolioIds);
        }

        Set<String> authorizedIds = Stream.concat(
                request.portfolioIds().stream(),
                portfolioRepository.findByName(request.name()).stream().map(Portfolio::getId)
            )
            .filter(i -> accessGatewayClient.hasPermission(user(token), PORTFOLIO_READ, i))
            .collect(Collectors.toSet());

        return authorizedIds.isEmpty() ? null : authorizedRequest.withPortfolioIds(authorizedIds);
    }

    private boolean hasAnyStaticReadPortfolioPermission(User user) {
        return accessGatewayClient.hasPermission(user, PORTFOLIO_READ)
            || accessGatewayClient.hasPermission(user, PORTFOLIO_VOSTRO_READ)
            || accessGatewayClient.hasPermission(user, PORTFOLIO_NOSTRO_READ);
    }

    private PortfolioModel.PortfolioSearch applyPortfolioType(PortfolioModel.PortfolioSearch request, User user) {
        if (accessGatewayClient.hasPermission(user, PORTFOLIO_READ)) {
            return request;
        }
        
        if (accessGatewayClient.hasPermission(user, PORTFOLIO_NOSTRO_READ)) {
            return request.withPortfolioType(PortfolioModel.PortfolioType.NOSTRO);
        }

        if (accessGatewayClient.hasPermission(user, PORTFOLIO_VOSTRO_READ)) {
            return request.withPortfolioType(PortfolioModel.PortfolioType.VOSTRO);
        }

        return request;
    }

    public boolean checkPortfolioCreate(PortfolioModel.PortfolioRequest request, WydenAuthenticationToken token, ServletWebRequest servletRequest) {
        if (accessGatewayClient.hasPermission(user(token), PORTFOLIO_CREATE)) {
            return true;
        }
        boolean result =  switch(request.portfolioType()){
            case VOSTRO -> accessGatewayClient.hasPermission(user(token), PORTFOLIO_VOSTRO_CREATE);
            case NOSTRO -> accessGatewayClient.hasPermission(user(token), PORTFOLIO_NOSTRO_CREATE);
            default -> false;
        };
        logIfAccessDenied(result, servletRequest, token, PORTFOLIO_CREATE, "");
        return result;
    }

    public boolean checkPortfolioGetById(String portfolioId, WydenAuthenticationToken token, ServletWebRequest servletRequest) {

        Optional<Portfolio> portfolio = portfolioRepository.find(portfolioId);
        if (portfolio.isEmpty() || accessGatewayClient.hasPermission(user(token), PORTFOLIO_READ, portfolioId)) {
            return true;
        }
        boolean result =  switch(portfolio.get().getPortfolioType()){
            case VOSTRO -> accessGatewayClient.hasPermission(user(token), PORTFOLIO_VOSTRO_READ);
            case NOSTRO -> accessGatewayClient.hasPermission(user(token), PORTFOLIO_NOSTRO_READ);
            default -> false;
        };
        logIfAccessDenied(result, servletRequest, token, PORTFOLIO_READ, portfolioId);
        return result;
    }

    public boolean checkPortfolioManage(String portfolioId, WydenAuthenticationToken token, ServletWebRequest servletRequest) {

        Optional<Portfolio> portfolio = portfolioRepository.find(portfolioId);
        if (portfolio.isEmpty() || accessGatewayClient.hasPermission(user(token), PORTFOLIO_MANAGE, portfolioId)) {
            return true;
        }
        boolean result =  switch(portfolio.get().getPortfolioType()){
            case VOSTRO -> accessGatewayClient.hasPermission(user(token), PORTFOLIO_VOSTRO_MANAGE);
            case NOSTRO -> accessGatewayClient.hasPermission(user(token), PORTFOLIO_NOSTRO_MANAGE);
            default -> false;
        };
        logIfAccessDenied(result, servletRequest, token, PORTFOLIO_MANAGE, portfolioId);
        return result;
    }

}
