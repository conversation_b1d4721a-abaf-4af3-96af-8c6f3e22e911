package io.wyden.rest.management.portfolio;

import io.wyden.published.common.CursorConnection;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.published.referencedata.PortfolioType;
import io.wyden.referencedata.client.PortfoliosCacheFacade;
import io.wyden.referencedata.domain.model.GetPortfoliosRequestDto;
import io.wyden.referencedata.domain.model.PortfolioPredicateDto;
import io.wyden.referencedata.domain.model.PortfolioPredicateTypeDto;
import io.wyden.referencedata.domain.model.PortfolioSearchTypeDto;
import io.wyden.referencedata.domain.model.SortingOrder;
import io.wyden.rest.management.domain.PortfolioModel;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.LinkedList;
import java.util.List;
import java.util.Optional;

import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Repository
public class PortfolioRepository {

    private final PortfoliosCacheFacade portfoliosCacheFacade;

    public PortfolioRepository(PortfoliosCacheFacade portfoliosCacheFacade) {
        this.portfoliosCacheFacade = portfoliosCacheFacade;
    }

    public Collection<Portfolio> findAll() {
        return portfoliosCacheFacade.findAll();
    }

    public Optional<Portfolio> find(String portfolioId) {
        if (isBlank(portfolioId)) {
            return Optional.empty();
        }

        return portfoliosCacheFacade.find(portfolioId);
    }

    public boolean exists(String portfolioId) {
        return find(portfolioId).isPresent();
    }

    public CursorConnection search(PortfolioModel.PortfolioSearch search) {
        List<String> searchTagKeys = new LinkedList<>();
        String tagKey = search.tagKey();
        if (isNotBlank(tagKey)) {
            searchTagKeys.add(tagKey);
        }

        List<String> searchTagValues = new LinkedList<>();
        String tagValue = search.tagValue();
        if (isNotBlank(tagValue)) {
            searchTagValues.add(tagValue);
        }

        PortfolioType portfolioType = PortfolioToProtoMapper.map(search.portfolioType(), null);
        List<String> portfolioIds = new LinkedList<>(search.portfolioIds());

        GetPortfoliosRequestDto request = new GetPortfoliosRequestDto(
            isBlank(search.name()) ? null : new PortfolioPredicateDto(PortfolioPredicateTypeDto.EQUALS, PortfolioSearchTypeDto.NAME, search.name()),
            portfolioIds,
            searchTagKeys,
            searchTagValues,
            portfolioType,
            false,
            GetPortfoliosRequestDto.SortBy.PORTFOLIO_NAME,
            SortingOrder.ASC,
            search.first(),
            search.after());

        return portfoliosCacheFacade.search(request);
    }

    public Collection<Portfolio> findByName(String portfolioName) {
        return portfoliosCacheFacade.findByName(portfolioName);
    }
}
