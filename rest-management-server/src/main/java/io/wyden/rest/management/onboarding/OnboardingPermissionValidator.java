package io.wyden.rest.management.onboarding;

import io.wyden.rest.management.domain.OnboardingModel;
import io.wyden.rest.management.domain.PortfolioModel;
import io.wyden.rest.management.security.authentication.WydenAuthenticationToken;
import io.wyden.rest.management.security.permission.AccessGatewayFacade;

import org.springframework.stereotype.Component;
import org.springframework.web.context.request.ServletWebRequest;

import java.util.Collection;

import static io.wyden.accessgateway.client.permission.Permission.PORTFOLIO_CREATE;
import static io.wyden.accessgateway.client.permission.Permission.PORTFOLIO_NOSTRO_CREATE;
import static io.wyden.accessgateway.client.permission.Permission.PORTFOLIO_VOSTRO_CREATE;
import static io.wyden.accessgateway.client.permission.Permission.WALLET_CREATE;
import static io.wyden.accessgateway.client.permission.Permission.WALLET_NOSTRO_CREATE;
import static io.wyden.accessgateway.client.permission.Permission.WALLET_VOSTRO_CREATE;
import static io.wyden.rest.management.security.permission.User.user;
import static io.wyden.rest.management.util.AccessDeniedLogger.logIfAccessDenied;

@Component("onboardingPermissionValidator")
public class OnboardingPermissionValidator {

    private final AccessGatewayFacade accessGatewayClient;

    public OnboardingPermissionValidator(AccessGatewayFacade accessGatewayClient) {
        this.accessGatewayClient = accessGatewayClient;
    }

    public boolean checkPortfolioCreate(Collection<OnboardingModel.PortfolioOnboardingRequest> request, WydenAuthenticationToken token,ServletWebRequest servletRequest) {
        boolean result = accessGatewayClient.hasPermission(user(token), PORTFOLIO_CREATE)
            || (accessGatewayClient.hasPermission(user(token), PORTFOLIO_VOSTRO_CREATE)
                && request.stream().allMatch(r -> r.portfolioType().equals(PortfolioModel.PortfolioType.VOSTRO)))
            || (accessGatewayClient.hasPermission(user(token), PORTFOLIO_NOSTRO_CREATE)
                && request.stream().allMatch(r -> r.portfolioType().equals(PortfolioModel.PortfolioType.NOSTRO)));
        logIfAccessDenied(result, servletRequest, token, PORTFOLIO_CREATE, "");
        return result;
    }

    public boolean checkWalletCreate(Collection<OnboardingModel.AccountOnboardingRequest> request, WydenAuthenticationToken token, ServletWebRequest servletRequest) {
        boolean result = accessGatewayClient.hasPermission(user(token), WALLET_CREATE)
            || (accessGatewayClient.hasPermission(user(token), WALLET_VOSTRO_CREATE)
                && request.stream().allMatch(r -> r.walletType().equals(OnboardingModel.WalletType.VOSTRO)))
            || (accessGatewayClient.hasPermission(user(token), WALLET_NOSTRO_CREATE)
                && request.stream().allMatch(r -> r.walletType().equals(OnboardingModel.WalletType.NOSTRO)));

        logIfAccessDenied(result, servletRequest, token, WALLET_CREATE, "");
        return result;
    }
}
