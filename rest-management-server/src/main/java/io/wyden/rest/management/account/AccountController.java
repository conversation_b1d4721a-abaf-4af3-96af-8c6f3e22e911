package io.wyden.rest.management.account;

import io.wyden.accessgateway.client.permission.Permission;
import io.wyden.cloud.utils.rest.pagination.PaginationModel;
import io.wyden.published.booking.PositionSearch;
import io.wyden.rest.management.booking.BookingEngineService;
import io.wyden.rest.management.booking.PositionToProtoMapper;
import io.wyden.rest.management.domain.AccountModel;
import io.wyden.rest.management.domain.PositionModel;
import io.wyden.rest.management.security.authentication.WydenAuthenticationToken;
import io.wyden.rest.management.security.documentation.SecurityNote;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.ServletWebRequest;
import org.springframework.web.server.ResponseStatusException;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.function.Function;

@RestController
@RequestMapping(path = "${rest.management.context-path}")
public class AccountController {

    private final AccountService accountService;
    private final BookingEngineService bookingEngineService;
    private final AccountPermissionValidator accountPermissionValidator;

    public AccountController(AccountService accountService,
                             BookingEngineService bookingEngineService,
                             AccountPermissionValidator accountPermissionValidator) {
        this.accountService = accountService;
        this.bookingEngineService = bookingEngineService;
        this.accountPermissionValidator = accountPermissionValidator;
    }

    @GetMapping("/accounts")
    @SecurityNote("Filtered by accessible account ids")
    public PaginationModel.CursorConnection<AccountModel.Account> getAccounts(AccountModel.AccountSearch search, WydenAuthenticationToken authentication) {
        if (authentication.hasPermission(Permission.VENUE_ACCOUNT_READ)) {
            return accountService.search(search);
        } else {
            return accountService.search(search, accountService.getAccountIds(authentication));
        }
    }

    @GetMapping("/accounts/{accountId}/positions")
    @PreAuthorize("@accountPermissionValidator.checkAccountReadPermission(#accountId, #authentication, #servletRequest)")
    public Collection<PositionModel.Position> getPositions(@PathVariable String accountId, WydenAuthenticationToken authentication, ServletWebRequest servletRequest) {
        if (!accountService.exists(accountId)) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Cannot find account with id: " + accountId);
        }

        return bookingEngineService
            .findPositionsByAccountId(accountId, authentication.getClientId())
            .getAllNodes();
    }

    @GetMapping("/accounts/{accountId}/balances")
    @PreAuthorize("@accountPermissionValidator.checkAccountReadPermission(#accountId, #authentication, #servletRequest)")
    public Collection<PositionModel.Balance> getBalances(@PathVariable String accountId, WydenAuthenticationToken authentication, ServletWebRequest servletRequest) {
        if (!accountService.exists(accountId)) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Cannot find account with id: " + accountId);
        }

        return bookingEngineService
            .findBalancesByAccountId(accountId, authentication.getClientId())
            .getAllNodes();
    }

    @GetMapping("/accounts/positions")
    @SecurityNote("Filtered by accessible account ids")
    public PaginationModel.CursorConnection<PositionModel.Position> getPositions(AccountModel.AccountSearch search, WydenAuthenticationToken authentication) {
        return findByAccountSearch(search, authentication, bookingEngineService::searchPositions);
    }

    @GetMapping("/accounts/balances")
    @SecurityNote("Filtered by accessible account ids")
    public PaginationModel.CursorConnection<PositionModel.Balance> getBalances(AccountModel.AccountSearch search, WydenAuthenticationToken authentication) {
        return findByAccountSearch(search, authentication, bookingEngineService::searchBalances);

    }

    private <T extends Serializable> PaginationModel.CursorConnection<T> findByAccountSearch(AccountModel.AccountSearch search,
                                                                                             WydenAuthenticationToken authentication,
                                                                                             Function<PositionSearch, PaginationModel.CursorConnection<T>> searchFunction) {
        // we cannot apply first and after parameters from request - these need to be applied to booking reuqest
        AccountModel.AccountSearch accountSearch = new AccountModel.AccountSearch(search.name(), search.venue(), null, null);

        PaginationModel.CursorConnection<AccountModel.Account> accountConnection = getAccounts(accountSearch, authentication);
        Collection<AccountModel.Account> accounts = accountConnection.getAllNodes();
        if (accounts.isEmpty()) {
            return PaginationModel.emptyCursorConnection();
        }

        List<String> accountIds = accounts.stream()
            .map(AccountModel.Account::id)
            .toList();

        PositionSearch positionSearch = PositionToProtoMapper.map(search, accountIds, authentication.getClientId());
        return searchFunction.apply(positionSearch);
    }
}
