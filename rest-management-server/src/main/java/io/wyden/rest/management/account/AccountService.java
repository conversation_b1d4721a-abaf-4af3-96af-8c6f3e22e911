package io.wyden.rest.management.account;

import io.wyden.cloud.utils.rest.pagination.PaginationModel;
import io.wyden.cloud.utils.rest.pagination.PaginationWrapper;
import io.wyden.published.referencedata.VenueAccount;
import io.wyden.published.referencedata.account.AccountOnboardingPositionCreatedEvent;
import io.wyden.referencedata.client.VenueAccountCacheFacade;
import io.wyden.rest.management.common.Identifiers;
import io.wyden.rest.management.domain.AccountModel;
import io.wyden.rest.management.domain.OnboardingModel;
import io.wyden.rest.management.onboarding.OnboardingRequestValidator;
import io.wyden.rest.management.security.authentication.WydenAuthenticationToken;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static io.wyden.cloudutils.tools.ProtobufUtils.shortDebugString;
import static io.wyden.rest.management.account.AccountFromProtoMapper.map;
import static io.wyden.rest.management.common.ProtoMappingUtils.dt;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.slf4j.LoggerFactory.getLogger;

@Service
public class AccountService {

    private static final Logger LOGGER = getLogger(AccountService.class);

    private final VenueAccountCacheFacade venueAccountCacheFacade;
    private final AccountCreateRequestEmitter accountCreateRequestEmitter;
    private final AccountOnboardingEventConsumer accountOnboardingEventConsumer;
    private final AccountPermissionValidator accountPermissionValidator;
    private final OnboardingRequestValidator validator;
    private final Long onboardingTimeoutSeconds;

    public AccountService(VenueAccountCacheFacade venueAccountCacheFacade,
                          AccountCreateRequestEmitter accountCreateRequestEmitter,
                          AccountOnboardingEventConsumer accountOnboardingEventConsumer,
                          AccountPermissionValidator accountPermissionValidator,
                          OnboardingRequestValidator validator,
                          @Value("${rest.management.account-onboarding-timeout-seconds}") Long onboardingTimeoutSeconds) {
        this.venueAccountCacheFacade = venueAccountCacheFacade;
        this.accountCreateRequestEmitter = accountCreateRequestEmitter;
        this.accountOnboardingEventConsumer = accountOnboardingEventConsumer;
        this.accountPermissionValidator = accountPermissionValidator;
        this.validator = validator;
        this.onboardingTimeoutSeconds = onboardingTimeoutSeconds;
    }

    public boolean exists(String accountId) {
        return venueAccountCacheFacade.exists(accountId);
    }

    public Collection<AccountModel.Account> findAll(@Nullable Set<String> venueAccountIds) {
        Collection<VenueAccount> venueAccounts = venueAccountIds != null ? venueAccountCacheFacade.venueAccountDetails(venueAccountIds) : venueAccountCacheFacade.venueAccountDetails();
        return venueAccounts.stream()
            .map(venueAccount -> new AccountModel.Account(
                venueAccount.getId(),
                venueAccount.getVenueAccountName(),
                venueAccount.getVenueName(),
                venueAccount.getAccountType().name(),
                map(venueAccount.getWalletType()),
                dt(venueAccount.getCreatedAt())))
            .toList();
    }

    public PaginationModel.CursorConnection<AccountModel.Account> search(AccountModel.AccountSearch search) {
        return search(search, null);
    }

    public PaginationModel.CursorConnection<AccountModel.Account> search(AccountModel.AccountSearch search, @Nullable Set<String> venueAccountIds) {
        Collection<AccountModel.Account> accounts = findAll(venueAccountIds).stream()
            .sorted(Comparator.comparing(AccountModel.Account::createdAt))
            .toList();

        Collection<Predicate<AccountModel.Account>> predicates = new LinkedList<>();

        if (isNotBlank(search.name())) {
            Predicate<AccountModel.Account> accountNamePredicate = account -> containsIgnoringCase(account.name(), search.name());
            predicates.add(accountNamePredicate);
        }

        if (isNotBlank(search.venue())) {
            Predicate<AccountModel.Account> venueNamePredicate = account -> containsIgnoringCase(account.venue(), search.venue());
            predicates.add(venueNamePredicate);
        }

        // all accounts matching search query
        Predicate<AccountModel.Account> filteredPredicates = predicates.stream()
            .reduce(Predicate::and)
            .orElse(account -> true);

        long total = accounts.stream()
            .filter(filteredPredicates)
            .count();

        // also all accounts matching search query + after cursor
        if (isNotBlank(search.after())) {
            Predicate<AccountModel.Account> afterPredicate = account -> String.valueOf(account.createdAt()).compareTo(search.after()) > 0;
            predicates.add(afterPredicate);
        }

        Predicate<AccountModel.Account> paginatedPredicates = predicates.stream()
            .reduce(Predicate::and)
            .orElse(account -> true);

        List<AccountModel.Account> filteredAccounts = accounts.stream()
            .filter(paginatedPredicates)
            .toList();

        int remaining = filteredAccounts.size();

        long limit = search.first() != null ? search.first() : Long.MAX_VALUE;

        List<AccountModel.Account> limitedAccounts = filteredAccounts.stream()
            .limit(limit)
            .toList();

        return PaginationWrapper.wrapToModel(
            limitedAccounts,
            account -> String.valueOf(account.createdAt()),
            remaining,
            total);
    }

    public Collection<OnboardingModel.AccountOnboardingResponse> onboardAccounts(Collection<OnboardingModel.AccountOnboardingRequest> onboardingRequests, String clientId) {
        String correlationId = Identifiers.randomIdentifier();

        LOGGER.info("Onboarding accounts for owner: {} using correlationId: {} and requests: {}", clientId, correlationId, onboardingRequests);

        Collection<OnboardingModel.AccountOnboardingResponse> failFastOnboardingResponses = new ArrayList<>();
        Collection<OnboardingModel.AccountOnboardingRequest> filteredOnboardingRequests = new ArrayList<>();

        validator.validateMaxSize(onboardingRequests);

        for (OnboardingModel.AccountOnboardingRequest request : onboardingRequests) {
            try {
                validator.validate(request);
                filteredOnboardingRequests.add(request);
            } catch (Exception e) {
                OnboardingModel.AccountOnboardingResponse failFastResponse = new OnboardingModel.AccountOnboardingResponse(
                    request.id(),
                    request.name(),
                    request.walletType(),
                    OnboardingModel.AccountOnboardingStatus.FAILED,
                    e.getMessage());

                failFastOnboardingResponses.add(failFastResponse);
            }
        }

        accountCreateRequestEmitter.emit(filteredOnboardingRequests, clientId, correlationId);

        AccountOnboardingPositionCreatedEvent onboardingEvent = accountOnboardingEventConsumer.getOnboardingEvent(correlationId)
            .timeout(Duration.ofSeconds(onboardingTimeoutSeconds))
            .block();

        LOGGER.info("Finished onboarding portfolios for owner: {} using correlationId: {}, results: {}", clientId, correlationId, shortDebugString(onboardingEvent));

        Collection<OnboardingModel.AccountOnboardingResponse> processedOnboardingResponses = map(onboardingEvent);

        Collection<OnboardingModel.AccountOnboardingResponse> onboardingResponses = new ArrayList<>();
        onboardingResponses.addAll(processedOnboardingResponses);
        onboardingResponses.addAll(failFastOnboardingResponses);
        return onboardingResponses;
    }

    public Set<String> getAccountIds(WydenAuthenticationToken wydenAuthenticationToken) {
        return venueAccountCacheFacade.findAllActive()
            .stream()
            .filter(va -> accountPermissionValidator.checkAccountReadPermission(va, wydenAuthenticationToken, null))
            .map(VenueAccount::getId)
            .collect(Collectors.toSet());
    }

    /**
     * Determines if a given string is contained within another string, ignoring case.
     */
    private static boolean containsIgnoringCase(String left, String right) {
        if (isBlank(left) || isBlank(right)) {
            return false;
        }

        return left.toLowerCase().contains(right.toLowerCase());
    }

    public Collection<VenueAccount> getAccountsByName(String name) {
        if (StringUtils.isEmpty(name)) {
            return Set.of();
        }
        return venueAccountCacheFacade.findByName(name);
    }
}
