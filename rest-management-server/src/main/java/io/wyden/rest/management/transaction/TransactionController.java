package io.wyden.rest.management.transaction;

import io.wyden.cloud.utils.rest.pagination.PaginationModel;
import io.wyden.rest.management.booking.BookingEngineService;
import io.wyden.rest.management.domain.TransactionModel;
import io.wyden.rest.management.security.authentication.WydenAuthenticationToken;
import io.wyden.rest.management.transaction.validator.TransactionRequestValidator;
import jakarta.validation.Valid;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PostAuthorize;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.ServletWebRequest;
import org.springframework.web.server.ResponseStatusException;

@RestController
@RequestMapping(path = "${rest.management.context-path}")
public class TransactionController {

    private final TransactionRequestValidator validator;
    private final BookingEngineService bookingEngineService;

    public TransactionController(TransactionRequestValidator validator,
                                 BookingEngineService bookingEngineService) {
        this.validator = validator;
        this.bookingEngineService = bookingEngineService;
    }

    @GetMapping("/transactions")
    @PreAuthorize("@bookTransactionValidator.check(#search, #authentication, #servletRequest)")
    public PaginationModel.CursorConnection<TransactionModel.Transaction> getTransactions(TransactionModel.TransactionSearch search, WydenAuthenticationToken authentication, ServletWebRequest servletRequest) {
        return bookingEngineService.search(search, authentication.getClientId());
    }

    @PostMapping("/transactions")
    @PreAuthorize("@bookTransactionValidator.check(#transaction, #authentication, #servletRequest)")
    public TransactionModel.Transaction bookTransaction(@Valid @RequestBody TransactionModel.TransactionRequest transaction, WydenAuthenticationToken authentication, ServletWebRequest servletRequest) {
        validator.validate(transaction);
        return bookingEngineService.createTransaction(transaction);
    }

    @GetMapping("/transactions/{transactionId}")
    @PostAuthorize("@transferByIdPermissionValidator.validateAccess(returnObject, #token, #servletRequest)")
    public TransactionModel.Transaction getTransaction(@PathVariable String transactionId, WydenAuthenticationToken token, ServletWebRequest servletRequest) {
        TransactionModel.Transaction transaction = bookingEngineService.findTransactionById(transactionId);
        if (transaction != null) {
            return transaction;
        }

        throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Cannot find transaction with id: " + transactionId);
    }
}
