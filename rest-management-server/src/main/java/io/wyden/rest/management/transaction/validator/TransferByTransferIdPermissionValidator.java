package io.wyden.rest.management.transaction.validator;

import io.wyden.rest.management.domain.TransactionModel;
import io.wyden.rest.management.security.authentication.WydenAuthenticationPermissionHelper;
import io.wyden.rest.management.security.authentication.WydenAuthenticationToken;

import org.springframework.stereotype.Component;
import org.springframework.web.context.request.ServletWebRequest;

import static io.wyden.rest.management.security.authentication.WydenAuthenticationPermissionHelper.hasReadPermissionsForAccount;
import static io.wyden.rest.management.security.authentication.WydenAuthenticationPermissionHelper.hasReadPermissionsForPortfolio;
import static io.wyden.rest.management.util.AccessDeniedLogger.logIfAccessDenied;


@Component("transferByIdPermissionValidator")
public class TransferByTransferIdPermissionValidator implements WydenAuthenticationPermissionHelper {

    public boolean validateAccess(TransactionModel.Deposit transaction, WydenAuthenticationToken token, ServletWebRequest servletRequest) {
        boolean p1 = hasReadPermissionsForPortfolio(transaction.portfolioId(), token);
        boolean p2 = hasReadPermissionsForPortfolio(transaction.feePortfolioId(), token);
        boolean a1 = hasReadPermissionsForAccount(transaction.accountId(), token);
        boolean a2 = hasReadPermissionsForAccount(transaction.feeAccountId(), token);

        return logIfAccessDenied(a1 || a2 || p1 || p2, servletRequest, token,
            String.format("missing read permission for at least one of the accounts [%s, %s] or one of the portfolios [%s, %s]",
                transaction.accountId(), transaction.feeAccountId(), transaction.portfolioId(), transaction.feePortfolioId()));
    }

    public boolean validateAccess(TransactionModel.Withdrawal transaction, WydenAuthenticationToken token, ServletWebRequest servletRequest) {
        boolean p1 = hasReadPermissionsForPortfolio(transaction.portfolioId(), token);
        boolean p2 = hasReadPermissionsForPortfolio(transaction.feePortfolioId(), token);
        boolean a1 = hasReadPermissionsForAccount(transaction.accountId(), token);
        boolean a2 = hasReadPermissionsForAccount(transaction.feeAccountId(), token);

        return logIfAccessDenied(a1 || a2 || p1 || p2, servletRequest, token,
            String.format("missing read permission for at least one of the accounts [%s, %s] or one of the portfolios [%s, %s]",
                transaction.accountId(), transaction.feeAccountId(), transaction.portfolioId(), transaction.feePortfolioId()));
    }

    public boolean validateAccess(TransactionModel.ClientCashTrade transaction, WydenAuthenticationToken token, ServletWebRequest servletRequest) {
        boolean p1 = hasReadPermissionsForPortfolio(transaction.portfolioId(), token);
        boolean p2 = hasReadPermissionsForPortfolio(transaction.counterPortfolioId(), token);

        return logIfAccessDenied(p1 || p2, servletRequest, token,
            String.format("missing read permission for at least one of the portfolios [%s, %s]",
                transaction.portfolioId(), transaction.counterPortfolioId()));
    }

    public boolean validateAccess(TransactionModel.StreetCashTrade transaction, WydenAuthenticationToken token, ServletWebRequest servletRequest) {
        boolean p1 = hasReadPermissionsForPortfolio(transaction.portfolioId(), token);
        boolean a1 = hasReadPermissionsForAccount(transaction.accountId(), token);

        return logIfAccessDenied(p1 || a1, servletRequest, token,
            String.format("missing read permission for at least one of the portfolio %s or account %s]",
                transaction.portfolioId(), transaction.accountId()));
    }

    public boolean validateAccess(TransactionModel.PortfolioCashTransfer transaction, WydenAuthenticationToken token, ServletWebRequest servletRequest) {
        boolean p1 = hasReadPermissionsForPortfolio(transaction.sourcePortfolioId(), token);
        boolean p2 = hasReadPermissionsForPortfolio(transaction.targetPortfolioId(), token);

        return logIfAccessDenied(p1 || p2, servletRequest, token,
            String.format("missing portfolio.read permission for at least one of the portfolios [%s, %s]",
                transaction.sourcePortfolioId(), transaction.targetPortfolioId()));
    }

    public boolean validateAccess(TransactionModel.AccountCashTransfer transaction, WydenAuthenticationToken token, ServletWebRequest servletRequest) {
        boolean a1 = hasReadPermissionsForAccount(transaction.sourceAccountId(), token);
        boolean a2 = hasReadPermissionsForAccount(transaction.targetAccountId(), token);
        boolean p1 = hasReadPermissionsForPortfolio(transaction.feePortfolioId(), token);
        boolean a3 = hasReadPermissionsForAccount(transaction.feeAccountId(), token);

        return logIfAccessDenied(a1 || a2 || a3 || p1, servletRequest, token,
            String.format("missing read permission for at least one of the accounts [%s, %s, %s] or the portfolio %s",
                transaction.sourceAccountId(), transaction.targetAccountId(), transaction.feeAccountId(), transaction.feePortfolioId()));
    }

    public boolean validateAccess(TransactionModel.Settlement transaction, WydenAuthenticationToken token, ServletWebRequest servletRequest) {
        return true;
    }
}