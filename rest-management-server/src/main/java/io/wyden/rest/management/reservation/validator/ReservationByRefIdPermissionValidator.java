package io.wyden.rest.management.reservation.validator;

import io.wyden.rest.management.domain.ReservationModel;
import io.wyden.rest.management.security.authentication.WydenAuthenticationPermissionHelper;
import io.wyden.rest.management.security.authentication.WydenAuthenticationToken;

import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.ServletWebRequest;

import static io.wyden.rest.management.security.authentication.WydenAuthenticationPermissionHelper.hasReadPermissionsForAccount;
import static io.wyden.rest.management.security.authentication.WydenAuthenticationPermissionHelper.hasReadPermissionsForPortfolio;
import static io.wyden.rest.management.util.AccessDeniedLogger.logIfAccessDenied;

@Component("reservationByRefIdValidator")
public class ReservationByRefIdPermissionValidator implements WydenAuthenticationPermissionHelper {

    public boolean validateAccess(ReservationModel.DepositReservation reservation, WydenAuthenticationToken token, ServletWebRequest servletRequest) {
        boolean p1 = hasReadPermissionsForPortfolio(reservation.portfolioId(), token);
        boolean p2 = hasReadPermissionsForPortfolio(reservation.feePortfolioId(), token);
        boolean a1 = hasReadPermissionsForAccount(reservation.accountId(), token);
        boolean a2 = hasReadPermissionsForAccount(reservation.feeAccountId(), token);

        return logIfAccessDenied(a1 || a2 || p1 || p2, servletRequest, token,
            String.format("missing read permission for at least one of the accounts [%s, %s] or one of the portfolios [%s, %s]",
                reservation.accountId(), reservation.feeAccountId(), reservation.portfolioId(), reservation.feePortfolioId()));
    }

    public boolean validateAccess(ReservationModel.WithdrawalReservation reservation, WydenAuthenticationToken token, ServletWebRequest servletRequest) {
        boolean p1 = hasReadPermissionsForPortfolio(reservation.portfolioId(), token);
        boolean p2 = hasReadPermissionsForPortfolio(reservation.feePortfolioId(), token);
        boolean a1 = hasReadPermissionsForAccount(reservation.accountId(), token);
        boolean a2 = hasReadPermissionsForAccount(reservation.feeAccountId(), token);

        return logIfAccessDenied(a1 || a2 || p1 || p2, servletRequest, token,
            String.format("missing read permission for at least one of the accounts [%s, %s] or one of the portfolios [%s, %s]",
                reservation.accountId(), reservation.feeAccountId(), reservation.portfolioId(), reservation.feePortfolioId()));
    }

    public boolean validateAccess(ReservationModel.ClientCashTradeReservation reservation, WydenAuthenticationToken token, ServletWebRequest servletRequest) {
        boolean p1 = hasReadPermissionsForPortfolio(reservation.portfolioId(), token);
        boolean p2 = hasReadPermissionsForPortfolio(reservation.counterPortfolioId(), token);

        return logIfAccessDenied(p1 || p2, servletRequest, token,
            String.format("missing read permission for at least one of the portfolios [%s, %s]",
                reservation.portfolioId(), reservation.counterPortfolioId()));
    }

    public boolean validateAccess(ReservationModel.StreetCashTradeReservation reservation, WydenAuthenticationToken token, ServletWebRequest servletRequest) {
        boolean p1 = hasReadPermissionsForPortfolio(reservation.portfolioId(), token);
        boolean a1 = hasReadPermissionsForAccount(reservation.accountId(), token);

        return logIfAccessDenied(p1 || a1, servletRequest, token,
            String.format("missing read permission for at least one of the portfolio %s or account %s",
                reservation.portfolioId(), reservation.accountId()));
    }

    public boolean validateAccess(ReservationModel.PortfolioCashTransferReservation reservation, WydenAuthenticationToken token, ServletWebRequest servletRequest) {
        boolean p1 = hasReadPermissionsForPortfolio(reservation.sourcePortfolioId(), token);
        boolean p2 = hasReadPermissionsForPortfolio(reservation.targetPortfolioId(), token);

        return logIfAccessDenied(p1 || p2, servletRequest, token,
            String.format("missing portfolio.read permission for at least one of the portfolios [%s, %s]",
                reservation.sourcePortfolioId(), reservation.targetPortfolioId()));
    }

    public boolean validateAccess(ReservationModel.AccountCashTransferReservation reservation, WydenAuthenticationToken token, ServletWebRequest servletRequest) {
        boolean a1 = hasReadPermissionsForAccount(reservation.sourceAccountId(), token);
        boolean a2 = hasReadPermissionsForAccount(reservation.targetAccountId(), token);
        boolean p1 = hasReadPermissionsForPortfolio(reservation.feePortfolioId(), token);
        boolean a3 = hasReadPermissionsForAccount(reservation.feeAccountId(), token);

        return logIfAccessDenied(a1 || a2 || a3 || p1, servletRequest, token,
            String.format("missing read permission for at least one of the accounts [%s, %s, %s] or the portfolio %s",
                reservation.sourceAccountId(), reservation.targetAccountId(), reservation.feeAccountId(), reservation.feePortfolioId()));

    }

    public void validateReservationAccess(ReservationModel.Reservation reservation, WydenAuthenticationToken authentication, ServletWebRequest servletRequest) {
        boolean result = false;
        if (reservation instanceof ReservationModel.DepositReservation depositReservation) {
            result = validateAccess(depositReservation, authentication, servletRequest);
        } else if (reservation instanceof ReservationModel.WithdrawalReservation withdrawalReservation) {
            result = validateAccess(withdrawalReservation, authentication, servletRequest);
        } else  if (reservation instanceof ReservationModel.ClientCashTradeReservation clientCashTradeReservation) {
            result = validateAccess(clientCashTradeReservation, authentication, servletRequest);
        } else if (reservation instanceof ReservationModel.StreetCashTradeReservation streetCashTradeReservation) {
            result = validateAccess(streetCashTradeReservation, authentication, servletRequest);
        } else if (reservation instanceof ReservationModel.PortfolioCashTransferReservation portfolioCashTransferReservation) {
            result = validateAccess(portfolioCashTransferReservation, authentication, servletRequest);
        } else if (reservation instanceof ReservationModel.AccountCashTransferReservation accountCashTransferReservation) {
            result = validateAccess(accountCashTransferReservation, authentication, servletRequest);
        }

        if (!result) {
            throw new AccessDeniedException("Access denied");
        }
    }


}