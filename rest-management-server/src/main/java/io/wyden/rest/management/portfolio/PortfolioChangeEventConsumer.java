package io.wyden.rest.management.portfolio;

import com.rabbitmq.client.AMQP;
import io.wyden.cloud.utils.spring.util.ExclusiveNameGenerator;
import io.wyden.cloudutils.rabbitmq.ConsumptionResult;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.queue.ExpiringRabbitQueueBuilder;
import io.wyden.cloudutils.rabbitmq.queue.MessageConsumer;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueue;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueueBuilder;
import io.wyden.published.referencedata.PortfolioChangeEvent;
import jakarta.annotation.PostConstruct;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;

import java.time.Duration;
import java.util.Arrays;
import java.util.Objects;

import static org.slf4j.LoggerFactory.getLogger;

@Component
public class PortfolioChangeEventConsumer implements MessageConsumer<PortfolioChangeEvent> {

    private static final Logger LOGGER = getLogger(PortfolioChangeEventConsumer.class);

    private final String queueName;
    private final String consumerName;
    private final RabbitIntegrator rabbitIntegrator;
    private final RabbitExchange<PortfolioChangeEvent> portfolioChangeEventExchange;
    private final Sinks.Many<PortfolioChangeEvent> portfolioChangeEventsSink;

    public PortfolioChangeEventConsumer(@Value("${rabbitmq.reference-data-rest-management-queue.portfolio-change-event}") final String queueNameFormat,
                                        ExclusiveNameGenerator queueNameGenerator,
                                        @Value("${spring.application.name}") String consumerName,
                                        RabbitIntegrator rabbitIntegrator,
                                        RabbitExchange<PortfolioChangeEvent> portfolioChangeEventExchange) {
        this.queueName = queueNameGenerator.getQueueName(queueNameFormat);
        this.consumerName = consumerName;
        this.rabbitIntegrator = rabbitIntegrator;
        this.portfolioChangeEventExchange = portfolioChangeEventExchange;
        this.portfolioChangeEventsSink = Sinks.many().replay().limit(100, Duration.ofMinutes(1));
    }

    @PostConstruct
    void init() {
        declareQueue();
        cleanupOldQueues("rest-management-queue.reference-data.PORTFOLIO-CHANGE-EVENT");
    }

    private void cleanupOldQueues(String... legacyQueueNames) {
        Arrays.stream(legacyQueueNames).forEach(rabbitIntegrator::deleteQueueIfUnused);
    }

    @Override
    public ConsumptionResult consume(PortfolioChangeEvent portfolioChangeEvent, AMQP.BasicProperties basicProperties) {
        try {
            LOGGER.info("Consuming PortfolioChangeEvent message, {}", portfolioChangeEvent);

            Sinks.EmitResult emitResult = portfolioChangeEventsSink.tryEmitNext(portfolioChangeEvent);
            if (emitResult.isFailure()) {
                LOGGER.trace("Failed to emit consumed portfolio change event: {}, re-queueing event: {}", emitResult, portfolioChangeEvent);
                return ConsumptionResult.failureNonRecoverable();
            }

        } catch (Exception e) {
            LOGGER.error("Failed to consume incoming: %s, re-queueing...".formatted(portfolioChangeEvent), e);
            return ConsumptionResult.failureNeedsRequeue();
        }

        return ConsumptionResult.consumed();
    }

    public Mono<PortfolioChangeEvent> getPortfolioChangeEvent(String correlationId) {
        return portfolioChangeEventsSink.asFlux()
            .filter(event -> Objects.equals(event.getPortfolio().getName(), correlationId))
            .next();
    }

    private void declareQueue() {
        RabbitQueue<PortfolioChangeEvent> queue = new ExpiringRabbitQueueBuilder<PortfolioChangeEvent>(rabbitIntegrator)
            .setQueueName(queueName)
            .setConsumerName(consumerName)
            .declare();

        queue.bindWithRoutingKey(portfolioChangeEventExchange, StringUtils.EMPTY);
        LOGGER.info("Binding exchange {} and queue {}", portfolioChangeEventExchange, queue);
        queue.attachConsumer(PortfolioChangeEvent.parser(), this);
    }
}
