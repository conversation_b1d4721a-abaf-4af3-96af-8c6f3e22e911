package io.wyden.rest.management.account;

import io.wyden.published.referencedata.AccountType;
import io.wyden.published.referencedata.VenueAccount;
import io.wyden.published.referencedata.WalletType;
import io.wyden.rest.management.security.authentication.WydenAuthenticationToken;
import io.wyden.rest.management.security.permission.AccessGatewayFacade;

import org.springframework.stereotype.Component;
import org.springframework.web.context.request.ServletWebRequest;

import java.util.Optional;

import static io.wyden.accessgateway.client.permission.Permission.VENUE_ACCOUNT_READ;
import static io.wyden.accessgateway.client.permission.Permission.WALLET_NOSTRO_READ;
import static io.wyden.accessgateway.client.permission.Permission.WALLET_READ;
import static io.wyden.accessgateway.client.permission.Permission.WALLET_VOSTRO_READ;
import static io.wyden.rest.management.security.permission.User.user;
import static io.wyden.rest.management.util.AccessDeniedLogger.logIfAccessDenied;

@Component("accountPermissionValidator")
public class AccountPermissionValidator {

    private final AccessGatewayFacade accessGatewayClient;
    private final VenueAccountRepository venueAccountrepository;

    public AccountPermissionValidator(AccessGatewayFacade accessGatewayClient,
                                      VenueAccountRepository venueAccountrepository) {
        this.accessGatewayClient = accessGatewayClient;
        this.venueAccountrepository = venueAccountrepository;
    }

    public boolean checkAccountReadPermission(String accountId, WydenAuthenticationToken token, ServletWebRequest servletRequest) {
        Optional<VenueAccount> venueAccount = venueAccountrepository.find(accountId);
        return venueAccount.filter(account -> checkAccountReadPermission(account, token, servletRequest)).isPresent();
    }

    public boolean checkAccountReadPermission(VenueAccount account, WydenAuthenticationToken token, ServletWebRequest servletRequest) {
        if (account == null) {
            return false;
        }

        boolean result = accessGatewayClient.hasPermission(user(token), VENUE_ACCOUNT_READ, account.getId())
                || (account.getAccountType().equals(AccountType.WALLET) && accessGatewayClient.hasPermission(user(token), WALLET_READ))
                || (account.getWalletType().equals(WalletType.WALLET_VOSTRO) && accessGatewayClient.hasPermission(user(token), WALLET_VOSTRO_READ))
                || (account.getWalletType().equals(WalletType.WALLET_NOSTRO) && accessGatewayClient.hasPermission(user(token), WALLET_NOSTRO_READ));
        logIfAccessDenied(result, servletRequest, token, VENUE_ACCOUNT_READ, account.getId());
        return result;
    }
}
