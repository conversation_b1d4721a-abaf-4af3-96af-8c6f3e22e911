package io.wyden.rest.management.util;

import io.wyden.accessgateway.client.permission.Permission;
import io.wyden.rest.management.security.authentication.WydenAuthenticationToken;

import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.ServletWebRequest;

import javax.annotation.Nullable;

public class AccessDeniedLogger {

    private AccessDeniedLogger() { //hide
    }

    public static final String ACCESS_DENIED_MESSAGE_REQUEST_ATTRIBUTE_KEY = "access-denied-message";


    public static boolean logIfAccessDenied(boolean permissionDecision, ServletWebRequest servletRequest, WydenAuthenticationToken token, Permission permission, String resourceId) {
        String perm = permission.getResource() + "." + permission.getScope();
        return logIfAccessDenied(permissionDecision, servletRequest, token, perm, resourceId);
    }

    public static boolean logIfAccessDenied(boolean permissionDecision, @Nullable ServletWebRequest servletRequest, WydenAuthenticationToken token, String permission, String resourceId) {
        if (!permissionDecision && servletRequest != null) {
            String apiKey = servletRequest.getRequest().getHeader("x-api-key");
            String path = servletRequest.getRequest().getRequestURI();
            String method = servletRequest.getRequest().getMethod();
            String query = servletRequest.getRequest().getQueryString() == null ? "" : " query: " + servletRequest.getRequest().getQueryString();

            if (StringUtils.isNotEmpty(resourceId)) {
                String message = String.format(
                    "Access denied for user: %s, api-key: %s for request: %s path: %s%s, error: missing %s for resource %s",
                    token.getClientId(), apiKey, method, path, query, permission, resourceId);
                addAccessDeniedMessageToRequestAttribute(servletRequest, message);
            } else {
                String message = String.format(
                    "Access denied for user: %s, api-key: %s for request: %s path: %s%s, error: missing %s",
                    token.getClientId(), apiKey, method, path, query, permission);
                addAccessDeniedMessageToRequestAttribute(servletRequest, message);
            }
        }

        return permissionDecision;
    }

    public static boolean logIfAccessDenied(boolean permissionDecision, ServletWebRequest servletRequest, WydenAuthenticationToken token, String msg) {
        if (!permissionDecision  && servletRequest != null) {
            String apiKey = servletRequest.getRequest().getHeader("x-api-key");
            String path = servletRequest.getRequest().getRequestURI();
            String method = servletRequest.getRequest().getMethod();
            String query = servletRequest.getRequest().getQueryString() == null ? "" : " query: " + servletRequest.getRequest().getQueryString();

            String message = String.format(
                "Access denied for user: %s with api-key: %s for request: %s path: %s%s, error: %s",
                token.getClientId(), apiKey, method, path, query, msg);
            addAccessDeniedMessageToRequestAttribute(servletRequest, message);

        }
        return permissionDecision;
    }

    private static void addAccessDeniedMessageToRequestAttribute(ServletWebRequest servletRequest, String mesage) {
        servletRequest.getRequest().setAttribute(ACCESS_DENIED_MESSAGE_REQUEST_ATTRIBUTE_KEY, mesage);
    }
}
