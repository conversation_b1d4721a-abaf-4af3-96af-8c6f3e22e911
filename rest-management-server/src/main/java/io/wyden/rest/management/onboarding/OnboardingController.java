package io.wyden.rest.management.onboarding;

import io.wyden.rest.management.account.AccountService;
import io.wyden.rest.management.domain.OnboardingModel;
import io.wyden.rest.management.portfolio.PortfolioService;
import io.wyden.rest.management.security.authentication.WydenAuthenticationToken;
import org.slf4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.ServletWebRequest;
import org.springframework.web.server.ResponseStatusException;

import java.util.Collection;

import static org.slf4j.LoggerFactory.getLogger;

@RestController
@Validated
@RequestMapping(path = "${rest.management.context-path}")
public class OnboardingController {

    private static final Logger LOGGER = getLogger(OnboardingController.class);

    private final PortfolioService portfolioService;
    private final AccountService accountService;

    public OnboardingController(PortfolioService portfolioService,
                                AccountService accountService) {
        this.portfolioService = portfolioService;
        this.accountService = accountService;
    }

    @PostMapping("/portfolios/onboarding")
    @PreAuthorize("@onboardingPermissionValidator.checkPortfolioCreate(#onboardingRequests, #authentication, #servletRequest)")
    public Collection<OnboardingModel.PortfolioOnboardingResponse> onboardPortfolios(@RequestBody Collection<OnboardingModel.PortfolioOnboardingRequest> onboardingRequests,
                                                                                     @RequestParam(required = false, defaultValue = "false") boolean ensureUniquePortfolioNames,
                                                                                     WydenAuthenticationToken authentication,
                                                                                     ServletWebRequest servletRequest) {
        try {
            return portfolioService.onboardPortfolios(onboardingRequests, ensureUniquePortfolioNames, authentication.getClientId());
        } catch (Exception e) {
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Cannot complete onboarding: " + e.getMessage());
        }
    }

    @PostMapping("/accounts/onboarding")
    @PreAuthorize("@onboardingPermissionValidator.checkWalletCreate(#onboardingRequests, #authentication, #servletRequest)")
    public Collection<OnboardingModel.AccountOnboardingResponse> onboardAccounts(@RequestBody Collection<OnboardingModel.AccountOnboardingRequest> onboardingRequests,
                                                                                 WydenAuthenticationToken authentication,
                                                                                 ServletWebRequest servletRequest) {
        try {
            return accountService.onboardAccounts(onboardingRequests, authentication.getClientId());
        } catch (Exception e) {
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Cannot complete onboarding: " + e.getMessage());
        }
    }
}
