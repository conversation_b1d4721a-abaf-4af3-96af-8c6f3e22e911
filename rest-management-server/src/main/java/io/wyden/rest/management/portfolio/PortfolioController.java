package io.wyden.rest.management.portfolio;

import io.wyden.cloud.utils.rest.pagination.PaginationModel;
import io.wyden.cloud.utils.rest.pagination.PaginationProtoToModelMapper;
import io.wyden.published.booking.PositionSearch;
import io.wyden.published.common.CursorConnection;
import io.wyden.rest.management.booking.BookingEngineService;
import io.wyden.rest.management.booking.PositionToProtoMapper;
import io.wyden.rest.management.domain.PortfolioModel;
import io.wyden.rest.management.domain.PositionModel;
import io.wyden.rest.management.security.authentication.WydenAuthenticationToken;
import io.wyden.rest.management.security.documentation.SecurityNote;
import org.slf4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.ServletWebRequest;
import org.springframework.web.server.ResponseStatusException;

import java.util.Collection;
import java.util.List;
import java.util.Set;

import static org.slf4j.LoggerFactory.getLogger;

@RestController
@Validated
@RequestMapping(path = "${rest.management.context-path}")
public class PortfolioController {

    private static final Logger LOGGER = getLogger(PortfolioController.class);

    private final PortfolioService portfolioService;
    private final PortfolioRepository portfolioRepository;
    private final PortfolioPermissionValidator portfolioPermissionValidator;
    private final PortfolioQueryLimitValidator portfolioQueryLimitValidator;
    private final BookingEngineService bookingEngineService;

    public PortfolioController(PortfolioService portfolioService,
                               PortfolioRepository portfolioRepository,
                               PortfolioPermissionValidator portfolioPermissionValidator,
                               PortfolioQueryLimitValidator portfolioQueryLimitValidator,
                               BookingEngineService bookingEngineService) {
        this.portfolioService = portfolioService;
        this.portfolioRepository = portfolioRepository;
        this.portfolioPermissionValidator = portfolioPermissionValidator;
        this.portfolioQueryLimitValidator = portfolioQueryLimitValidator;
        this.bookingEngineService = bookingEngineService;
    }

    @GetMapping("/portfolios")
    public PaginationModel.CursorConnection<PortfolioModel.Portfolio> getPortfolios(PortfolioModel.PortfolioSearch search,
                                                                                    WydenAuthenticationToken authentication) {

        PortfolioModel.PortfolioSearch authorizedSearch = portfolioPermissionValidator.authorizeRequest(search, authentication);
        if(authorizedSearch == null) {
            return PaginationModel.emptyCursorConnection();
        }

        portfolioQueryLimitValidator.validate(search);

        LOGGER.info("Requesting paginated portfolios using search: {}", search);
        CursorConnection portfolioConnection = portfolioRepository.search(authorizedSearch);
        return PaginationProtoToModelMapper.map(portfolioConnection, node -> PortfolioFromProtoMapper.map(node.getPortfolio()));
    }

    @PostMapping("/portfolios")
    @PreAuthorize("@portfolioPermissionValidator.checkPortfolioCreate(#portfolio, #authentication, #servletRequest)")
    public PortfolioModel.Portfolio createPortfolio(@RequestBody PortfolioModel.PortfolioRequest portfolio,
                                                    WydenAuthenticationToken authentication,
                                                    ServletWebRequest servletRequest) {
        try {
            return portfolioService.createPortfolio(portfolio, authentication.getClientId());
        } catch (Exception e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Failed to create portfolio: " + e.getMessage());
        }
    }

    @GetMapping("/portfolios/{portfolioId}")
    @PreAuthorize("@portfolioPermissionValidator.checkPortfolioGetById(#portfolioId, #authentication, #servletRequest)")
    public PortfolioModel.Portfolio getPortfolioById(@PathVariable String portfolioId,
                                                     WydenAuthenticationToken authentication,
                                                     ServletWebRequest servletRequest) {
        return portfolioRepository.find(portfolioId)
            .map(PortfolioFromProtoMapper::map)
            .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Cannot find portfolio with id: " + portfolioId));
    }

    @GetMapping("/portfolios/{portfolioId}/positions")
    @PreAuthorize("@portfolioPermissionValidator.checkPortfolioGetById(#portfolioId, #authentication, #servletRequest)")
    public Collection<PositionModel.Position> getPositions(@PathVariable String portfolioId,
                                                           WydenAuthenticationToken authentication,
                                                           ServletWebRequest servletRequest) {
        if (!portfolioRepository.exists(portfolioId)) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Cannot find portfolio with id: " + portfolioId);
        }

        PaginationModel.CursorConnection<PositionModel.Position> positions = bookingEngineService.findPositionsByPortfolioId(portfolioId, authentication.getClientId());
        return positions.getAllNodes();
    }

    @GetMapping("/portfolios/{portfolioId}/balances")
    @PreAuthorize("@portfolioPermissionValidator.checkPortfolioGetById(#portfolioId, #authentication, #servletRequest)")
    public Collection<PositionModel.Balance> getBalances(@PathVariable String portfolioId,
                                                         WydenAuthenticationToken authentication,
                                                         ServletWebRequest servletRequest) {
        if (!portfolioRepository.exists(portfolioId)) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Cannot find portfolio with id: " + portfolioId);
        }

        PaginationModel.CursorConnection<PositionModel.Balance> balances = bookingEngineService.findBalancesByPortfolioId(portfolioId, authentication.getClientId());
        return balances.getAllNodes();
    }

    @PutMapping("/portfolios/{portfolioId}/tags")
    @PreAuthorize("@portfolioPermissionValidator.checkPortfolioManage(#portfolioId, #authentication, #servletRequest)")
    public PortfolioModel.Portfolio updateTags(@PathVariable String portfolioId,
                                               @RequestBody Collection<PortfolioModel.Tag> tags,
                                               WydenAuthenticationToken authentication,
                                               ServletWebRequest servletRequest) {
        return portfolioService.updateTags(portfolioId, tags, authentication.getClientId())
            .map(PortfolioFromProtoMapper::map)
            .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Cannot find portfolio with id: " + portfolioId));
    }

    @GetMapping("/portfolios/positions")
    @SecurityNote("Secured inside getPortfolios()")
    public PaginationModel.CursorConnection<PositionModel.Position> getPositions(PositionModel.PositionByPortfolioSearch search,
                                                                                 WydenAuthenticationToken authentication) {
        LOGGER.info("Requesting paginated portfolio positions using search: {}", search);

        PortfolioModel.PortfolioSearch portfolioSearch = new PortfolioModel.PortfolioSearch(
            search.name(),
            search.tagKey(),
            search.tagValue(),
            search.portfolioType(),
            Set.of(),
            search.first(),
            search.after());

        PaginationModel.CursorConnection<PortfolioModel.Portfolio> portfolioConnection = getPortfolios(portfolioSearch, authentication);
        Collection<PortfolioModel.Portfolio> portfolios = portfolioConnection.getAllNodes();
        if (portfolios.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Cannot find positions for portfolios in query: " + search);
        }

        List<String> portfolioIds = portfolios.stream()
            .map(PortfolioModel.Portfolio::id)
            .toList();

        PositionSearch positionSearch = PositionToProtoMapper.map(search, portfolioIds, authentication.getClientId());

        return bookingEngineService.searchPositions(positionSearch);
    }

    @GetMapping("/portfolios/balances")
    @SecurityNote("Secured inside getPortfolios()")
    public PaginationModel.CursorConnection<PositionModel.Balance> getBalances(PositionModel.PositionByPortfolioSearch search,
                                                                               WydenAuthenticationToken authentication,
                                                                               ServletWebRequest servletRequest) {
        LOGGER.info("Requesting paginated portfolio balances using search: {}", search);

        // we cannot apply first and after parameters from request - these need to be applied to booking reuqest
        PortfolioModel.PortfolioSearch portfolioSearch = new PortfolioModel.PortfolioSearch(
            search.name(),
            search.tagKey(),
            search.tagValue(),
            search.portfolioType(),
            Set.of(),
            null,
            null);

        PaginationModel.CursorConnection<PortfolioModel.Portfolio> portfolioConnection = getPortfolios(portfolioSearch, authentication);
        Collection<PortfolioModel.Portfolio> portfolios = portfolioConnection.getAllNodes();
        if (portfolios.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Cannot find balances for portfolios in query: " + search);
        }

        List<String> portfolioIds = portfolios.stream()
            .map(PortfolioModel.Portfolio::id)
            .toList();

        PositionSearch positionSearch = PositionToProtoMapper.map(search, portfolioIds, authentication.getClientId());

        return bookingEngineService.searchBalances(positionSearch);
    }
}
