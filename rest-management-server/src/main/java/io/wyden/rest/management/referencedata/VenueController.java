package io.wyden.rest.management.referencedata;

import io.wyden.published.referencedata.VenueCreateRequest;
import io.wyden.rest.management.domain.VenueModel;
import io.wyden.rest.management.security.authentication.WydenAuthenticationToken;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.ServletWebRequest;

@RestController
@Validated
@RequestMapping(path = "/api/v1")
public class VenueController {

    private final VenueService venueService;

    public VenueController(final VenueService venueService) {
        this.venueService = venueService;
    }

    @PostMapping("/venues")
    @PreAuthorize("@defaultPermissionValidator.hasPermission('venue', 'create', #authentication, #servletRequest)")
    public void createVenue(@RequestBody VenueModel.VenueCreateRequest request,
                            WydenAuthenticationToken authentication,
                            ServletWebRequest servletRequest) {
        VenueCreateRequest venueCreateRequest = VenueToProtoMapper.map(request, authentication.getClientId());
        venueService.create(venueCreateRequest);
    }
}
