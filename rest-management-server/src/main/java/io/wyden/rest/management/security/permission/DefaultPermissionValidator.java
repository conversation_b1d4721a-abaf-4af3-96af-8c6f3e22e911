package io.wyden.rest.management.security.permission;

import io.wyden.accessgateway.client.permission.PermissionChecker;
import io.wyden.rest.management.security.authentication.WydenAuthenticationToken;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.ServletWebRequest;

import javax.annotation.Nullable;
import java.io.Serializable;
import java.util.Collection;

import static io.wyden.rest.management.util.AccessDeniedLogger.logIfAccessDenied;

@Component("defaultPermissionValidator")
public class DefaultPermissionValidator {

    private static final Logger LOGGER = LoggerFactory.getLogger(DefaultPermissionValidator.class);

    private final PermissionChecker permissionChecker;

    public DefaultPermissionValidator(PermissionChecker accessService) {
        this.permissionChecker = accessService;
    }

    public boolean hasPermission(Object resourceObject, Object scopeObject, WydenAuthenticationToken token, ServletWebRequest servletRequest) {
        try {
            String resource = toResource(resourceObject);
            String scope = toScope(scopeObject);
            boolean result = permissionChecker.hasPermission(token.getRoles(), token.getGroups(), token.getClientId(), resource, scope);
            logIfAccessDenied(result, servletRequest, token, resource + "." +scope, "");
            return result;
        } catch (Exception ex) {
            LOGGER.warn("Permission check failed, returning Access Denied", ex);
            return false;
        }
    }

    public boolean hasPermission(@Nullable Serializable targetId, String resource, Object scopeObject, WydenAuthenticationToken token, ServletWebRequest servletRequest) {
        try {
            if (targetId == null) {
                return hasPermission(resource, scopeObject, token, servletRequest);
            } else if (targetId instanceof Collection<?> collection) {
                return collection.stream()
                    .allMatch(t -> hasPermission(toSerializable(t), resource, scopeObject, token, servletRequest));
            } else {
                String scope = toScope(scopeObject);
                String resourceId = String.valueOf(targetId);
                boolean result =  permissionChecker.hasPermission(token.getRoles(), token.getGroups(), token.getClientId(), resource, scope, resourceId);
                logIfAccessDenied(result, servletRequest, token, resource + "." +scope, resourceId);
                return result;
            }
        } catch (Exception ex) {
            LOGGER.warn("Permission check failed, returning Access Denied", ex);
            return false;
        }
    }

    private String toResource(Object resourceObject) {
        return objectToString("Resource", resourceObject);
    }

    private String toScope(Object scopeObject) {
        return objectToString("Scope", scopeObject);
    }

    private String objectToString(String name, Object resourceObject) {
        if (resourceObject == null) {
            throw new IllegalArgumentException("%s cannot be null".formatted(name));
        } else if (resourceObject instanceof String resourceString) {
            return resourceString;
        } else {
            throw new IllegalArgumentException("%s %s is not a string".formatted(name, resourceObject.getClass().getSimpleName()));
        }
    }

    private Serializable toSerializable(Object object) {
        if (object == null) {
            throw new IllegalArgumentException("ResourceId cannot be null");
        } else if (object instanceof Serializable serializable) {
            return serializable;
        } else {
            throw new IllegalArgumentException("ResourceId %s is not a Serializable".formatted(object.getClass().getSimpleName()));
        }
    }
}
