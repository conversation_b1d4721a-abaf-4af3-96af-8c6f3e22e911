package io.wyden.rest.management.infrastructure.hazelcast;

import com.hazelcast.client.HazelcastClient;
import com.hazelcast.client.config.ClientConfig;
import com.hazelcast.client.config.ClientNetworkConfig;
import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import io.wyden.cloudutils.hazelcast.HazelcastMapConfig;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.rate.client.RateDataProvider;
import io.wyden.rate.client.RatesCacheFacade;
import io.wyden.rate.domain.map.RateMapConfig;
import io.wyden.referencedata.client.CurrencyCacheFacade;
import io.wyden.referencedata.client.PortfoliosCacheFacade;
import io.wyden.referencedata.client.ReferenceDataProvider;
import io.wyden.referencedata.client.VenueAccountCacheFacade;
import io.wyden.referencedata.domain.CurrencyMapConfig;
import io.wyden.referencedata.domain.PortfolioMapConfig;
import io.wyden.referencedata.domain.VenueAccountMapConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.List;

@Configuration
public class HazelcastConfig {

    @Bean
    IMap<String, Long> noncePerApiKeyMap(HazelcastInstance hazelcast) {
        return ApiKeyToNonceMapConfig.getMap(hazelcast);
    }

    @Bean
    public ClientConfig createClientConfig(@Value("${hz.addressList}") String addressList,
                                           @Value("${hz.outboundPortDefinition}") String outboundPortDefinition,
                                           List<HazelcastMapConfig> hazelcastMaps) {

        ClientConfig clientConfig = new ClientConfig();
        clientConfig.setInstanceName("rest_management");
        clientConfig.getConnectionStrategyConfig().getConnectionRetryConfig().setMaxBackoffMillis(5000);

        ClientNetworkConfig networkConfig = clientConfig.getNetworkConfig();
        Arrays.stream(addressList.split(",")).forEach(networkConfig::addAddress);
        networkConfig.setSmartRouting(true);

        if (!outboundPortDefinition.isBlank()) {
            networkConfig.addOutboundPortDefinition(outboundPortDefinition);
        }

        networkConfig.setRedoOperation(true);
        networkConfig.setConnectionTimeout(5000);

        hazelcastMaps.forEach(m -> m.applyConfig(clientConfig));

        return clientConfig;
    }

    @Bean("hazelcast")
    public HazelcastInstance createHazelcastInstance(ClientConfig clientConfig,
                                                     Telemetry telemetry,
                                                     List<HazelcastMapConfig> hazelcastMaps) {
        HazelcastInstance hz = HazelcastClient.newHazelcastClient(clientConfig);
        hazelcastMaps.forEach(m -> m.setupClientInstance(hz));
        hazelcastMaps.forEach(m -> m.setupMetrics(hz, telemetry.getMeterRegistry()));
        return hz;
    }

    @Bean
    public PortfolioMapConfig portfolioMapConfig() {
        return new PortfolioMapConfig();
    }

    @Bean
    public PortfoliosCacheFacade portfoliosCacheFacade(HazelcastInstance hazelcast,
                                                       Tracing otlTracing) {
        return ReferenceDataProvider.getPortfoliosCacheFacade(hazelcast, otlTracing);
    }

    @Bean
    public VenueAccountMapConfig venueAccountMapConfig() {
        return new VenueAccountMapConfig();
    }

    @Bean
    public VenueAccountCacheFacade venueAccountCacheFacade(HazelcastInstance hazelcast,
                                                           Tracing otlTracing) {
        return ReferenceDataProvider.getVenueAccountCacheFacade(hazelcast, otlTracing);
    }

    @Bean
    public CurrencyMapConfig currencyMapConfig() {
        return new CurrencyMapConfig();
    }

    @Bean
    public CurrencyCacheFacade currencyCacheFacade(HazelcastInstance hazelcast,
                                                   Tracing otlTracing) {
        return ReferenceDataProvider.getCurrencyCacheFacade(hazelcast, otlTracing);
    }

    @Bean
    public RateMapConfig rateMapConfig() {
        return new RateMapConfig();
    }

    @Bean
    public RatesCacheFacade ratesCacheFacade(HazelcastInstance hazelcast,
                                             Tracing otlTracing) {
        return RateDataProvider.getRatesCacheFacade(hazelcast, otlTracing);
    }
}
