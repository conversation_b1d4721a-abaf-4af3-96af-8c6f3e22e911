package io.wyden.rest.management.reservation;

import io.wyden.published.booking.AccountCashTransferReservationSnapshot;
import io.wyden.published.booking.ClientCashTradeReservationSnapshot;
import io.wyden.published.booking.DepositReservationSnapshot;
import io.wyden.published.booking.Fee;
import io.wyden.published.booking.PortfolioCashTransferReservationSnapshot;
import io.wyden.published.booking.ReservationBalance;
import io.wyden.published.booking.ReservationBalanceList;
import io.wyden.published.booking.ReservationSnapshot;
import io.wyden.published.booking.StreetCashTradeReservationSnapshot;
import io.wyden.published.booking.TransactionType;
import io.wyden.published.booking.WithdrawalReservationSnapshot;
import io.wyden.rest.management.domain.ReservationModel;
import io.wyden.rest.management.domain.TransactionModel;
import io.wyden.rest.management.transaction.TransactionFromProtoMapper;
import org.slf4j.Logger;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

import static io.wyden.rest.management.common.ProtoMappingUtils.bd;
import static io.wyden.rest.management.common.ProtoMappingUtils.dt;
import static io.wyden.rest.management.domain.TransactionModel.TransactionType.ACCOUNT_CASH_TRANSFER;
import static io.wyden.rest.management.domain.TransactionModel.TransactionType.CLIENT_CASH_TRADE;
import static io.wyden.rest.management.domain.TransactionModel.TransactionType.DEPOSIT;
import static io.wyden.rest.management.domain.TransactionModel.TransactionType.PORTFOLIO_CASH_TRANSFER;
import static io.wyden.rest.management.domain.TransactionModel.TransactionType.STREET_CASH_TRADE;
import static io.wyden.rest.management.domain.TransactionModel.TransactionType.UNSPECIFIED;
import static io.wyden.rest.management.domain.TransactionModel.TransactionType.WITHDRAWAL;
import static org.slf4j.LoggerFactory.getLogger;

public final class ReservationFromProtoMapper {

    private static final Logger LOGGER = getLogger(ReservationFromProtoMapper.class);

    private ReservationFromProtoMapper() {
    }

    public static ReservationModel.Reservation map(ReservationSnapshot reservation) {
        if (reservation == null) {
            return null;
        }

        if (reservation.hasDepositReservation()) {
            DepositReservationSnapshot snapshot = reservation.getDepositReservation();
            return new ReservationModel.DepositReservation(
                snapshot.getReservationRef(),
                map(snapshot.getTransactionType()),
                dt(snapshot.getDateTime()),
                snapshot.getCurrency(),
                bd(snapshot.getQuantity()),
                snapshot.getPortfolioId(),
                snapshot.getAccountId(),
                snapshot.getFeePortfolioId(),
                snapshot.getFeeAccountId(),
                map(snapshot.getReservationFeeList())
            );
        }

        if (reservation.hasWithdrawalReservation()) {
            WithdrawalReservationSnapshot snapshot = reservation.getWithdrawalReservation();
            return new ReservationModel.WithdrawalReservation(
                snapshot.getReservationRef(),
                map(snapshot.getTransactionType()),
                dt(snapshot.getDateTime()),
                snapshot.getCurrency(),
                bd(snapshot.getQuantity()),
                snapshot.getPortfolioId(),
                snapshot.getAccountId(),
                snapshot.getFeePortfolioId(),
                snapshot.getFeeAccountId(),
                map(snapshot.getReservationFeeList())
            );
        }

        if (reservation.hasClientCashTradeReservation()) {
            ClientCashTradeReservationSnapshot snapshot = reservation.getClientCashTradeReservation();
            return new ReservationModel.ClientCashTradeReservation(
                snapshot.getReservationRef(),
                map(snapshot.getTransactionType()),
                dt(snapshot.getDateTime()),
                snapshot.getCurrency(),
                snapshot.getBaseCurrency(),
                bd(snapshot.getQuantity()),
                bd(snapshot.getPrice()),
                bd(snapshot.getStopPrice()),
                snapshot.getPortfolioId(),
                snapshot.getCounterPortfolioId(),
                map(snapshot.getReservationFeeList())
            );
        }

        if (reservation.hasStreetCashTradeReservation()) {
            StreetCashTradeReservationSnapshot snapshot = reservation.getStreetCashTradeReservation();
            return new ReservationModel.StreetCashTradeReservation(
                snapshot.getReservationRef(),
                map(snapshot.getTransactionType()),
                dt(snapshot.getDateTime()),
                snapshot.getCurrency(),
                snapshot.getBaseCurrency(),
                bd(snapshot.getQuantity()),
                bd(snapshot.getPrice()),
                bd(snapshot.getStopPrice()),
                snapshot.getPortfolioId(),
                snapshot.getAccountId(),
                map(snapshot.getReservationFeeList())
            );
        }

        if (reservation.hasAccountCashTransferReservation()) {
            AccountCashTransferReservationSnapshot snapshot = reservation.getAccountCashTransferReservation();
            return new ReservationModel.AccountCashTransferReservation(
                snapshot.getReservationRef(),
                map(snapshot.getTransactionType()),
                dt(snapshot.getDateTime()),
                snapshot.getCurrency(),
                bd(snapshot.getQuantity()),
                snapshot.getFromAccountId(),
                snapshot.getToAccountId(),
                snapshot.getFeeAccountId(),
                snapshot.getFeePortfolioId(),
                map(snapshot.getReservationFeeList())
            );
        }

        if (reservation.hasPortfolioCashTransferReservation()) {
            PortfolioCashTransferReservationSnapshot snapshot = reservation.getPortfolioCashTransferReservation();
            return new ReservationModel.PortfolioCashTransferReservation(
                snapshot.getReservationRef(),
                map(snapshot.getTransactionType()),
                dt(snapshot.getDateTime()),
                snapshot.getCurrency(),
                bd(snapshot.getQuantity()),
                snapshot.getFromPortfolioId(),
                snapshot.getToPortfolioId(),
                snapshot.getFeePortfolioId(),
                map(snapshot.getReservationFeeList())
            );
        }

        LOGGER.warn("Cannot find mapping (proto -> model) for reservation: {}", reservation);
        return null;
    }

    public static Collection<ReservationModel.ReservationBalance> map(ReservationBalanceList reservationBalances) {
        if (reservationBalances == null || reservationBalances.getReservationBalanceCount() == 0) {
            return Collections.emptyList();
        }

        return reservationBalances.getReservationBalanceList().stream()
            .map(ReservationFromProtoMapper::map)
            .toList();
    }

    private static ReservationModel.ReservationBalance map(ReservationBalance reservationBalance) {
        if (reservationBalance == null) {
            return null;
        }

        return new ReservationModel.ReservationBalance(
            bd(reservationBalance.getQuantity()),
            reservationBalance.getCurrency(),
            reservationBalance.getPortfolioId(),
            reservationBalance.getAccountId());
    }

    private static TransactionModel.TransactionType map(TransactionType transactionType) {
        if (transactionType == null) {
            return null;
        }

        return switch (transactionType) {
            case TRANSACTION_TYPE_CLIENT_CASH_TRADE -> CLIENT_CASH_TRADE;
            case TRANSACTION_TYPE_STREET_CASH_TRADE -> STREET_CASH_TRADE;
            case TRANSACTION_TYPE_PORTFOLIO_CASH_TRANSFER -> PORTFOLIO_CASH_TRANSFER;
            case TRANSACTION_TYPE_ACCOUNT_CASH_TRANSFER -> ACCOUNT_CASH_TRANSFER;
            case TRANSACTION_TYPE_DEPOSIT -> DEPOSIT;
            case TRANSACTION_TYPE_WITHDRAWAL -> WITHDRAWAL;
            default -> UNSPECIFIED;
        };
    }

    public static Collection<ReservationModel.ReservationFee> map(List<Fee> fees) {
        if (fees == null) {
            return null;
        }

        return fees.stream()
            .map(fee -> new ReservationModel.ReservationFee(
                bd(fee.getAmount()),
                fee.getCurrency(),
                TransactionFromProtoMapper.map(fee.getFeeType())))
            .toList();
    }
}
