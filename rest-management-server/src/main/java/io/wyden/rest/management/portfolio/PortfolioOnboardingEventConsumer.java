package io.wyden.rest.management.portfolio;

import com.rabbitmq.client.AMQP;
import io.wyden.cloud.utils.spring.util.ExclusiveNameGenerator;
import io.wyden.cloudutils.rabbitmq.ConsumptionResult;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.queue.ExpiringRabbitQueueBuilder;
import io.wyden.cloudutils.rabbitmq.queue.MessageConsumer;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueue;
import io.wyden.published.referencedata.PortfolioOnboardingPositionCreatedEvent;
import jakarta.annotation.PostConstruct;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;

import java.time.Duration;
import java.util.Arrays;
import java.util.Objects;

import static org.slf4j.LoggerFactory.getLogger;

@Component
public class PortfolioOnboardingEventConsumer implements MessageConsumer<PortfolioOnboardingPositionCreatedEvent> {

    private static final Logger LOGGER = getLogger(PortfolioOnboardingEventConsumer.class);

    private final String queueName;
    private final String consumerName;
    private final RabbitIntegrator rabbitIntegrator;
    private final RabbitExchange<PortfolioOnboardingPositionCreatedEvent> portfolioOnboardingPositionCreatedEventExchange;
    private final Sinks.Many<PortfolioOnboardingPositionCreatedEvent> onboardingEventsSink;

    public PortfolioOnboardingEventConsumer(@Value("${rabbitmq.booking-engine-rest-management-queue.portfolio-onboarding-event}") final String queueNameFormat,
                                            ExclusiveNameGenerator queueNameGenerator,
                                            @Value("${spring.application.name}") String consumerName,
                                            RabbitIntegrator rabbitIntegrator,
                                            RabbitExchange<PortfolioOnboardingPositionCreatedEvent> portfolioOnboardingPositionCreatedEventExchange) {
        this.queueName = queueNameGenerator.getQueueName(queueNameFormat);
        this.consumerName = consumerName;
        this.rabbitIntegrator = rabbitIntegrator;
        this.portfolioOnboardingPositionCreatedEventExchange = portfolioOnboardingPositionCreatedEventExchange;
        this.onboardingEventsSink = Sinks.many().replay().limit(100, Duration.ofMinutes(1));
    }

    @PostConstruct
    void init() {
        declareQueue();
        cleanupOldQueues(
            "rest-management-queue.booking-engine.PORTFOLIO-ONBOARDING-EVENT"
        );
    }

    private void cleanupOldQueues(String... legacyQueueNames) {
        Arrays.stream(legacyQueueNames).forEach(rabbitIntegrator::deleteQueueIfUnused);
    }

    @Override
    public ConsumptionResult consume(PortfolioOnboardingPositionCreatedEvent portfolioOnboardingEvent, AMQP.BasicProperties basicProperties) {
        try {
            LOGGER.info("Consuming PortfolioOnboardingPositionCreatedEvent message, {}", portfolioOnboardingEvent);

            Sinks.EmitResult emitResult = onboardingEventsSink.tryEmitNext(portfolioOnboardingEvent);
            if (emitResult.isFailure()) {
                LOGGER.error("Failed to emit consumed onboarding event: {}, re-queueing event: {}", emitResult, portfolioOnboardingEvent);
                return ConsumptionResult.failureNeedsRequeue();
            }

        } catch (Exception e) {
            LOGGER.error("Failed to consume incoming: %s, re-queueing...".formatted(portfolioOnboardingEvent), e);
            return ConsumptionResult.failureNeedsRequeue();
        }

        return ConsumptionResult.consumed();
    }

    public Mono<PortfolioOnboardingPositionCreatedEvent> getOnboardingEvent(String correlationId) {
        return onboardingEventsSink.asFlux()
            .filter(event -> Objects.equals(event.getMetadata().getCorrelationObject(), correlationId))
            .next();
    }

    private void declareQueue() {
        RabbitQueue<PortfolioOnboardingPositionCreatedEvent> queue = new ExpiringRabbitQueueBuilder<PortfolioOnboardingPositionCreatedEvent>(rabbitIntegrator)
            .setQueueName(queueName)
            .setConsumerName(consumerName)
            .declare();

        queue.bindWithRoutingKey(portfolioOnboardingPositionCreatedEventExchange, StringUtils.EMPTY);
        LOGGER.info("Binding exchange {} and queue {}", portfolioOnboardingPositionCreatedEventExchange, queue);
        queue.attachConsumer(PortfolioOnboardingPositionCreatedEvent.parser(), this);
    }
}
