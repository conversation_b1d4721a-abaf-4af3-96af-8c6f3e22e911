package io.wyden.quotingorderservice.service.fsm;

import io.opentelemetry.api.trace.Span;
import io.wyden.quotingorderservice.service.fsm.StatusNew;
import io.wyden.quotingorderservice.service.oems.outbound.OemsResponseFactory;
import io.wyden.published.oems.OemsOrderStatus;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Intermediate state, coexists with other intermediate states, but always takes precedence before them.
 * Emits CancelReject on cancel requests.
 * Order moves to PendingCancel when order in PendingNew, New, PartialFill receives Cancel request.
 * Can transition to:
 *  - PendingNew, New, PartialFill - on CancelReject connector messages
 *  - Filled - on trade ExecutionReport fulfilling the order
 *  - Cancelled - on CANCELED ExecutionReport
 * Trade execution orders not fulfilling the order do not change the state, but update remainingQty
 * and underlying state.
 */
class StatusPendingCancel extends StatusNew {

    private static final Logger LOGGER = LoggerFactory.getLogger(StatusPendingCancel.class);

    private static final OrderStatus instance = new StatusPendingCancel();

    static OrderStatus create() {
        return instance;
    }

    @Override
    void onCancelReject(OrderContext context, OemsResponse venueCancelReject) {
        if (context.getOrderState().isForceCancel()) {
            LOGGER.info("Ignoring CancelReject '{}' for force cancelled order. Current OrderState: {}", venueCancelReject.getMetadata().getRequestId(), context.getOrderState());
            return;
        }

        String requestId = venueCancelReject.getMetadata().getInResponseToRequestId();
        OrderState orderState = context.getOrderState();
        if (orderState.getPendingCancelRequestId().equals(requestId)) {
            LOGGER.debug("Cancel requestId matches - removing PendingCancel state");
            String cancelRequestId = orderState.removeStatusPendingCancel();
            context.emit(OemsResponseFactory.createCancelRejectViaExternalSystem(venueCancelReject, context.getOrderState(), cancelRequestId));
        } else {
            LOGGER.warn("RequestId '{}' didn't match pending cancel requestId '{}' - discarding.", requestId, orderState.getPendingCancelRequestId());
            Span.current().addEvent("RequestId didn't match pending cancel requestId");
        }
    }

    @Override
    void onCancelNotDelivered(OrderContext context, OemsRequest venueCancel) {
        OrderState orderState = context.getOrderState();
        String cancelRequestId = orderState.removeStatusPendingCancel();
        context.emit(OemsResponseFactory.createCancelRejectNoConnector(
            cancelRequestId,
            context.getOrderState().getClientId(),
            context.getOrderState()));
    }

    @Override
    void onRejected(OrderContext context, OemsResponse report) {
        LOGGER.warn("Received ExecType.REJECTED inconsistent with OrderState. Current OrderState: {}", context.getOrderState());
    }

    @Override
    Precedence getPrecedence() {
        return Precedence.PENDING_CANCEL;
    }

    @Override
    OemsOrderStatus toOemsOrderStatus() {
        return OemsOrderStatus.STATUS_PENDING_CANCEL;
    }
}
