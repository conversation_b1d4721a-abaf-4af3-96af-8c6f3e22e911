package io.wyden.rate.it;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hazelcast.config.Config;
import com.hazelcast.core.Hazelcast;
import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.pricing.client.ClientSideSubscriptionCacheFacade;
import io.wyden.published.marketdata.MarketDataEvent;
import io.wyden.published.rate.ConversionSource;
import io.wyden.published.rate.ConversionSourceList;
import io.wyden.published.rate.Rate;
import io.wyden.published.rate.RateEvent;
import io.wyden.published.rate.RateKey;
import io.wyden.published.rate.RateSubscription;
import io.wyden.rate.client.MarketDataCacheFacade;
import io.wyden.rate.client.RatesCacheFacade;
import io.wyden.rate.domain.map.MarketDataKey;
import io.wyden.rate.infrastructure.rabbit.RateDestinationsConfig;
import io.wyden.rate.interfaces.web.AdminDashboardApiController;
import io.wyden.rate.interfaces.web.rates.RateController;
import io.wyden.rate.service.ConversionSourceRepository;
import io.wyden.rate.service.RateSubscriptionEnrichmentService;
import io.wyden.rate.service.RateSubscriptionRepository;
import io.wyden.rate.service.SubscriptionHealthMonitor;
import io.wyden.rate.utils.ConnectorWrapper;
import io.wyden.rate.utils.PricingService;
import io.wyden.referencedata.client.InstrumentsCacheFacade;
import io.wyden.referencedata.client.VenueAccountCacheFacade;
import org.awaitility.Awaitility;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.boot.test.util.TestPropertyValues;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.http.converter.protobuf.ProtobufHttpMessageConverter;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.testcontainers.containers.RabbitMQContainer;
import org.testcontainers.utility.DockerImageName;

import java.time.Duration;
import java.util.UUID;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ContextConfiguration(initializers = RateServiceIntegrationTestBase.Initializer.class)
@SpringBootTest(properties = {"spring.main.allow-bean-definition-overriding=true"})
@TestPropertySource(locations = "classpath:integration-test.properties")
@AutoConfigureMockMvc
public abstract class RateServiceIntegrationTestBase {

    public static final RabbitMQContainer RABBIT_MQ = new RabbitMQContainer(DockerImageName.parse("docker.wyden.io/mirror/rabbitmq:3.12-management")
        .asCompatibleSubstituteFor("rabbitmq:management"))
        .withReuse(true);
    private static final String MARKET_DATA_MAP = "rate-service-market-data_0.1";
    private static final String RATE_MAP = "rate-service-rates_0.1";
    private static final String RATE_SUBSCRIPTION_MAP = "rate-service-rate-subscription_0.1";
    private static final String CONVERSION_SOURCE_MAP = "rate-service-conversion-source-list_0.1";

    static final String INSTRUMENT_ID = "instrument_id";
    static final String INSTRUMENT_ID_2 = "instrument_id_2";
    static final String INSTRUMENT_ID_3 = "instrument_id_3";
    static final String PORTFOLIO_ID = "portfolio_id";
    static final String STREAM_ID = "stream_id";
    static final String STREAM_ID_2 = "stream_id_2";
    static final String STREAM_ID_3 = "stream_id_3";
    static final String STREAM_ID_4 = "stream_id_4";
    static final String BASE_CURRENCY = "IOP";
    static final String QUOTE_CURRENCY = "JKL";
    static final String ADAPTER_TICKER = BASE_CURRENCY + QUOTE_CURRENCY;
    static final Duration TIMEOUT = Duration.ofSeconds(3);
    static final Duration SUBSCRIPTION_INACTIVE_TIMEOUT = Duration.ofSeconds(10);
    static final Duration TIMEOUT_RATE_NOT_DISCOVERED = Duration.ofSeconds(1);
    public static final String VENUE_ACCOUNT = "venue_account";
    public static final String VENUE_ACCOUNT_2 = "venue_account2";
    static final String RATE_DISCOVERY_SOURCE_TIMEOUT = "1s";
    static final String HEARTBEAT_INTERVAL = "2s";
    static final String INACTIVE_SUBSCRIPTION_TIMEOUT = "3s";
    static final String REPEAT_RATE_DISCOVERY_INTERVAL = "1s";
    static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    static {
        RABBIT_MQ.start();
        Awaitility.setDefaultPollDelay(Duration.ZERO);
        Awaitility.setDefaultPollInterval(Duration.ofMillis(10));
        Awaitility.setDefaultTimeout(Duration.ofMinutes(5));
    }

    @Autowired
    RabbitIntegrator rabbitIntegrator;

    @Autowired
    RateDestinationsConfig rateDestinationsConfig;

    @Autowired
    RabbitExchange<MarketDataEvent> marketDataExchange;

    @Autowired
    MarketDataCacheFacade marketDataCacheFacade;

    @Autowired
    RatesCacheFacade ratesCacheFacade;

    @Autowired
    HazelcastInstance hz;

    @Autowired
    RateSubscriptionRepository rateSubscriptionRepository;

    @Autowired
    ConversionSourceRepository conversionSourceRepository;

    @Autowired
    SubscriptionHealthMonitor subscriptionHealthMonitor;

    @Autowired
    MockMvc mockMvc;

    @Autowired
    RateController rateController;

    @SpyBean
    RateSubscriptionEnrichmentService rateSubscriptionEnrichmentService;

    @MockBean
    InstrumentsCacheFacade instrumentsCacheFacade;

    @MockBean
    VenueAccountCacheFacade venueAccountCacheFacade;

    @Autowired
    AdminDashboardApiController adminDashboardApiController;

    PricingService pricingService;
    ConnectorWrapper connectorWrapper;
    ConnectorWrapper secondConnectorWrapper;

    @BeforeEach
    void setup() {
        pricingService = new PricingService(marketDataExchange);
        connectorWrapper = new ConnectorWrapper(rateDestinationsConfig, rabbitIntegrator, VENUE_ACCOUNT);
        secondConnectorWrapper = new ConnectorWrapper(rateDestinationsConfig, rabbitIntegrator, VENUE_ACCOUNT_2);
    }

    @AfterEach
    void tearDown() {
        hz.getMap(MARKET_DATA_MAP).clear();
        hz.getMap(RATE_MAP).clear();
        hz.getMap(RATE_SUBSCRIPTION_MAP).clear();
        hz.getMap(CONVERSION_SOURCE_MAP).clear();
        connectorWrapper.cleanup();
        secondConnectorWrapper.cleanup();
    }

    public static class Initializer implements ApplicationContextInitializer<ConfigurableApplicationContext> {

        @Override
        public void initialize(@NotNull ConfigurableApplicationContext applicationContext) {
            var values = TestPropertyValues.of(
                "rabbitmq.host=" + RABBIT_MQ.getHost(),
                "rabbitmq.port=" + RABBIT_MQ.getMappedPort(5672),
                "rabbitmq.username=" + RABBIT_MQ.getAdminUsername(),
                "rabbitmq.password=" + RABBIT_MQ.getAdminPassword(),
                "rate.subscription.heartbeat-interval=" + HEARTBEAT_INTERVAL,
                "rate.subscription.rate-discovery-source-timeout=" + RATE_DISCOVERY_SOURCE_TIMEOUT,
                "rate.subscription.inactive-subscription-timeout=" + INACTIVE_SUBSCRIPTION_TIMEOUT,
                "rate.subscription.repeat-rate-discovery-interval=" + REPEAT_RATE_DISCOVERY_INTERVAL,
                "rate.subscriptions.default.load=false"
            );

            values.applyTo(applicationContext);
        }
    }

    @TestConfiguration
    static class TestHazelcastConfiguration {

        @Primary
        @Bean("hazelcast")
        HazelcastInstance createHazelcastInstance() {
            Config config = new Config();
            config.setClusterName(UUID.randomUUID().toString());
            return Hazelcast.newHazelcastInstance(config);
        }

        @Primary
        @Bean
        MarketDataCacheFacade marketDataCacheFacade(HazelcastInstance hazelcastInstance) {
            IMap<MarketDataKey, MarketDataEvent> marketDataMap = hazelcastInstance.getMap(MARKET_DATA_MAP);
            ClientSideSubscriptionCacheFacade clientSideSubscriptionCacheFacade = mock(ClientSideSubscriptionCacheFacade.class);
            when(clientSideSubscriptionCacheFacade.findStreamId(INSTRUMENT_ID, PORTFOLIO_ID)).thenReturn(STREAM_ID);
            when(clientSideSubscriptionCacheFacade.findStreamId(INSTRUMENT_ID_2, PORTFOLIO_ID)).thenReturn(STREAM_ID_2);
            when(clientSideSubscriptionCacheFacade.findStreamId(INSTRUMENT_ID_3, PORTFOLIO_ID)).thenReturn(STREAM_ID_4);
            return new MarketDataCacheFacade(marketDataMap, clientSideSubscriptionCacheFacade, mock(Tracing.class));
        }

        @Bean
        RatesCacheFacade ratesCacheFacade(HazelcastInstance hazelcastInstance) {
            IMap<RateKey, Rate> rateMap = hazelcastInstance.getMap(RATE_MAP);
            return new RatesCacheFacade(rateMap, mock(Tracing.class));
        }

        @Bean
        RateSubscriptionRepository rateSubscriptionRepository(HazelcastInstance hazelcastInstance) {
            IMap<String, RateSubscription> rateMap = hazelcastInstance.getMap(RATE_SUBSCRIPTION_MAP);
            return new RateSubscriptionRepository(rateMap);
        }

        @Bean
        ConversionSourceRepository conversionSourceRepository(HazelcastInstance hazelcastInstance) {
            IMap<String, ConversionSourceList> rateMap = hazelcastInstance.getMap(CONVERSION_SOURCE_MAP);
            return new ConversionSourceRepository(rateMap);
        }

        @Bean
        public ProtobufHttpMessageConverter protobufHttpMessageConverter() {
            return new ProtobufHttpMessageConverter();
        }
    }

    RateEvent getRateEvent() {
        return RateEvent.newBuilder()
            .setBaseCurrency(BASE_CURRENCY)
            .setQuoteCurrency(QUOTE_CURRENCY)
            .setValue(String.valueOf(Math.random() * 5))
            .build();
    }

    ConversionSource getConversionSource() {
        return ConversionSource.newBuilder()
            .setVenueAccount(VENUE_ACCOUNT)
            .build();
    }

    static ConversionSource getSecondConversionSource() {
        return ConversionSource.newBuilder()
            .setVenueAccount(VENUE_ACCOUNT_2)
            .build();
    }

    static ConversionSource getInvalidConversionSource() {
        return ConversionSource.newBuilder()
            .setVenueAccount("invalid")
            .build();
    }

    static RateSubscription getRateSubscription() {
        return RateSubscription.newBuilder()
            .setBaseCurrency(BASE_CURRENCY)
            .setQuoteCurrency(QUOTE_CURRENCY)
            .setVenueAccount(VENUE_ACCOUNT)
            .setAdapterTicker(ADAPTER_TICKER)
            .build();
    }

    static RateSubscription getRateSubscriptionWithVA2() {
        return getRateSubscription().toBuilder()
            .setVenueAccount(VENUE_ACCOUNT_2)
            .build();
    }
}
