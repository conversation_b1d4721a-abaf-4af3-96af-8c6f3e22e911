package io.wyden.clob.gateway.domain.map;

import com.hazelcast.config.Config;
import com.hazelcast.config.MapStoreConfig;
import com.hazelcast.config.SerializationConfig;
import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import io.wyden.cloudutils.hazelcast.HazelcastMapConfig;
import io.wyden.published.brokerdesk.ClobConfig;
import jakarta.annotation.Nullable;

import static io.wyden.cloudutils.hazelcast.Serializers.protobufSerializer;

public class ClobConfigMapConfig extends HazelcastMapConfig {

    private static final String MAP_NAME = "clob-config_v0.1";

    public static IMap<String, ClobConfig> getMap(HazelcastInstance hazelcastInstance) {
        return hazelcastInstance.getMap(MAP_NAME);
    }

    @Override
    public String getMapName() {
        return MAP_NAME;
    }

    @Override
    protected void addSerializersConfig(SerializationConfig serializationConfig) {
        serializationConfig.addSerializerConfig(protobufSerializer(ClobConfig.class, ClobConfig.parser()));
    }

    @Override
    protected void addMapConfig(Config config, @Nullable MapStoreConfig mapStoreConfig) {
        super.addMapConfig(config, mapStoreConfig);
    }
}
