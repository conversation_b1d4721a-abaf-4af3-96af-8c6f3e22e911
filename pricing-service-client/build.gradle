plugins {
    id 'java-library'
    id 'maven-publish'
    alias dependencyCatalog.plugins.sonarqube
    alias dependencyCatalog.plugins.jacocoToCobertura
}

def versionPropsFile = file('version.properties')
if (versionPropsFile.canRead()) {
    Properties versionProps = new Properties()
    versionProps.load(new FileInputStream(versionPropsFile))
    def ver = versionProps['VERSION'].toString()
    project.version = ver
} else {
    throw new GradleException("Could not read version.properties!")
}

dependencies {
    api project(':pricing-service-domain')
    implementation dependencyCatalog.published.language.oems
    implementation dependencyCatalog.cloud.utils.telemetry
    implementation dependencyCatalog.cloud.utils.hazelcast
    implementation dependencyCatalog.cloud.utils.tools

    implementation dependencyCatalog.hazelcast
    implementation dependencyCatalog.hazelcast.jet.protobuf
    implementation dependencyCatalog.jakarta.annotation

    testImplementation dependencyCatalog.assertj.core
    testImplementation dependencyCatalog.cloud.utils.test
    testImplementation dependencyCatalog.junit.jupiter.api
    testImplementation dependencyCatalog.junit.jupiter.params
    testImplementation dependencyCatalog.mockito.junit.jupiter
}

publishing {
    publications {
        mavenJava(MavenPublication) {
            from components.java
        }
    }
    repositories {
        maven {
            name 'nexus-snapshots'
            url 'https://repo.wyden.io/nexus/repository/snapshots/'
            credentials {
                username repository_username
                password repository_password
            }
        }
    }
}

sonarqube {
    properties {
        property "sonar.projectKey", "pricing-service-client"
        property "sonar.projectName", "Pricing Service Client"
    }
}

testing {
    suites {
        test {
            useJUnitJupiter()
        }
    }
}
