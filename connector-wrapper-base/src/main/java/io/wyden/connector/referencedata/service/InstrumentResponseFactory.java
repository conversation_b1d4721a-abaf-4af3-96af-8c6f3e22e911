package io.wyden.connector.referencedata.service;

import ch.algotrader.api.domain.security.ForexDTO;
import ch.algotrader.api.domain.security.SecurityDTO;
import ch.algotrader.api.domain.security.SecurityIdentity;

import io.wyden.connector.utils.NumberUtils;
import io.wyden.published.referencedata.AssetClass;
import io.wyden.published.referencedata.BaseInstrument;
import io.wyden.published.referencedata.ForexSpotProperties;
import io.wyden.published.referencedata.Instrument;
import io.wyden.published.referencedata.InstrumentIdentifiers;
import io.wyden.published.referencedata.TradingConstraints;
import io.wyden.published.referencedata.VenueType;

import java.util.Map;
import java.util.Objects;

import static org.apache.commons.lang3.BooleanUtils.isTrue;
import static org.apache.commons.lang3.ObjectUtils.firstNonNull;
import static org.apache.commons.lang3.StringUtils.EMPTY;

class InstrumentResponseFactory {

    private static final String ADAPTER_TICKER = "adapterTicker";
    private static final String TRADING_VIEW_ID = "tradingViewId";

    static Instrument toInstrumentResponse(SecurityDTO securityDTO) {
        Instrument.Builder builder = Instrument.newBuilder();
        setBaseInstrument(securityDTO, builder);
        setInstrumentIdentifiers(securityDTO, builder);
        setTradingConstraints(securityDTO, builder);
        setAssetClassSpecificParameters(securityDTO, builder);
        return builder.build();
    }

    private static void setBaseInstrument(SecurityDTO securityDTO, Instrument.Builder builder) {
        BaseInstrument.Builder baseInstrumentBuilder = BaseInstrument.newBuilder();

        baseInstrumentBuilder.setAssetClass(toAssetClass(securityDTO));
        baseInstrumentBuilder.setDescription(firstNonNull(securityDTO.getDescription(), EMPTY));
        baseInstrumentBuilder.setQuoteCurrency(firstNonNull(securityDTO.getQuoteCurrency(), EMPTY));
        baseInstrumentBuilder.setSettlementCurrency(firstNonNull(securityDTO.getSettlementCurrency(), EMPTY));
        baseInstrumentBuilder.setInverseContract(isTrue(securityDTO.isInverseContract()));
        baseInstrumentBuilder.setVenueType(VenueType.STREET);

        builder.setBaseInstrument(baseInstrumentBuilder.build());
    }

    private static void setInstrumentIdentifiers(SecurityDTO securityDTO, Instrument.Builder responseBuilder) {
        if (Objects.isNull(securityDTO.getSecurityIdentity())) {
            return;
        }
        InstrumentIdentifiers.Builder builder = InstrumentIdentifiers.newBuilder();
        SecurityIdentity securityIdentity = securityDTO.getSecurityIdentity();
        if (Objects.nonNull(securityIdentity.getIdentifiers())) {
            Map<String, String> identifiers = securityIdentity.getIdentifiers();
            builder.setAdapterTicker(identifiers.getOrDefault(ADAPTER_TICKER, EMPTY));
            builder.setTradingViewId(identifiers.getOrDefault(TRADING_VIEW_ID, EMPTY));
        }
        builder.setCnpid(firstNonNull(securityIdentity.getCnpid(), EMPTY));
        builder.setIsin(firstNonNull(securityIdentity.getIsin(), EMPTY));
        builder.setBbgid(firstNonNull(securityIdentity.getBbgid(), EMPTY));
        builder.setConid(firstNonNull(securityIdentity.getConid(), EMPTY));
        builder.setLmaxid(firstNonNull(securityIdentity.getLmaxid(), EMPTY));
        builder.setTtid(firstNonNull(securityIdentity.getTtid(), EMPTY));
        builder.setXntid(firstNonNull(securityIdentity.getXntid(), EMPTY));
        builder.setEgmid(firstNonNull(securityIdentity.getEgmid(), EMPTY));
        builder.setNasdaqDlDatabase(firstNonNull(securityIdentity.getNasdaqDLDatabase(), EMPTY));
        builder.setNasdaqDlDataset(firstNonNull(securityIdentity.getNasdaqDLDataset(), EMPTY));
        builder.setKksWebsocketTicker(firstNonNull(securityIdentity.getKksWebsocketTicker(), EMPTY));
        builder.setCombinationTicker(firstNonNull(securityIdentity.getCombinationTicker(), EMPTY));
        builder.setPrimeXmId(firstNonNull(securityIdentity.getPrimeXmId(), EMPTY));
        builder.setRic(firstNonNull(securityIdentity.getRic(), EMPTY));

        responseBuilder.setInstrumentIdentifiers(builder.build());
    }

    private static void setTradingConstraints(SecurityDTO securityDTO, Instrument.Builder responseBuilder) {
        if (Objects.isNull(securityDTO.getTradingConstraints())) {
            return;
        }
        TradingConstraints.Builder builder = TradingConstraints.newBuilder();
        ch.algotrader.api.connector.referencedata.TradingConstraints constraints = securityDTO.getTradingConstraints();
        builder.setMinQty(NumberUtils.toString(constraints.getMinQty()));
        builder.setMaxQty(NumberUtils.toString(constraints.getMaxQty()));
        builder.setQtyIncr(NumberUtils.toString(constraints.getQtyIncr()));
        builder.setMinPrice(NumberUtils.toString(constraints.getMinPrice()));
        builder.setMaxPrice(NumberUtils.toString(constraints.getMaxPrice()));
        builder.setPriceIncr(NumberUtils.toString(constraints.getPriceIncr()));
        builder.setMinNotional(NumberUtils.toString(constraints.getMinNotional()));
        builder.setPriceScale(NumberUtils.toString(constraints.getPriceScale()));
        builder.setQtyScale(NumberUtils.toString(constraints.getQtyScale()));
        builder.setContractSize(NumberUtils.toString(constraints.getContractSize()));
        builder.setTradeable(isTrue(constraints.isTradeable()));

        responseBuilder.setTradingConstraints(builder.build());
    }

    private static void setAssetClassSpecificParameters(SecurityDTO securityDTO, Instrument.Builder builder) {
        if (securityDTO instanceof ForexDTO forexDto) {
            if (Objects.nonNull(forexDto.getBaseCurrency())) {
                builder.setForexSpotProperties(ForexSpotProperties.newBuilder().setBaseCurrency(forexDto.getBaseCurrency()));
            }
            //TODO define and fill specific parameters for each asset class
        }
    }

    private static AssetClass toAssetClass(SecurityDTO securityDTO) {
        if (securityDTO instanceof ForexDTO) {
            return AssetClass.FOREX;
        } else {
            return AssetClass.UNRECOGNIZED;
        }
    }
}
