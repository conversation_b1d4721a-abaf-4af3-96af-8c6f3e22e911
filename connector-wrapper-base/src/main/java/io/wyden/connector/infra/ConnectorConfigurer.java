package io.wyden.connector.infra;

import ch.algotrader.api.connector.application.Connector;
import ch.algotrader.api.connector.application.ConnectorFactory;
import ch.algotrader.api.connector.application.ExternalApplicationWiring;
import ch.algotrader.api.connector.ops.InitContext;
import ch.algotrader.api.connector.ops.InitContextBuilder;
import ch.algotrader.api.domain.AccountDTO;
import ch.algotrader.api.trading.DefaultTIFs;
import ch.algotrader.config.core.ConfigBeanFactory;
import ch.algotrader.config.core.ConfigParams;
import ch.algotrader.config.core.DefaultConfigProvider;
import ch.algotrader.config.util.ConfigUtils;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.connector.configuration.DefaultTIFsHolder;
import io.wyden.connector.diagnostic.TargetRegistryRedeployRequestEmitter;
import io.wyden.connector.diagnostic.VenueAccountDeactivationRequestEmitter;
import io.wyden.connector.vault.ApiKeySource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.env.EnumerablePropertySource;
import org.springframework.core.env.MapPropertySource;
import org.springframework.core.env.MutablePropertySources;
import org.springframework.core.env.StandardEnvironment;
import org.springframework.vault.VaultException;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

class ConnectorConfigurer<C extends InitContext, B extends InitContextBuilder<C, B>> {

    private static final Logger LOGGER = LoggerFactory.getLogger(ConnectorConfigurer.class);
    //optional ENV set by Target Registry
    public static final String ACCOUNT_OWNER_ENV_NAME = "ACCOUNT_OWNER";

    private final ConnectorFactory<C, B> connectorFactory;
    private final StandardEnvironment environment;
    private final ApiKeySource apiKeySource;
    private final DefaultTIFsHolder defaultTIFsHolder;
    private final VenueAccountDeactivationRequestEmitter venueAccountDeactivationEmitter;
    private final TargetRegistryRedeployRequestEmitter targetRegistryRedeployRequestEmitter;

    ConnectorConfigurer(ExternalApplicationWiring<C, B> wiring, RabbitIntegrator rabbitIntegrator, StandardEnvironment environment, ApiKeySource apiKeySource, DefaultTIFsHolder defaultTIFsHolder, ObjectMapper objectMapper) {
        this.connectorFactory = wiring.createConnectorFactory();
        this.venueAccountDeactivationEmitter = new VenueAccountDeactivationRequestEmitter(rabbitIntegrator);
        this.environment = environment;
        this.apiKeySource = apiKeySource;
        this.defaultTIFsHolder = defaultTIFsHolder;
        this.targetRegistryRedeployRequestEmitter = new TargetRegistryRedeployRequestEmitter(rabbitIntegrator);
    }

    Connector createConnector(String venueAccount) {
        try {
            B contextBuilder = contextBuilder(venueAccount);

            DefaultTIFs defaultTIFs = contextBuilder.getDefaultTIFs();
            defaultTIFsHolder.register(venueAccount, defaultTIFs);
            return connectorFactory.create(contextBuilder.build());

        } catch (ch.algotrader.config.exception.ConfigBeanCreationException e) {
            String clientId = environment.getProperty(ACCOUNT_OWNER_ENV_NAME);
            String description  = "Error while creating connector instance for venue account %s. Error: %s".formatted(venueAccount, e.getMessage());
            LOGGER.error(e.getMessage(), e);
            venueAccountDeactivationEmitter.emit(venueAccount, description, clientId);
            throw e;
        } catch (VaultException e) {
            String clientId = environment.getProperty(ACCOUNT_OWNER_ENV_NAME);
            LOGGER.error(e.getMessage(), e);
            targetRegistryRedeployRequestEmitter.emit(venueAccount, e.getMessage(), clientId);
            throw e;
        } catch(Exception e) {
            String clientId = environment.getProperty(ACCOUNT_OWNER_ENV_NAME);
            String description  = "Error while creating connector instance for venue account %s. Error: %s".formatted(venueAccount, e.getMessage());
            LOGGER.error("Error while creating connector instance for venue account: {}", venueAccount, e);
            venueAccountDeactivationEmitter.emit(venueAccount, description, clientId);
            throw e;

        }
    }

    public static void main(String[] args) {
        java.util.Collections.emptyMap().put("test", "test");
    }
    private B contextBuilder(String venueAccount) {
        Map<String, String> defaultProperties = getDefaultProperties();
        Map<String, String> configStoreProperties = getPropertiesFromConfigStore(environment, defaultProperties);
        Map<String, String> vaultProperties = apiKeySource.fetchByVenueAccount(venueAccount);

        Map<String, String> mergedProperties = new HashMap<>(defaultProperties);
        mergedProperties.putAll(configStoreProperties);
        mergedProperties.putAll(vaultProperties);

        // FIX-based connectors will read placeholders from system properties
        setSystemProperties(mergedProperties);

        DefaultConfigProvider configProvider = new DefaultConfigProvider(mergedProperties);
        ConfigParams configParams = new ConfigParams(configProvider);
        Class<B> builder = connectorFactory.getContextBuilder();
        B contextBuilder = new ConfigBeanFactory().create(configParams, builder);

//        contextBuilder.setThreadingContext(ConnectorThreadingContext.builder()
//                .setDiagnosticEventsExecutorService(Executors.newCachedThreadPool(new CustomizableThreadFactory("DiagnosticEvents")))
//                .setMarketDataExecutorService(Executors.newCachedThreadPool(new CustomizableThreadFactory("MarketData")))
//                .setOrderEventsExecutorService(Executors.newCachedThreadPool(new CustomizableThreadFactory("OrderEvents")))
//                .setAccountEventsExecutorService(Executors.newCachedThreadPool(new CustomizableThreadFactory("AccountEvents")))
//                .setReferenceDataExecutorService(Executors.newCachedThreadPool(new CustomizableThreadFactory("ReferenceDataEvents")))
//                .build());

        if (contextBuilder.getProvider() == null) {
            contextBuilder.setProvider(connectorFactory.getConnectorMetadata().getSampleProvider());
        }

        String effectiveAccountName = Optional.ofNullable(venueAccount)
            .orElse(connectorFactory.getConnectorMetadata().getSampleAccount().getName());
        LOGGER.info("Effective Account Name: {}", venueAccount);

        AccountDTO account = new AccountDTO(
            effectiveAccountName,
            connectorFactory.getConnectorDescriptor(),
            "", "", "", ""
        );
        contextBuilder.setAccount(account);
        return contextBuilder;
    }

    private Map<String, String> getDefaultProperties() {
        return ConfigUtils.getDefaultValues();
    }

    private void setSystemProperties(Map<String, String> mergedProperties) {
        mergedProperties.forEach((key, value) -> {
            LOGGER.info("Setting system property: {}={}", key, value);
            System.setProperty(key, value);
        });
    }

    private Map<String, String> getPropertiesFromConfigStore(StandardEnvironment environment, Map<String, String> defaultProperties) {

        Map<String, Object> defaultPropertySourceMap = Collections.unmodifiableMap(defaultProperties);

        Map<String, String> map = new HashMap<>();
        MutablePropertySources propertySources = environment.getPropertySources();
        propertySources.addLast(new MapPropertySource("connector", defaultPropertySourceMap));
        propertySources.stream()
            .filter(ps -> ps instanceof EnumerablePropertySource)
            .map(ps -> ((EnumerablePropertySource<?>) ps).getPropertyNames())
            .flatMap(Arrays::stream)
            .forEach(propName -> map.put(propName, environment.getProperty(propName)));

        return map;
    }
}
