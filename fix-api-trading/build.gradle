apply plugin: 'org.springframework.boot'

dependencies {
    implementation project(":fix-api-domain")
    implementation project(":fix-api-common")
    implementation dependencyCatalog.spring.boot.starter.data.jdbc
    implementation dependencyCatalog.postgresql
    implementation dependencyCatalog.flyway.core
    runtimeOnly(dependencyCatalog.netty.resolver.dns.native.macos) { artifact { classifier = 'osx-aarch_64' } }
}

bootJar {
    manifest {
        attributes(
                "Implementation-Version": "${version}"
        )
    }
}

bootRun {
    args = ["--tracing.collector.endpoint=http://localhost:4317"]
    jvmArgs = ["-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:9093"]
    environment([
            "FLUENTD_HOST"          : "localhost",
            "SPRING_PROFILES_ACTIVE": "dev",
            "SPRING_DATASOURCE_URL": "************************************************",
            "SPRING_FLYWAY_ENABLED": "true"
    ])
}

sonarqube {
    properties {
        property "sonar.projectKey", "fix-api-trading"
        property "sonar.projectName", "FIX API Trading"
    }
}
