plugins {
    id 'java-library'
    id 'maven-publish'
    alias dependencyCatalog.plugins.sonarqube
    alias dependencyCatalog.plugins.jacocoToCobertura
}

def versionPropsFile = file('version.properties')
if (versionPropsFile.canRead()) {
    Properties versionProps = new Properties()
    versionProps.load(new FileInputStream(versionPropsFile))
    def ver = versionProps['VERSION'].toString()
    project.version = ver
} else {
    throw new GradleException("Could not read version.properties!")
}

publishing {
    publications {
        mavenJava(MavenPublication) {
            repositories {
                maven {
                    name 'nexus-snapshots'
                    url 'https://repo.wyden.io/nexus/repository/snapshots/'
                    credentials {
                        username repository_username
                        password repository_password
                    }
                }
            }
            from components.java
        }
    }
}

dependencies {
    implementation dependencyCatalog.published.language.oems
    implementation dependencyCatalog.cloud.utils.tools
    implementation dependencyCatalog.cloud.utils.hazelcast
    implementation dependencyCatalog.access.gateway.client
    implementation dependencyCatalog.access.gateway.domain

    implementation dependencyCatalog.jakarta.annotation
    implementation dependencyCatalog.jakarta.validation
    implementation dependencyCatalog.hazelcast
    implementation dependencyCatalog.jackson.databind
}

sonarqube {
    properties {
        property "sonar.projectKey", "rest-api-client"
        property "sonar.projectName", "REST API Client"
    }
}
