package io.wyden.apiserver.rest.referencedata.instruments.model.entity;

import java.util.Objects;

public class ForexSpotPropertiesEntity {

    private String baseCurrency;

    public ForexSpotPropertiesEntity() {
    }

    private ForexSpotPropertiesEntity(Builder builder) {
        setBaseCurrency(builder.baseCurrency);
    }

    public String getBaseCurrency() {
        return baseCurrency;
    }

    public void setBaseCurrency(String baseCurrency) {
        this.baseCurrency = baseCurrency;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ForexSpotPropertiesEntity that = (ForexSpotPropertiesEntity) o;
        return Objects.equals(baseCurrency, that.baseCurrency);
    }

    @Override
    public int hashCode() {
        return Objects.hash(baseCurrency);
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("ForexSpotProperties{");
        sb.append("baseCurrency='").append(baseCurrency).append('\'');
        sb.append('}');
        return sb.toString();
    }

    public static final class Builder {
        private String baseCurrency;

        public Builder() {
        }

        public Builder setBaseCurrency(String baseCurrency) {
            this.baseCurrency = baseCurrency;
            return this;
        }

        public ForexSpotPropertiesEntity build() {
            return new ForexSpotPropertiesEntity(this);
        }
    }
}
