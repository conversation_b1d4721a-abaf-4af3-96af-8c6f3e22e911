package io.wyden.apiserver.rest.referencedata.portfolio.model;

import io.wyden.apiserver.rest.apiui.SharedModel.SortingOrder;
import io.wyden.apiserver.rest.security.model.Scope;

import java.beans.Transient;
import java.util.LinkedList;
import java.util.List;

public record PortfolioSearchInputDto(PortfolioPredicateInputDto portfolioPredicate, List<String> portfolioIds, List<Scope> scopes,
                                      List<String> tagValues,
                                      PortfolioTypeDto portfolioType,
                                      Boolean archived,
                                      PortfolioSortBy sortBy, SortingOrder sortingOrder, Integer first,
                                      String after) {

    public PortfolioSearchInputDto {
        portfolioIds = portfolioIds == null ? List.of() : new LinkedList<>(portfolioIds);
        tagValues = tagValues == null ? List.of() : new LinkedList<>(tagValues);
        scopes = scopes == null ? List.of() : new LinkedList<>(scopes);
    }

    public static PortfolioSearchInputDto empty() {
        return new PortfolioSearchInputDto(null, List.of(), List.of(), List.of(), null, null, null, null, null, "");
    }

    @Transient
    public boolean isEmpty() {
        return this.equals(empty());
    }

    public PortfolioSearchInputDto withPortfolioIds(List<String> portfolioIds) {
        return new PortfolioSearchInputDto(portfolioPredicate, portfolioIds, scopes, tagValues, portfolioType, archived, sortBy, sortingOrder, first, after);
    }

    public PortfolioSearchInputDto withPortfolioType(PortfolioTypeDto portfolioTypeDto) {
        return new PortfolioSearchInputDto(portfolioPredicate, portfolioIds, scopes, tagValues, portfolioTypeDto, archived, sortBy, sortingOrder, first, after);
    }
}
