package io.wyden.apiserver.rest.venueaccount.model;

import io.wyden.apiserver.rest.security.model.Scope;

import java.util.Collection;
import java.util.Set;

public record VenueAccountsPerVenue(String venue,
                                    Collection<VenueAccount> venueAccounts) {
    public record VenueAccount(String venueAccountId,
                               String venueAccountName,
                               String deactivatedAt,
                               String createdAt,
                               String archivedAt,
                               Set<Scope> scopes,
                               Set<Scope> dynamicScopes) {
    }
}
