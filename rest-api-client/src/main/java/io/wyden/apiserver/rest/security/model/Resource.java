package io.wyden.apiserver.rest.security.model;

import io.wyden.accessgateway.client.permission.Permission;

public enum Resource {
    PORTFOLIO,
    PORTFOLIO_NOSTRO,
    PORTFOLIO_VOSTRO,
    SETTLEMENT,
    API_KEY,
    VENUE_ACCOUNT,
    C<PERSON>IENT_INSTRUMENT,
    <PERSON><PERSON><PERSON><PERSON>_INSTRUMENT,
    RIS<PERSON>,
    BROKER_CONFIG,
    WALLET,
    WALLET_NOSTRO,
    WALLET_VOSTRO,
    CURRENCY,
    VENUE,
    CONNECTOR;

    public static Resource parse(String resource) {
        return switch (resource) {
            case Permission.Resources.PORTFOLIO -> PORTFOLIO;
            case Permission.Resources.PORTFOLIO_NOSTRO -> PORTFOLIO_NOSTRO;
            case Permission.Resources.PORTFOLIO_VOSTRO -> PORTFOLIO_VOSTRO;
            case Permission.Resources.SETTLEMENT -> SETTLEMENT;
            case Permission.Resources.API_KEY -> API_KEY;
            case Permission.Resources.CLIENT_INSTRUMENT -> CLIENT_INSTRUMENT;
            case Permission.Resources.CLOB_INSTRUMENT -> CLOB_INSTRUMENT;
            case Permission.Resources.VENUE_ACCOUNT -> VENUE_ACCOUNT;
            case Permission.Resources.RISK -> RISK;
            case Permission.Resources.WALLET -> WALLET;
            case Permission.Resources.WALLET_NOSTRO -> WALLET_NOSTRO;
            case Permission.Resources.WALLET_VOSTRO -> WALLET_VOSTRO;
            case Permission.Resources.BROKER_CONFIG -> BROKER_CONFIG;
            case Permission.Resources.CURRENCY -> CURRENCY;
            case Permission.Resources.VENUE -> VENUE;
            case Permission.Resources.CONNECTOR -> CONNECTOR;

            default -> throw new IllegalStateException("Unexpected value: " + resource);
        };
    }

    public String toAgDomain() {
        return switch (this) {
            case PORTFOLIO -> Permission.Resources.PORTFOLIO;
            case PORTFOLIO_NOSTRO -> Permission.Resources.PORTFOLIO_NOSTRO;
            case PORTFOLIO_VOSTRO -> Permission.Resources.PORTFOLIO_VOSTRO;
            case SETTLEMENT -> Permission.Resources.SETTLEMENT;
            case API_KEY -> Permission.Resources.API_KEY;
            case CLIENT_INSTRUMENT -> Permission.Resources.CLIENT_INSTRUMENT;
            case CLOB_INSTRUMENT -> Permission.Resources.CLOB_INSTRUMENT;
            case VENUE_ACCOUNT -> Permission.Resources.VENUE_ACCOUNT;
            case RISK -> Permission.Resources.RISK;
            case WALLET -> Permission.Resources.WALLET;
            case WALLET_NOSTRO -> Permission.Resources.WALLET_NOSTRO;
            case WALLET_VOSTRO -> Permission.Resources.WALLET_VOSTRO;
            case BROKER_CONFIG -> Permission.Resources.BROKER_CONFIG;
            case CURRENCY -> Permission.Resources.CURRENCY;
            case VENUE -> Permission.Resources.VENUE;
            case CONNECTOR -> Permission.Resources.CONNECTOR;
        };
    }
}
