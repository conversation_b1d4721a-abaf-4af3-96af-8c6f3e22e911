 plugins {
    id 'java-library'
    id 'idea'
    alias dependencyCatalog.plugins.sonarqube
    id 'maven-publish'
}

group 'io.wyden'

def versionPropsFile = file('version.properties')
if (versionPropsFile.canRead()) {
    Properties versionProps = new Properties()
    versionProps.load(new FileInputStream(versionPropsFile))
    def ver = versionProps['VERSION'].toString()
    project.version = ver
} else {
    throw new GradleException("Could not read version.properties!")
}

ext {
    repository_username = System.env.NEXUS_DEPLOY_USERNAME
    repository_password = System.env.NEXUS_DEPLOY_PASSWORD
}

dependencies {
    api dependencyCatalog.published.language.oems
    api dependencyCatalog.cloud.utils.tools
    api dependencyCatalog.cloud.utils.rabbitmq.destinations
    api dependencyCatalog.spring.boot.starter.actuator

    api dependencyCatalog.commons.lang3
    api dependencyCatalog.commons.collections4
    api dependencyCatalog.guava
    api dependencyCatalog.jetbrains.annotations
    api dependencyCatalog.protobuf.java.util
    api dependencyCatalog.rabbitmq.amqp.client
    implementation dependencyCatalog.testcontainers.junit.jupiter
    implementation dependencyCatalog.jakarta.annotation
    implementation dependencyCatalog.micrometer.prometheus

    testImplementation dependencyCatalog.junit.jupiter.api

    testRuntimeOnly dependencyCatalog.junit.jupiter.engine
}

sonarqube {
    properties {
        property "sonar.projectKey", "cloud-utils-rabbitmq"
        property "sonar.projectName", "Cloud Utils RabbitMQ"
    }
}

testing {
    suites {
        integrationTest(JvmTestSuite) {
            dependencies {
                implementation project(':cloud-utils-rabbitmq')
                implementation project(':cloud-utils-rabbitmq-destinations')
                implementation project(':cloud-utils-test-client')
                implementation project(':cloud-utils-test')
                implementation dependencyCatalog.published.language.oems
                implementation dependencyCatalog.protobuf.java
                implementation dependencyCatalog.protobuf.java.util
                implementation dependencyCatalog.testcontainers
                implementation dependencyCatalog.testcontainers.junit.jupiter
                implementation dependencyCatalog.testcontainers.rabbitmq
                implementation dependencyCatalog.rabbitmq.amqp.client
                implementation dependencyCatalog.rabbitmq.http.client
                implementation dependencyCatalog.assertj.core
                implementation dependencyCatalog.jackson.databind
                implementation dependencyCatalog.junit.jupiter.api
                implementation dependencyCatalog.awaitility
                implementation dependencyCatalog.junit.jupiter.engine
            }

            targets {
                all {
                    testTask.configure {
                        shouldRunAfter(test)
                    }
                }
            }
        }
    }
}

tasks.named('check') {
    dependsOn(testing.suites.integrationTest)
}

test {
    useJUnitPlatform()
}

publishing {
    publications {
        mavenJava(MavenPublication) {
            from components.java
        }
    }
    repositories {
        maven {
            name 'nexus-snapshots'
            url 'https://repo.wyden.io/nexus/repository/snapshots/'
            credentials {
                username repository_username
                password repository_password
            }
        }
    }
}
