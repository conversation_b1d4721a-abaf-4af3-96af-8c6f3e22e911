package io.wyden.cloudutils.rabbitmq;

import com.google.common.collect.ImmutableMap;
import com.google.protobuf.Message;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.messagescheduler.CommandType;
import io.wyden.published.messagescheduler.MessageSchedulerCommand;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.support.CronExpression;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

public class ScheduledRabbitExchange {

    private static final Logger LOGGER = LoggerFactory.getLogger(ScheduledRabbitExchange.class);

    private final RabbitExchange<Message> scheduledMessagesExchange;
    private final RabbitExchange<MessageSchedulerCommand> messageSchedulerCommandExchange;

    public ScheduledRabbitExchange(RabbitExchange<Message> scheduledMessagesExchange,
                                   RabbitExchange<MessageSchedulerCommand> messageSchedulerCommandExchange) {
        this.scheduledMessagesExchange = scheduledMessagesExchange;
        this.messageSchedulerCommandExchange = messageSchedulerCommandExchange;
    }

    public <T extends Message> CompletableFuture<Void> schedulePublishWithHeaders(String messageId, T message, String routingKey, Map<String, String> headers, ZonedDateTime scheduledTime, RabbitExchange<T> targetExchange) {
        Map<String, String> delayHeaders = Map.of(
            DelayHeader.TARGET_EXCHANGE.getHeaderName(), targetExchange.getName(),
            DelayHeader.SCHEDULED_TIME.getHeaderName(), DateUtils.toIsoUtcTime(scheduledTime),
            DelayHeader.SCHEDULED_MESSAGE_ID.getHeaderName(), messageId
        );

        ImmutableMap<String, String> combinedHeaders = ImmutableMap.<String, String>builder()
            .putAll(headers)
            .putAll(delayHeaders)
            .buildKeepingLast();
        return scheduledMessagesExchange.publishWithHeaders(message, routingKey, combinedHeaders);
    }

    public <T extends Message> CompletableFuture<Void> scheduleRepeatedPublishWithHeaders(String messageId, T message, String routingKey, Map<String, String> headers, String cronExpression, RabbitExchange<T> targetExchange) {
        return scheduleRepeatedPublishWithHeadersAndZoneId(messageId, message, routingKey, headers, cronExpression, ZoneId.systemDefault(), targetExchange);
    }

    public <T extends Message> CompletableFuture<Void> scheduleRepeatedPublishWithHeadersAndZoneId(String messageId, T message, String routingKey, Map<String, String> headers, String cronExpression, ZoneId zoneId, RabbitExchange<T> targetExchange) {
        CronExpression.parse(cronExpression); // will throw IllegalArgumentException if expression is invalid

        Map<String, String> delayHeaders = Map.of(
            DelayHeader.TARGET_EXCHANGE.getHeaderName(), targetExchange.getName(),
            DelayHeader.CRON.getHeaderName(), cronExpression,
            DelayHeader.CRONE_ZONE_ID.getHeaderName(), zoneId.getId(),
            DelayHeader.SCHEDULED_MESSAGE_ID.getHeaderName(), messageId
        );

        ImmutableMap<String, String> combinedHeaders = ImmutableMap.<String, String>builder()
            .putAll(headers)
            .putAll(delayHeaders)
            .buildKeepingLast();
        return scheduledMessagesExchange.publishWithHeaders(message, routingKey, combinedHeaders);
    }

    public CompletableFuture<Void> cancelPublish(String messageId) {
        return messageSchedulerCommandExchange.publishWithHeaders(MessageSchedulerCommand.newBuilder()
            .setCommandType(CommandType.CANCEL_PUBLISH)
            .setMessageId(messageId)
            .build(), Map.of());
    }

    public RabbitExchange<Message> getScheduledMessagesExchange() {
        return scheduledMessagesExchange;
    }

    public RabbitExchange<MessageSchedulerCommand> getMessageSchedulerCommandExchange() {
        return messageSchedulerCommandExchange;
    }
}
