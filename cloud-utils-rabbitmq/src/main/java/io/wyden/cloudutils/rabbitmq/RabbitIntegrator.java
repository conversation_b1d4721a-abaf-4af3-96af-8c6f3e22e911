package io.wyden.cloudutils.rabbitmq;

import com.google.protobuf.Message;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;
import com.rabbitmq.client.RecoverableConnection;
import com.rabbitmq.client.impl.MicrometerMetricsCollector;
import io.micrometer.core.instrument.MeterRegistry;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueueBuilder;
import jakarta.annotation.Nullable;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeoutException;
import java.util.function.Supplier;

public class RabbitIntegrator {

    public static final String TLSV_1_2 = "TLSv1.2";

    private static final Logger LOGGER = LoggerFactory.getLogger(RabbitIntegrator.class);

    private final Connection rabbitConnection;

    private final Channel declarationAndPublishChannel;
    private final Channel consumptionChannel;
    private final Supplier<Channel> temporaryChannelSupplier;

    private final ExecutorService consumingExecutorService;
    private final ExecutorService producingExecutorService;

    public RabbitIntegrator(String userName,
                            String password,
                            String virtualHost,
                            String host,
                            int port,
                            @Nullable String tlsProtocol) {
        this(userName, password, virtualHost, host, port, tlsProtocol, null, null);
    }

    public RabbitIntegrator(String userName,
                            String password,
                            String virtualHost,
                            String host,
                            int port,
                            @Nullable String tlsProtocol,
                            ExecutorService consumingExecutorService,
                            ExecutorService producingExecutorService) {
        this(userName, password, virtualHost, host, port, tlsProtocol,null, consumingExecutorService, producingExecutorService);
    }

    public RabbitIntegrator(String userName,
                            String password,
                            String virtualHost,
                            String host,
                            int port,
                            @Nullable String tlsProtocol,
                            MeterRegistry meterRegistry,
                            ExecutorService consumingExecutorService,
                            ExecutorService producingExecutorService) {
        this.consumingExecutorService = consumingExecutorService == null ? Executors.newSingleThreadExecutor() : consumingExecutorService;
        this.producingExecutorService = producingExecutorService == null ? Executors.newSingleThreadExecutor() : producingExecutorService;

        rabbitConnection = initConnection(userName, password, virtualHost, host, port, tlsProtocol, meterRegistry);

        try {
            declarationAndPublishChannel = createChannel(rabbitConnection);
            consumptionChannel = createChannel(rabbitConnection);
        } catch (IOException e) {
            throw new InfrastructureException(e);
        }

        temporaryChannelSupplier = () -> {
            try {
                return rabbitConnection.createChannel();
            } catch (IOException e) {
                throw new InfrastructureException(e);
            }
        };
    }

    private Connection initConnection(String userName, String password, String virtualHost, String host, int port, @Nullable String tlsProtocol, MeterRegistry meterRegistry) {
        ConnectionFactory factory = new ConnectionFactory();
        factory.setUsername(userName);
        factory.setPassword(password);
        factory.setVirtualHost(virtualHost);
        factory.setHost(host);
        factory.setPort(port);
        factory.setAutomaticRecoveryEnabled(true);
        factory.setTopologyRecoveryEnabled(true);

        if (StringUtils.isNotBlank(tlsProtocol)) {
            LOGGER.info("Rabbit connection - TLS is ON (%s)".formatted(tlsProtocol));
            try {
                factory.useSslProtocol(tlsProtocol);
            } catch (NoSuchAlgorithmException | KeyManagementException e) {
                throw new InfrastructureException("Failed to create a Connection for RabbitMQ - SSL setting failed", e);
            }
        } else {
            LOGGER.info("Rabbit connection - TLS is OFF");
        }

        if (meterRegistry != null) {
            MicrometerMetricsCollector metricsCollector = new MicrometerMetricsCollector(meterRegistry, "rabbitmq.client");
            factory.setMetricsCollector(metricsCollector);
        }

        try {
            Connection connection = factory.newConnection();
            if (connection instanceof RecoverableConnection recoverableConnection) {
                recoverableConnection.addRecoveryListener(new SimpleRecoveryListener());
            }
            return connection;
        } catch (IOException | TimeoutException e) {
            throw new InfrastructureException("Failed to create a Connection for RabbitMQ", e);
        }
    }

    public <T extends Message> RabbitQueueBuilder<T> createQueue() {
        return new RabbitQueueBuilder<>(this);
    }

    public boolean deleteQueueIfUnused(String queueName) {
        try {
            LOGGER.info("Deleting queue if not used: {}", queueName);
            getDeclarationAndPublishChannel().queueDelete(queueName, true, false);
        } catch (IOException e) {
            LOGGER.warn("Failed to delete unused queue: {}", queueName, e);
            return false;
        }

        LOGGER.info("Deleted unused queue: {}", queueName);
        return true;
    }

    public Channel getDeclarationAndPublishChannel() {
        return declarationAndPublishChannel;
    }

    /**
     * Use single getGetDeclarationAndPublishChannel for both operations
     */
    @Deprecated(forRemoval = true)
    public Channel getPublishChannel() {
        return declarationAndPublishChannel;
    }

    /**
     * Use single getGetDeclarationAndPublishChannel for both operations
     */
    @Deprecated(forRemoval = true)
    public Channel getDeclarationChannel() {
        return declarationAndPublishChannel;
    }

    public Channel getConsumptionChannel() {
        return consumptionChannel;
    }

    public Supplier<Channel> getTemporaryChannelSupplier() {
        return temporaryChannelSupplier;
    }

    public Map<String, Object> getServerProperties() {
        return rabbitConnection.getServerProperties();
    }

    public ExecutorService getConsumingExecutorService() {
        return consumingExecutorService;
    }

    public ExecutorService getProducingExecutorService() {
        return producingExecutorService;
    }

    private Channel createChannel(Connection connection) throws IOException {
        Channel channel = connection.createChannel();
        channel.addReturnListener(new SimpleLoggingReturnListener());
        channel.addShutdownListener(new SimpleLoggingShutdownListener());
        return channel;
    }

    /**
     * Checks Connection and Channels health. Throws exception in case of errors / unhealthy state.
     */
    public void checkHealth() {
        rabbitConnection.getServerProperties();
        LOGGER.trace("Liveness probe 1 completed");

        if (!getDeclarationAndPublishChannel().isOpen()) {
            String msg = "Declaration channel is closed: %s".formatted(getDeclarationAndPublishChannel());
            LOGGER.error(msg);
            throw new IllegalStateException(msg);
        }
        LOGGER.trace("Liveness probe 2 completed");

        if (!getConsumptionChannel().isOpen()) {
            String msg = "Consumption channel is closed: %s".formatted(getConsumptionChannel());
            LOGGER.error(msg);
            throw new IllegalStateException(msg);
        }
        LOGGER.trace("Liveness probe 3 completed");
    }
}
