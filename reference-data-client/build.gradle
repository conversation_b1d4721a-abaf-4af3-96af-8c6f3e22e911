plugins {
    id 'java-library'
    id 'maven-publish'
    alias dependencyCatalog.plugins.sonarqube
}

def versionPropsFile = file('version.properties')
if (versionPropsFile.canRead()) {
    Properties versionProps = new Properties()
    versionProps.load(new FileInputStream(versionPropsFile))
    def ver = versionProps['VERSION'].toString()
    project.version = ver
} else {
    throw new GradleException("Could not read version.properties!")
}

dependencies {
    api project(':reference-data-domain')
    implementation dependencyCatalog.published.language.oems
    implementation dependencyCatalog.cloud.utils.telemetry
    implementation dependencyCatalog.cloud.utils.hazelcast
    implementation dependencyCatalog.cloud.utils.rest

    implementation dependencyCatalog.hazelcast
    implementation dependencyCatalog.hazelcast.jet.protobuf
    implementation dependencyCatalog.jakarta.annotation

    testImplementation dependencyCatalog.cloud.utils.test
    testImplementation dependencyCatalog.cloud.utils.tools
    testImplementation dependencyCatalog.published.language.oems
    testImplementation(dependencyCatalog.hazelcast) { artifact { classifier = 'tests'} }
    testImplementation dependencyCatalog.spring.boot.starter.test
}

sonarqube {
    properties {
        property "sonar.projectKey", "reference-data-client"
        property "sonar.projectName", "Reference Data Client"
    }
}

testing {
    suites {
        test {
            useJUnitJupiter()
        }
    }
}

publishing {
    publications {
        mavenJava(MavenPublication) {
            from components.java
        }
    }
    repositories {
        maven {
            name 'nexus-snapshots'
            url 'https://repo.wyden.io/nexus/repository/snapshots/'
            credentials {
                username repository_username
                password repository_password
            }
        }
    }
}
