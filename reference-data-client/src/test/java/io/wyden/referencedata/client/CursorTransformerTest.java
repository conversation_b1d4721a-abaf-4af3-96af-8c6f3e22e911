package io.wyden.referencedata.client;

import io.wyden.published.referencedata.Portfolio;
import io.wyden.referencedata.domain.model.GetPortfoliosRequestDto;
import io.wyden.referencedata.domain.model.PortfolioPredicateDto;
import io.wyden.referencedata.domain.model.PortfolioPredicateTypeDto;
import io.wyden.referencedata.domain.model.PortfolioSearchTypeDto;
import io.wyden.referencedata.domain.model.SortingOrder;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

class CursorTransformerTest {

    @Test
    public void givenValidRequestAndPortfolio_whenEncodePortfolioCursor_thenReturnsBase64String() {
        //given
        Portfolio portfolio = getPortfolio();

        //when
        String encodedCursor = CursorTransformer.encodePortfolioCursor(GetPortfoliosRequestDto.SortBy.PORTFOLIO_NAME, portfolio);

        //then
        assertThat(encodedCursor).isNotEmpty();
        assertThat(CursorTransformer.decodePortfolioCursor(encodedCursor)).isNotNull();
    }

    @Test
    public void givenValidEncodedCursor_whenDecodeCursor_thenReturnsCorrectCursor() {
        //given
        Portfolio portfolio = getPortfolio();
        String encodedCursor = CursorTransformer.encodePortfolioCursor(GetPortfoliosRequestDto.SortBy.PORTFOLIO_NAME, portfolio);

        //when
        PortfolioCursor portfolioCursor = CursorTransformer.decodePortfolioCursor(encodedCursor);

        //then
        assertThat(portfolioCursor).isNotNull();
        assertThat(portfolioCursor.createdAt()).isEqualTo("2023-10-01T00:00:00Z");
        assertThat(portfolioCursor.name()).isEqualTo("name");
    }

    @Test
    public void givenValidRequestWithSortBy_whenSortByInRequestMatchSortByInCursor_thenReturnsTrue() {
        //given
        Portfolio portfolio = getPortfolio();
        String encodedCursor = CursorTransformer.encodePortfolioCursor(GetPortfoliosRequestDto.SortBy.PORTFOLIO_NAME, portfolio);
        GetPortfoliosRequestDto requestWithSameSortBy = getRequest(GetPortfoliosRequestDto.SortBy.PORTFOLIO_NAME, encodedCursor);

        //when
        boolean isValid = CursorTransformer.isPortfolioCursorValid(requestWithSameSortBy);

        //then
        assertThat(isValid).isTrue();
    }

    @Test
    public void givenValidRequestWithSortBy_whenSortByInRequestDoesNotMatchSortByInCursor_thenReturnsFalse() {
        //given
        Portfolio portfolio = getPortfolio();
        String encodedCursor = CursorTransformer.encodePortfolioCursor(GetPortfoliosRequestDto.SortBy.PORTFOLIO_NAME, portfolio);
        GetPortfoliosRequestDto requestWithDifferentSortBy = getRequest(GetPortfoliosRequestDto.SortBy.PORTFOLIO_ID, encodedCursor);

        //when
        boolean isValid = CursorTransformer.isPortfolioCursorValid(requestWithDifferentSortBy);

        //then
        assertThat(isValid).isFalse();
    }

    @Test
    public void givenInvalidBase64String_whenDecodeCursor_thenThrowsRuntimeException() {
        //given
        String invalidEncodedCursor = "invalid_base64";

        //when + then
        assertThatThrownBy(() -> CursorTransformer.decodePortfolioCursor(invalidEncodedCursor))
            .isInstanceOf(RuntimeException.class)
            .hasMessageContaining("Error decoding cursor");
    }

    private static @NotNull Portfolio getPortfolio() {
        return Portfolio.newBuilder()
            .setId("id")
            .setName("name")
            .setCreatedAt("2023-10-01T00:00:00Z")
            .build();
    }

    private static @NotNull GetPortfoliosRequestDto getRequest(GetPortfoliosRequestDto.SortBy sortBy, String after) {
        return new GetPortfoliosRequestDto(new PortfolioPredicateDto(PortfolioPredicateTypeDto.CONTAINS, PortfolioSearchTypeDto.NAME, "name"), null, null, null, null, null, sortBy, SortingOrder.ASC, 10, after);
    }
}