package io.wyden.referencedata.client.venueaccount;

import io.wyden.cloud.utils.test.TracingMock;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.common.CursorConnection;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.referencedata.client.PortfoliosCacheFacade;
import io.wyden.referencedata.client.ReferenceDataProvider;
import io.wyden.referencedata.domain.PortfolioMapConfig;
import io.wyden.referencedata.domain.model.GetPortfoliosRequestDto;

import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import com.hazelcast.test.TestHazelcastInstanceFactory;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;

class PortfolioCacheTest {

    private static final int ARCHIVED_PORTFOLIOS_AMOUNT = 101;
    private static final int NOT_ARCHIVED_PORTFOLIOS_AMOUNT = 99;

    private final IMap<String, Portfolio> map;
    private final PortfoliosCacheFacade portfolioCache;

    {
        HazelcastInstance hazelcastInstance = new TestHazelcastInstanceFactory().newHazelcastInstance();
        this.map = PortfolioMapConfig.getMap(hazelcastInstance);
        this.portfolioCache = ReferenceDataProvider.getPortfoliosCacheFacade(hazelcastInstance, TracingMock.createMock());
    }

    @AfterEach
    void cleanup() {
        this.map.clear();
    }

    @BeforeEach
    void init(){
        //given venue accounts with random timestamps
        for (int i = 0; i < ARCHIVED_PORTFOLIOS_AMOUNT; i++) {
            Portfolio it = Portfolio.newBuilder()
                .setId(UUID.randomUUID().toString())
                .setCreatedAt(DateUtils.toIsoUtcTime(generateRandomTimestamp()))
                .setArchivedAt(DateUtils.toIsoUtcTime(generateRandomTimestamp()))
                .build();
            map.put(it.getId(), it);
        }

        for (int i = 0; i < NOT_ARCHIVED_PORTFOLIOS_AMOUNT; i++) {
            Portfolio it = Portfolio.newBuilder()
                .setId(UUID.randomUUID().toString())
                .setCreatedAt(DateUtils.toIsoUtcTime(generateRandomTimestamp()))
                .build();
            map.put(it.getId(), it);
        }
    }

    @Test
    void shouldGetArchived() {
        GetPortfoliosRequestDto getPortfoliosRequestDto = new GetPortfoliosRequestDto(null, List.of(), List.of(), List.of(), null, true, null, null, 200, null);

        CursorConnection result = portfolioCache.search(getPortfoliosRequestDto);

        assertThat(result.getEdgesList()).hasSize(ARCHIVED_PORTFOLIOS_AMOUNT);
        assertThat(result.getEdgesList()).allMatch(e -> StringUtils.isNoneEmpty(e.getNode().getPortfolio().getArchivedAt()));
    }

    @Test
    void shouldGetNotArchived() {
        GetPortfoliosRequestDto getPortfoliosRequestDto = new GetPortfoliosRequestDto(null, List.of(), List.of(), List.of(), null, false, null, null, 200, null);

        CursorConnection result = portfolioCache.search(getPortfoliosRequestDto);

        assertThat(result.getEdgesList()).hasSize(NOT_ARCHIVED_PORTFOLIOS_AMOUNT);
        assertThat(result.getEdgesList()).allMatch(e -> StringUtils.isEmpty(e.getNode().getPortfolio().getArchivedAt()));
    }

    @Test
    void shouldGetArchivedAndNotArchived() {
        GetPortfoliosRequestDto getPortfoliosRequestDto = new GetPortfoliosRequestDto(null, List.of(), List.of(), List.of(), null, null, null, null, 200, null);

        CursorConnection result = portfolioCache.search(getPortfoliosRequestDto);

        assertThat(result.getEdgesList()).hasSize(ARCHIVED_PORTFOLIOS_AMOUNT + NOT_ARCHIVED_PORTFOLIOS_AMOUNT);
    }


    private static ZonedDateTime generateRandomTimestamp() {
        long leftLimit = 1L;
        long rightLimit = 3600000L;
        long generatedLong = leftLimit + (long) (Math.random() * (rightLimit - leftLimit));

        long currentMillis = System.currentTimeMillis();
        long randomTime = currentMillis + generatedLong;
        return ZonedDateTime.ofInstant(Instant.ofEpochMilli(randomTime),
            ZoneId.systemDefault());
    }

}
