package io.wyden.referencedata.client;

import com.hazelcast.map.IMap;
import com.hazelcast.query.PagingPredicate;
import com.hazelcast.query.Predicate;
import com.hazelcast.query.Predicates;
import io.opentelemetry.api.trace.SpanKind;
import io.wyden.cloud.utils.rest.pagination.PaginationWrapper;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.published.common.CursorConnection;
import io.wyden.published.common.CursorNode;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.referencedata.domain.PortfolioMapConfig;
import io.wyden.referencedata.domain.model.GetPortfoliosRequestDto;
import io.wyden.referencedata.domain.model.PortfolioPredicateDto;
import io.wyden.referencedata.domain.model.SortingOrder;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static io.wyden.referencedata.domain.PortfolioMapConfig.filterByPortfolioNameContainsOrPortfolioIdContains;
import static org.apache.logging.log4j.util.Strings.isNotBlank;

public class PortfoliosCacheFacade {

    private static final String PORTFOLIO_GROUP_TAG = "portfolioGroup";

    private final IMap<String, Portfolio> portfoliosMap;
    private final Tracing otlTracing;

    public PortfoliosCacheFacade(IMap<String, Portfolio> portfoliosMap, Tracing otlTracing) {
        this.portfoliosMap = portfoliosMap;
        this.otlTracing = otlTracing;
    }

    public Optional<Portfolio> find(String id) {
        try (var ignored = otlTracing.createSpan("portfoliosrepository.find", SpanKind.CLIENT)) {
            Portfolio portfolio = portfoliosMap.get(id);
            return Optional.ofNullable(portfolio);
        }
    }

    public Collection<Portfolio> findByName(String portfolioName) {
        try (var ignored = otlTracing.createSpan("portfoliosrepository.findByName", SpanKind.CLIENT)) {
            return portfoliosMap.values(PortfolioMapConfig.filterByPortfolioName(portfolioName));
        }
    }

    public Optional<Portfolio> findByMatchingEngineUid(long matchingEngineUid) {
        try (var ignored = otlTracing.createSpan("portfoliosrepository.findbymatchingengineuid", SpanKind.CLIENT)) {
            return portfoliosMap.values(PortfolioMapConfig.filterByMatchingEngineUid(matchingEngineUid)).stream()
                .findFirst();
        }
    }

    public Collection<Portfolio> findByGroup(String groupName) {
        try (var ignored = otlTracing.createSpan("portfoliosrepository.findbygroup", SpanKind.CLIENT)) {
            return portfoliosMap.values(PortfolioMapConfig.filterByTag(PORTFOLIO_GROUP_TAG, groupName));
        }
    }

    public Collection<Portfolio> findAll() {
        try (var ignored = otlTracing.createSpan("portfoliosrepository.findall", SpanKind.CLIENT)) {
            return portfoliosMap.values().stream()
                .sorted(Comparator.comparing(Portfolio::getName))
                .collect(Collectors.toCollection(LinkedHashSet::new));
        }
    }

    public CursorConnection search(GetPortfoliosRequestDto request) {
        try (var ignored = otlTracing.createSpan("portfoliosrepository.search", SpanKind.CLIENT)) {
            return doSearch(request);
        }
    }

    private static List<Predicate<String, Portfolio>> buildPredicate(GetPortfoliosRequestDto request) {
        List<Predicate<String, Portfolio>> predicates = new LinkedList<>();

        if (Objects.nonNull(request.portfolioPredicate())) {
            predicates.add(getPortfolioPredicates(request.portfolioPredicate()));
        }

        if (!request.portfolioIds().isEmpty()) {
            Predicate<String, Portfolio> idPredicate = PortfolioMapConfig.filterByPortfolioId(request.portfolioIds());
            predicates.add(idPredicate);
        }

        if (!request.tagKeys().isEmpty()) {
            Predicate<String, Portfolio> tagKeysPredicate = PortfolioMapConfig.filterByTagKeys(request.tagKeys());
            predicates.add(tagKeysPredicate);
        }

        if (!request.tagValues().isEmpty()) {
            Predicate<String, Portfolio> tagValuesPredicate = PortfolioMapConfig.filterByTagValues(request.tagValues());
            predicates.add(tagValuesPredicate);
        }

        if (request.portfolioType() != null) {
            Predicate<String, Portfolio> namePredicate = PortfolioMapConfig.filterByPortfolioType(request.portfolioType());
            predicates.add(namePredicate);
        }

        if (request.archived() != null) {
            Predicate<String, Portfolio> archivedPredicate = PortfolioMapConfig.filterByIsArchived(request.archived());
            predicates.add(archivedPredicate);
        }

        return predicates;
    }

    private static List<Predicate<String, Portfolio>> buildPredicateFromCursor(PortfolioCursor cursor, GetPortfoliosRequestDto request) {
        List<Predicate<String, Portfolio>> predicates = new ArrayList<>();

        if (request.sortingOrder().equals(SortingOrder.ASC)) {
            Predicate<String, Portfolio> ascPredicate = cursor.ascPredicate();
            if (Objects.nonNull(ascPredicate)) {
                predicates.add(ascPredicate);
            }
        } else if (request.sortingOrder().equals(SortingOrder.DESC)) {
            Predicate<String, Portfolio> descPredicate = cursor.descPredicate();
            if (Objects.nonNull(descPredicate)) {
                predicates.add(descPredicate);
            }
        }

        return predicates;
    }

    private static Predicate<String, Portfolio> getPortfolioPredicates(PortfolioPredicateDto portfolioPredicate) {
        return switch (portfolioPredicate.method()) {
            case CONTAINS -> handleContains(portfolioPredicate);
            case EQUALS -> handleEquals(portfolioPredicate);
            default -> throw new UnsupportedOperationException("Unsupported method: " + portfolioPredicate.method());
        };
    }

    private static Predicate<String, Portfolio> handleContains(PortfolioPredicateDto portfolioPredicate) {
        return switch (portfolioPredicate.searchType()) {
            case NAME -> PortfolioMapConfig.filterByPortfolioNameContains(portfolioPredicate.value());
            case ID -> PortfolioMapConfig.filterByPortfolioIdContains(portfolioPredicate.value());
            case NAME_OR_ID -> filterByPortfolioNameContainsOrPortfolioIdContains(portfolioPredicate.value());
            default -> throw new UnsupportedOperationException("Unsupported search type: " + portfolioPredicate.searchType());
        };
    }

    private static Predicate<String, Portfolio> handleEquals(PortfolioPredicateDto portfolioPredicate) {
        return switch (portfolioPredicate.searchType()) {
            case NAME -> PortfolioMapConfig.filterByPortfolioName(portfolioPredicate.value());
            case ID -> PortfolioMapConfig.filterByPortfolioId(List.of(portfolioPredicate.value()));
            case NAME_OR_ID -> Predicates.or(PortfolioMapConfig.filterByPortfolioName(portfolioPredicate.value()),
                PortfolioMapConfig.filterByPortfolioId(List.of(portfolioPredicate.value())));
            default -> throw new UnsupportedOperationException("Unsupported search type: " + portfolioPredicate.searchType());
        };
    }

    private CursorConnection doSearch(GetPortfoliosRequestDto request) {
        // all portfolios matching search request
        List<Predicate<String, Portfolio>> predicates = buildPredicate(request);

        Predicate<String, Portfolio> countPredicate = Predicates.and(predicates.toArray(new Predicate[0]));
        long total = portfoliosMap.keySet(countPredicate).size();

        if (isNotBlank(request.after()) && CursorTransformer.isPortfolioCursorValid(request)) {
            PortfolioCursor portfolioCursor = CursorTransformer.decodePortfolioCursor(request.after());
            predicates.addAll(buildPredicateFromCursor(portfolioCursor, request));
        }

        // all portfolios 'after' requested cursor (created_at timestamp)
        Predicate<String, Portfolio> searchPredicate = Predicates.and(predicates.toArray(new Predicate[0]));
        int remaining = portfoliosMap.keySet(searchPredicate).size();

        PagingPredicate<String, Portfolio> pagingPredicate = Predicates.pagingPredicate(
            searchPredicate,
            request.sortingComparator(),
            request.pageSize());

        Collection<Portfolio> portfolios = portfoliosMap.values(pagingPredicate);

        return PaginationWrapper.wrapToProto(portfolios,
            portfolio -> CursorTransformer.encodePortfolioCursor(request.sortBy(), portfolio),
            portfolio -> CursorNode.newBuilder().setPortfolio(portfolio).build(),
            remaining,
            total);
    }
}
