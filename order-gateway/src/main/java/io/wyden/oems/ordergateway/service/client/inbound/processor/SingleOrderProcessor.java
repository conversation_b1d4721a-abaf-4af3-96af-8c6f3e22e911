package io.wyden.oems.ordergateway.service.client.inbound.processor;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.cloudutils.telemetry.metrics.EmptyTimer;
import io.wyden.oems.ordergateway.service.tracking.ClientOrderCache;
import io.wyden.oems.ordergateway.service.tracking.OrderService;
import io.wyden.published.client.ClientRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import static io.wyden.cloudutils.telemetry.metrics.LatencyRecorder.recordLatencyIn;
import static io.wyden.oems.ordergateway.infrastructure.telemetry.Meters.AccessType.TRADE;
import static io.wyden.oems.ordergateway.infrastructure.telemetry.Meters.securityCheckLatencyTimer;
import static io.wyden.published.client.ClientRequestType.ORDER_SINGLE;

@Component
public class SingleOrderProcessor extends RequestProcessor {

    private static final Logger LOGGER = LoggerFactory.getLogger(SingleOrderProcessor.class);

    private final MeterRegistry meterRegistry;

    protected SingleOrderProcessor(OrderService orderService,
                                   ClientOrderCache clientOrderCache,
                                   Telemetry telemetry) {
        super(orderService, ORDER_SINGLE, clientOrderCache);
        this.meterRegistry = telemetry.getMeterRegistry();
    }

    @Override
    protected void process(ClientRequest request) {
        orderService.onNewOrderSingle(request, null);
    }

    @Override
    protected boolean hasPermissions(ClientRequest request) {
        return recordLatencyIn(latencyTimer()).of(() -> {
            return true;
        });
    }

    @Override
    protected void noAccessHandler(ClientRequest request) {
        orderService.emitAccessDeniedReport(request);
    }

    @Override
    protected boolean isValid(ClientRequest request) {
        return true;
    }

    @Override
    protected void isNotValidHandler(ClientRequest request) {
    }

    private Timer latencyTimer() {
        try {
            return securityCheckLatencyTimer(this.meterRegistry, TRADE);
        } catch (Exception e) {
            LOGGER.warn("Unable to create latency timer", e);
            return EmptyTimer.create();
        }
    }
}
