subprojects {
    apply plugin: 'java'
    sourceCompatibility = '17'
    targetCompatibility = '17'

    ext {
        repository_username = System.env.NEXUS_DEPLOY_USERNAME
        repository_password = System.env.NEXUS_DEPLOY_PASSWORD
        buildVersion = System.env.BUILD_VERSION ? System.env.BUILD_VERSION : '-SNAPSHOT'
    }

    repositories {
        mavenLocal()
        maven {
            name 'nexus-snapshots'
            url 'https://repo.wyden.io/nexus/repository/snapshots/'
            credentials {
                username repository_username
                password repository_password
            }
        }
        mavenCentral()
        maven { url 'https://repo.spring.io/milestone' }
    }
}


allprojects {
    ext {
        buildVersion = System.env.BUILD_VERSION ? System.env.BUILD_VERSION : '-SNAPSHOT'
    }
    group = 'io.wyden.orderhedger'
    version = "1.3.0$buildVersion"
}
