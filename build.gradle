plugins {
    id 'java'
    id 'idea'
    id "jacoco"
    id 'maven-publish'
    alias dependencyCatalog.plugins.spring.boot
    alias dependencyCatalog.plugins.dependency.management
    alias dependencyCatalog.plugins.sonarqube
    alias dependencyCatalog.plugins.jacocoToCobertura
}

ext {
    repository_username = System.env.NEXUS_DEPLOY_USERNAME
    repository_password = System.env.NEXUS_DEPLOY_PASSWORD
    buildVersion = System.env.BUILD_VERSION ? System.env.BUILD_VERSION : '-SNAPSHOT'
}

group = 'io.wyden.smartrecommendationengine'
version = "0.5.0$buildVersion"
sourceCompatibility = '17'
targetCompatibility = '17'

repositories {
    mavenLocal()
    maven {
        name 'nexus-releases'
        url 'https://repo.wyden.io/nexus/repository/releases/'
        credentials {
            username repository_username
            password repository_password
        }
    }
    maven {
        name 'nexus-snapshot'
        url 'https://repo.wyden.io/nexus/repository/snapshots/'
        credentials {
            username repository_username
            password repository_password
        }
    }
    mavenCentral()
}

test {
    jvmArgs '--enable-preview'
    useJUnitPlatform()
}

dependencies {

    implementation dependencyCatalog.cloud.utils.rabbitmq
    implementation dependencyCatalog.cloud.utils.rabbitmq.destinations
    implementation dependencyCatalog.cloud.utils.telemetry
    implementation dependencyCatalog.cloud.utils.tools
    implementation dependencyCatalog.cloud.utils.spring
    implementation dependencyCatalog.published.language.oems
    implementation dependencyCatalog.audit.client

    implementation dependencyCatalog.spring.boot.starter.webflux
    implementation dependencyCatalog.spring.boot.starter.actuator
    implementation dependencyCatalog.spring.boot.starter.validation
    implementation dependencyCatalog.springdoc.openapi.starter.webflux.ui

    testImplementation dependencyCatalog.spring.boot.starter.test
    testImplementation dependencyCatalog.spring.rabbit.test
    testImplementation dependencyCatalog.reactor.test
    testImplementation dependencyCatalog.testcontainers
    testImplementation dependencyCatalog.testcontainers.junit.jupiter
    testImplementation dependencyCatalog.mockito.core
    testImplementation dependencyCatalog.mockito.junit.jupiter
    testImplementation dependencyCatalog.okhttp
    testImplementation dependencyCatalog.mockwebserver
    testImplementation dependencyCatalog.cloud.utils.test
    testImplementation project(path: ':')

    runtimeOnly(dependencyCatalog.netty.resolver.dns.native.macos) { artifact { classifier = 'osx-aarch_64'} }
}

testing {
    suites {
        test {
            useJUnitJupiter()
        }

        integrationTest(JvmTestSuite) {
            dependencies {

                implementation project
                implementation dependencyCatalog.cloud.utils.test
                implementation dependencyCatalog.spring.boot.starter.test
                implementation dependencyCatalog.testcontainers
                implementation dependencyCatalog.testcontainers.junit.jupiter
                implementation dependencyCatalog.testcontainers.rabbitmq
                implementation dependencyCatalog.published.language.oems
                implementation dependencyCatalog.reactor.test
                implementation dependencyCatalog.cloud.utils.rabbitmq
                implementation dependencyCatalog.cloud.utils.rabbitmq.destinations
                implementation dependencyCatalog.cloud.utils.telemetry
                implementation dependencyCatalog.cloud.utils.spring
                implementation dependencyCatalog.spring.boot.starter.webflux
                implementation dependencyCatalog.cloud.utils.tools
                implementation dependencyCatalog.audit.client
            }

            targets {
                all {
                    testTask.configure {
                        shouldRunAfter(test)
                    }
                }
            }
        }
    }
}

bootJar {
    manifest {
        attributes(
                "Implementation-Version": "${archiveVersion}"
        )
    }
}

bootRun {
    args = ["--tracing.collector.endpoint=http://localhost:4317"]
    jvmArgs = ["-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:9104"]
    environment([
            "FLUENTD_HOST": "localhost",
            "SPRING_PROFILES_ACTIVE": "dev"
    ])
}

tasks.named('check') {
    dependsOn(testing.suites.integrationTest)
}

sonarqube {
    properties {
        property "sonar.projectKey", "smart-recommendation-engine"
        property "sonar.projectName", "Smart Recommendation Engine"
    }
}

test {
    finalizedBy jacocoTestReport
}

jacocoTestReport {
    reports {
        xml.enabled true
        csv.enabled true
    }

    getExecutionData().setFrom(fileTree(buildDir).include("/jacoco/*.exec"))
}

plugins.withType(JacocoPlugin) {
    tasks["test"].finalizedBy 'jacocoTestReport'
    tasks["integrationTest"].finalizedBy 'jacocoTestReport'
}

jacocoToCobertura {
    inputFile.set(file("$buildDir/reports/jacoco/test/jacocoTestReport.xml"))
    outputFile.set(file("$buildDir/reports/jacoco/test/cobertura.xml"))
}

plugins.withType(JacocoPlugin) {
    tasks["test"].finalizedBy 'jacocoTestReport'
    tasks["integrationTest"].finalizedBy 'jacocoTestReport'
    tasks["jacocoTestReport"].finalizedBy 'jacocoToCobertura'
    tasks["jacocoToCobertura"].dependsOn 'jacocoTestReport'
}

import org.gradle.api.tasks.testing.logging.TestExceptionFormat
import org.gradle.api.tasks.testing.logging.TestLogEvent

tasks.withType(Test) {
    testLogging {
        info {
            events TestLogEvent.FAILED,
                    TestLogEvent.PASSED,
                    TestLogEvent.SKIPPED
        }
        debug {
            events TestLogEvent.STARTED,
                    TestLogEvent.FAILED,
                    TestLogEvent.PASSED,
                    TestLogEvent.SKIPPED,
                    TestLogEvent.STANDARD_OUT,
                    TestLogEvent.STANDARD_ERROR
            exceptionFormat TestExceptionFormat.FULL
            showExceptions true
            showCauses true
            showStackTraces true
            showStandardStreams true
        }

        afterSuite { desc, result ->
            if (!desc.parent) { // will match the outermost suite
                def output = "Results: ${result.resultType} (${result.testCount} tests, ${result.successfulTestCount} passed, ${result.failedTestCount} failed, ${result.skippedTestCount} skipped)"
                def startItem = '|  ', endItem = '  |'
                def repeatLength = startItem.length() + output.length() + endItem.length()
                println('\n' + ('-' * repeatLength) + '\n' + startItem + output + endItem + '\n' + ('-' * repeatLength))
            }
        }
    }
}
