plugins {
    id 'version-catalog'
    id 'maven-publish'
}

group 'io.wyden'
version '0.2.3-SNAPSHOT'

ext {
    repository_username = System.env.NEXUS_DEPLOY_USERNAME
    repository_password = System.env.NEXUS_DEPLOY_PASSWORD
}

repositories {
    mavenLocal()
    maven {
        name 'nexus-snapshots'
        url 'https://repo.wyden.io/nexus/repository/snapshots/'
        credentials {
            username repository_username
            password repository_password
        }
    }
    mavenCentral()
}

catalog {
    versionCatalog {
        /*-----------------------------------------pattern--------------------------------------------------------------*/
        /* library(<alias>, <group>, <artifact>)                                                                        */
        /* When declaring aliases, it’s worth noting that any of the -, _ and . characters can be used as separators,   */
        /* but the generated catalog will have all normalized to .: for example foo-bar as an alias is converted        */
        /* to foo.bar automatically.                                                                                    */
        /*-----------------------------------------modules------------------------------------------------*/
        version("cloud-utils-rabbitmq", "0.1.0-SNAPSHOT")
            library("cloud-utils-rabbitmq", "io.wyden", "cloud-utils-rabbitmq").versionRef("cloud-utils-rabbitmq")

        version("cloud-utils-telemetry", "0.0.42-SNAPSHOT")
            library("cloud-utils-telemetry", "io.wyden", "cloud-utils-telemetry").versionRef("cloud-utils-telemetry")

        version("cloud-utils-hazelcast", "0.0.7-SNAPSHOT")
            library("cloud-utils-hazelcast", "io.wyden", "cloud-utils-hazelcast").versionRef("cloud-utils-hazelcast")

        version("cloud-utils-rabbitmq-destinations", "0.4.0-SNAPSHOT")
            library("cloud-utils-rabbitmq-destinations", "io.wyden", "cloud-utils-rabbitmq-destinations").versionRef("cloud-utils-rabbitmq-destinations")

        version("cloud-utils-spring", "0.0.12-SNAPSHOT")
            library("cloud-utils-spring", "io.wyden", "cloud-utils-spring").versionRef("cloud-utils-spring")

        version("cloud-utils-rest", "0.0.8-SNAPSHOT")
            library("cloud-utils-rest", "io.wyden", "cloud-utils-rest").versionRef("cloud-utils-rest")

        version("cloud-utils-test", "0.0.14-SNAPSHOT")
            library("cloud-utils-test", "io.wyden", "cloud-utils-test").versionRef("cloud-utils-test")

        version("cloud-utils-tools", "0.0.21-SNAPSHOT")
            library("cloud-utils-tools", "io.wyden", "cloud-utils-tools").versionRef("cloud-utils-tools")

        version("market-data-client", "0.0.6-SNAPSHOT")
            library("market-data-client", "io.wyden", "market-data-client").versionRef("market-data-client")

        version("reference-data-domain", "0.0.32-SNAPSHOT")
            library("reference-data-domain", "io.wyden", "reference-data-domain").versionRef("reference-data-domain")

        version("target-registry-domain", "0.0.1-SNAPSHOT")
            library("target-registry-domain", "io.wyden", "target-registry-domain").versionRef("target-registry-domain")

        version("reference-data-client", "0.0.61-SNAPSHOT")
            library("reference-data-client", "io.wyden", "reference-data-client").versionRef("reference-data-client")

        version("published-language-oems", "1.1.1-SNAPSHOT")
            library("published-language-oems", "io.wyden.published-language", "oems").versionRef("published-language-oems")

        version("ordercollider-domain", "0.0.14-SNAPSHOT")
            library("ordercollider-domain", "io.wyden.oems.ordercollider", "domain").versionRef("ordercollider-domain")

        version("order-gateway-domain", "0.0.13-SNAPSHOT")
            library("order-gateway-domain", "io.wyden.oems.order-gateway", "order-gateway-domain").versionRef("order-gateway-domain")

        version("execution-engine-domain", "0.0.4-SNAPSHOT")
            library("execution-engine-domain", "io.wyden.oems.execution-engine", "execution-engine-domain").versionRef("execution-engine-domain")

        version("booking-engine-domain", "0.0.5-SNAPSHOT")
            library("booking-engine-domain", "io.wyden", "booking-engine-domain").versionRef("booking-engine-domain")

        version("access-gateway-domain", "0.0.28-SNAPSHOT")
            library("access-gateway-domain", "io.wyden", "access-gateway-domain").versionRef("access-gateway-domain")

        version("access-gateway-client", "0.0.41-SNAPSHOT")
            library("access-gateway-client", "io.wyden", "access-gateway-client").versionRef("access-gateway-client")

        version ("risk-engine-domain", "0.0.2-SNAPSHOT")
            library("risk-engine-domain", "io.wyden.risk-engine", "risk-engine-domain").versionRef("risk-engine-domain")

        version("rest-api-domain", "0.0.3-SNAPSHOT")
            library("rest-api-domain", "io.wyden.apiserver", "rest-api-domain").versionRef("rest-api-domain")

        version("rest-api-client", "0.3.1-SNAPSHOT")
            library("rest-api-client", "io.wyden.apiserver", "rest-api-client").versionRef("rest-api-client")

        version("rest-api-server", "0.0.17-SNAPSHOT")
            library("rest-api-server", "io.wyden.apiserver", "rest-api-server").versionRef("rest-api-server")

        version("settlement-client", "0.3.0-SNAPSHOT")
        library("settlement-client", "io.wyden", "settlement-client").versionRef("settlement-client")

        version("rest-management-domain", "0.11.2-SNAPSHOT")
            library("rest-management-domain", "io.wyden", "rest-management-domain").versionRef("rest-management-domain")

        version("message-scheduler-domain", "0.1.0-SNAPSHOT")
            library("message-scheduler-domain", "io.wyden.messagescheduler", "message-scheduler-domain").versionRef("message-scheduler-domain")

        version("scenario-runner", "0.0.112-SNAPSHOT")
            library("scenario-runner", "io.wyden.integrationtests", "scenario-runner").versionRef("scenario-runner")

        version("audit-client", "0.0.2-SNAPSHOT")
            library("audit-client", "io.wyden.audit", "audit-client").versionRef("audit-client")

        version("rate-service-client", "0.0.13-SNAPSHOT")
            library("rate-service-client", "io.wyden.rate", "rate-service-client").versionRef("rate-service-client")

        version("rate-service-domain", "0.0.7-SNAPSHOT")
            library("rate-service-domain", "io.wyden.rate", "rate-service-domain").versionRef("rate-service-domain")

        version("pricing-service-client", "0.0.1-SNAPSHOT")
            library("pricing-service-client", "io.wyden.pricing", "pricing-service-client").versionRef("pricing-service-client")

        version("pricing-service-domain", "0.0.1-SNAPSHOT")
            library("pricing-service-domain", "io.wyden.pricing", "pricing-service-domain").versionRef("pricing-service-domain")

        version("broker-config-service-domain", "0.0.1-SNAPSHOT")
            library("broker-config-service-domain", "io.wyden.broker-config-service", "broker-config-domain").versionRef("broker-config-service-domain")

        version("agency-trading-service-domain", "0.0.9-SNAPSHOT")
            library("agency-trading-service-domain", "io.wyden.agencytrading", "agency-trading-service-domain").versionRef("agency-trading-service-domain")

        version("clob-gateway-domain", "0.0.21-SNAPSHOT")
            library("clob-gateway-domain", "io.wyden.clob.gateway", "clob-gateway-domain").versionRef("clob-gateway-domain")

        version("quoting-engine-domain", "0.0.5-SNAPSHOT")
            library("quoting-engine-domain", "io.wyden.quoting", "quoting-engine-domain").versionRef("quoting-engine-domain")

        version("fix-api-domain", "0.0.2-SNAPSHOT")
            library("fix-api-domain", "io.wyden.apiserver", "fix-api-domain").versionRef("fix-api-domain")

        version("exchange-core2-collections", "0.5.1-AERON-SNAPSHOT")
            library("exchange-core2-collections", "exchange.core2", "collections").versionRef("exchange-core2-collections")

        version("exchange-core", "1.2.2-AERON-SNAPSHOT")
            library("exchange-core", "exchange.core2", "exchange-core").versionRef("exchange-core")

        version("smart-order-router-domain", "0.0.16-SNAPSHOT")
            library("smart-order-router-domain", "io.wyden.sor", "smart-order-router-domain").versionRef("smart-order-router-domain")

        version("auto-hedger-domain", "0.0.1-SNAPSHOT")
            library("auto-hedger-domain", "io.wyden.hedger", "auto-hedger-domain").versionRef("auto-hedger-domain")

        /*-----------------------------------------libraries----------------------------------------------*/

        version("resourcemanager", "5.1.20-3010")
        library("resourcemanager", "algotrader", "resourcemanager").versionRef("resourcemanager")

//      When changing version number or adding new connector - see target-registry/README.md for additional steps
        version("algotrader-adapters", "6.6.10-3093")
            library("connector-sdk", "algotrader", "connector-sdk").versionRef("algotrader-adapters")
            library("connector-bitfinex", "algotrader", "connector-bitfinex").versionRef("algotrader-adapters")
            library("connector-coinapi", "algotrader", "connector-coinapi").versionRef("algotrader-adapters")
            library("connector-coinbase", "algotrader", "connector-coinbase").versionRef("algotrader-adapters")
            library("connector-bitmex", "algotrader", "connector-bitmex").versionRef("algotrader-adapters")
            library("connector-generic", "algotrader", "connector-generic").versionRef("algotrader-adapters")
            library("connector-binance-spot", "algotrader", "connector-binance-global").versionRef("algotrader-adapters")
            library("connector-bitstamp", "algotrader", "connector-bitstamp").versionRef("algotrader-adapters")
            library("connector-kraken", "algotrader", "connector-kraken").versionRef("algotrader-adapters")
            library("connector-bit2me", "algotrader", "connector-bit2me").versionRef("algotrader-adapters")
            library("connector-goldman-sachs", "algotrader", "connector-goldman-sachs").versionRef("algotrader-adapters")
            library("connector-lmax-global", "algotrader", "connector-lmax-global").versionRef("algotrader-adapters")
            library("connector-c360t", "algotrader", "connector-360T").versionRef("algotrader-adapters")
            library("connector-b2c2", "algotrader", "connector-b2c2").versionRef("algotrader-adapters")
            library("connector-wintermute", "algotrader", "connector-wintermute").versionRef("algotrader-adapters")
            library("connector-scrypt", "algotrader", "connector-scrypt").versionRef("algotrader-adapters")
            library("connector-fireblocks", "algotrader", "connector-fireblocks").versionRef("algotrader-adapters")
            library("connector-bullish", "algotrader", "connector-bullish").versionRef("algotrader-adapters")

        version("flyway", "9.16.3")
            library("flyway-core", "org.flywaydb", "flyway-core").versionRef("flyway")

        version("spring-vault", "4.0.1")
            library("spring-cloud-starter-vault-config", "org.springframework.cloud", "spring-cloud-starter-vault-config").versionRef("spring-vault")

        version("postgresql", "42.6.0")
            library("postgresql", "org.postgresql", "postgresql").versionRef("postgresql")

        version("r2dbc-postgresql", "1.0.1.RELEASE")
            library("r2dbc-postgresql", "org.postgresql", "r2dbc-postgresql").versionRef("r2dbc-postgresql")

        version("oracle", "19.3.0.0")
            library("oracle", "com.oracle.ojdbc", "ojdbc8").versionRef("oracle")

        version("spring-security", "6.1.1")
            library("spring-security-test", "org.springframework.security", "spring-security-test").versionRef("spring-security")

        version("spring-boot", "3.1.1")
            library("spring-boot-starter", "org.springframework.boot", "spring-boot-starter").versionRef("spring-boot")
            library("spring-boot-starter-web", "org.springframework.boot", "spring-boot-starter-web").versionRef("spring-boot")
            library("spring-boot-starter-security", "org.springframework.boot", "spring-boot-starter-security").versionRef("spring-boot")
            library("spring-boot-starter-webflux", "org.springframework.boot", "spring-boot-starter-webflux").versionRef("spring-boot")
            library("spring-boot-starter-websocket", "org.springframework.boot", "spring-boot-starter-websocket").versionRef("spring-boot")
            library("spring-boot-starter-data-jpa", "org.springframework.boot", "spring-boot-starter-data-jpa").versionRef("spring-boot")
            library("spring-boot-starter-data-jdbc", "org.springframework.boot", "spring-boot-starter-data-jdbc").versionRef("spring-boot")
            library("spring-boot-starter-data-r2dbc", "org.springframework.boot", "spring-boot-starter-data-r2dbc").versionRef("spring-boot")
            library("spring-boot-starter-actuator", "org.springframework.boot", "spring-boot-starter-actuator").versionRef("spring-boot")
            library("spring-boot-starter-graphql", "org.springframework.boot", "spring-boot-starter-graphql").versionRef("spring-boot")
            library("spring-boot-starter-oauth2-resource-server", "org.springframework.boot", "spring-boot-starter-oauth2-resource-server").versionRef("spring-boot")
            library("spring-boot-starter-oauth2-client", "org.springframework.boot", "spring-boot-starter-oauth2-client").versionRef("spring-boot")
            library("spring-boot-configuration-processor", "org.springframework.boot", "spring-boot-configuration-processor").versionRef("spring-boot")
            library("spring-boot-starter-test", "org.springframework.boot", "spring-boot-starter-test").versionRef("spring-boot")
            library("spring-boot-starter-validation", "org.springframework.boot", "spring-boot-starter-validation").versionRef("spring-boot")

        version("spring-aspects", "6.0.10")
            library("spring-aspects", "org.springframework", "spring-aspects").versionRef("spring-aspects")

        version("spring-graphql-test", "1.2.2")
                library("spring-graphql-test", "org.springframework.graphql", "spring-graphql-test").versionRef("spring-graphql-test")

        version("kubernetes-client-java", "18.0.1")
                library("kubernetes-client-java", "io.kubernetes", "client-java").versionRef("kubernetes-client-java")

        version("micrometer", "1.11.1") // version in sync with spring boot actuator
            library("micrometer-prometheus", "io.micrometer", "micrometer-registry-prometheus").versionRef("micrometer")

        version("spring-framework", "6.0.10")
            library("spring-context", "org.springframework", "spring-context").versionRef("spring-framework")

        version("spring-framework-amqp", "3.0.5")
            library("spring-rabbit-test", "org.springframework.amqp", "spring-rabbit-test").versionRef("spring-framework-amqp")

        version("openapi", "2.6.0")
            library("springdoc-openapi-starter-webflux-ui", "org.springdoc", "springdoc-openapi-starter-webflux-ui").versionRef("openapi")
            library("springdoc-openapi-starter-webmvc-ui", "org.springdoc", "springdoc-openapi-starter-webmvc-ui").versionRef("openapi")

        version("spring-retry", "2.0.3")
            library("spring-retry", "org.springframework.retry", "spring-retry").versionRef("spring-retry")

        version("h2", "2.1.214")
            library("h2", "com.h2database", "h2").versionRef("h2")

        version("jbehave", "5.1")
            library("jbehave-core", "org.jbehave", "jbehave-core").versionRef("jbehave")
            library("jbehave-site", "org.jbehave.site:jbehave-site-resources:3.5")

        version("junit", "5.9.2")
            library("junit-jupiter", "org.junit.jupiter", "junit-jupiter").versionRef("junit")
            library("junit-jupiter-api", "org.junit.jupiter", "junit-jupiter-api").versionRef("junit")
            library("junit-jupiter-engine", "org.junit.jupiter", "junit-jupiter-engine").versionRef("junit")
            library("junit-jupiter-params", "org.junit.jupiter", "junit-jupiter-params").versionRef("junit")

        version("playwright", "1.51.0")
        library("playwright", "com.microsoft.playwright", "playwright").versionRef("playwright")

        // update together with junit
        // check junit-bom: https://mvnrepository.com/artifact/org.junit/junit-bom on compatible junit-platform version
        version("junit-platform", "1.9.2")
            library("junit-platform-suite", "org.junit.platform", "junit-platform-suite").versionRef("junit-platform")

        version("systemstubs", "2.1.3")
            library("systemstubs-jupiter", "uk.org.webcompere", "system-stubs-jupiter").versionRef("systemstubs")

        version("junit4", "4.13.2")
            library("junit4", "junit", "junit").versionRef("junit4")

        version("mockito", "5.3.1")
            library("mockito-core", "org.mockito", "mockito-core").versionRef("mockito")
            library("mockito-junit-jupiter", "org.mockito", "mockito-junit-jupiter").versionRef("mockito")

        version("protobuf-java", "3.22.3")
            library("protobuf-protoc", "com.google.protobuf", "protoc").versionRef("protobuf-java")
            library("protobuf-java", "com.google.protobuf", "protobuf-java").versionRef("protobuf-java")
            library("protobuf-java-util", "com.google.protobuf", "protobuf-java-util").versionRef("protobuf-java")

        version("slf4j", "2.0.9")
            library("slf4j-api", "org.slf4j", "slf4j-api").versionRef("slf4j")

        version("guava", "31.1-jre")
            library("guava", "com.google.guava", "guava").versionRef("guava")

        version("commons-lang3", "3.12.0")
            library("commons-lang3", "org.apache.commons", "commons-lang3").versionRef("commons-lang3")

        version("commons-collections4", "4.4")
            library("commons-collections4", "org.apache.commons", "commons-collections4").versionRef("commons-collections4")

        version("commons-math", "3.6.1")
            library("commons-math", "org.apache.commons", "commons-math3").versionRef("commons-math")

        version("eclipse-collections-api", "10.2.0")
            library("eclipse-collections-api", "org.eclipse.collections", "eclipse-collections-api").versionRef("eclipse-collections-api")

        version("eclipse-collections", "10.2.0")
            library("eclipse-collections", "org.eclipse.collections", "eclipse-collections").versionRef("eclipse-collections")

        version("agrona", "1.20.0")
            library("agrona", "org.agrona", "agrona").versionRef("agrona")

        version("aeron", "1.46.5")
            library("aeron-all", "io.aeron", "aeron-all").versionRef("aeron")
            library("aeron-agent", "io.aeron", "aeron-agent").versionRef("aeron")

        version("hdrhistogram", "2.1.12")
            library("hdrhistogram", "org.hdrhistogram", "HdrHistogram").versionRef("hdrhistogram")

        version("hamcrest-library", "1.3")
            library("hamcrest-library", "org.hamcrest", "hamcrest-library").versionRef("hamcrest-library")

        version("jna", "5.11.0")
            library("jna", "net.java.dev.jna", "jna").versionRef("jna")
            library("jna-platform", "net.java.dev.jna", "jna-platform").versionRef("jna")

        version("logback-classic", "1.4.14")
            library("logback-classic", "ch.qos.logback", "logback-classic").versionRef("logback-classic")

        version("lz4-java", "1.8.0")
            library("lz4-java", "org.lz4", "lz4-java").versionRef("lz4-java")

        version("disruptor", "3.4.2")
            library("disruptor", "com.lmax", "disruptor").versionRef("disruptor")

        version("affinity", "3.2.2")
            library("affinity", "net.openhft", "affinity").versionRef("affinity")

        version("chronicle-wire", "2.19.1")
            library("chronicle-wire", "net.openhft", "chronicle-wire").versionRef("chronicle-wire")

        version("chronicle-wire-ea13", "2.25ea13")
            library("chronicle-wire-ea13", "net.openhft", "chronicle-wire").versionRef("chronicle-wire-ea13")

        version("juncture-nasdaq", "1.0.0")
            library("juncture-nasdaq", "com.paritytrading.juncture", "juncture-nasdaq").versionRef("juncture-nasdaq")

        version("nassau-util", "1.0.0")
            library("nassau-util", "com.paritytrading.nassau", "nassau-util").versionRef("nassau-util")

        version("cucumber", "7.21.1")
            library("cucumber-java", "io.cucumber", "cucumber-java").versionRef("cucumber")
            library("cucumber-java8", "io.cucumber", "cucumber-java8").versionRef("cucumber")
            library("cucumber-junit", "io.cucumber", "cucumber-junit").versionRef("cucumber")
            library("cucumber-junit-platform-engine", "io.cucumber", "cucumber-junit-platform-engine").versionRef("cucumber")
            library("cucumber-spring", "io.cucumber", "cucumber-spring").versionRef("cucumber")
            library("cucumber-picocontainer", "io.cucumber", "cucumber-picocontainer").versionRef("cucumber")

        version("jetbrains-annotations", "23.0.0")
            library("jetbrains-annotations", "org.jetbrains", "annotations").versionRef("jetbrains-annotations")

        version("fluentd", "0.3.4")
            library("fluent-logger", "org.fluentd", "fluent-logger").versionRef("fluentd")

        version("sbe", "1.2.0-SNAPSHOT")
            library("sbe", "io.wyden.published-language", "sbe").versionRef("sbe")

        version("testcontainers", "1.19.1")
            library("testcontainers", "org.testcontainers", "testcontainers").versionRef("testcontainers")
            library("testcontainers-junit-jupiter", "org.testcontainers", "junit-jupiter").versionRef("testcontainers")
            library("testcontainers-rabbitmq", "org.testcontainers", "rabbitmq").versionRef("testcontainers")
            library("testcontainers-vault", "org.testcontainers", "vault").versionRef("testcontainers")
            library("testcontainers-postgresql", "org.testcontainers", "postgresql").versionRef("testcontainers")
            library("testcontainers-mockserver", "org.testcontainers", "mockserver").versionRef("testcontainers")
            library("testcontainers-k3s", "org.testcontainers", "k3s").versionRef("testcontainers")
            library("testcontainers-oracle", "org.testcontainers", "oracle-xe").versionRef("testcontainers")

        version("testcontainers-keycloak", "2.3.0")
            library("testcontainers-keycloak", "com.github.dasniko", "testcontainers-keycloak").versionRef("testcontainers-keycloak")

        version("rest-assured", "5.3.0")
            library("rest-assured", "io.rest-assured", "rest-assured").versionRef("rest-assured")

        version("okhttp3", "4.10.0")
            library("mockwebserver", "com.squareup.okhttp3", "mockwebserver").versionRef("okhttp3")
            library("okhttp", "com.squareup.okhttp3", "okhttp").versionRef("okhttp3")

        version("rabbitmq-amqp-client", "5.16.0")
            library("rabbitmq-amqp-client", "com.rabbitmq", "amqp-client").versionRef("rabbitmq-amqp-client")

        version("rabbitmq-http-client", "4.1.2")
            library("rabbitmq-http-client", "com.rabbitmq", "http-client").versionRef("rabbitmq-http-client")

        version("assertj", "3.23.1")
            library("assertj-core", "org.assertj", "assertj-core").versionRef("assertj")

        version("jackson-databind", "2.15.2")
            library("jackson-databind", "com.fasterxml.jackson.core", "jackson-databind").versionRef("jackson-databind")

        version("jackson-databind-nullable", "0.2.6")
            library("jackson-databind-nullable", "org.openapitools", "jackson-databind-nullable").versionRef("jackson-databind-nullable")

        version("uuid-creator", "6.1.1")
            library("uuid-creator", "com.github.f4b6a3", "uuid-creator").versionRef("uuid-creator")

        version("projectreactor", "3.5.7")
            library("reactor-core", "io.projectreactor", "reactor-core").versionRef("projectreactor")
            library("reactor-test", "io.projectreactor", "reactor-test").versionRef("projectreactor")

        version("reactor-core-micrometer", "1.1.2")
            library("reactor-core-micrometer", "io.projectreactor", "reactor-core-micrometer").versionRef("reactor-core-micrometer")

        version("hazelcast", "5.2.3")
            library("hazelcast", "com.hazelcast", "hazelcast").versionRef("hazelcast")
            library("hazelcast-jet-protobuf", "com.hazelcast.jet", "hazelcast-jet-protobuf").versionRef("hazelcast")
            library("hazelcast-sql", "com.hazelcast", "hazelcast-sql").versionRef("hazelcast")

        version("opentelemetry", "1.25.0")
        version("opentelemetry-alpha", "1.25.0-alpha")
            library("opentelemetry-api", "io.opentelemetry", "opentelemetry-api").versionRef("opentelemetry")
            library("opentelemetry-sdk", "io.opentelemetry", "opentelemetry-sdk").versionRef("opentelemetry")
            library("opentelemetry-exporter-zipkin", "io.opentelemetry", "opentelemetry-exporter-zipkin").versionRef("opentelemetry")
            library("opentelemetry-exporter-otlp", "io.opentelemetry", "opentelemetry-exporter-otlp").versionRef("opentelemetry")
            library("opentelemetry-semconv", "io.opentelemetry", "opentelemetry-semconv").versionRef("opentelemetry-alpha")
            library("opentelemetry-logback-mdc", "io.opentelemetry.instrumentation", "opentelemetry-logback-mdc-1.0").versionRef("opentelemetry-alpha")

        version("netty", "4.1.94.Final")
            library("netty-resolver-dns-native-macos", "io.netty", "netty-resolver-dns-native-macos").versionRef("netty")

        version("mock-server", "5.15.0")
            library("mockserver-client", "org.mock-server", "mockserver-client-java").versionRef("mock-server")

        version("quickfixj", "2.3.1")
            library("quickfixj-core", "org.quickfixj", "quickfixj-core").versionRef("quickfixj")
            library("quickfixj-messages-fix44", "org.quickfixj", "quickfixj-messages-fix44").versionRef("quickfixj")

        version("snakeyaml", "1.33")
            library("snakeyaml", "org.yaml", "snakeyaml").versionRef("snakeyaml")

        version("javafaker", "1.0.2")
            library("javafaker", "com.github.javafaker", "javafaker").versionRef("javafaker")

        version("awaitility", "4.2.0")
            library("awaitility", "org.awaitility", "awaitility").versionRef("awaitility")

        version("logstash-logback-encoder", "7.2")
            library("logstash-logback-encoder", "net.logstash.logback", "logstash-logback-encoder").versionRef("logstash-logback-encoder")

        version("keycloak-admin-client", "22.0.1")
            library("keycloak-admin-client", "org.keycloak", "keycloak-admin-client").versionRef("keycloak-admin-client")

        version("vavr-vavr", "0.10.4")
            library("vavr-vavr", "io.vavr", "vavr").versionRef("vavr-vavr")

        version("jakarta-validation", "3.0.2")
            library("jakarta-validation", "jakarta.validation", "jakarta.validation-api").versionRef("jakarta-validation")

        version("jakarta-annotation", "2.1.1")
            library("jakarta-annotation", "jakarta.annotation", "jakarta.annotation-api").versionRef("jakarta-annotation")

        version("jakarta-annotation-api", "3.0.0")
            library("jakarta-annotation-api", "jakarta.annotation", "jakarta.annotation-api").versionRef("jakarta-annotation-api")

        version("javax-annotation-api", "1.3.2")
            library("javax-annotation-api", "javax.annotation", "javax.annotation-api").versionRef("javax-annotation-api")

        version("hibernate-jpamodelgen", "6.2.5.Final")
            library("hibernate-jpamodelgen", "org.hibernate.orm", "hibernate-jpamodelgen").versionRef("hibernate-jpamodelgen")

        version("aspectjweaver", "1.9.20.1")
            library("aspectjweaver", "org.aspectj", "aspectjweaver").versionRef("aspectjweaver")

        /*-----------------------------------------plugins------------------------------------------------*/
            plugin("spring.boot", "org.springframework.boot").versionRef("spring-boot")

        version("dependency-management-plugin", "1.1.0")
            plugin("dependency-management", "io.spring.dependency-management").versionRef("dependency-management-plugin")

        version("sonarqube-plugin", "6.0.1.5171")
            plugin("sonarqube", "org.sonarqube").versionRef("sonarqube-plugin")

        version("protobuf-plugin", "0.9.1")
            plugin("protobuf", "com.google.protobuf").versionRef("protobuf-plugin")

        version("jacocoToCobertura", "1.0.2")
            plugin("jacocoToCobertura", "net.razvan.jacoco-to-cobertura").versionRef("jacocoToCobertura")

        version("test-retry", "1.5.6")
            plugin("test-retry", "org.gradle.test-retry").versionRef("test-retry")

        version("allure-plugin", "2.11.2")
            plugin("allure-plugin", "io.qameta.allure").versionRef("allure-plugin")

        version("openapi-generator", "6.6.0")
            plugin("openapi-generator", "org.openapi.generator").versionRef("openapi-generator")
    }
}


publishing {
    publications {
        mavenJava(MavenPublication) {
            repositories {
                maven {
                    name 'nexus-snapshots'
                    url 'https://repo.wyden.io/nexus/repository/snapshots/'
                    credentials {
                        username repository_username
                        password repository_password
                    }
                }
            }
            from components.versionCatalog
        }
    }
}
