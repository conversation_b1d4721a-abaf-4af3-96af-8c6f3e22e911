plugins {
    id 'java'
    id 'idea'
    id 'jaco<PERSON>'
    alias dependencyCatalog.plugins.spring.boot
    alias dependencyCatalog.plugins.dependency.management
    alias dependencyCatalog.plugins.sonarqube
    alias dependencyCatalog.plugins.jacocoToCobertura
}

ext {
    repository_username = System.env.NEXUS_DEPLOY_USERNAME
    repository_password = System.env.NEXUS_DEPLOY_PASSWORD
    buildVersion = System.env.BUILD_VERSION ? System.env.BUILD_VERSION : '-SNAPSHOT'
}

group = 'io.wyden.storage'
version = "0.8.0$buildVersion"
sourceCompatibility = '17'
targetCompatibility = '17'

repositories {
    mavenLocal()
    maven {
        name 'nexus-snapshots'
        url 'https://repo.wyden.io/nexus/repository/snapshots/'
        credentials {
            username repository_username
            password repository_password
        }
    }
    mavenCentral()
    maven { url 'https://repo.spring.io/milestone' }
}

dependencyManagement {
    resolutionStrategy {
        cacheChangingModulesFor 0, 'seconds'
    }
}

dependencies {
    implementation dependencyCatalog.cloud.utils.spring
    implementation dependencyCatalog.cloud.utils.telemetry
    implementation dependencyCatalog.cloud.utils.hazelcast
    implementation dependencyCatalog.cloud.utils.tools
    implementation dependencyCatalog.published.language.oems
    implementation dependencyCatalog.ordercollider.domain
    implementation dependencyCatalog.order.gateway.domain
    implementation dependencyCatalog.booking.engine.domain
    implementation dependencyCatalog.reference.data.domain
    implementation dependencyCatalog.access.gateway.domain
    implementation dependencyCatalog.rest.api.domain
    implementation dependencyCatalog.risk.engine.domain
    implementation dependencyCatalog.rate.service.domain
    implementation dependencyCatalog.broker.config.service.domain
    implementation dependencyCatalog.agency.trading.service.domain
    implementation dependencyCatalog.clob.gateway.domain
    implementation dependencyCatalog.smart.order.router.domain
    implementation dependencyCatalog.quoting.engine.domain
    implementation dependencyCatalog.target.registry.domain
    implementation dependencyCatalog.message.scheduler.domain

    implementation(dependencyCatalog.fix.api.domain) {
        // Exclude transitive dependencies
        transitive = false
    }
    implementation(dependencyCatalog.quickfixj.core) {
        // Exclude transitive dependencies
        transitive = false
    }
    implementation(dependencyCatalog.quickfixj.messages.fix44) {
        // Exclude transitive dependencies
        transitive = false
    }

    implementation dependencyCatalog.spring.boot.starter.web
    implementation dependencyCatalog.springdoc.openapi.starter.webmvc.ui
    implementation dependencyCatalog.spring.boot.starter.actuator
    implementation dependencyCatalog.spring.boot.starter.data.jdbc
    implementation dependencyCatalog.postgresql
    implementation dependencyCatalog.oracle
    implementation dependencyCatalog.flyway.core
    implementation dependencyCatalog.hazelcast
    implementation dependencyCatalog.hazelcast.jet.protobuf
    implementation dependencyCatalog.protobuf.java.util

    implementation dependencyCatalog.hazelcast.sql

    testImplementation dependencyCatalog.spring.boot.starter.test
    testImplementation dependencyCatalog.junit.jupiter.api

    testRuntimeOnly dependencyCatalog.junit.jupiter.engine
}

testing {
    suites {
        test {
            useJUnitJupiter()
        }

        integrationTest(JvmTestSuite) {
            dependencies {
                implementation project
                implementation dependencyCatalog.cloud.utils.spring
                implementation dependencyCatalog.cloud.utils.telemetry
                implementation dependencyCatalog.cloud.utils.hazelcast
                implementation dependencyCatalog.cloud.utils.tools
                implementation dependencyCatalog.published.language.oems
                implementation dependencyCatalog.access.gateway.domain
                implementation dependencyCatalog.ordercollider.domain
                implementation dependencyCatalog.order.gateway.domain
                implementation dependencyCatalog.booking.engine.domain
                implementation dependencyCatalog.reference.data.domain
                implementation dependencyCatalog.rest.api.domain
                implementation dependencyCatalog.risk.engine.domain
                implementation dependencyCatalog.rate.service.domain
                implementation dependencyCatalog.broker.config.service.domain
                implementation dependencyCatalog.agency.trading.service.domain
                implementation dependencyCatalog.clob.gateway.domain
                implementation dependencyCatalog.quoting.engine.domain
                implementation dependencyCatalog.smart.order.router.domain
                implementation dependencyCatalog.target.registry.domain
                implementation dependencyCatalog.message.scheduler.domain
                implementation(dependencyCatalog.fix.api.domain) {
                    // Exclude transitive dependencies
                    transitive = false
                }
                implementation(dependencyCatalog.quickfixj.core) {
                    // Exclude transitive dependencies
                    transitive = false
                }
                implementation(dependencyCatalog.quickfixj.messages.fix44) {
                    // Exclude transitive dependencies
                    transitive = false
                }

                implementation dependencyCatalog.spring.boot.starter.web
                implementation dependencyCatalog.spring.boot.starter.actuator
                implementation dependencyCatalog.spring.boot.starter.data.jdbc
                implementation dependencyCatalog.postgresql
                implementation dependencyCatalog.oracle
                implementation dependencyCatalog.flyway.core
                implementation dependencyCatalog.hazelcast
                implementation dependencyCatalog.hazelcast.jet.protobuf
                implementation dependencyCatalog.hazelcast.sql

                implementation dependencyCatalog.spring.boot.starter.test
                implementation dependencyCatalog.junit.jupiter.api
                implementation dependencyCatalog.awaitility
                implementation dependencyCatalog.testcontainers
                implementation dependencyCatalog.testcontainers.junit.jupiter
                implementation dependencyCatalog.testcontainers.postgresql
                implementation dependencyCatalog.testcontainers.oracle

                runtimeOnly dependencyCatalog.junit.jupiter.engine

            }
            targets {
                all {
                    testTask.configure {
                        shouldRunAfter(test)
                    }
                }
            }
        }
    }
}

tasks.named('check') {
    dependsOn(testing.suites.integrationTest)
}


test {
    finalizedBy jacocoTestReport
    testLogging.showStandardStreams = true
}

jacocoTestReport {
    reports {
        xml.enabled true
        csv.enabled true
    }

    getExecutionData().setFrom(fileTree(buildDir).include("/jacoco/*.exec"))
}

jacocoToCobertura {
    inputFile.set(file("$buildDir/reports/jacoco/test/jacocoTestReport.xml"))
    outputFile.set(file("$buildDir/reports/jacoco/test/cobertura.xml"))
}

plugins.withType(JacocoPlugin) {
    tasks["test"].finalizedBy 'jacocoTestReport'
    tasks["integrationTest"].finalizedBy 'jacocoTestReport'
    tasks["jacocoTestReport"].finalizedBy 'jacocoToCobertura'
    tasks["jacocoToCobertura"].dependsOn 'jacocoTestReport'
}

bootJar {
    manifest {
        attributes(
                "Implementation-Version": "${version}"
        )
    }
}

bootRun {
    jvmArgs = ["-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:9070"]
    environment = [
            "FLUENTD_HOST": "localhost",
            "SPRING_PROFILES_ACTIVE": "dev"
    ]
}

sonarqube {
    properties {
        property "sonar.projectKey", "storage"
        property "sonar.projectName", "Storage"
    }
}

test {
    useJUnitPlatform()
}
