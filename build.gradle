import org.gradle.api.tasks.testing.logging.TestExceptionFormat
import org.gradle.api.tasks.testing.logging.TestLogEvent

plugins {
	id 'java'
	id 'idea'
	id 'jacoco'
	alias dependencyCatalog.plugins.spring.boot apply false
	alias dependencyCatalog.plugins.dependency.management apply false
	alias dependencyCatalog.plugins.sonarqube apply false
	alias dependencyCatalog.plugins.jacocoToCobertura apply false
}

ext {
	repository_username = System.env.NEXUS_DEPLOY_USERNAME
	repository_password = System.env.NEXUS_DEPLOY_PASSWORD
	buildVersion = System.env.BUILD_VERSION ? System.env.BUILD_VERSION : '-SNAPSHOT'
}

allprojects {
	ext {
		buildVersion = System.env.BUILD_VERSION ? System.env.BUILD_VERSION : '-SNAPSHOT'
	}
	group = 'io.wyden.apiserver'
	version = "0.9.0$buildVersion"
	sourceCompatibility = '17'

	repositories {
		mavenLocal()
		maven {
			name 'nexus-snapshots'
			url 'https://repo.wyden.io/nexus/repository/snapshots/'
			credentials {
				username repository_username
				password repository_password
			}
		}
		mavenCentral()
		maven { url 'https://repo.spring.io/milestone' }
	}
}

subprojects {
	apply plugin: 'io.spring.dependency-management'
	apply plugin: 'java-library'
	apply plugin: 'org.sonarqube'
	apply plugin: 'jacoco'
	apply plugin: 'net.razvan.jacoco-to-cobertura'

	dependencyManagement {
		resolutionStrategy {
			cacheChangingModulesFor 0, 'seconds'
		}
	}

	dependencies {
		implementation dependencyCatalog.access.gateway.domain
		implementation dependencyCatalog.access.gateway.client
		implementation dependencyCatalog.reference.data.client
		implementation dependencyCatalog.cloud.utils.rabbitmq
		implementation dependencyCatalog.cloud.utils.rabbitmq.destinations
		implementation dependencyCatalog.cloud.utils.telemetry
		implementation dependencyCatalog.cloud.utils.tools
		implementation dependencyCatalog.cloud.utils.spring
		implementation dependencyCatalog.cloud.utils.hazelcast
		implementation dependencyCatalog.published.language.oems
		implementation dependencyCatalog.rest.api.domain

		implementation dependencyCatalog.spring.boot.starter.webflux
		implementation dependencyCatalog.spring.boot.starter.actuator
		implementation dependencyCatalog.quickfixj.core
		implementation dependencyCatalog.quickfixj.messages.fix44
		implementation dependencyCatalog.hazelcast


		testImplementation dependencyCatalog.cloud.utils.test
		testImplementation dependencyCatalog.spring.boot.starter.test
		testImplementation dependencyCatalog.junit.jupiter.api
		testImplementation dependencyCatalog.reactor.test
		testImplementation dependencyCatalog.okhttp
		testImplementation dependencyCatalog.mockwebserver
		testImplementation(dependencyCatalog.hazelcast) { artifact { classifier = 'tests' } }
		testImplementation(dependencyCatalog.awaitility)

		testRuntimeOnly dependencyCatalog.junit.jupiter.engine
	}

	testing {
		suites {
			test {
				useJUnitJupiter()
			}

			integrationTest(JvmTestSuite) {
				dependencies {
					implementation project
					implementation project(":fix-api-common")
					implementation project(':fix-api-common').sourceSets.test.output
					implementation dependencyCatalog.published.language.oems
					implementation dependencyCatalog.cloud.utils.rabbitmq
					implementation dependencyCatalog.cloud.utils.rabbitmq.destinations
					implementation dependencyCatalog.cloud.utils.telemetry
					implementation dependencyCatalog.cloud.utils.test
					implementation dependencyCatalog.cloud.utils.tools
					implementation dependencyCatalog.cloud.utils.hazelcast
					implementation dependencyCatalog.rest.api.domain
					implementation dependencyCatalog.hazelcast
					implementation dependencyCatalog.hazelcast.sql
					implementation(dependencyCatalog.hazelcast) { artifact { classifier = 'tests' } }
					implementation dependencyCatalog.reactor.test
					implementation dependencyCatalog.spring.boot.starter.test
					implementation dependencyCatalog.testcontainers
					implementation dependencyCatalog.testcontainers.junit.jupiter
					implementation dependencyCatalog.testcontainers.rabbitmq
					implementation dependencyCatalog.testcontainers.postgresql
					implementation dependencyCatalog.quickfixj.core
					implementation dependencyCatalog.quickfixj.messages.fix44
					implementation dependencyCatalog.snakeyaml
					implementation dependencyCatalog.javafaker
					implementation dependencyCatalog.okhttp
					implementation dependencyCatalog.mockwebserver
					implementation dependencyCatalog.reference.data.client
				}

				targets {
					all {
						testTask.configure {
							shouldRunAfter(test)
						}
					}
				}
			}
		}
	}

	tasks.named('check') {
		dependsOn(testing.suites.integrationTest)
	}


	test {
		finalizedBy jacocoTestReport
	}

	jacocoTestReport {
		reports {
			xml.enabled true
			csv.enabled true
		}

		getExecutionData().setFrom(fileTree(buildDir).include("/jacoco/*.exec"))
	}

	jacocoToCobertura {
		inputFile.set(file("$buildDir/reports/jacoco/test/jacocoTestReport.xml"))
		outputFile.set(file("$buildDir/reports/jacoco/test/cobertura.xml"))
	}

	plugins.withType(JacocoPlugin) {
		tasks["test"].finalizedBy 'jacocoTestReport'
		tasks["integrationTest"].finalizedBy 'jacocoTestReport'
		tasks["jacocoTestReport"].finalizedBy 'jacocoToCobertura'
		tasks["jacocoToCobertura"].dependsOn 'jacocoTestReport'
	}

	tasks.withType(Test) {
		testLogging {
			info {
				events TestLogEvent.FAILED,
						TestLogEvent.PASSED,
						TestLogEvent.SKIPPED
			}
			debug {
				events TestLogEvent.STARTED,
						TestLogEvent.FAILED,
						TestLogEvent.PASSED,
						TestLogEvent.SKIPPED,
						TestLogEvent.STANDARD_OUT,
						TestLogEvent.STANDARD_ERROR
				exceptionFormat TestExceptionFormat.FULL
				showExceptions true
				showCauses true
				showStackTraces true
				showStandardStreams true
			}

			afterSuite { desc, result ->
				if (!desc.parent) { // will match the outermost suite
					def output = "Results: ${result.resultType} (${result.testCount} tests, ${result.successfulTestCount} passed, ${result.failedTestCount} failed, ${result.skippedTestCount} skipped)"
					def startItem = '|  ', endItem = '  |'
					def repeatLength = startItem.length() + output.length() + endItem.length()
					println('\n' + ('-' * repeatLength) + '\n' + startItem + output + endItem + '\n' + ('-' * repeatLength))
				}
			}
		}
	}
}
