import org.gradle.api.tasks.testing.logging.TestExceptionFormat
import org.gradle.api.tasks.testing.logging.TestLogEvent

plugins {
    id 'java-library'
    id 'idea'
    id "jacoco"
    alias dependencyCatalog.plugins.spring.boot apply false
    alias dependencyCatalog.plugins.dependency.management apply false
    alias dependencyCatalog.plugins.jacocoToCobertura apply false
    alias dependencyCatalog.plugins.sonarqube
}

ext {
    repository_username = System.env.NEXUS_DEPLOY_USERNAME
    repository_password = System.env.NEXUS_DEPLOY_PASSWORD
    buildVersion = System.env.BUILD_VERSION ? System.env.BUILD_VERSION : '-SNAPSHOT'
}

sonarqube {
    properties {
        property "sonar.projectKey", "connector-wrapper"
        property "sonar.projectName", "Connector Wrapper"
    }
}

allprojects {
    ext {
        buildVersion = System.env.BUILD_VERSION ? System.env.BUILD_VERSION : '-SNAPSHOT'
    }
    group = 'io.wyden'
    version = "0.7.0$buildVersion"
    sourceCompatibility = '17'
    targetCompatibility = '17'

    repositories {
        mavenLocal()
        maven {
            name 'nexus-releases'
            url 'https://repo.wyden.io/nexus/repository/releases/'
            credentials {
                username repository_username
                password repository_password
            }
        }
        maven {
            name 'nexus-main'
            url 'https://repo.wyden.io/nexus/repository/main/'
            credentials {
                username repository_username
                password repository_password
            }
        }
        maven {
            name 'nexus-snapshot'
            url 'https://repo.wyden.io/nexus/repository/snapshots/'
            credentials {
                username repository_username
                password repository_password
            }
        }
        mavenCentral()
        maven { url 'https://repo.spring.io/milestone' }
    }
}

subprojects {

    apply plugin: 'org.springframework.boot'
    apply plugin: 'io.spring.dependency-management'
    apply plugin: 'java-library'
    apply plugin: 'org.sonarqube'
    apply plugin: 'jacoco'
    apply plugin: 'net.razvan.jacoco-to-cobertura'

    dependencies {
        api dependencyCatalog.fluent.logger
        api dependencyCatalog.cloud.utils.spring
        api dependencyCatalog.cloud.utils.telemetry
        api dependencyCatalog.cloud.utils.tools

        implementation dependencyCatalog.published.language.oems
        implementation dependencyCatalog.rate.service.client
        api dependencyCatalog.cloud.utils.rabbitmq
        api dependencyCatalog.cloud.utils.rabbitmq.destinations

        api dependencyCatalog.connector.sdk

        api dependencyCatalog.spring.boot.starter.webflux
        api dependencyCatalog.spring.boot.starter.actuator
        api dependencyCatalog.springdoc.openapi.starter.webflux.ui
        implementation dependencyCatalog.jakarta.validation

        testImplementation dependencyCatalog.spring.boot.starter.test
        testImplementation dependencyCatalog.cloud.utils.test
        testImplementation dependencyCatalog.reactor.test
        testImplementation dependencyCatalog.spring.rabbit.test
        testImplementation dependencyCatalog.junit.jupiter.api
        testImplementation dependencyCatalog.awaitility

        testRuntimeOnly dependencyCatalog.junit.jupiter.engine
    }

    testing {
        suites {
            test {
                useJUnitJupiter()
            }

            integrationTest(JvmTestSuite) {
                dependencies {
                    implementation project
                    implementation project(':connector-wrapper-base')
                    implementation dependencyCatalog.published.language.oems
                    implementation dependencyCatalog.cloud.utils.rabbitmq
                    implementation dependencyCatalog.cloud.utils.rabbitmq.destinations
                    implementation dependencyCatalog.spring.boot.starter.test
                    implementation dependencyCatalog.testcontainers
                    implementation dependencyCatalog.testcontainers.junit.jupiter
                    implementation dependencyCatalog.testcontainers.rabbitmq
                    implementation(dependencyCatalog.hazelcast) { artifact { classifier = 'tests'} }

                    implementation dependencyCatalog.rest.assured
                    implementation dependencyCatalog.cloud.utils.test
                    implementation dependencyCatalog.reactor.test
                }

                targets {
                    all {
                        testTask.configure {
                            shouldRunAfter(test)
                        }
                    }
                }
            }
        }
    }

    bootJar {
        manifest {
            attributes(
                    "Implementation-Version": "${version}"
            )
        }
    }

    tasks.named('check') {
        dependsOn(testing.suites.integrationTest)
    }

    test {
        finalizedBy jacocoTestReport
    }

    jacocoTestReport {
        reports {
            xml.enabled true
            csv.enabled true
        }

        getExecutionData().setFrom(fileTree(buildDir).include("/jacoco/*.exec"))
    }

    jacocoToCobertura {
        inputFile.set(file("$buildDir/reports/jacoco/test/jacocoTestReport.xml"))
        outputFile.set(file("$buildDir/reports/jacoco/test/cobertura.xml"))
    }

    plugins.withType(JacocoPlugin) {
        tasks["test"].finalizedBy 'jacocoTestReport'
        tasks["integrationTest"].finalizedBy 'jacocoTestReport'
        tasks["jacocoTestReport"].finalizedBy 'jacocoToCobertura'
        tasks["jacocoToCobertura"].dependsOn 'jacocoTestReport'
    }

    tasks.withType(Test) {
        testLogging {
            info {
                events TestLogEvent.FAILED,
                        TestLogEvent.PASSED,
                        TestLogEvent.SKIPPED
            }
            debug {
                events TestLogEvent.STARTED,
                        TestLogEvent.FAILED,
                        TestLogEvent.PASSED,
                        TestLogEvent.SKIPPED,
                        TestLogEvent.STANDARD_OUT,
                        TestLogEvent.STANDARD_ERROR
                exceptionFormat TestExceptionFormat.FULL
                showExceptions true
                showCauses true
                showStackTraces true
                showStandardStreams true
            }

            afterSuite { desc, result ->
                if (!desc.parent) { // will match the outermost suite
                    def output = "Results: ${result.resultType} (${result.testCount} tests, ${result.successfulTestCount} passed, ${result.failedTestCount} failed, ${result.skippedTestCount} skipped)"
                    def startItem = '|  ', endItem = '  |'
                    def repeatLength = startItem.length() + output.length() + endItem.length()
                    println('\n' + ('-' * repeatLength) + '\n' + startItem + output + endItem + '\n' + ('-' * repeatLength))
                }
            }
        }
    }
}
