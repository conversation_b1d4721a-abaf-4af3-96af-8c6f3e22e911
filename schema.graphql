### REFERENCE DATA ###

## REFERENCE DATA - Venues ##

type Venue {
  name: String!
  venueType: VenueType!
}

## REFERENCE DATA - Instruments

"""
Requests reference data reload for given Venue, via given venueAccount
Note: Only street side reference data reload is possible to request, requests with client-side venue or venue accounts will be rejected.
"""
# instrumentsRefresh mutation
input InstrumentsRefreshRequestInput {
  """
  VenueAccount name which will be used for downloading the instruments.
  Requester has to hold 'read' scope permission on venueAccount resource, otherwise request will be rejected.
  """
  venueAccount: String

  """
  Optional.
  If provided and request has failed, an event log will be emitted that contains failure reason and this request's correlationObject
  """
  correlationObject: String
}

# instrumentSearch query
input InstrumentsSearchInput {
  venueNames: [String!]
  instrumentIdsPredicate: InstrumentIdsPredicateInput
  venueType: VenueType
  archived: Boolean
  tradeable: Boolean
  """
  Null sortBy defaults to InstrumentSortBy.INSTRUMENT_ID
  """
  sortBy: InstrumentSortBy
  """
  Result list will be sorted by instrumentId field
  Default sorting order is ASC if this field is not set
  """
  sortingOrder: SortingOrder
  first: Int
  after: String
}

input InstrumentIdsPredicateInput {
  method: InstrumentIdsPredicateType!
  instrumentIds: [String!]!
}

enum InstrumentIdsPredicateType {
  CONTAINS, EQUALS
}

type InstrumentsConnection {
  edges: [InstrumentEdge!]!
  pageInfo: PageInfo!
}

type InstrumentEdge {
  node: InstrumentResponse!
  cursor: String!
}

## REFERENCE DATA

# symbolSearch query
input SymbolSearchInput {
  symbolPredicate: SymbolPredicateInput
  venueNames: [String!]
  """
  Result list will be sorted by symbol field
  Default sorting order is ASC if this field is not set
  """
  sortingOrder: SortingOrder = ASC
  first: Int
  after: String
}

input SymbolPredicateInput {
  method: SymbolPredicateType!
  symbols: [String!]!
}

enum SymbolPredicateType {
  CONTAINS, EQUALS
}

type SymbolSearchConnection {
  edges: [SymbolSearchEdge!]!
  pageInfo: PageInfo!
}

type SymbolSearchEdge {
  node: SymbolSearchResponse!
  cursor: String!
}

type SymbolSearchResponse {
  symbol: String
  assetClass: AssetClass
  """
  Only venues where instrument is active (not archived)
  """
  venues: [VenueAccountNamesPerVenue]
}

type VenueAccountNamesPerVenue {
  venue: String
  venueAccountDescs: [VenueAccountDesc]
  """
  Only active (not archived) instruments are included
  """
  instrument: InstrumentResponse
}

# updateInstrument mutation
input UpdateInstrumentInput {
  baseInstrument: UpdateBaseInstrumentInput
  instrumentIdentifiers: UpdateInstrumentIdentifiersInput
  tradingConstraints: UpdateTradingConstraintsInput
  instrumentId: String!
  """
  Optional
  If set to true, this Instrument will become archived and it will not be possible to trade it any more.
  Since there's no possibility to hard-delete Instrument from the system (for audit purposes), archiving option should be used instead
  """
  archived: Boolean
  correlationObject: String
}

input UpdateBaseInstrumentInput {
  description: String
  inverseContract: Boolean
  symbol: String
}

input UpdateInstrumentIdentifiersInput {
  adapterTicker: String
  tradingViewId: String
  """
  Venue identifier for this instrument that will be used when communicating with TradingView
  """
  venueTradingViewId: String
}

input UpdateTradingConstraintsInput {
  """
  Minimum quantity allowed for trading
  """
  minQty: String!

  """
  Maximum quantity allowed for trading
  """
  maxQty: String!

  """
  Minimum quote quantity allowed for trading
  Optional, no constraint if not set
  """
  minQuoteQty: String

  """
  Maximum quote quantity allowed for trading
  Optional, no constraint if not set
  """
  maxQuoteQty: String

  """
  Minimum quantity increment
  """
  qtyIncr: String!


  """
  Minimum quote quantity increment
  """
  quoteQtyIncr: String

  """
  Minimum trading price - constraints Limit and StopLimit Orders
  Optional, no constraint if not set
  """
  minPrice: String

  """
  Maximum trading price - constraints Limit and StopLimit Orders
  Optional, no constraint if not set
  """
  maxPrice: String

  """
  Minimum price increment
  - constraints Limit and StopLimit Orders
  - defines tickSize in OrderBook
  """
  priceIncr: String!

  """
  Minimum notional value that should be used for trading
  Typically it can be calculated by multiplying ContractSize * NotionalPrice (Note: For FOREX Spot, the contract size is always 1 = baseCurrency quantity)
  For more details, see: https://www.investopedia.com/terms/n/notionalvalue.asp
  """
  minNotional: String

  """
  Contract size
  """
  contractSize: String

  """
  Defines whether this Instrument can be traded or not
  """
  tradeable: Boolean!
}

#
enum InstrumentType {
  FOREX
  FUTURE
  OPTION
  PERPETUAL_SWAP
  BOND
  COMODITY
  FUND
  INDEX
  SPREAD
  STOCK
  INSTRUMENT_TYPE_UNDETERMINED
}

#
enum Side {
  BUY
  REDUCE_SHORT
  SELL
  SELL_SHORT
  SIDE_UNDETERMINED
}

enum OrderRequest {
  SUBMIT
  MODIFY
  CANCEL
  CANCEL_REPLACE
}

enum OrderBookLevel {
  L1
  L2
  L1L2
}

# createInstrument mutation
input CreateInstrumentInput {
  baseInstrument: CreateBaseInstrumentInput!
  instrumentIdentifiers: CreateInstrumentIdentifiersInput!
  tradingConstraints: CreateTradingConstraintsInput!
  forexSpotProperties: CreateForexSpotPropertiesInput!
  correlationObject: String
}

type CreateInstrumentResult {
  status: String
}

type UpdateInstrumentResult {
  status: String
}

input CreateBaseInstrumentInput {
  venueName: String!
  venueType: VenueType
  assetClass: AssetClass!
  description: String
  quoteCurrency: String!
  inverseContract: Boolean
  symbol: String
}

input CreateInstrumentIdentifiersInput {
  """
  Instrument identifier that will be used when communicating with the external venue directly
  """
  adapterTicker: String

  """
  Instrument identifier that will be used when communicating with TradingView
  """
  tradingViewId: String

  """
  Venue identifier for this instrument that will be used when communicating with TradingView
  """
  venueTradingViewId: String
}

input CreateTradingConstraintsInput {
  """
  Minimum quantity allowed for trading
  """
  minQty: String!

  """
  Maximum quantity allowed for trading
  """
  maxQty: String!

  """
  Minimum quote quantity allowed for trading
  Optional, no constraint if not set
  """
  minQuoteQty: String

  """
  Maximum quote quantity allowed for trading
  Optional, no constraint if not set
  """
  maxQuoteQty: String

  """
  Minimum quantity increment
  """
  qtyIncr: String!

  """
  Minimum quote quantity increment
  """
  quoteQtyIncr: String

  """
  Minimum trading price - constraints Limit and StopLimit Orders
  Optional, no constraint if not set
  """
  minPrice: String

  """
  Maximum trading price - constraints Limit and StopLimit Orders
  Optional, no constraint if not set
  """
  maxPrice: String

  """
  Minimum price increment
  - constraints Limit and StopLimit Orders
  - defines tickSize in OrderBook
  """
  priceIncr: String!

  """
  Minimum notional value that should be used for trading
  Typically it can be calculated by multiplying ContractSize * NotionalPrice (Note: For FOREX Spot, the contract size is always 1 = baseCurrency quantity)
  For more details, see: https://www.investopedia.com/terms/n/notionalvalue.asp
  """
  minNotional: String

  """
  Contract size
  """
  contractSize: String

  """
  Defines whether this Instrument can be traded or not
  """
  tradeable: Boolean!
}

input CreateForexSpotPropertiesInput {
  # this field is forex-specific but it is required until we have more asset classes
  baseCurrency: String!
}

## REFERENCE DATA - VenueAccounts

#
input UpdateApiKeyNameInput {
  apiKeyId: String
  apiKeyName: String
}

#
input CreateVenueAccountInput {
  venueAccountId: String
  venueAccountName: String!
  venueName: String!
  apiKey: String
  apiSecret: String
  keyValues: [KeyValueInput]
  correlationObject: String
}

#
input UpdateVenueAccountInput {
  venueAccountId: String
  venueAccountName: String!
  keyValues: [KeyValueInput]
  archived: Boolean
}

#
input DeactivateVenueAccountInput {
  venueAccountId: String
  correlationObject: String
}
#
input ActivateVenueAccountInput {
  venueAccountId: String
  correlationObject: String
}

type VenueAccountDesc {
  id: String!
  name: String!
}

type VenueAccountsPerVenue {
  venue: String
  venueAccounts: [VenueAccount]
}

type VenueAccountDetailsResponse {
  venueAccountId: String!
  venueAccountName: String!
  venueName: String!
  keyValues: [KeyValue]
  """
  Permission scopes, granted to the originator of the request.
  """
  scopes: [Scope!]!
  dynamicScopes: [Scope!]!
}

#
type InternalExchangeConfigurationResponse {
  account: String!
  accountName: String!
  availableOrderTypes: [OrderType!]!
  availableTifs: [TIF!]!
}

## REFERENCE DATA - Wallet Accounts

type WalletAccountConnection {
  edges: [WalletAccountEdge!]!
  pageInfo: PageInfo!
}

type WalletAccountEdge {
  node: WalletAccountResponse!
  cursor: String!
}

type WalletAccountResponse {
  id: String!
  name: String!
  walletType: WalletType
  createdAt: String
  scopes: [Scope!]!
  dynamicScopes: [Scope!]!
}

input WalletAccountSearchInput {
  scopes: [Scope!]
  name: String
  first: Int
  after: String
}

#
input CreateWalletAccountInput {
  id: String
  name: String!
  walletType: WalletType
  correlationObject: String
}

#
input InternalExchangeConfigurationInput {
  account: String!
  availableOrderTypes: [OrderType!]!
  availableTifs: [TIF!]!
}

#
type VenueCapabilitiesResponse {
  venue: String!
  capabilities: DetailedCapabilities
}

type DetailedCapabilities {
  accountCapabilities: AccountCapabilities
  connectorCapabilities: [Capability]
  dropCopyCapabilities: Boolean
  genericEventCapabilities: Boolean
  historicalDataCapabilities: HistoricalDataCapabilities
  marketDataCapabilities: MarketDataCapabilities
  rateLimited: Boolean
  referenceDataCapabilities: ReferenceDataCapabilities
  tradingCapabilities: TradingCapabilities,
  transferCapabilities: Boolean
}

type KeyValues {
  key: String
  values: [String]
}

type OrderRequestsPerOrderType {
  key: OrderType
  values: [OrderRequest!]!
}

type TifsPerOrderType {
  key: OrderType
  values: [TIF!]!
}

type DefaultTif {
  key: OrderType
  value: TIF
}

type TradingCapabilities {
    accountingLongAndShortPositionsSeparately: Boolean,
    allowingMultipleRFQsForTheSameSecurity: Boolean,
    allowingMultipleRFQsOnAdapterLevel: Boolean,
    allowingOrdersWhenRFQIsOngoingForTheSameSecurity: Boolean,
    exchangeTradingSupported: Boolean,
    marginTradingSupported: Boolean,
    postOnlyOrderSupportedTypesAndTIFs:[TifsPerOrderType],
    rfqcancelSupported: Boolean,
    rfqstreamingSupported: Boolean,
    rfqsupported: Boolean,
    supportedOrderRequests: [OrderRequestsPerOrderType],
    supportedOrderTypes: [OrderType],
    supportedSecurityTypes: [InstrumentType],
    supportedTIFs: [TifsPerOrderType],
    supportingCustomizedOrders: Boolean,
    supportingFixLiteralProperties: Boolean,
    defaultTIFs: [DefaultTif]
}

type ReferenceDataCapabilities {
  canRetrieveAll: Boolean,
  canRetrieveSampleData: Boolean,
  canRetrieveSampleDataRequests: Boolean,
  retrievableByRequest: [KeyValues]
}

type MarketDataCapabilities {
  fetchingMarketDataSupported: Boolean,
  marketScanSupported: Boolean,
  otcBroker: Boolean,
  streamingLiveMarketDataSupported: Boolean,
  supportedOrderBookLevels: [OrderBookLevel]
  supportedSecurityTypes: [InstrumentType]
}

type HistoricalDataCapabilities {
  supportingCollectingAsks: Boolean,
  supportingCollectingBars: Boolean,
  supportingCollectingBidAsks: Boolean,
  supportingCollectingBids: Boolean,
  supportingCollectingTrades: Boolean
}

type AccountCapabilities {
  accountPositionsRetrievalSupported: Boolean,
  allocationSupported: Boolean,
  balanceRetrievalBySubAccount: Boolean,
  balanceRetrievalSupported: Boolean,
  balanceStreamingSupported: Boolean,
  depositAddressRetrievalSupported: Boolean,
  extAccountsRetrievalSupported: Boolean,
  orderReconciliationSupported: Boolean,
  subAccountRetrievalSupported: Boolean,
  walletHistorySupported: Boolean,
  withdrawalSupported: Boolean
}

type VenueAccount {
  venueAccountId: String!
  venueAccountName: String!
  deactivatedAt : String
  createdAt: String
  archivedAt: String
  """
  Permission scopes, granted to the originator of the request.
  """
  scopes: [Scope!]!
  dynamicScopes: [Scope!]!
}

type EffectiveVenueAccountsResponse {
  venueAccounts: [String!]!
  isSor: Boolean
}

### / REFERENCE DATA ###


### TRADING ###


"""
New Order request
"""
input NewOrderSingleRequestInput {
  """
  Client Order Id.
  Generated on client-side.
  Can be used to refer to the Order later (in combination with clientId).
  Should be unique in scope of the clientId, otherwise will be rejected
  """
  clOrderId: String!

  orderType: OrderType!

  """
  Mandatory for Limit and StopLimit Orders
  """
  limitPrice: Float

  """
  Mandatory for Stop and StopLimit Orders
  """
  stopPrice: Float

  """
  Order quantity
  """
  quantity: Float!

  """
  Quantity currency
  """
  currency: String

  side: Side!

  """
  Mandatory for simple Orders.
  Ignored for SOR. Use symbol and assetClass instead
  Example: BTCUSD@FOREX@Binance
  """
  instrumentId: String

  """
  Mandatory for SOR Orders.
  Ignored for simple Orders. Use instrumentId instead
  Example: BTCUSD
  """
  symbol: String

  """
  Mandatory for SOR Orders.
  Ignored for simple Orders. Use instrumentId instead
  """
  assetClass: AssetClass

  """
  VenueAccounts to be used for Order execution.
  Exactly 1-element is required for simple Orders, otherwise Order will be rejected.
  1+ elements allowed for SOR orders.
  """
  venueAccounts: [String!]

  """
  Time in force
  """
  tif: TIF!

  """
  Valid For
  In case of GTD orders, this field specifies the date and time when the order will be cancelled.
  Epoch Unix Timestamp
  """
  expirationDateTime: String

  """
  Portfolio where this Order should be booked.
  Mandatory.
  Requires portfolio#trade permission on client side.
  """
  portfolioId: String!
}

"""
Order cancel request
"""
input OrderCancelRequestInput {
  """
  Order id assigned by the Wyden system.
  Mandatory if origClOrderId is not present and takes percedence over it
  Order with this id will be requested to be cancelled.
  """
  orderId: String

  """
  Identifies the cancel request itself, NOT the Order to be cancelled.
  If the cancel operation fails, the report will contain this clOrderId as an id of the request.
  """
  clOrderId: String

  """
  ClientId of user sending the order - used togethere with origClOrderId for order identification.
  If not present, user sending the cancel is used instead.
  """
  origClientId : String

  """
  Describes Order to be cancelled by client-assigned clOrderId in NewOrderSingleRequestInput message.
  Mandatory if orderId is not present.
  """
  origClOrderId: String

  forceCancel: Boolean
}

#
input OrderCancelReplaceRequestInput {

  """
  Order id assigned by the Wyden system.
  Mandatory if origClOrderId is not present.
  Order with this id will be requested to be cancelled and replaced with a new order
  """
  orderId: String

  """
  ClientId of user sending the order - used togethere with origClOrderId for order identification.
  If not present, user sending the cancel is used instead.
  """
  origClientId : String

  """
  Identifies Order to be cancelled with client-assigned clOrderId in NewOrderSingleRequestInput message.
  Mandatory if orderId is not present.
  """
  origClOrderId: String

  """
  New Order that will be placed after cancelling the previous Order
  """
  replacingOrder: NewOrderSingleRequestInput
}

#
enum OrderStatus {
  """
  Order has been cancelled by the user.
  Terminal status.
  """
  CANCELED

  CALCULATED

  EXPIRED

  """
  Order has been fuly filled.
  Terminal status.
  """
  FILLED

  """
  Order has been submitted successfully and is awaiting to be filled.
  """
  NEW

  """
  Unknown
  """
  ORDER_STATUS_UNSPECIFIED

  """
  Order has been submitted successfully and partially filled. It is awaiting to be fully filled.
  """
  PARTIALLY_FILLED

  """
  Order has been requested to be cancelled by the user and is awaiting cancel confirmation.
  """
  PENDING_CANCEL

  """
  Order has been requested by the user to be submitted and is awaiting submit confirmation.
  """
  PENDING_NEW

  """
  Order has been requested by the user to be replaced with a different Order. It is currently awaiting cancellation confirmation.
  """
  PENDING_REPLACE

  """
  Order placement has been rejected - either internally or by the external venue.
  """
  REJECTED

  """
  Order has been replaced by another Order is no longer active.
  Terminal status.
  """
  REPLACED
}

#
enum OrderType {
  ORDER_TYPE_UNSPECIFIED
  PREVIOUSLY_INDICATED
  MARKET
  LIMIT
  STOP
  STOP_LIMIT
  MARKET_CASH
  LIMIT_CASH
  STOP_CASH
  STOP_LIMIT_CASH
}

"""
Describes current state of the Order
"""
type OrderStateResponse {

  """
  Order type
  """
  orderType: OrderType!

  """
  Order id, as assigned by the Wyden system
  """
  orderId: String!

  """
  External Order id, as assigned by the client
  """

  extOrderId: String

  """
  Id of the client who has initiated the Order
  """
  clientId: String

  """
  Client Order id - assigned by the client
  """
  clOrderId: String

  """
  Optional
  Counter portfolio id
  """
  counterPortfolioId: String
  """
  Optional
  Name of the related counter portfolio
  """
  counterPortfolioName: String
  """
  Optional
  If this Order is replacing another other, the previous Order id will be filled here
  """
  origClOrderId: String

  """
  Portfolio id where transactions created by this Order will be booked
  """
  portfolioId: String!

  """
  Name of the related portfolio
  """
  portfolioName: String!

  """
  Current Order status
  """
  orderStatus: OrderStatus!

  """
  Requested Order quantity to be filled
  """
  orderQty: Float!

  """
  Quantity currency
  """
  currency: String

  """
  Mandatory for Limit and StopLimit Orders
  """
  limitPrice: Float

  """
  Mandatory for Stop and StopLimit Orders
  """
  stopPrice: Float

  """
  Time in Force
  """
  tif: TIF

  """
  Valid For
  In case of GTD orders, this field specifies the date and time when the order will be cancelled.
  Epoch Unix Timestamp
  """
  expirationDateTime: String

  """
  Filled quantity
  """
  filledQty: Float!

  """
  Remaining quantity to be filled
  """
  remainingQty: Float!

  """
  Quantity of the last fill
  """
  lastQty: Float

  """
  Average fill price
  """
  avgPrice: String

  """
  Price of the last fill
  """
  lastPrice: String

  """
  Optional
  If the request result is not successful, describes a reason
  """
  reason: String

  """
  Mandatory
  Describes last result of action requested by user (NEW, CANCEL, CANCEL-REPLACE)
  """
  lastRequestResult: String!

  """
  Side
  """
  side: Side!

  """
  Wyden Instrument bound to that Order
  Mandatory for simple Orders, empty for SOR orders
  """
  instrument: InstrumentResponse

  """
  Instrument symbol.
  For SOR Orders, can be used to determine the common symbol of all candidate instruments
  Example: BTCUSD
  """
  symbol: String

  """
  Instrument asset class.
  For SOR Orders, can be used to determine the common asset class of all candidate instruments
  """
  assetClass: AssetClass

  """
  Instrument venue.
  For SOR Orders, can be used to determine the venue of last filed child order
  """
  venue: String

  """
  Time of the last update on the venue side
  """
  venueTimestamp: String

  """
  Order creation time
  """
  createdAt: String

  """
  Time of the last Order update
  """
  updatedAt: String

  """
  Used to determine the order of order status updates
  """
  sequenceNumber: Int

  """
  Order category (Simple, Parent or Child)
  """
  orderCategory: OrderCategory!

  """
  Id of the parent order if current is "children"
  """
  parentOrderId: String

  """
  Id of the root order - in a chain of Orders, this is the first Order in the chain
  """
  rootOrderId: String

  """
  VenueAccounts used for Order execution.
  """
  venueAccountDescs : [VenueAccountDesc!]

  """
  Id of a specific order state. Empty for a snapshot.
  """
  orderStateId: String
}

type OrderStateConnection {
  edges: [OrderStateEdge!]!
  pageInfo: PageInfo!
}

type OrderStateEdge {
  node: OrderStateResponse!
  cursor: String!
}

input OrderStateSearchInput {
  simplePredicates: [SimplePredicateInput]
  collectionPredicates: [CollectionPredicateInput]
  datePredicateInputs: [DatePredicate]
  """
  Result list will be sorted by order.updatedAt field
  Default sorting order will be DESC if this field is not set
  """
  sortingOrder: SortingOrder
  first: Int
  after: String
}

"""
Notifies the user if the Cancel request has been rejected
"""
type CancelRejectResponse {
  """
  Contains an error code
  TODO: To be converted to enum in scope of https://algotrader.atlassian.net/browse/AC-1335
  """
  result: String

  cancelRejectReason: String
  cancelRejectResponseTo: CancelRejectResponseTo

  """
  Id of the cancel request that was rejected
  """
  clOrderId: String
  clientId: String

  """
  Current Order status
  """
  orderStatus: OrderStatus

  """
  Id of the Order requested to be cancelled
  """
  origClOrderId: String

  """
  Id of the Order requested to be cancelled
  """
  origOrderId: String
}

#
enum CancelRejectResponseTo {
  ORDER_CANCEL_REPLACE_REQUEST
  ORDER_CANCEL_REQUEST
  UNSPECIFIED
}


"""
Time in Force
Describes how long the Order should be valid
"""
enum TIF {
  """
  Valid till end of day
  """
  DAY

  """
  Fill or Kill
  """
  FOK

  """
  Good till cancelled
  """
  GTC

  """
  Good till Date
  """
  GTD

  """
  Immediate or Cancel
  """
  IOC

  """
  Unknown
  Should never be set by client
  """
  TIF_UNSPECIFIED
}

### / TRADING ###


### MATCHING ###

input MatchesSearchInput {
  orderId: String
}

enum ParticipantReferenceType {
  PORTFOLIO
  VENUE_ACCOUNT
}

type Counterparty {
  portfolioId: String # vostro portfolio id (for internal execution)
  portfolioName: String
  venueAccount: String # account name (for external execution)
  venueAccountName: String
  referenceType: ParticipantReferenceType
  orderId: String
  rootOrderId: String
  price: Float!
  volume: Float!
}

type MarketQuote {
  instrument: InstrumentResponse!
  marketAskPrice: Float
  marketBidPrice: Float
  marketAskSize: Float
  marketBidSize: Float
  markupAskPrice: Float
  markupBidPrice: Float
  timestamp: String
}

type MatchResponse {
  id: String!
  """
    Epoch Unix Timestamp
  """
  timestamp: String!
  taker: Counterparty!
  makers: [Counterparty!]!
  primarySymbolQuotes: [MarketQuote!]!
  secondarySymbolQuotes: [MarketQuote!]
}

type MatchesResult {
  totalQuantity: Float
  totalAmount: Float
  avgPrice: Float
  matches: [MatchResponse!]!
}

### / MATCHING ###


### MARKET DATA ###

type MarketDataResponse {
  instrument: InstrumentResponse
  venue: String
  venueAccount: String
  venueAccountName: String
  dateTime: Float
  bidPrice: Float
  bidSize: Float
  askPrice: Float
  askSize: Float
  vol: Float
  lastPrice: Float
  lastSize: Float
  side: Side
  marketDataType: MarketDataType
}

#
type OrderBookResponse {
  instrumentId: String
  venue: String
  venueAccount: String
  venueAccountName: String
  dateTime: String
  bids: [OrderBook]
  asks: [OrderBook]
}

type OrderBook {
  price: String
  amount: String
  count: String
}

type OrderBookSnapshot {
  bids: [OrderBook]
  asks: [OrderBook]
}


enum MarketDataType {
  BID
  ASK
  BIDASK
  TRADE
  ORDERBOOK
  TICK
}

### / MARKET DATA ###


### PORTFOLIO ###

input CreatePortfolioInput {
  id: String
  name: String!
  portfolioCurrency: String!
  tags: [TagInput]
  portfolioType: PortfolioType
  correlationObject: String
}

input UpdatePortfolioInput {
  id: String!
  name: String
  portfolioType: PortfolioType
  tags: [TagInput]
  archived: Boolean
  correlationObject: String
}

# portfolioSearch query
input PortfolioSearchInput {
  """
  Result set will contain all portfolios that meet the following predicate. Overwrites "name" when both used
  """
  portfolioPredicate: PortfolioPredicateInput
  """
  Result set will be restricted to given list of portfolioIds.
  Optional - if field is null or empty then all permitted portfolios will be returned.
  """
  portfolioIds: [String!]
  """
  Optional - if field is null all portfolios will be returned
  If set to:
   - true, result will only contain archived portfolios,
   - false, than only not archived
  """
  archived: Boolean
  """
  Result set will only contain Portfolios where requesting user is authorized to use Portfolio in one of given scopes.
  If collection is null or empty then Portfolios authorized to the user in any Scope will be returned.
  """
  scopes: [Scope!]
  portfolioType: PortfolioType
  tagValues: [String!]
  """
  Null sortBy defaults to PortfolioSortBy.PORTFOLIO_NAME
  """
  sortBy: PortfolioSortBy
  """
  Default sorting order is ASC if this field is not set
  """
  sortingOrder: SortingOrder = ASC
  first: Int
  after: String
}

input PortfolioPredicateInput {
  method: PortfolioPredicateType!
  searchType: PortfolioSearchType!
  value: String!
}

enum PortfolioPredicateType {
  CONTAINS
}

enum PortfolioSearchType {
  NAME, ID, NAME_OR_ID
}

type PortfolioConnection {
  edges: [PortfolioEdge!]!
  pageInfo: PageInfo!
}

type PortfolioEdge {
  node: PortfolioResponse!
  cursor: String!
}

type PortfolioResponse {
  id: String!
  name: String!
  createdAt: String!
  portfolioCurrency: String!
  portfolioType: PortfolioType
  tags: [Tag!]!
  """
  Nullable - only present if the Portfolio is archived. Null value means not archived.
  """
  archivedAt: String
  """
  Permission scopes, granted to the originator of the request.
  """
  scopes: [Scope!]!
  dynamicScopes: [Scope!]!
}

type Tag {
  key: String!
  value: String!
}

input TagInput {
  key: String!
  value: String!
}

enum ConfigurationLevel {
  PORTFOLIO_GROUP, PORTFOLIO_GROUP_INSTRUMENT, PORTFOLIO, PORTFOLIO_INSTRUMENT
}

enum ConfigurationType {
  EXECUTION, PRICING, HEDGING, QUOTING
}

### / PORTFOLIO ###

### CURRENCY ###

input CurrencySearchInput {
  """
  Result set will be restricted to given list of symbols (using "equals" match mode)
  Optional - if field is null or empty then all permitted symbols will be returned
  """
  symbols: [String!]
  type: CurrencyCategory
}

input CurrencyInput {
  symbol: String!
  type: CurrencyCategory!
  precision: Int!
  displayPrecision: Int!
}

type CurrencyResponse {
  symbol: String!
  type: CurrencyCategory!
  precision: Int!
  displayPrecision: Int!
}

enum CurrencyCategory {
  FIAT, CRYPTO
}

### / CURRENCY ###

### HISTORY ORDER STATUSES ###

input HistoryOrderStatusesInput {
  orderId: String!
  includeRelated: Boolean
}

### / HISTORY ORDER STATUSES ###

### ACCOUNTING ###

type PositionConnection {
  edges: [PositionEdge!]!
  pageInfo: PageInfo!
}

type PositionEdge {
  node: PositionResponse!
  cursor: String!
}

type PositionResponse {
  updatedAt: Float!
  quantity: Float!
  bookingCurrency: String!
  netRealizedPnl: Float!
  grossRealizedPnl: Float!
  netCost: Float!
  grossCost: Float!
  symbol: String! # currency symbol BTC (CashPosition) or asset symbol BTCEUR (AssetPosition)
  currency: String # BTC (CashPosition) or EUR (AssetPosition)
  portfolio: PortfolioResponse
  account: String # accountId, bitmex-testnet1
  accountName: String
  notionalQuantity: Float
  marketValue: Float
  # Sc - value in System Currency, otherwise it is in Portfolio Currency
  marketValueSc: Float
  netRealizedPnlSc: Float
  netCostSc: Float
  grossRealizedPnlSc: Float
  grossCostSc: Float
  netAveragePrice: Float
  grossAveragePrice: Float
  netUnrealizedPnl: Float
  grossUnrealizedPnl: Float
  netUnrealizedPnlSc: Float
  grossUnrealizedPnlSc: Float
  pendingQuantity: Float
  availableForTradingQuantity: Float
  availableForWithdrawalQuantity: Float
  settledQuantity: Float
  unsettledQuantity: Float
}

type LedgerEntryConnection {
  edges: [LedgerEntryEdge!]!
  pageInfo: PageInfo!
}

type LedgerEntryEdge {
  node: LedgerEntryResponse!
  cursor: String!
}

type LedgerEntryResponse {
  id: ID!
  updatedAt: Float!
  quantity: Float!
  price: Float!
  fee: Float!
  type: LedgerEntryType!
  currency: String # currency code, BTC
  symbol: String # security symbol, BTCUSD
  portfolioId: String
  portfolioName: String
  accountId: String # venue account id, bitmex-testnet1
  accountName: String
  transactionId: String # transaction that created this entry (internal id)
  orderId: String # order that created this entry (one order can lead to multiple transactions) (internal id)
  balanceBefore: Float
  balanceAfter: Float
}

enum LedgerEntryType {
  ASSET_TRADE_BUY
  ASSET_TRADE_SELL
  CASH_TRADE_CREDIT
  CASH_TRADE_DEBIT
  ASSET_TRADE_PROCEEDS
  DEPOSIT
  WITHDRAWAL
  TRANSFER
  FEE
  TRADING_FEE
  DEPOSIT_FEE
  WITHDRAWAL_FEE
  TRANSFER_FEE
  RESERVATION
  RESERVATION_RELEASE
  RESERVATION_RELEASE_REMAINING
  WITHDRAWAL_RESERVATION
}

input PositionSearchInput {
  symbol: [String!]
  currency: [String!]
  portfolio: [String!]
  venueAccount: [String!]
  first: Int
  after: String
  """
  Result list will be sorted by updatedAt field
  Default sorting order will be DESC if this field is not set
  """
  sortingOrder: SortingOrder
}

input LedgerEntrySearchInput {
  symbol: [String!]
  currency: [String!]
  portfolioId: [String!]
  accountName: [String!]
  ledgerEntryType: [LedgerEntryType!]
  transactionId: String
  orderId: String
  from: String # inclusive, Epoch Unix Timestamp
  to: String # exclusive, Epoch Unix Timestamp
  first: Int
  after: String
  """
  Result list will be sorted by updatedAt field
  Default sorting order will be DESC if this field is not set
  """
  sortingOrder: SortingOrder
}

input TransactionSearchInput {
  uuid: String
  symbol: [String!]
  currency: [String!]
  accountId: [String!]
  portfolioId: [String!]
  transactionType: [String!]
  orderId: String
  parentOrderId: String
  rootOrderId: String
  underlyingExecutionId: String
  venueExecutionId: String
  executionId: String
  rootExecutionId: String
  # from, to : Epoch Unix Timestamp
  from: String
  to: String
  first: Int
  after: String
  """
  Result list will be sorted by updatedAt field
  Default sorting order will be DESC if this field is not set
  """
  sortingOrder: SortingOrder
}

enum TransactionType {
  WITHDRAWAL
  DEPOSIT
  TRADE
  TRANSFER
}

input ConversionRate {
  rate: String!
  baseCurrency: String!
  quoteCurrency: String!
}

input TransactionInput {
  type: TransactionType!
  dateTime: String!
  portfolioId: String
  description: String
  venueAccountId: String
  sourceAccountId: String
  sourcePortfolioId: String
  fee: String
  feePortfolioId: String
  feeAccountId: String
  counterPortfolioId: String
  instrumentId: String
  currency: String
  quantity: String!
  price: String
  isSettled: Boolean
  settlementDate: String
  externalOrderId: String
  executionId: String
  venueExecutionId: String
  conversionRates: [ConversionRate!]
}

### / ACCOUNTING ###

### BROKER DESK ###

type PortfolioGroupConfiguration {
  id: ID!
  name: String
  portfolioType: PortfolioType
  executionConfiguration: ExecutionConfiguration
  pricingConfiguration: PricingConfiguration
  hedgingConfiguration: HedgingConfiguration
  instrumentConfiguration: [InstrumentGroupConfiguration!]
}

type PortfolioGroupConfigurationFlat {
  id: ID!
  name: String
  portfolioType: PortfolioType
}

input PortfolioGroupConfigurationInput {
  id: ID!
  name: String
  portfolioType: PortfolioType
  executionConfiguration: ExecutionConfigurationInput
  pricingConfiguration: PricingConfigurationInput
  hedgingConfiguration: HedgingConfigurationInput
  instrumentConfiguration: [InstrumentGroupConfigurationInput!]
}

type PortfolioConfiguration {
  id: ID!
  name: String
  portfolioGroupConfiguration: PortfolioGroupConfiguration
  executionConfiguration: ExecutionConfiguration
  pricingConfiguration: PricingConfiguration
  hedgingConfiguration: HedgingConfiguration
  instrumentConfiguration: [InstrumentConfiguration!]
}

type PortfolioConfigurationFlat {
  id: ID!
  name: String
}

input PortfolioConfigurationInput {
  id: ID!
  name: String
  portfolioGroupConfigurationId: ID
  executionConfiguration: ExecutionConfigurationInput
  pricingConfiguration: PricingConfigurationInput
  hedgingConfiguration: HedgingConfigurationInput
  instrumentConfiguration: [InstrumentConfigurationInput!]
}

type ExecutionConfiguration {
  tradingMode: TradingMode
  counterPortfolioId: String
  counterPortfolioName: String
  fixedFee: Float
  fixedFeeCurrency: String
  percentageFee: Float
  percentageFeeCurrency: String
  percentageFeeCurrencyType: CurrencyType
  minFee: Float
  minFeeCurrency: String
  agencyTradingAccount: String
  agencyTradingAccountName: String
  agencyTargetInstrument: InstrumentResponse
  chargeExchangeFee: Boolean
  discloseTradingVenue: Boolean
  executionMode: ExecutionMode
  sorTradingAccountDescs: [VenueAccountDesc!]
  sorTarget: SorTarget
}

input ExecutionConfigurationInput {
  tradingMode: TradingMode
  counterPortfolioId: String
  fixedFee: Float
  fixedFeeCurrency: String
  percentageFee: Float
  percentageFeeCurrency: String
  percentageFeeCurrencyType: CurrencyType
  minFee: Float
  minFeeCurrency: String
  agencyTradingAccount: String
  agencyTargetInstrumentId: String
  chargeExchangeFee: Boolean
  discloseTradingVenue: Boolean
  executionMode: ExecutionMode
  sorTradingAccounts: [String!]
  sorTarget: SorTargetInput
}

type PricingConfiguration {
  venueAccounts: [VenueAccountDesc!]
  #  Percentage added to the calculated reference price - range [0, 1]
  markup: Float
}

input PricingConfigurationInput {
  venueAccount: [String!]
  #  Percentage added to the calculated reference price - range [0, 1]
  markup: Float
}

type InstrumentPricingConfiguration {
  pricingSource: [InstrumentKey!]
  #  Percentage added to the calculated reference price - range [0, 1]
  markup: Float
}

input InstrumentPricingConfigurationInput {
  pricingSource: [InstrumentKeyInput!]
  #  Percentage added to the calculated reference price - range [0, 1]
  markup: Float
}


input QuotingConfigurationInput {
  displayName: String!

  askMarkup: Float
  bidMarkup: Float

  nostroPortfolioId: String!

  # Street side accounts that should be used as a liquidity source.
  # @deprecated(reason: "Use sources instead")
  sourceAccounts: [String!]
  sources: [QuotingSourceAccountConfigInput!]

  # Defines the minimal percentage of the liquidity that is mirrored from the external venue.
  # Range [0, 1]
  minQuantityFactor: Float!

  # Defines the maximal percentage of the liquidity that is mirrored from the external venue.
  # Range [0, 1]
  maxQuantityFactor: Float!

  # Maximum depth - defines the maximum depth (in percent) of the orderbook to be mirrored.
  # Range [0, 1]
  # Optional.
  maximumDepth: Float

  # Throttling Period – defines the throttling interval (in seconds) for the updates.
  # Unit: seconds
  # Optional
  # Empty value means no throttling
  throttlingPeriod: Int

  # TTL, in seconds.
  # Unless, there was an update from the source within given TTL, quotes will be considered stale and discarded after TTL passes.
  # Null or 0 means no TTL - quotes will never be discarded.
  # Optional, defaults to null if not set.
  quoteTTL: Int @deprecated(reason: "Not used anymore")
  
  deactivated: Boolean!

  # Street side accounts that should be used for hedging.
  hedgingAccounts: [String!]!

  hedgingSafetyMargin: Float!
}

input QuotingSourceAccountConfigInput {
  accountId: String!
  isDroppingQuotesOnDisconnection: Boolean
  defaultQuoteTTL: Int
  quoteTTLUnit: QuoteTTLUnit!
  calendarEntries: [QuotingCalendarInput!]
}

input QuotingCalendarInput {
  fromDayOfTheWeek: DayOfTheWeek!
  toDayOfTheWeek: DayOfTheWeek!
  startTime: String!
  endTime: String!
  quoteTTL: Int!
  quoteTTLUnit: QuoteTTLUnit!
  additionalMarkup: Float!
}

enum QuoteTTLUnit {
  MILLISECONDS
  SECONDS
  MINUTES
  HOURS
  DAYS
}


type QuotingConfiguration {
  clobUid: String!

  updatedAt: String!

  displayName: String!

  askMarkup: Float
  bidMarkup: Float

  nostroPortfolioId: String!

  nostroPortfolioName: String!

  # Street side accounts that should be used as a liquidity source.
  # @deprecated(reason: "Use sources instead")
  sourceAccounts: [String!]
  sources: [QuotingSourceAccountConfig!]

  sourceAccountDescs: [VenueAccountDesc!]

  # Defines the maximal percentage of the liquidity that is mirrored from the external venue.
  # Range [0, 1]
  minQuantityFactor: Float!

  # Defines the minimal percentage of the liquidity that is mirrored from the external venue.
  # Range [0, 1]
  maxQuantityFactor: Float!

  # Maximum depth - defines the maximum depth (in percent) of the orderbook to be mirrored.
  # Range [0, 1]
  # Optional.
  maximumDepth: Float

  # Throttling Period – defines the throttling interval (in seconds) for the updates.
  # Unit: seconds
  # Optional
  # Empty value means no throttling
  throttlingPeriod: Int

  instrumentConfigurations: [InstrumentQuotingConfiguration!]!

  # TTL, in seconds.
  # Unless, there was an update from the source within given TTL, quotes will be considered stale and discarded after TTL passes.
  # Null or 0 means no TTL - quotes will never be discarded.
  # Optional, defaults to null if not set.
  quoteTTL: Int @deprecated(reason: "Not used anymore")

  deactivated: Boolean!

  # Street side accounts that should be used for hedging.
  hedgingAccounts: [String!]!

  hedgingAccountDescs: [VenueAccountDesc!]

  hedgingSafetyMargin: Float!
}

type QuotingSourceAccountConfig {
  accountId: String!
  accountName: String!
  isDroppingQuotesOnDisconnection: Boolean
  defaultQuoteTTL: Int
  quoteTTLUnit: QuoteTTLUnit!
  calendarEntries: [QuotingCalendar!]
}

type QuotingCalendar {
  fromDayOfTheWeek: DayOfTheWeek!
  toDayOfTheWeek: DayOfTheWeek!
  startTime: String!
  endTime: String!
  quoteTTL: Int!
  quoteTTLUnit: QuoteTTLUnit!
  additionalMarkup: Float!
}

type InstrumentQuotingConfiguration {
  instrumentId: String!
  sourceConfigurations: [SourceConfiguration!]!
  priceLevelIncrement: Float
  quantityIncrement: Float
  maximumDepth: Float
  askMarkup: Float
  bidMarkup: Float
  # TTL, in seconds.
  # Unless, there was an update from the source within given TTL, quotes will be considered stale and discarded after TTL passes.
  # Null or 0 means no TTL - quotes will never be discarded.
  # Optional, defaults to null if not set.
  quoteTTL: Int @deprecated(reason: "Not used anymore")
  deactivated: Boolean!
  # Defines the minimal percentage of the liquidity that is mirrored from the external venue.
  # Range [0, 1]
  minQuantityFactor: Float

  # Defines the maximal percentage of the liquidity that is mirrored from the external venue.
  # Range [0, 1]
  maxQuantityFactor: Float
  hedgingSafetyMargin: Float
}

input InstrumentQuotingConfigurationInput {
  instrumentId: String!
  sourceConfigurations: [SourceConfigurationInput!]!
  priceLevelIncrement: Float
  quantityIncrement: Float
  maximumDepth: Float
  askMarkup: Float
  bidMarkup: Float
  deactivated: Boolean!
  # TTL, in seconds.
  # Unless, there was an update from the source within given TTL, quotes will be considered stale and discarded after TTL passes.
  # Null or 0 means no TTL - quotes will never be discarded.
  # Optional, defaults to null if not set.
  quoteTTL: Int @deprecated(reason: "Not used anymore")
  # Defines the minimal percentage of the liquidity that is mirrored from the external venue.
  # Range [0, 1]
  minQuantityFactor: Float

  # Defines the maximal percentage of the liquidity that is mirrored from the external venue.
  # Range [0, 1]
  maxQuantityFactor: Float
  hedgingSafetyMargin: Float
}

type SourceConfiguration {
  sourceInstrument: InstrumentResponse!
  conversionSourceInstrument: InstrumentResponse
  inverse: Boolean!
}

input SourceConfigurationInput {
  sourceInstrumentId: String!
  conversionSourceInstrumentId: String
  inverse: Boolean!
}

type HedgingConfiguration {
  autoHedging: Boolean
  targetAccountId: String
  targetAccountName: String
  thresholdConfiguration: [ThresholdConfiguration!]
}

input HedgingConfigurationInput {
  autoHedging: Boolean
  targetAccountId: String
  thresholdConfiguration: [ThresholdConfigurationInput!]
}

type InstrumentGroupConfiguration {
  instrumentId: ID!
  tradeable: Boolean
  executionConfiguration: ExecutionConfiguration
  pricingConfiguration: InstrumentPricingConfiguration
}

input InstrumentGroupConfigurationInput {
  instrumentId: ID!
  tradeable: Boolean
  executionConfiguration: ExecutionConfigurationInput
  pricingConfiguration: InstrumentPricingConfigurationInput
}

type InstrumentConfiguration {
  instrumentId: ID!
  tradeable: Boolean
  instrumentGroupConfiguration: InstrumentGroupConfiguration
  executionConfiguration: ExecutionConfiguration
  pricingConfiguration: InstrumentPricingConfiguration
}

input InstrumentConfigurationInput {
  instrumentId: ID!
  tradeable: Boolean
  executionConfiguration: ExecutionConfigurationInput
  pricingConfiguration: InstrumentPricingConfigurationInput
}

type ThresholdConfiguration {
  asset: String
  highThreshold: String
  lowThreshold: String
  targetExposure: String
  hedgeInstrument: InstrumentResponse
}

input ThresholdConfigurationInput {
  asset: String
  highThreshold: String
  lowThreshold: String
  targetExposure: String
  hedgeInstrumentId: String
}

type InstrumentKey {
  venueAccount: String!
  venueAccountName: String!
  instrument: InstrumentResponse!
}

input InstrumentKeyInput {
  venueAccount: String!
  instrumentId: String!
}

type SorTarget {
  assetClass: AssetClass!
  symbol: String!
}

input SorTargetInput {
  assetClass: AssetClass!
  symbol: String!
}

input ResetConfigurationInput {
  configurationLevel: ConfigurationLevel!
  configurationType: ConfigurationType!
  resourceId: ID!
  instrumentId: ID
}

enum PortfolioType {
  NOSTRO,
  VOSTRO
}

enum WalletType {
  NOSTRO,
  VOSTRO
}

enum TradingMode {
  AGENCY,
  PRINCIPAL
}

enum ExecutionMode {
  SIMPLE,
  SOR
}

enum CurrencyType {
  BASE_CURRENCY
  QUOTE_CURRENCY
  SPECIFIC_CURRENCY
}

type ConfigValidationResult {
  resultsPerInstrument: [ConfigInstrumentValidationResult!]!
}

type QuotingConfigValidationResult {
  clobUid: String!
  resultsPerInstrument: [ConfigInstrumentValidationResult!]!
}

type ConfigInstrumentValidationResult {
  instrumentId: String!

  # True, if effective configuration is complete and valid. Only true if preventsTrading = false and preventsMarketData = false
  isValid: Boolean!
  # True, if there are errors present that affect trading capabilities.
  preventsTrading: Boolean!
  # True, if there are errors present that affect market data streaming capabilities.
  preventsMarketData: Boolean!

  errors: [ConfigValidationError!]!
}

type ConfigValidationError {
  errorMessage: String
  errorType: ErrorType!
  # True, if effective configuration is not complete for trading.
  preventsTrading: Boolean!
  # True, if effective configuration is not complete for market data.
  preventsMarketData: Boolean!
  fieldName: String!
}

enum ErrorType {
  MISSING,
  INVALID
}

type HedgingConfigValidationResult {
  portfolioId: String!
  resultsPerCurrency: [ConfigCurrencyValidationResult!]!
}

type ConfigCurrencyValidationResult {
  currency: String!
  isValid: Boolean!
  error: String
}

### / BROKER DESK ###

### PERMISSIONS ###

#
input AddOrRemoveUserPermissionsInput {
  username: String!
  permissions: [PermissionInput!]!
}

#
input AddOrRemoveGroupPermissionsInput {
  groupName: String!
  permissions: [PermissionInput!]!
}

#
input PermissionInput {
  resource: Resource!
  scope: Scope!
  resourceId: String
}

#
type GroupsPermissionsForResourceResponse {
  groupName: String!
  scopes: [Scope!]!
}

#
type UserResponse {
  username: String!
  groups: [GroupResponse!]!
  permissions: [Permission!]!
}

#
type GroupResponse {
  name: String!
  permissions: [Permission!]!
}

#
type UsersPermissionsForResourceResponse {
  username: String!
  scopes: [Scope!]!
}

#
type Permission {
  resource: Resource!
  scope: Scope!
  resourceId: String
}

input PermissionSearchInput {
  first: Int
  after: String
}

type PermissionConnection {
  edges: [PermissionEdge!]!
  pageInfo: PageInfo!
}

type PermissionEdge {
  node: FlatPermission!
  cursor: String!
}

type FlatPermission {
  username: String!
  resource: Resource!
  resourceId: String
  userScopes: [Scope!]
  groups: [GroupPermission!]
}

type GroupPermission {
  groupName: String!
  groupScopes: [Scope!]
}

enum Resource {
  API_KEY
  PORTFOLIO
  PORTFOLIO_VOSTRO
  PORTFOLIO_NOSTRO
  SETTLEMENT
  CLIENT_INSTRUMENT
  CLOB_INSTRUMENT
  VENUE_ACCOUNT
  WALLET
  WALLET_VOSTRO
  WALLET_NOSTRO
  RISK
  BROKER_CONFIG
  CURRENCY
  VENUE,
  CONNECTOR
}

### / PERMISSIONS ###

### USER ###


input UpdateUserDataInput {
  data: String!
}

#
type UserDataResponse {
  data: String
}

### / USER ###

### EVENT LOG ###

type EventLogResponse {
  status: Status!
  clientId: String!
  message: String!
  # Epoch Unix Timestamp
  timestamp: String!
  eventType: String!
  correlationObject: String
}

### / EVENT LOG ###


### TIMELINE ###

input TimelineSearchInput {
  orderId: String!
  rootOrderId: String
  includeRelated: Boolean
  eventType: [TimelineEventType]
  sortingOrder: SortingOrder
  first: Int
  after: String
}

input OrderBookSnapshotSearchInput {
  orderId: String! # needed as long as matchIds are not unique
  matchId: String!
}

type TimelineConnection {
  edges: [TimelineEdge!]!
  pageInfo: PageInfo!
}

type TimelineEdge {
  node: TimelineResult!
  cursor: String!
}

type TimelineResult {
  type: TimelineEventType
  data: TimelineResponse
}

type HedgeResult {
  timestamp: String!
  clobOrderId: String!
  clobRootOrderId: String!
  matchId: String!
  estimatedPrice: Float #matchPrice
  hedgeOrderId: String!
  hedgeRootOrderId: String!
  hedgingVenue: String!
  hedgeOrderQuantity: Float!
  hedgeOrderLimitPrice: Float
  hedgeOrderSide: Side!
  success: Boolean!
  executionPrice: Float
  hedgeOrderAmount: Float #value
  hedgeOrderAmountInClobQuoteCurrency: Float #valueTc
  primarySymbolQuote: MarketQuote #top_of_book_on_venue
  secondarySymbolQuote: MarketQuote #fx_rate
  breakEvenFxRate: Float
}

union TimelineResponse = OrderStateResponse | MatchResponse | HedgeResult

enum TimelineEventType {
  ORDER_STATE,
  MATCH_RESPONSE,
  HEDGE_RESULT
}

### / TIMELINE ###

### RATE CONVERSION ###

input CreateConversionSourceInput {
  venueAccount: String!
  priority: Int
}

input UpdateConversionSourceInput {
  venueAccount: String!
  priority: Int!
}

type ConversionSourceResponse {
  venueAccount: String!
  venueAccountName: String!
  priority: Int!
}

type ConversionSourceVenueAccountResponse {
  venueName: String!
  venueAccounts: [ConversionSourceVenueAccount!]!
}

type ConversionSourceVenueAccount {
  venueAccount: String!
  venueAccountName: String!
}

type RateSubscriptionResponse {
  baseCurrency: String!
  quoteCurrency: String!
  venueAccountId: String
  venueAccountName: String
  lastPrice: String
  health: RateSubscriptionHealth!
  lastEventTimestamp: String
}

input CreateRateSubscriptionInput {
  baseCurrency: String!
  quoteCurrency: String!
}

input DeleteRateSubscriptionInput {
  baseCurrency: String!
  quoteCurrency: String!
}

enum RateSubscriptionHealth {
  UP
  PENDING
  DOWN
}


### / RATE CONVERSION ###

type ConfigField {
type: String
secret: Boolean
enumValues: [String]
propertyKey: String
defaultValue: String
required: Boolean
multiline: Boolean
description: String
}

# Mutation root
# can be extended in other graphql files
type Mutation {
  createApiKey(name: String): ApiKeyResponse!
  updateApiKeyName(request: UpdateApiKeyNameInput): MutationReturnValue
  deactivateApiKey(id: String!): MutationReturnValue
  createVenueAccount(request: CreateVenueAccountInput!): MutationSubmittedResponse
  updateVenueAccount(request: UpdateVenueAccountInput!): MutationSubmittedResponse
  deactivateVenueAccount(request: DeactivateVenueAccountInput!): MutationSubmittedResponse
  activateVenueAccount(request: ActivateVenueAccountInput!): MutationSubmittedResponse
  updateUserData(request: UpdateUserDataInput!): UserDataResponse!
  sendOrder(request: NewOrderSingleRequestInput): MutationReturnValue
  cancelOrder(request: OrderCancelRequestInput): MutationReturnValue
  cancelReplaceOrder(request: OrderCancelReplaceRequestInput): MutationReturnValue
  instrumentsRefresh(request: InstrumentsRefreshRequestInput!): MutationReturnValue
  createInstrument(request: CreateInstrumentInput): CreateInstrumentResult
  updateInstrument(request: UpdateInstrumentInput): UpdateInstrumentResult
  createPortfolio(request: CreatePortfolioInput!): MutationSubmittedResponse
  updatePortfolio(request: UpdatePortfolioInput!): MutationSubmittedResponse
  addGroupPermissions(request: AddOrRemoveGroupPermissionsInput!): MutationReturnValue
  removeGroupPermissions(request: AddOrRemoveGroupPermissionsInput!): MutationReturnValue
  addUserPermissions(request: AddOrRemoveUserPermissionsInput!): MutationReturnValue
  removeUserPermissions(request: AddOrRemoveUserPermissionsInput!): MutationReturnValue
  savePreTradeCheck(request: PreTradeCheckInput!): MutationReturnValue
  deletePreTradeCheck(preTradeCheckId: String!): MutationReturnValue
  createWalletAccount(request: CreateWalletAccountInput!): MutationSubmittedResponse
  createInternalExchangeConfiguration(request: InternalExchangeConfigurationInput!): MutationSubmittedResponse
  updateInternalExchangeConfiguration(request: InternalExchangeConfigurationInput!): MutationSubmittedResponse

  #  portfolio config
  updatePortfolioConfiguration(portfolioId: ID!, request: PortfolioConfigurationInput): ConfigValidationResult
  updatePortfolioExecutionConfiguration(portfolioId: ID!, request: ExecutionConfigurationInput!): ConfigValidationResult
  updatePortfolioPricingConfiguration(portfolioId: ID!, request: PricingConfigurationInput!): ConfigValidationResult
  updatePortfolioHedgingConfiguration(portfolioId: ID!, request: HedgingConfigurationInput!): HedgingConfigValidationResult
  updatePortfolioInstrumentConfiguration(portfolioId: ID!, request: InstrumentConfigurationInput!): ConfigValidationResult
  deletePortfolioConfiguration(portfolioId: ID!): String

  #  portfolio group config
  updatePortfolioGroupConfiguration(portfolioGroupId: ID!, request: PortfolioGroupConfigurationInput): String
  updatePortfolioGroupExecutionConfiguration(portfolioGroupId: ID!, request: ExecutionConfigurationInput!): String
  updatePortfolioGroupPricingConfiguration(portfolioGroupId: ID!, request: PricingConfigurationInput!): String
  updatePortfolioGroupHedgingConfiguration(portfolioGroupId: ID!, request: HedgingConfigurationInput!): String
  updatePortfolioGroupInstrumentConfiguration(portfolioGroupId: ID!, request: InstrumentConfigurationInput!): String
  deletePortfolioGroupConfiguration(portfolioGroupId: ID!): String

  resetConfiguration(resetConfigurationInput: ResetConfigurationInput): ConfigValidationResult

  # Quoting Engine
  updateQuotingConfiguration(clobUid: String!, request: QuotingConfigurationInput!): QuotingConfigValidationResult
  createQuotingConfiguration(request: QuotingConfigurationInput!): QuotingConfigValidationResult
  deleteQuotingConfiguration(clobUid: String!): String
  updateInstrumentQuotingConfiguration(clobUid: String!, request: InstrumentQuotingConfigurationInput!): QuotingConfigValidationResult

  # Currency
  saveCurrency(request: CurrencyInput!): MutationSubmittedResponse

  addTransaction(input: TransactionInput!): MutationSubmittedResponse!

  # Conversion Rates
  createConversionSource(request: CreateConversionSourceInput!): MutationSubmittedResponse
  updateConversionSource(request: UpdateConversionSourceInput!): MutationSubmittedResponse
  deleteConversionSource(venueAccount: String!): MutationSubmittedResponse

  # Rate Subscriptions
  createRateSubscription(request: CreateRateSubscriptionInput!): MutationSubmittedResponse
  deleteRateSubscription(request: DeleteRateSubscriptionInput!): MutationSubmittedResponse
}

type ApiKeyResponse {
  id: String!
  name: String
  createdAt: String!
  secret: String!
  status: ApiKeyStatus!
}

#
type ValidationResponse {
  isValid: Boolean!
  errors: [ValidationError!]
}

#
type ValidationError {
  field: String!
  message: String!
}

#
type MutationReturnValue {
  clientId: String
}

# Query root
# can be extended in other graphql files
type Query {
  checkAccountName(name: String): ValidationResponse
  checkPortfolioName(name: String): ValidationResponse
  userData: UserDataResponse!
  """
  Result list will be sorted by instrumentId field
  Default sorting order is ASC if this field is not set
  """
  instruments(venueNames: [String!], query: String, limit: Int, sortingOrder: SortingOrder): [InstrumentResponse!]!
  instrumentSearch(search: InstrumentsSearchInput): InstrumentsConnection
  symbolSearch(search: SymbolSearchInput): SymbolSearchConnection
  # from, to: Epoch Unix Timestamp
  orderStates(portfolioId: String, venueAccount: String, instrumentId: String, orderId: String, clOrderId: String, from: String, to: String, extOrderId: String): [OrderStateResponse!]
  orderStatesWithPredicates(search: OrderStateSearchInput!): OrderStateConnection
  groupNames: [String!]!
  groupsPermissionsForResource(resource: Resource!, resourceId: String): [GroupsPermissionsForResourceResponse!]!
  usersPermissionsForResource(resource: Resource!, resourceId: String): [UsersPermissionsForResourceResponse!]!
  userStaticPermissions: [Permission!]
  groupStaticPermissions: [Permission!]
  userNames: [String!]!
  userWithPermissions: UserResponse!
  permissionSearch(search: PermissionSearchInput): PermissionConnection!
  hasOwnPermission(resource: Resource!, scope: Scope!, resourceId: String): Boolean
  hasUserPermission(clientId: String!, resource: Resource!, scope: Scope!, resourceId: String): Boolean
  venueAccountById(venueAccountId: String!): VenueAccount
  venueAccounts: [VenueAccountsPerVenue]
  venueCapabilities: [VenueCapabilitiesResponse]
  effectiveVenueAccounts(portfolioId: String!, instrumentId: String!): EffectiveVenueAccountsResponse
  venueAccountDetails(request: VenueAccountDetailsInput): VenueAccountDetailsResponse
  portfolioById(portfolioId: String!): PortfolioResponse
  portfolioSearch(search: PortfolioSearchInput): PortfolioConnection
  portfolioTags: [PortfolioTag!]!
  positions(search: PositionSearchInput!): PositionConnection
  positionTypes: [String!]!
  ledgerEntries(search: LedgerEntrySearchInput!): LedgerEntryConnection
  transactions(search: TransactionSearchInput!): TransactionConnection
  transactionTypes: [String!]!
  venues(venueType: VenueType): [Venue!]!
  preTradeChecks: [PreTradeCheck!]!
  preTradeCheckFormSchema: [PreTradeCheckSchema!]!
  portfolioConfigurationList: [PortfolioConfigurationFlat!]!
  portfolioGroupConfigurationList: [PortfolioGroupConfigurationFlat!]!
  portfolioConfiguration(id: ID!): PortfolioConfiguration
  portfolioGroupConfiguration(id: ID!): PortfolioGroupConfiguration
  portfolioConfigValidation(portfolioId: String!): ConfigValidationResult
  portfolioHedgingConfigValidation(portfolioId: String!): HedgingConfigValidationResult
  quotingConfigValidations: [QuotingConfigValidationResult!]!
  preTradeCheckAuditLogs(search: PreTradeCheckAuditLogSearchInput): PreTradeCheckAuditLogConnection
  auditEventLogsByType (type: PayloadType!): [AuditLogEvent]
  apiKeys: [ApiKey]
  walletAccountSearch(search: WalletAccountSearchInput): WalletAccountConnection!
  matches(search: MatchesSearchInput): MatchesResult
  internalExchangeConfiguration(account: String!): InternalExchangeConfigurationResponse
  quotingConfiguration: [QuotingConfiguration!]!
  currencySearch(search: CurrencySearchInput): [CurrencyResponse!]!
  timeline(search: TimelineSearchInput): TimelineConnection
  orderBookSnapshot(search: OrderBookSnapshotSearchInput): OrderBookSnapshot
  conversionSources: [ConversionSourceResponse!]!
  conversionSourceVenueAccounts: [ConversionSourceVenueAccountResponse!]!
  rateSubscriptions: [RateSubscriptionResponse!]!
  systemCurrency: String!
  connectorTemplate(venue: String): [ConfigField!]
  entitlements: [String]!
}

#API Key

enum ApiKeyStatus {
  ACTIVE
  INACTIVE
}

type ApiKey {
  id: String!
  name: String
  createdAt: String!
  status: ApiKeyStatus!
}


### PTC ###

enum PreTradeCheckChannels {
  UI, API
}

enum PreTradeCheckPropertyType {
  STRING, NUMBER, DATE_TIME, STRING_LIST
}

enum PreTradeCheckLevel {
  WARN, BLOCK
}

type PreTradeCheckProperty {
  type: PreTradeCheckPropertyType!
  name: String!
  values: [String!]!
}

type PreTradeCheck {
  id: String!
  type: String!
  level: PreTradeCheckLevel!
  portfolios: [PortfolioResponse!]!
  portfolioTags: [Tag!]!
  channels: [PreTradeCheckChannels!]!
  configuration: [PreTradeCheckProperty!]!
}

input PreTradeCheckAuditLogSearchInput {
  from: String
  to: String
  portfolioIds: [String]
  first: Int
  after: String
}

type OrderSnapshot {
  orderId: String!
  venueAccount: String!
  venueAccountName: String!
  instrumentType: InstrumentType!
  instrumentId: String!
  baseCurrency: String!
  quoteCurrency: String!
  portfolioId: String!
  portfolioName: String!
  orderType: OrderType!
  side: Side!
  quantity: Float
  price: Float
  stopPrice: Float
  tif: TIF
  postOnly: Boolean
  parentOrderId: String
  venueAccountDescs: [VenueAccountDesc!]
}

type PreTradeCheckResult {
  status: PreTradeCheckStatus!
  reason: String!
  preTradeCheck: PreTradeCheck!
}

type PreTradeCheckAuditLog {
  id: String!
  createdAt: String
  preTradeCheckResults: [PreTradeCheckResult]
  portfolioId: String
  portfolioName: String
  order: OrderSnapshot
}

type AuditLogEvent {
  uuid: String!
  createdAt: String
  source: String!
  payloadType: PayloadType!
  payload: String!
}

type PreTradeCheckAuditLogEdge {
  node: PreTradeCheckAuditLog!
  cursor: String!
}

type PreTradeCheckAuditLogConnection {
  edges: [PreTradeCheckAuditLogEdge!]!
  pageInfo: PageInfo!
}

type PreTradeCheckPropertySchema {
  type: PreTradeCheckPropertyType!
  name: String!
  required: Boolean!
  isEnum: Boolean!
  format: PreTradeCheckPropertyFormat
  options: [String!]
}

enum PreTradeCheckPropertyFormat {
  FORMAT_UNSPECIFIED, CURRENCY_PAIR
}

type PreTradeCheckSchema {
  type: String!
  configuration: [PreTradeCheckPropertySchema!]!
}

input VenueAccountDetailsInput{
  venueAccount: String!
}

input PreTradeCheckPropertyInput {
  type: PreTradeCheckPropertyType!
  name: String!
  values: [String!]!
}

input PreTradeCheckInput {
  id: String!
  type: String!
  level: PreTradeCheckLevel!
  portfolios: [String!]!
  portfolioTags: [TagInput!]!
  channels: [PreTradeCheckChannels!]!
  configuration: [PreTradeCheckPropertyInput!]!
}

type PortfolioTag {
  key: String!
  values: [String!]!
}

input SimplePredicateInput {
  method: SimplePredicateType,
  field: OrderStateStringField,
  value: String!
}

enum SimplePredicateType {
  CONTAINS, EQUAL, NOT_EQUAL
}

input CollectionPredicateInput {
  method: CollectionPredicateType,
  field: OrderStateCollectionField,
  value: [String]!
}

enum CollectionPredicateType {
  IN
}

input DatePredicate {
  method: DatePredicateType,
  field: OrderStateDateField,
  value: String!
}

enum DatePredicateType {
  FROM, TO
}

enum OrderStateDateField {
  CREATED_AT, UPDATED_AT,
}

enum OrderStateStringField {
  PORTFOLIO_ID, VENUE_ACCOUNT_ID, INSTRUMENT_ID, ORDER_ID, CL_ORDER_ID, ORDER_STATUS, PARENT_ORDER_ID, ROOT_ORDER_ID, EXT_ORDER_ID
}

enum OrderStateCollectionField {
  INSTRUMENT_ID, ORDER_STATUS, ORDER_CATEGORY, PORTFOLIO_ID, VENUE_ACCOUNT_ID
}

# deprecated - use OrderStateResponse instead
type ExecutionReportResponse {
  parentOrderId: String
  avgPrice: String
  clOrderId: String
  clientId: String
  portfolioId: String
  cumQty: Float
  execType: ExecType
  executionId: String
  venueExecutionId: String
  lastPrice: String
  lastQty: Float
  leavesQty: Float
  orderId: String
  orderQty: Float
  currency: String
  orderStatus: OrderStatus
  orderStatusRequestId: String
  origClOrderId: String
  reason: String
  result: String
  symbol: String
  instrumentType: InstrumentType
  venueAccount: String
  underlyingVenueAccount: String
  side: Side
  instrumentId: String
  targetVenueAccount: String
  targetVenueTicker: String
  targetVenueTimestamp: String
  text: String
  timestamp: String
  createdAt: String
  updatedAt: String
  fee: Float
  feeCurrency: String
  sequenceNumber: Int
}

# deprecated - FIX protocol specific, use OrderStateResponse fields instead
enum ExecType {
  CALCULATED
  CANCELED
  DONE_FOR_DAY
  EXPIRED
  FILL
  NEW
  ORDER_STATUS
  PARTIAL_FILL
  PENDING_CANCEL
  PENDING_NEW
  PENDING_REPLACE
  REJECTED
  REPLACED
  RESTATED
  STOPPED
  SUSPENDED
  TRADE
  TRADE_CANCEL
  TRADE_CORRECT
  TRADE_HAS_BEEN_RELEASED_TO_CLEARING
  TRADE_IN_A_CLEARING_HOLD
  TRIGGERED_OR_ACTIVATED_BY_SYSTEM
  UNSPECIFIED
}

# Subscription root
# can be extended in other graphql files
type Subscription {
  # Don't remove it until: https://algotrader.atlassian.net/browse/AC-2810 is done
  executionReport: ExecutionReportResponse @deprecated(reason: "executionReport is deprecated. Use orderStates instead.")
  orderStatesWithSnapshot(portfolioId: String, venueAccount: String, isOpen: Boolean, historyCap: Int): OrderStateResponse
  transactionsWithSnapshot(search: TransactionSearchInput!): TransactionResponse
  cancelReject: CancelRejectResponse @deprecated(reason: "cancelReject is deprecated. Use orderStates instead.")
  """
  Stream of tick (L1, e.g. top-of-book) events.
  vanueAccount parameter is conditionally required - Street-side market data streaming (e.g. data produced by external venue)
  portfolioId parameter is conditionally required - Client-side market data streaming (e.g. data produced by broker desk)
  """
  tick(venueAccount: String, instrumentId: String!, portfolioId: String): MarketDataResponse
  """
  Stream of order book snapshot (L2, e.g. full-book) events.
  vanueAccount parameter is conditionally required - Street-side market data streaming (e.g. data produced by external venue)
  portfolioId parameter is conditionally required - Client-side market data streaming (e.g. data produced by broker desk)
  """
  orderBook(venueAccount: String, instrumentId: String!, portfolioId: String): OrderBookResponse
  positionChanges(search: PositionSearchInput!): PositionResponse!
  eventsLogs: EventLogResponse!
  targetStates: TargetStatesResponse!
}

type TargetStatesResponse {
  target: String!
  targetName: String
  capabilities: [TargetCapabilityStatus]!
}

type TargetCapabilityStatus {
  capability:Capability!
  healthStatus: HealthStatus!
  message: String
  timestamp: String
}

enum HealthStatus {
  HEALTH_STATUS_UNSPECIFIED
  ALIVE
  UNHEALTHY
  DEAD
}

enum Capability {
  CAPABILITY_UNSPECIFIED
  REFERENCE_DATA
  TRADING
  REQUEST_FOR_QUOTE
  MARKET_DATA
  ACCOUNT
  HISTORICAL_DATA
  DROP_COPY
  RECONCILIATION
  GENERIC_EVENT
  TRANSFER
  REQUEST_RELAY
  OFF_EXCHANGE
}

enum Status {
  SUCCESS
  FAILURE
}

enum InstrumentSortBy {
  INSTRUMENT_ID,
  SYMBOL,
  CREATED_AT
}

enum PortfolioSortBy {
  PORTFOLIO_ID,
  PORTFOLIO_NAME,
  CREATED_AT
}

enum OrderCategory {
  ORDER_CATEGORY_UNSPECIFIED,
  DIRECT_MARKET_ACCESS_ORDER,
  SOR_ORDER,
  SOR_CHILD_ORDER,
  AGENCY_ORDER,
  AGENCY_STREET_ORDER,
  AGENCY_SOR_ORDER,
  AGENCY_CLOB_ORDER,
  CLOB_QUOTING_ORDER,
  CLOB_EXTERNAL_HEDGE_ORDER,
  AUTO_HEDGER_EXTERNAL_HEDGE_ORDER
}

enum PreTradeCheckStatus {
  STATUS_UNSPECIFIED,
  APPROVED,
  APPROVED_WITH_WARNING,
  REJECTED
}

enum PayloadType {
  RECOMMENDATION,
  ORDER_BOOK,
  PRE_TRADE_CHECK
}
