plugins {
    id 'java'
    id 'idea'
    id 'maven-publish'
    alias dependencyCatalog.plugins.spring.boot
    alias dependencyCatalog.plugins.dependency.management
    alias dependencyCatalog.plugins.protobuf
    alias dependencyCatalog.plugins.sonarqube
}

ext {
    repository_username = System.env.NEXUS_DEPLOY_USERNAME
    repository_password = System.env.NEXUS_DEPLOY_PASSWORD
}

dependencies {

    implementation project(':target-registry-domain')

    implementation dependencyCatalog.published.language.oems
    implementation dependencyCatalog.jackson.databind
    implementation dependencyCatalog.protobuf.java
    implementation dependencyCatalog.connector.c360t
    implementation dependencyCatalog.connector.bitfinex
    implementation dependencyCatalog.connector.coinapi
    implementation dependencyCatalog.connector.coinbase
    implementation dependencyCatalog.connector.bitmex
    implementation dependencyCatalog.connector.generic
    implementation dependencyCatalog.connector.binance.spot
    implementation dependencyCatalog.connector.bitstamp
    implementation dependencyCatalog.connector.kraken
    implementation dependencyCatalog.connector.bit2me
    implementation dependencyCatalog.connector.goldman.sachs
    implementation dependencyCatalog.connector.lmax.global
    implementation dependencyCatalog.connector.b2c2
    implementation dependencyCatalog.connector.wintermute
    implementation dependencyCatalog.connector.scrypt
    implementation dependencyCatalog.connector.fireblocks
    implementation dependencyCatalog.connector.bullish
}

test {
    useJUnitPlatform()
}

sonarqube {
    properties {
        property "sonar.projectKey", "target-registry-schema-extractor"
        property "sonar.projectName", "Target Registry Schema Extractor"
    }
}
