package io.wyden.oems.ordercollider.collider.service.fsm;

import com.rabbitmq.client.Return;
import io.wyden.cloud.utils.test.TracingMock;
import io.wyden.oems.ordercollider.collider.service.CommandNotReachedConnectorHandler;
import io.wyden.oems.ordercollider.collider.service.tracking.FailureRequeueException;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import io.wyden.published.venue.VenueRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.UUID;

import static io.wyden.published.oems.OemsExecType.REJECTED;
import static io.wyden.published.oems.OemsOrderStatus.STATUS_NEW;
import static io.wyden.published.oems.OemsOrderStatus.STATUS_REJECTED;
import static io.wyden.published.oems.OemsResponse.OemsResponseType.EXECUTION_REPORT;
import static io.wyden.published.oems.OemsResponse.Result.CONNECTOR_UNAVAILABLE;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.timeout;
import static org.mockito.Mockito.verify;

class ConnectorUnavailableTest extends OrderServiceBase {

    CommandNotReachedConnectorHandler commandNotReachedConnectorHandler;

    VenueRequest venueOrder;

    @BeforeEach
    void beforeEach() {
        commandNotReachedConnectorHandler = new CommandNotReachedConnectorHandler(orderService, TracingMock.createMock());

        orderService.onOemsRequest(order);
        verify(venueRequestEmitter).emit(venueOrderCaptor.capture());
        venueOrder = venueOrderCaptor.getValue();
    }

    @Test
    void whenVenueAccountNotAvailableSendReject() {
        Return returnMessage = new Return(312, "No route", "venue.NEW", order.getTarget(), null, venueOrder.toByteArray());
        commandNotReachedConnectorHandler.onBrokerReturn(returnMessage);

        verify(oemsResponseEmitter, timeout(1000).times(2)).emit(oemsResponseCaptor.capture());
        OemsResponse oemsResponse = oemsResponseCaptor.getValue();
        assertThat(oemsResponse.getResponseType()).isEqualTo(EXECUTION_REPORT);
        assertThat(oemsResponse.getOrderId()).isEqualTo(order.getOrderId());
        assertThat(oemsResponse.getExecType()).isEqualTo(REJECTED);
        assertThat(oemsResponse.getOrderStatus()).isEqualTo(STATUS_REJECTED);
        assertThat(oemsResponse.getRequestResult()).isEqualTo(CONNECTOR_UNAVAILABLE);
        assertThat(oemsResponse.getReason()).isEqualTo("Connector not available");
    }

    @Test
    void whenOrderExpiredOnConnectorSendReject() {
        orderService.onOrderNotDelivered(venueOrder);

        verify(oemsResponseEmitter, TWICE).emit(oemsResponseCaptor.capture());
        OemsResponse oemsResponse = oemsResponseCaptor.getValue();
        assertThat(oemsResponse.getResponseType()).isEqualTo(EXECUTION_REPORT);
        assertThat(oemsResponse.getOrderId()).isEqualTo(order.getOrderId());
        assertThat(oemsResponse.getExecType()).isEqualTo(REJECTED);
        assertThat(oemsResponse.getOrderStatus()).isEqualTo(STATUS_REJECTED);
        assertThat(oemsResponse.getRequestResult()).isEqualTo(CONNECTOR_UNAVAILABLE);
        assertThat(oemsResponse.getReason()).isEqualTo("Connector not available");
    }

    @Test
    void whenOrderExpiredOnConnectorAndStateCannotBeFoundThenRequeue() {
        VenueRequest returnedOrder = venueOrder.toBuilder()
            .setOrderId(UUID.randomUUID().toString())
            .build();

        Exception ex = assertThrows(FailureRequeueException.class, () -> orderService.onOrderNotDelivered(returnedOrder));
        assertThat(ex.getMessage()).isEqualTo("OrderState not found for venueOrderId=" + returnedOrder.getOrderId());
        verify(oemsResponseEmitter, ONCE).emit(any());
    }

    @Test
    void whenCancelExpiredOnConnectorSendCancelReject() {
        reset(venueRequestEmitter);

        orderService.onExecutionReport(newExecutionReport());
        OemsRequest cancel = defaultCancel();
        orderService.onOemsRequest(cancel);
        verify(venueRequestEmitter, ONCE).emit(venueCancelCaptor.capture());
        VenueRequest venueCancel = venueCancelCaptor.getValue();
        reset(oemsResponseEmitter);

        orderService.onCancelNotDelivered(venueCancel);
        verify(oemsResponseEmitter, ONCE).emit(oemsResponseCaptor.capture());
        OemsResponse response = oemsResponseCaptor.getValue();
        assertThat(response.getResponseType()).isEqualTo(OemsResponse.OemsResponseType.CANCEL_REJECT);
        assertThat(response.getMetadata().getInResponseToRequestId()).isEqualTo(cancel.getMetadata().getRequestId());
        assertThat(response.getOrderStatus()).isEqualTo(STATUS_NEW);
        assertThat(response.getRequestResult()).isEqualTo(CONNECTOR_UNAVAILABLE);
    }

    @Test
    void whenCancelExpiredOnConnectorAndStateCannotBeFoundThenRequeue() {
        reset(venueRequestEmitter);

        orderService.onExecutionReport(newExecutionReport());
        OemsRequest cancel = defaultCancel();
        orderService.onOemsRequest(cancel);
        verify(venueRequestEmitter, ONCE).emit(venueCancelCaptor.capture());
        VenueRequest venueCancel = venueCancelCaptor.getValue();
        reset(oemsResponseEmitter);

        VenueRequest returnCancel = venueCancel.toBuilder()
            .setOrderId(UUID.randomUUID().toString())
            .build();
        Exception ex = assertThrows(FailureRequeueException.class, () -> orderService.onCancelNotDelivered(returnCancel));
        assertThat(ex.getMessage()).isEqualTo("OrderState not found for venueOrderId=" + returnCancel.getOrderId());
        verify(oemsResponseEmitter, NEVER).emit(any());
    }
}
