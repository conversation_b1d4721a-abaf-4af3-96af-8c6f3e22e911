plugins {
    id 'idea'
    id 'maven-publish'
}

group = 'io.wyden.apiserver'

def versionPropsFile = file('version.properties')
if (versionPropsFile.canRead()) {
    Properties versionProps = new Properties()
    versionProps.load(new FileInputStream(versionPropsFile))
    def ver = versionProps['VERSION'].toString()
    project.version = ver
} else {
    throw new GradleException("Could not read version.properties!")
}

ext {
    repository_username = System.env.NEXUS_DEPLOY_USERNAME
    repository_password = System.env.NEXUS_DEPLOY_PASSWORD
}

dependencies {
    testImplementation platform('org.junit:junit-bom:5.9.1')
    testImplementation 'org.junit.jupiter:junit-jupiter'
}

test {
    useJUnitPlatform()
}

publishing {
    publications {
        mavenJava(MavenPublication) {
            repositories {
                maven {
                    name 'nexus-snapshots'
                    url 'https://repo.wyden.io/nexus/repository/snapshots/'
                    credentials {
                        username repository_username
                        password repository_password
                    }
                }
            }
            from components.java
        }
    }
}

sonarqube {
    properties {
        property "sonar.projectKey", "fix-api-domain"
        property "sonar.projectName", "FIX API Domain"
    }
}
