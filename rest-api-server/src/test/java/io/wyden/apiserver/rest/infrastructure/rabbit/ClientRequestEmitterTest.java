package io.wyden.apiserver.rest.infrastructure.rabbit;

import com.google.protobuf.Message;
import io.micrometer.core.instrument.MeterRegistry;
import io.wyden.cloud.utils.test.TracingMock;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.destination.OemsHeader;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.published.client.ClientRequest;
import io.wyden.published.client.ClientRequestType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class ClientRequestEmitterTest {

    private static final String CLIENT_ID = "john_doe";
    private static final String VENUE_ACCOUNT = "johndoe@Hexnet";
    private static final String INSTRUMENT_ID = "HEXUSD@FOREX@Hexnet";
    private ClientRequestEmitter rabbitPublisher;

    @Mock
    private RabbitExchange<Message> tradingExchange;

    @BeforeEach
    void setUp() {
        Telemetry telemetryMock = new Telemetry(TracingMock.createMock(), mock(MeterRegistry.class));
        rabbitPublisher = new ClientRequestEmitter(tradingExchange, telemetryMock);
    }

    @Test
    void shouldPublishNewOrderSingleWithHeaders() {
        rabbitPublisher.emit(ClientRequest.newBuilder()
            .setRequestType(ClientRequestType.ORDER_SINGLE)
            .setClientId(CLIENT_ID)
            .addVenueAccounts(VENUE_ACCOUNT)
            .setInstrumentId(INSTRUMENT_ID)
            .build()
        );

        ArgumentCaptor<Map<String, String>> captor = ArgumentCaptor.forClass(Map.class);
        verify(tradingExchange).publishWithHeaders(any(), captor.capture());

        Map<String, String> captured = captor.getValue();
        assertThat(captured).containsEntry(OemsHeader.MESSAGE_TYPE.getHeaderName(), ClientRequest.class.getSimpleName());
        assertThat(captured).containsEntry(OemsHeader.CLIENT_ID.getHeaderName(), CLIENT_ID);
        assertThat(captured).containsEntry(OemsHeader.VENUE_ACCOUNT.getHeaderName(), VENUE_ACCOUNT);
        assertThat(captured).containsEntry(OemsHeader.INSTRUMENT_ID.getHeaderName(), INSTRUMENT_ID);
    }

    @Test
    void shouldPublishCancelWithHeaders() {
        rabbitPublisher.emit(ClientRequest.newBuilder()
            .setRequestType(ClientRequestType.CANCEL)
            .setClientId(CLIENT_ID)
            .addVenueAccounts(VENUE_ACCOUNT)
            .setInstrumentId(INSTRUMENT_ID)
            .build()
        );

        ArgumentCaptor<Map<String, String>> captor = ArgumentCaptor.forClass(Map.class);
        verify(tradingExchange).publishWithHeaders(any(), captor.capture());

        Map<String, String> captured = captor.getValue();
        assertThat(captured).containsEntry(OemsHeader.MESSAGE_TYPE.getHeaderName(), ClientRequest.class.getSimpleName());
        assertThat(captured).containsEntry(OemsHeader.CLIENT_ID.getHeaderName(), CLIENT_ID);
        assertThat(captured).containsEntry(OemsHeader.VENUE_ACCOUNT.getHeaderName(), VENUE_ACCOUNT);
        assertThat(captured).containsEntry(OemsHeader.INSTRUMENT_ID.getHeaderName(), INSTRUMENT_ID);
    }

    @Test
    void shouldPublishCancelReplaceWithHeaders() {
        rabbitPublisher.emit(ClientRequest.newBuilder()
            .setRequestType(ClientRequestType.CANCEL_REPLACE)
            .setClientId(CLIENT_ID)
            .addVenueAccounts(VENUE_ACCOUNT)
            .setInstrumentId(INSTRUMENT_ID)
            .build()
        );

        ArgumentCaptor<Map<String, String>> captor = ArgumentCaptor.forClass(Map.class);
        verify(tradingExchange).publishWithHeaders(any(), captor.capture());

        Map<String, String> captured = captor.getValue();
        assertThat(captured).containsEntry(OemsHeader.MESSAGE_TYPE.getHeaderName(), ClientRequest.class.getSimpleName());
        assertThat(captured).containsEntry(OemsHeader.CLIENT_ID.getHeaderName(), CLIENT_ID);
        assertThat(captured).containsEntry(OemsHeader.VENUE_ACCOUNT.getHeaderName(), VENUE_ACCOUNT);
        assertThat(captured).containsEntry(OemsHeader.INSTRUMENT_ID.getHeaderName(), INSTRUMENT_ID);
    }

    @Test
    void shouldPublishOrderStatusRequestWithHeaders() {
        rabbitPublisher.emit(ClientRequest.newBuilder()
            .setRequestType(ClientRequestType.ORDER_STATUS_REQUEST)
            .setClientId(CLIENT_ID)
            .addVenueAccounts(VENUE_ACCOUNT)
            .setInstrumentId(INSTRUMENT_ID)
            .build()
        );

        ArgumentCaptor<Map<String, String>> captor = ArgumentCaptor.forClass(Map.class);
        verify(tradingExchange).publishWithHeaders(any(), captor.capture());

        Map<String, String> captured = captor.getValue();
        assertThat(captured).containsEntry(OemsHeader.MESSAGE_TYPE.getHeaderName(), ClientRequest.class.getSimpleName());
        assertThat(captured).containsEntry(OemsHeader.CLIENT_ID.getHeaderName(), CLIENT_ID);
        assertThat(captured).containsEntry(OemsHeader.VENUE_ACCOUNT.getHeaderName(), VENUE_ACCOUNT);
        assertThat(captured).containsEntry(OemsHeader.INSTRUMENT_ID.getHeaderName(), INSTRUMENT_ID);
    }
}
