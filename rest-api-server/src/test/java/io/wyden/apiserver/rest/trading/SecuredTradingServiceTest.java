package io.wyden.apiserver.rest.trading;

import io.wyden.accessgateway.client.apikey.dto.AuthResponseDto;
import io.wyden.accessgateway.client.permission.Permission;
import io.wyden.apiserver.rest.apiui.SharedModel;
import io.wyden.apiserver.rest.security.AccessService;
import io.wyden.apiserver.rest.security.SecurityService;
import io.wyden.apiserver.rest.security.model.WydenAuthenticationToken;
import io.wyden.apiserver.rest.trading.model.NewOrderSingleRequest;
import io.wyden.apiserver.rest.trading.model.OrderCancelReplaceRequest;
import io.wyden.apiserver.rest.trading.model.OrderCancelRequest;
import io.wyden.apiserver.rest.trading.model.OrderStatusRequest;
import io.wyden.published.client.ClientRequest;
import io.wyden.published.common.ApiType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.security.access.AccessDeniedException;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyCollection;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;
import static org.testcontainers.shaded.org.apache.commons.lang3.StringUtils.EMPTY;

class SecuredTradingServiceTest {

    private static final String ORIG_CLIENT_ID = UUID.randomUUID().toString();
    private static final String ORIG_ORDER_ID = UUID.randomUUID().toString();
    private static final String ORIG_CL_ORDER_ID = UUID.randomUUID().toString();
    private static final String CLIENT_ID = UUID.randomUUID().toString();
    private static final String CL_ORDER_ID = UUID.randomUUID().toString();
    private static final String PORTFOLIO_ID = UUID.randomUUID().toString();
    private static final String VENUE_ACCOUNT_ID1 = UUID.randomUUID().toString();
    private static final String VENUE_ACCOUNT_ID2 = UUID.randomUUID().toString();
    private SecuredTradingService securedTradingService;
    private TradingService tradingService;
    private SecurityService securityService;
    private AccessService accessService;
    private ClientToOrderIdRegistry clientToOrderIdRegistry;
    private ClientRequestRegistry clientRequestRegistry;
    private WydenAuthenticationToken token;
    private String apiKey = "k3y";
    private String apiSecret = "s3cr3t";

    @BeforeEach
    void setUp() {
        token = new WydenAuthenticationToken(CLIENT_ID);
        tradingService = mock(TradingService.class);
        securityService = mock(SecurityService.class);
        accessService = mock(AccessService.class);
        clientToOrderIdRegistry = mock(ClientToOrderIdRegistry.class);
        clientRequestRegistry = mock(ClientRequestRegistry.class);
        securedTradingService = new SecuredTradingService(tradingService, securityService, accessService, clientToOrderIdRegistry, clientRequestRegistry);
    }

    // New Order

    @Test
    void sendingNewOrderRequestViaAPIKeyShouldRequireDynamicVenuePermissions() {
        // given
        NewOrderSingleRequest orderRequest = getNewOrderSingleRequest();
        when(securityService.authenticate(any(), any())).thenReturn(new AuthResponseDto(CLIENT_ID, CLIENT_ID, Set.of(), Set.of()));
        when(accessService.hasPermission(any(), any(Permission.class), anyString())).thenReturn(true);
        when(accessService.hasPermission(any(), any(Permission.class), anyCollection())).thenReturn(true);

        // when
        securedTradingService.sendOrder(orderRequest, CLIENT_ID, apiKey, apiSecret, ApiType.REST);

        // then
        verify(securityService).authenticate(eq(apiKey), eq(apiSecret));
        verify(tradingService).sendOrder(orderRequest, CLIENT_ID, ApiType.REST);
        verifyNoMoreInteractions(tradingService);
    }

    // Cancel

    @Test
    void givenOrderIdIsProvidedWhenCancelIsCalledViaGraphQlThenItShouldCancelTheOrder() {
        OrderCancelRequest cancelRequest = new OrderCancelRequest(ORIG_ORDER_ID, ORIG_CLIENT_ID, ORIG_CL_ORDER_ID, CL_ORDER_ID, false);
        securedTradingService.cancelOrder(cancelRequest, ApiType.GRAPHQL, token);
        verify(tradingService).cancelOrder(eq(cancelRequest), eq(CLIENT_ID), eq(ORIG_ORDER_ID), eq(ApiType.GRAPHQL));
        verifyNoMoreInteractions(tradingService);
    }

    @Test
    void givenOrderIdIsProvidedWhenCancelIsCalledViaRestThenItShouldCancelTheOrder() {
        OrderCancelRequest cancelRequest = new OrderCancelRequest(ORIG_ORDER_ID, ORIG_CLIENT_ID, ORIG_CL_ORDER_ID, CL_ORDER_ID, false);
        when(securityService.authenticate(any(), any())).thenReturn(new AuthResponseDto(CLIENT_ID, CLIENT_ID, Set.of(), Set.of()));
        securedTradingService.cancelOrder(cancelRequest, CLIENT_ID, apiKey, apiSecret, ApiType.REST);
        verify(tradingService).cancelOrder(eq(cancelRequest), eq(CLIENT_ID), eq(ORIG_ORDER_ID), eq(ApiType.REST));
        verifyNoMoreInteractions(tradingService);
    }

    @Test
    void givenClientIdAndClOrderIdIsProvidedWhenCancelIsCalledThenItShouldCancelTheOrder() {
        OrderCancelRequest cancelRequest = new OrderCancelRequest("", ORIG_CLIENT_ID, ORIG_CL_ORDER_ID, CL_ORDER_ID, false);
        when(clientToOrderIdRegistry.findOrderId(eq(ORIG_CLIENT_ID), eq(ORIG_CL_ORDER_ID))).thenReturn(Optional.of(ORIG_ORDER_ID));
        securedTradingService.cancelOrder(cancelRequest, ApiType.GRAPHQL, token);
        verify(tradingService).cancelOrder(eq(cancelRequest), eq(CLIENT_ID), eq(ORIG_ORDER_ID), eq(ApiType.GRAPHQL));
        verifyNoMoreInteractions(tradingService);
    }

    @Test
    void givenClOrderIdAndNoClientIdIsProvidedWhenCancelIsCalledViaGraphQLThenTokenShouldBeUsedInstead() {
        OrderCancelRequest cancelRequest = new OrderCancelRequest(null, null, ORIG_CL_ORDER_ID, CL_ORDER_ID, false);
        when(clientToOrderIdRegistry.findOrderId(eq(CLIENT_ID), eq(ORIG_CL_ORDER_ID))).thenReturn(Optional.of(ORIG_ORDER_ID));
        securedTradingService.cancelOrder(cancelRequest, ApiType.GRAPHQL, token);
        verify(tradingService).cancelOrder(eq(cancelRequest), eq(CLIENT_ID), eq(ORIG_ORDER_ID), eq(ApiType.GRAPHQL));
        verifyNoMoreInteractions(tradingService);
    }

    @Test
    void givenClOrderIdAndNoClientIdIsProvidedWhenCancelIsCalledViaRestThenApiKeyShouldBeUsedInstead() {
        OrderCancelRequest cancelRequest = new OrderCancelRequest(" ", " ", ORIG_CL_ORDER_ID, CL_ORDER_ID, true);
        when(securityService.authenticate(eq(apiKey), eq(apiSecret))).thenReturn(new AuthResponseDto(CLIENT_ID, CLIENT_ID, Set.of(), Set.of()));
        when(clientToOrderIdRegistry.findOrderId(eq(CLIENT_ID), eq(ORIG_CL_ORDER_ID))).thenReturn(Optional.of(ORIG_ORDER_ID));
        securedTradingService.cancelOrder(cancelRequest, CLIENT_ID, apiKey, apiSecret, ApiType.REST);
        verify(tradingService).cancelOrder(eq(cancelRequest), eq(CLIENT_ID), eq(ORIG_ORDER_ID), eq(ApiType.REST));
        verifyNoMoreInteractions(tradingService);
    }

    @Test
    void givenApiKeyIsMissingWhenCancelIsCalledViaRestThenAccessShouldBeDenied() {
        OrderCancelRequest cancelRequest = new OrderCancelRequest(ORIG_ORDER_ID, ORIG_CLIENT_ID, ORIG_CL_ORDER_ID, CL_ORDER_ID, false);
        when(securityService.authenticate(any(), any())).thenReturn(new AuthResponseDto(CLIENT_ID, CLIENT_ID, Set.of(), Set.of()));
        assertThrows(AccessDeniedException.class, () -> securedTradingService.cancelOrder(cancelRequest, CLIENT_ID, EMPTY, apiSecret, ApiType.REST));
        verifyNoMoreInteractions(tradingService);
    }

    @Test
    void givenApiSecretIsMissingWhenCancelIsCalledViaRestThenAccessShouldBeDenied() {
        OrderCancelRequest cancelRequest = new OrderCancelRequest(ORIG_ORDER_ID, ORIG_CLIENT_ID, ORIG_CL_ORDER_ID, CL_ORDER_ID, false);
        when(securityService.authenticate(any(), any())).thenReturn(new AuthResponseDto(CLIENT_ID, CLIENT_ID, Set.of(), Set.of()));
        assertThrows(AccessDeniedException.class, () -> securedTradingService.cancelOrder(cancelRequest, CLIENT_ID, apiKey, EMPTY, ApiType.REST));
        verifyNoMoreInteractions(tradingService);
    }

    @Test
    void givenAuthenticationDoesntMatchClientIdWhenCancelIsCalledViaRestThenAccessShouldBeDenied() {
        OrderCancelRequest cancelRequest = new OrderCancelRequest(ORIG_ORDER_ID, ORIG_CLIENT_ID, ORIG_CL_ORDER_ID, CL_ORDER_ID, false);
        when(securityService.authenticate(any(), any())).thenReturn(new AuthResponseDto(ORIG_CLIENT_ID, ORIG_CLIENT_ID, Set.of(), Set.of()));
        assertThrows(AccessDeniedException.class, () -> securedTradingService.cancelOrder(cancelRequest, CLIENT_ID, apiKey, apiSecret, ApiType.REST));
        verifyNoMoreInteractions(tradingService);
    }

    @Test
    void givenClientRequestIsPresentWhenCancelIsCalledPortfolioPresenceIsVerified() {
        ClientRequest clientRequest = clientRequest().setPortfolioId("").build();
        OrderCancelRequest cancelRequest = new OrderCancelRequest(ORIG_ORDER_ID, ORIG_CLIENT_ID, ORIG_CL_ORDER_ID, CL_ORDER_ID, false);
        when(clientRequestRegistry.find(ORIG_ORDER_ID)).thenReturn(Optional.of(clientRequest));
        when(accessService.hasPermission(any(), eq(Permission.PORTFOLIO_TRADE), eq(PORTFOLIO_ID))).thenReturn(true);
        when(accessService.hasPermission(any(), eq(Permission.VENUE_ACCOUNT_TRADE), eq(List.of(VENUE_ACCOUNT_ID1, VENUE_ACCOUNT_ID2)))).thenReturn(true);
        assertThrows(AccessDeniedException.class, () -> securedTradingService.cancelOrder(cancelRequest, ApiType.GRAPHQL, token));
        verifyNoMoreInteractions(tradingService);
    }

    @Test
    void givenClientRequestIsPresentWhenCancelIsCalledPortfolioPermissionIsVerified() {
        ClientRequest clientRequest = clientRequest().build();
        OrderCancelRequest cancelRequest = new OrderCancelRequest(ORIG_ORDER_ID, ORIG_CLIENT_ID, ORIG_CL_ORDER_ID, CL_ORDER_ID, false);
        when(clientRequestRegistry.find(ORIG_ORDER_ID)).thenReturn(Optional.of(clientRequest));
        when(accessService.hasPermission(any(), eq(Permission.VENUE_ACCOUNT_TRADE), eq(List.of(VENUE_ACCOUNT_ID1, VENUE_ACCOUNT_ID2)))).thenReturn(true);
        assertThrows(AccessDeniedException.class, () -> securedTradingService.cancelOrder(cancelRequest, ApiType.GRAPHQL, token));
        verifyNoMoreInteractions(tradingService);
    }

    @Test
    void givenClientRequestIsPresentWhenCancelIsCalledVenueAccountsPermissionsAreVerified() {
        ClientRequest clientRequest = clientRequest().build();
        OrderCancelRequest cancelRequest = new OrderCancelRequest(ORIG_ORDER_ID, ORIG_CLIENT_ID, ORIG_CL_ORDER_ID, CL_ORDER_ID, false);
        when(clientRequestRegistry.find(ORIG_ORDER_ID)).thenReturn(Optional.of(clientRequest));
        when(accessService.hasPermission(any(), eq(Permission.PORTFOLIO_TRADE), eq(PORTFOLIO_ID))).thenReturn(true);
        assertThrows(AccessDeniedException.class, () -> securedTradingService.cancelOrder(cancelRequest, ApiType.GRAPHQL, token));
        verifyNoMoreInteractions(tradingService);
    }

    // CancelReplace

    @Test
    void givenOrderIdIsProvidedWhenCancelReplaceIsCalledViaGraphQlThenItShouldCancelReplaceTheOrder() {
        OrderCancelReplaceRequest cancelReplaceRequest = new OrderCancelReplaceRequest(ORIG_ORDER_ID, ORIG_CLIENT_ID, ORIG_CL_ORDER_ID, replacingOrder());
        securedTradingService.cancelReplaceOrder(cancelReplaceRequest, ApiType.GRAPHQL, token);
        verify(tradingService).cancelReplaceOrder(eq(cancelReplaceRequest), eq(CLIENT_ID), eq(ORIG_ORDER_ID), eq(ApiType.GRAPHQL));
        verifyNoMoreInteractions(tradingService);
    }

    @Test
    void givenOrderIdIsProvidedWhenCancelReplaceIsCalledViaRestThenItShouldCancelReplaceTheOrder() {
        OrderCancelReplaceRequest cancelReplaceRequest = new OrderCancelReplaceRequest(ORIG_ORDER_ID, ORIG_CLIENT_ID, ORIG_CL_ORDER_ID, replacingOrder());
        when(securityService.authenticate(any(), any())).thenReturn(new AuthResponseDto(CLIENT_ID, CLIENT_ID, Set.of(), Set.of()));
        when(accessService.hasPermission(any(), eq(Permission.PORTFOLIO_TRADE), eq(PORTFOLIO_ID))).thenReturn(true);
        when(accessService.hasPermission(any(), eq(Permission.VENUE_ACCOUNT_TRADE), eq(List.of(VENUE_ACCOUNT_ID1, VENUE_ACCOUNT_ID2)))).thenReturn(true);
        securedTradingService.cancelReplaceOrder(cancelReplaceRequest, CLIENT_ID, apiKey, apiSecret, ApiType.REST);
        verify(tradingService).cancelReplaceOrder(eq(cancelReplaceRequest), eq(CLIENT_ID), eq(ORIG_ORDER_ID), eq(ApiType.REST));
        verifyNoMoreInteractions(tradingService);
    }

    @Test
    void givenClientIdAndClOrderIdIsProvidedWhenCancelReplaceIsCalledThenItShouldCancelReplaceTheOrder() {
        OrderCancelReplaceRequest cancelReplaceRequest = new OrderCancelReplaceRequest(null, ORIG_CLIENT_ID, ORIG_CL_ORDER_ID, replacingOrder());
        when(clientToOrderIdRegistry.findOrderId(eq(ORIG_CLIENT_ID), eq(ORIG_CL_ORDER_ID))).thenReturn(Optional.of(ORIG_ORDER_ID));
        securedTradingService.cancelReplaceOrder(cancelReplaceRequest, ApiType.GRAPHQL, token);
        verify(tradingService).cancelReplaceOrder(eq(cancelReplaceRequest), eq(CLIENT_ID), eq(ORIG_ORDER_ID), eq(ApiType.GRAPHQL));
        verifyNoMoreInteractions(tradingService);
    }

    @Test
    void givenClOrderIdAndNoClientIdIsProvidedWhenCancelReplaceIsCalledViaGraphQLThenTokenShouldBeUsedInstead() {
        OrderCancelReplaceRequest cancelReplaceRequest = new OrderCancelReplaceRequest("\t", "\n", ORIG_CL_ORDER_ID, replacingOrder());
        when(clientToOrderIdRegistry.findOrderId(eq(CLIENT_ID), eq(ORIG_CL_ORDER_ID))).thenReturn(Optional.of(ORIG_ORDER_ID));
        securedTradingService.cancelReplaceOrder(cancelReplaceRequest, ApiType.GRAPHQL, token);
        verify(tradingService).cancelReplaceOrder(eq(cancelReplaceRequest), eq(CLIENT_ID), eq(ORIG_ORDER_ID), eq(ApiType.GRAPHQL));
        verifyNoMoreInteractions(tradingService);
    }

    @Test
    void givenClOrderIdAndNoClientIdIsProvidedWhenCancelReplaceIsCalledViaRestThenApiKeyShouldBeUsedInstead() {
        OrderCancelReplaceRequest cancelReplaceRequest = new OrderCancelReplaceRequest(null, null, ORIG_CL_ORDER_ID, replacingOrder());
        when(securityService.authenticate(eq(apiKey), eq(apiSecret))).thenReturn(new AuthResponseDto(CLIENT_ID, CLIENT_ID, Set.of(), Set.of()));
        when(clientToOrderIdRegistry.findOrderId(eq(CLIENT_ID), eq(ORIG_CL_ORDER_ID))).thenReturn(Optional.of(ORIG_ORDER_ID));
        when(accessService.hasPermission(any(), eq(Permission.PORTFOLIO_TRADE), eq(PORTFOLIO_ID))).thenReturn(true);
        when(accessService.hasPermission(any(), eq(Permission.VENUE_ACCOUNT_TRADE), eq(List.of(VENUE_ACCOUNT_ID1, VENUE_ACCOUNT_ID2)))).thenReturn(true);
        securedTradingService.cancelReplaceOrder(cancelReplaceRequest, CLIENT_ID, apiKey, apiSecret, ApiType.REST);
        verify(tradingService).cancelReplaceOrder(eq(cancelReplaceRequest), eq(CLIENT_ID), eq(ORIG_ORDER_ID), eq(ApiType.REST));
        verifyNoMoreInteractions(tradingService);
    }

    @Test
    void givenApiKeyIsMissingWhenCancelReplaceIsCalledViaRestThenAccessShouldBeDenied() {
        OrderCancelReplaceRequest cancelReplaceRequest = new OrderCancelReplaceRequest(ORIG_ORDER_ID, ORIG_CLIENT_ID, ORIG_CL_ORDER_ID, replacingOrder());
        when(securityService.authenticate(any(), any())).thenReturn(new AuthResponseDto(CLIENT_ID, CLIENT_ID, Set.of(), Set.of()));
        assertThrows(AccessDeniedException.class, () -> securedTradingService.cancelReplaceOrder(cancelReplaceRequest, CLIENT_ID, null, apiSecret, ApiType.REST));
        verifyNoMoreInteractions(tradingService);
    }

    @Test
    void givenApiSecretIsMissingWhenCancelReplaceIsCalledViaRestThenAccessShouldBeDenied() {
        OrderCancelReplaceRequest cancelReplaceRequest = new OrderCancelReplaceRequest(ORIG_ORDER_ID, ORIG_CLIENT_ID, ORIG_CL_ORDER_ID, replacingOrder());
        when(securityService.authenticate(any(), any())).thenReturn(new AuthResponseDto(CLIENT_ID, CLIENT_ID, Set.of(), Set.of()));
        assertThrows(AccessDeniedException.class, () -> securedTradingService.cancelReplaceOrder(cancelReplaceRequest, CLIENT_ID, apiKey, null, ApiType.REST));
        verifyNoMoreInteractions(tradingService);
    }

    @Test
    void givenAuthenticationDoesntMatchClientIdWhenCancelReplaceIsCalledViaRestThenAccessShouldBeDenied() {
        OrderCancelReplaceRequest cancelReplaceRequest = new OrderCancelReplaceRequest(ORIG_ORDER_ID, ORIG_CLIENT_ID, ORIG_CL_ORDER_ID, replacingOrder());
        when(securityService.authenticate(any(), any())).thenReturn(new AuthResponseDto(ORIG_CLIENT_ID, ORIG_CLIENT_ID, Set.of(), Set.of()));
        assertThrows(AccessDeniedException.class, () -> securedTradingService.cancelReplaceOrder(cancelReplaceRequest, CLIENT_ID, apiKey, apiSecret, ApiType.REST));
        verifyNoMoreInteractions(tradingService);
    }

    @Test
    void givenClientRequestIsPresentWhenCancelReplaceIsCalledPortfolioPresenceIsVerified() {
        ClientRequest clientRequest = clientRequest().setPortfolioId("").build();
        OrderCancelReplaceRequest cancelReplaceRequest = new OrderCancelReplaceRequest(ORIG_ORDER_ID, ORIG_CLIENT_ID, ORIG_CL_ORDER_ID, replacingOrder());
        when(clientRequestRegistry.find(ORIG_ORDER_ID)).thenReturn(Optional.of(clientRequest));
        when(accessService.hasPermission(any(), eq(Permission.PORTFOLIO_TRADE), eq(PORTFOLIO_ID))).thenReturn(true);
        when(accessService.hasPermission(any(), eq(Permission.VENUE_ACCOUNT_TRADE), eq(List.of(VENUE_ACCOUNT_ID1, VENUE_ACCOUNT_ID2)))).thenReturn(true);
        assertThrows(AccessDeniedException.class, () -> securedTradingService.cancelReplaceOrder(cancelReplaceRequest, ApiType.GRAPHQL, token));
        verifyNoMoreInteractions(tradingService);
    }

    @Test
    void givenClientRequestIsPresentWhenCancelReplaceIsCalledPortfolioPermissionIsVerified() {
        ClientRequest clientRequest = clientRequest().build();
        OrderCancelReplaceRequest cancelReplaceRequest = new OrderCancelReplaceRequest(ORIG_ORDER_ID, ORIG_CLIENT_ID, ORIG_CL_ORDER_ID, replacingOrder());
        when(clientRequestRegistry.find(ORIG_ORDER_ID)).thenReturn(Optional.of(clientRequest));
        when(accessService.hasPermission(any(), eq(Permission.VENUE_ACCOUNT_TRADE), eq(List.of(VENUE_ACCOUNT_ID1, VENUE_ACCOUNT_ID2)))).thenReturn(true);
        assertThrows(AccessDeniedException.class, () -> securedTradingService.cancelReplaceOrder(cancelReplaceRequest, ApiType.GRAPHQL, token));
        verifyNoMoreInteractions(tradingService);
    }

    @Test
    void givenClientRequestIsPresentWhenCancelReplaceIsCalledVenueAccountsPermissionsAreVerified() {
        ClientRequest clientRequest = clientRequest().build();
        OrderCancelReplaceRequest cancelReplaceRequest = new OrderCancelReplaceRequest(ORIG_ORDER_ID, ORIG_CLIENT_ID, ORIG_CL_ORDER_ID, replacingOrder());
        when(clientRequestRegistry.find(ORIG_ORDER_ID)).thenReturn(Optional.of(clientRequest));
        when(accessService.hasPermission(any(), eq(Permission.PORTFOLIO_TRADE), eq(PORTFOLIO_ID))).thenReturn(true);
        when(accessService.hasPermission(any(), eq(Permission.VENUE_ACCOUNT_TRADE), eq(VENUE_ACCOUNT_ID2))).thenReturn(true);
        assertThrows(AccessDeniedException.class, () -> securedTradingService.cancelReplaceOrder(cancelReplaceRequest, ApiType.GRAPHQL, token));
        verifyNoMoreInteractions(tradingService);
    }

    // OrderStatus

    @Test
    void givenOrderIdIsProvidedWhenOrderStatusIsCalledViaRestThenItShouldOrderStatus() {
        OrderStatusRequest orderStatusRequest = new OrderStatusRequest(ORIG_ORDER_ID, ORIG_CLIENT_ID, ORIG_CL_ORDER_ID);
        when(securityService.authenticate(any(), any())).thenReturn(new AuthResponseDto(CLIENT_ID, CLIENT_ID, Set.of(), Set.of()));
        securedTradingService.orderStatusRequest(orderStatusRequest, CLIENT_ID, apiKey, apiSecret, ApiType.REST);
        verify(tradingService).orderStatusRequest(eq(orderStatusRequest), eq(CLIENT_ID), eq(ORIG_ORDER_ID), eq(ApiType.REST));
        verifyNoMoreInteractions(tradingService);
    }

    @Test
    void givenClientIdAndClOrderIdIsProvidedWhenOrderStatusIsCalledThenItShouldOrderStatus() {
        OrderStatusRequest orderStatusRequest = new OrderStatusRequest(" ", ORIG_CLIENT_ID, ORIG_CL_ORDER_ID);
        when(securityService.authenticate(any(), any())).thenReturn(new AuthResponseDto(CLIENT_ID, CLIENT_ID, Set.of(), Set.of()));
        when(clientToOrderIdRegistry.findOrderId(eq(ORIG_CLIENT_ID), eq(ORIG_CL_ORDER_ID))).thenReturn(Optional.of(ORIG_ORDER_ID));
        securedTradingService.orderStatusRequest(orderStatusRequest, CLIENT_ID, apiKey, apiSecret, ApiType.REST);
        verify(tradingService).orderStatusRequest(eq(orderStatusRequest), eq(CLIENT_ID), eq(ORIG_ORDER_ID), eq(ApiType.REST));
        verifyNoMoreInteractions(tradingService);
    }

    @Test
    void givenClOrderIdAndNoClientIdIsProvidedWhenOrderStatusIsCalledViaRestThenApiKeyShouldBeUsedInstead() {
        OrderStatusRequest orderStatusRequest = new OrderStatusRequest(" ", " ", ORIG_CL_ORDER_ID);
        when(securityService.authenticate(eq(apiKey), eq(apiSecret))).thenReturn(new AuthResponseDto(CLIENT_ID, CLIENT_ID, Set.of(), Set.of()));
        when(clientToOrderIdRegistry.findOrderId(eq(CLIENT_ID), eq(ORIG_CL_ORDER_ID))).thenReturn(Optional.of(ORIG_ORDER_ID));
        securedTradingService.orderStatusRequest(orderStatusRequest, CLIENT_ID, apiKey, apiSecret, ApiType.REST);
        verify(tradingService).orderStatusRequest(eq(orderStatusRequest), eq(CLIENT_ID), eq(ORIG_ORDER_ID), eq(ApiType.REST));
        verifyNoMoreInteractions(tradingService);
    }

    @Test
    void givenApiKeyIsMissingWhenOrderStatusIsCalledViaRestThenAccessShouldBeDenied() {
        OrderStatusRequest orderStatusRequest = new OrderStatusRequest(ORIG_ORDER_ID, ORIG_CLIENT_ID, ORIG_CL_ORDER_ID);
        when(securityService.authenticate(any(), any())).thenReturn(new AuthResponseDto(CLIENT_ID, CLIENT_ID, Set.of(), Set.of()));
        assertThrows(AccessDeniedException.class, () -> securedTradingService.orderStatusRequest(orderStatusRequest, CLIENT_ID, null, apiSecret, ApiType.REST));
        verifyNoMoreInteractions(tradingService);
    }

    @Test
    void givenApiSecretIsMissingWhenOrderStatusIsCalledViaRestThenAccessShouldBeDenied() {
        OrderStatusRequest orderStatusRequest = new OrderStatusRequest(ORIG_ORDER_ID, ORIG_CLIENT_ID, ORIG_CL_ORDER_ID);
        when(securityService.authenticate(any(), any())).thenReturn(new AuthResponseDto(CLIENT_ID, CLIENT_ID, Set.of(), Set.of()));
        assertThrows(AccessDeniedException.class, () -> securedTradingService.orderStatusRequest(orderStatusRequest, CLIENT_ID, apiKey, null, ApiType.REST));
        verifyNoMoreInteractions(tradingService);
    }

    @Test
    void givenAuthenticationDoesntMatchClientIdWhenOrderStatusIsCalledViaRestThenAccessShouldBeDenied() {
        OrderStatusRequest orderStatusRequest = new OrderStatusRequest(ORIG_ORDER_ID, ORIG_CLIENT_ID, ORIG_CL_ORDER_ID);
        when(securityService.authenticate(any(), any())).thenReturn(new AuthResponseDto(ORIG_CLIENT_ID, ORIG_CLIENT_ID, Set.of(), Set.of()));
        assertThrows(AccessDeniedException.class, () -> securedTradingService.orderStatusRequest(orderStatusRequest, CLIENT_ID, apiKey, apiSecret, ApiType.REST));
        verifyNoMoreInteractions(tradingService);
    }

    @Test
    void givenClientRequestIsPresentWhenOrderStatusIsCalledPortfolioPresenceIsVerified() {
        ClientRequest clientRequest = clientRequest().setPortfolioId("").build();
        OrderStatusRequest orderStatusRequest = new OrderStatusRequest(ORIG_ORDER_ID, ORIG_CLIENT_ID, ORIG_CL_ORDER_ID);
        when(securityService.authenticate(any(), any())).thenReturn(new AuthResponseDto(ORIG_CLIENT_ID, ORIG_CLIENT_ID, Set.of(), Set.of()));
        when(clientRequestRegistry.find(ORIG_ORDER_ID)).thenReturn(Optional.of(clientRequest));
        when(accessService.hasPermission(any(), eq(Permission.PORTFOLIO_TRADE), eq(PORTFOLIO_ID))).thenReturn(true);
        when(accessService.hasPermission(any(), eq(Permission.VENUE_ACCOUNT_TRADE), eq(List.of(VENUE_ACCOUNT_ID1, VENUE_ACCOUNT_ID2)))).thenReturn(true);
        assertThrows(AccessDeniedException.class, () -> securedTradingService.orderStatusRequest(orderStatusRequest, CLIENT_ID, apiKey, apiSecret, ApiType.REST));
        verifyNoMoreInteractions(tradingService);
    }

    @Test
    void givenClientRequestIsPresentWhenOrderStatusIsCalledPortfolioPermissionIsVerified() {
        ClientRequest clientRequest = clientRequest().build();
        OrderStatusRequest orderStatusRequest = new OrderStatusRequest(ORIG_ORDER_ID, ORIG_CLIENT_ID, ORIG_CL_ORDER_ID);
        when(securityService.authenticate(any(), any())).thenReturn(new AuthResponseDto(ORIG_CLIENT_ID, ORIG_CLIENT_ID, Set.of(), Set.of()));
        when(clientRequestRegistry.find(ORIG_ORDER_ID)).thenReturn(Optional.of(clientRequest));
        when(accessService.hasPermission(any(), eq(Permission.VENUE_ACCOUNT_TRADE), eq(List.of(VENUE_ACCOUNT_ID1, VENUE_ACCOUNT_ID2)))).thenReturn(true);
        assertThrows(AccessDeniedException.class, () -> securedTradingService.orderStatusRequest(orderStatusRequest, CLIENT_ID, apiKey, apiSecret, ApiType.REST));
        verifyNoMoreInteractions(tradingService);
    }

    @Test
    void givenClientRequestIsPresentWhenOrderStatusIsCalledVenueAccountsPermissionsAreVerified() {
        ClientRequest clientRequest = clientRequest().build();
        OrderStatusRequest orderStatusRequest = new OrderStatusRequest(ORIG_ORDER_ID, ORIG_CLIENT_ID, ORIG_CL_ORDER_ID);
        when(securityService.authenticate(any(), any())).thenReturn(new AuthResponseDto(ORIG_CLIENT_ID, ORIG_CLIENT_ID, Set.of(), Set.of()));
        when(clientRequestRegistry.find(ORIG_ORDER_ID)).thenReturn(Optional.of(clientRequest));
        when(accessService.hasPermission(any(), eq(Permission.PORTFOLIO_TRADE), eq(PORTFOLIO_ID))).thenReturn(true);
        when(accessService.hasPermission(any(), eq(Permission.VENUE_ACCOUNT_TRADE), eq(VENUE_ACCOUNT_ID2))).thenReturn(true);
        assertThrows(AccessDeniedException.class, () -> securedTradingService.orderStatusRequest(orderStatusRequest, CLIENT_ID, apiKey, apiSecret, ApiType.REST));
        verifyNoMoreInteractions(tradingService);
    }

    private static NewOrderSingleRequest getNewOrderSingleRequest() {
        return NewOrderSingleRequest.newBuilder()
            .setClOrderId(ORIG_CL_ORDER_ID)
            .setOrderType(SharedModel.OrderType.MARKET)
            .setQuantity(BigDecimal.ONE)
            .setInstrumentId("BTCUSD")
            .setTif(SharedModel.TIF.GTC)
            .setVenueAccounts(List.of("Bitmex_11"))
            .setSide(SharedModel.Side.BUY)
            .setPortfolioId(PORTFOLIO_ID)
            .build();
    }

    private static NewOrderSingleRequest replacingOrder() {
        return NewOrderSingleRequest.newBuilder()
            .setClOrderId(CL_ORDER_ID)
            .setOrderType(SharedModel.OrderType.MARKET)
            .setQuantity(BigDecimal.ONE)
            .setInstrumentId("BTCUSD")
            .setTif(SharedModel.TIF.GTC)
            .setVenueAccounts(List.of(VENUE_ACCOUNT_ID1, VENUE_ACCOUNT_ID2))
            .setSide(SharedModel.Side.BUY)
            .setPortfolioId(PORTFOLIO_ID)
            .build();
    }

    private static ClientRequest.Builder clientRequest() {
        return ClientRequest.newBuilder()
            .setOrderId(ORIG_ORDER_ID)
            .setClientId(ORIG_CLIENT_ID)
            .setPortfolioId(PORTFOLIO_ID)
            .addVenueAccounts(VENUE_ACCOUNT_ID1)
            .addVenueAccounts(VENUE_ACCOUNT_ID2);
    }
}
