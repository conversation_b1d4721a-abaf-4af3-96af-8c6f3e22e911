package io.wyden.apiserver.rest.security;

import com.google.common.collect.Sets;
import io.wyden.apiserver.rest.security.accessgateway.AddOrRemoveGroupPermissionsRequestDto;
import io.wyden.apiserver.rest.security.accessgateway.AddOrRemoveUserPermissionsRequestDto;
import io.wyden.apiserver.rest.security.model.AuthorityDto;
import io.wyden.apiserver.rest.security.model.Resource;
import io.wyden.apiserver.rest.security.model.Scope;
import io.wyden.apiserver.rest.security.model.WydenAuthenticationToken;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.Set;

import static io.wyden.accessgateway.client.permission.Permission.Resources.BROKER_CONFIG;
import static io.wyden.accessgateway.client.permission.Permission.Resources.PORTFOLIO;
import static io.wyden.accessgateway.client.permission.Permission.Resources.VENUE_ACCOUNT;
import static io.wyden.accessgateway.client.permission.Permission.Resources.WALLET;
import static io.wyden.accessgateway.client.permission.Permission.Scopes.MANAGE;
import static io.wyden.accessgateway.client.permission.Permission.Scopes.TRADE;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;


@SuppressWarnings("SameParameterValue")
class PermissionValidatorTest {

    private static final String USER_NAME = "user";
    private static final String RESOURCE_ID = "resource";

    WydenAuthenticationToken token = new WydenAuthenticationToken(USER_NAME);
    AccessService accessService = Mockito.mock(AccessService.class);
    PermissionValidator sut = new PermissionValidator(accessService);

    @Test
    void userWithoutPrivileges_cantModifyUserResourcePermissions() {
        AddOrRemoveUserPermissionsRequestDto request = userRequest(USER_NAME,
            auth(Resource.PORTFOLIO, Scope.READ, RESOURCE_ID));
        Assertions.assertFalse(sut.canChangePermissions(request, token, null));
    }

    @Test
    void userWithoutPrivileges_cantModifyGroupResourcePermissions() {
        AddOrRemoveGroupPermissionsRequestDto request = groupRequest(USER_NAME,
            auth(Resource.VENUE_ACCOUNT, Scope.MANAGE, RESOURCE_ID));
        Assertions.assertFalse(sut.canChangePermissions(request, token, null));
    }

    @Test
    void userWithoutPrivileges_cantModifyUserStaticPermissions() {
        AddOrRemoveUserPermissionsRequestDto request = userRequest(USER_NAME,
            auth(Resource.BROKER_CONFIG, Scope.TRADE));
        Assertions.assertFalse(sut.canChangePermissions(request, token, null));
    }

    @Test
    void userWithoutPrivileges_cantModifyGroupStaticPermissions() {
        AddOrRemoveGroupPermissionsRequestDto request = groupRequest(USER_NAME,
            auth(Resource.WALLET, Scope.CREATE));
        Assertions.assertFalse(sut.canChangePermissions(request, token, null));
    }

    @Test
    void userWithoutOnePrivilege_cantModifyUserPermissions() {
        grant(VENUE_ACCOUNT, MANAGE, RESOURCE_ID);
        AddOrRemoveUserPermissionsRequestDto request = userRequest(USER_NAME,
            auth(Resource.BROKER_CONFIG, Scope.TRADE),
            auth(Resource.VENUE_ACCOUNT, Scope.MANAGE, RESOURCE_ID)
        );
        Assertions.assertFalse(sut.canChangePermissions(request, token, null));
    }

    @Test
    void userWithManagePrivilege_canModifyUserResourcePermissions() {
        grant(PORTFOLIO, MANAGE, RESOURCE_ID);
        AddOrRemoveUserPermissionsRequestDto request = userRequest(USER_NAME,
            auth(Resource.PORTFOLIO, Scope.READ, RESOURCE_ID));
        Assertions.assertTrue(sut.canChangePermissions(request, token, null));
    }

    @Test
    void userWithManagePrivilege_canModifyGroupResourcePermissions() {
        grant(VENUE_ACCOUNT, MANAGE, RESOURCE_ID);
        AddOrRemoveGroupPermissionsRequestDto request = groupRequest(USER_NAME,
            auth(Resource.VENUE_ACCOUNT, Scope.MANAGE, RESOURCE_ID));
        Assertions.assertTrue(sut.canChangePermissions(request, token, null));
    }

    @Test
    void userWithManagePrivilege_canModifyUserStaticPermissions() {
        grant(BROKER_CONFIG, MANAGE);
        AddOrRemoveUserPermissionsRequestDto request = userRequest(USER_NAME,
            auth(Resource.BROKER_CONFIG, Scope.TRADE));
        Assertions.assertTrue(sut.canChangePermissions(request, token, null));
    }

    @Test
    void userWithManagePrivilege_canModifyGroupStaticPermissions() {
        grant(WALLET, MANAGE);
        AddOrRemoveGroupPermissionsRequestDto request = groupRequest(USER_NAME,
            auth(Resource.WALLET, Scope.CREATE));
        Assertions.assertTrue(sut.canChangePermissions(request, token, null));
    }

    @Test
    void userWithAllPrivileges_canModifyUserPermissions() {
        grant(BROKER_CONFIG, TRADE);
        grant(VENUE_ACCOUNT, MANAGE, RESOURCE_ID);
        AddOrRemoveUserPermissionsRequestDto request = userRequest(USER_NAME,
            auth(Resource.BROKER_CONFIG, Scope.TRADE),
            auth(Resource.VENUE_ACCOUNT, Scope.MANAGE, RESOURCE_ID)
        );
        Assertions.assertFalse(sut.canChangePermissions(request, token, null));
    }

    private AddOrRemoveUserPermissionsRequestDto userRequest(String name, AuthorityDto... authorities) {
        Set<AuthorityDto> authoritiesSet = Sets.newHashSet(authorities);
        return new AddOrRemoveUserPermissionsRequestDto(name, authoritiesSet);
    }

    private AddOrRemoveGroupPermissionsRequestDto groupRequest(String name, AuthorityDto... authorities) {
        Set<AuthorityDto> authoritiesSet = Sets.newHashSet(authorities);
        return new AddOrRemoveGroupPermissionsRequestDto(name, authoritiesSet);
    }

    private AuthorityDto auth(Resource resource, Scope scope, String resourceId) {
        return new AuthorityDto(resource, resourceId, scope);
    }

    private AuthorityDto auth(Resource resource, Scope scope) {
        return new AuthorityDto(resource, scope);
    }

    private void grant(String resource, String scope, String resourceId) {
        Mockito.when(accessService.hasPermission(any(), eq(resource), eq(scope), eq(resourceId))).thenReturn(true);
    }

    private void grant(String resource, String scope) {
        Mockito.when(accessService.hasPermission(any(), eq(resource), eq(scope))).thenReturn(true);
    }
}
