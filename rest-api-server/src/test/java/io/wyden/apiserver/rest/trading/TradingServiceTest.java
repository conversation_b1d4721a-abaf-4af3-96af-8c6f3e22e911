package io.wyden.apiserver.rest.trading;

import io.wyden.accessgateway.client.license.LicenseService;
import io.wyden.apiserver.rest.apiui.SharedModel;
import io.wyden.apiserver.rest.infrastructure.rabbit.ClientRequestEmitter;
import io.wyden.apiserver.rest.infrastructure.rabbit.ClientResponseHandler;
import io.wyden.apiserver.rest.referencedata.instruments.model.dto.AssetClassDto;
import io.wyden.apiserver.rest.referencedata.portfolio.service.PortfolioValidator;
import io.wyden.apiserver.rest.security.AccessGatewayMockClient;
import io.wyden.apiserver.rest.security.AccessService;
import io.wyden.apiserver.rest.security.TradingPermissionValidator;
import io.wyden.apiserver.rest.session.SecuredSinkManager;
import io.wyden.apiserver.rest.trading.model.CancelRejectResponse;
import io.wyden.apiserver.rest.trading.model.ExecutionReportResponse;
import io.wyden.apiserver.rest.trading.model.NewOrderSingleRequest;
import io.wyden.apiserver.rest.trading.model.OrderCancelReplaceRequest;
import io.wyden.apiserver.rest.trading.model.OrderCancelRequest;
import io.wyden.apiserver.rest.trading.model.OrderStatusRequest;
import io.wyden.apiserver.rest.walletaccount.WalletAccountValidator;
import io.wyden.cloud.utils.test.TracingMock;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.published.client.ClientOrderStatus;
import io.wyden.published.client.ClientRequest;
import io.wyden.published.client.ClientResponse;
import io.wyden.published.client.ClientResponseType;
import io.wyden.published.common.ApiType;

import com.rabbitmq.client.AMQP;
import io.micrometer.core.instrument.MeterRegistry;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.mockito.ArgumentCaptor;
import reactor.core.Disposable;
import reactor.core.publisher.Flux;
import reactor.test.StepVerifier;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;

import static io.wyden.apiserver.rest.security.AccessService.User.user;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.params.provider.EnumSource.Mode.EXCLUDE;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class TradingServiceTest {

    private static final int TTL_SEC = 1;
    private static final String CLIENT_ID = "GEORGE";
    private static final String CLIENT_ORDER_ID = "GEORGES_ORDER_1";
    private static final String CLIENT_ID2 = "JOE";
    private static final String CLIENT_ORDER_ID2 = "JOE'S_ORDER_1";
    private static final String VENUE_ACCOUNT = "VENUE_1";
    private static final String VENUE_ACCOUNT2 = "VENUE_2";
    private static final String PORTFOLIO_ID = "PORTFOLIO_1";
    private static final String PORTFOLIO_ID2 = "PORTFOLIO_2";

    private TradingService tradingService;
    private ClientRequestEmitter publisher;
    private ClientToOrderIdRegistry registry;
    private ClientRequestRegistry clientRequestRegistry;
    private ClientResponseHandler responseConsumer;
    private Telemetry telemetryMock;

    private final LicenseService licenseService = mock(LicenseService.class);
    private final AccessService accessService = new AccessService(new AccessGatewayMockClient(new HashSet<>()));
    private final TradingPermissionValidator tradingPermissionValidator = mock(TradingPermissionValidator.class);
    private final SecuredSinkManager<ExecutionReportResponse> executionReportResponseSinkManager = new SecuredSinkManager<>(accessService, tradingPermissionValidator);
    private final SecuredSinkManager<CancelRejectResponse> cancelRejectResponseSinkManager = new SecuredSinkManager<>(accessService, tradingPermissionValidator);

    @BeforeEach
    void setUp() {
        telemetryMock = new Telemetry(TracingMock.createMock(), mock(MeterRegistry.class));
        publisher = mock(ClientRequestEmitter.class);
        registry = mock(ClientToOrderIdRegistry.class);
        clientRequestRegistry = mock(ClientRequestRegistry.class);
        when(registry.register(anyString(), anyString(), anyString())).thenAnswer(i -> i.getArgument(2));
        when(tradingPermissionValidator.checkSubscribePermissions(any(), any(), any(AccessService.User.class), any())).thenReturn(true);
        responseConsumer = mock(ClientResponseHandler.class);
        tradingService = new TradingService(publisher, registry, clientRequestRegistry, licenseService, mock(PortfolioValidator.class), mock(WalletAccountValidator.class), executionReportResponseSinkManager,
            cancelRejectResponseSinkManager, telemetryMock);
    }

    @Test
    void incomingOrderRequestShouldReceiveOrderId() {
        // when
        tradingService.sendOrder(newOrderSingleBuilder()
            .setAssetClass(AssetClassDto.FOREX)
            .setClOrderId(CLIENT_ORDER_ID)
            .build(), CLIENT_ID, ApiType.REST);

        // then
        ArgumentCaptor<String> generatedId = ArgumentCaptor.forClass(String.class);
        verify(registry).register(eq(CLIENT_ID), eq(CLIENT_ORDER_ID), generatedId.capture());

        ArgumentCaptor<ClientRequest> publishedReq = ArgumentCaptor.forClass(ClientRequest.class);
        verify(publisher).emit(publishedReq.capture());
        assertThat(publishedReq.getValue().getOrderId()).isEqualTo(generatedId.getValue());
        verify(clientRequestRegistry).register(eq(generatedId.getValue()), eq(publishedReq.getValue()));
    }

    @Test
    void cancelRequestShouldBePropagatedWithOrigOrderId() {
        // given
        String orderId = "15";
        when(registry.findOrderId(CLIENT_ID, CLIENT_ORDER_ID)).thenReturn(Optional.of(orderId));

        // when
        tradingService.cancelOrder(new OrderCancelRequest(
            null, null, CLIENT_ORDER_ID, "please-cancel-abcd1", false
        ), CLIENT_ID, orderId, ApiType.REST);

        // then
        ArgumentCaptor<ClientRequest> publishedReq = ArgumentCaptor.forClass(ClientRequest.class);
        verify(publisher).emit(publishedReq.capture());
        assertThat(publishedReq.getValue().getOrigOrderId()).isEqualTo(orderId);
    }

    @Test
    void cancelReplaceRequestShouldBePropagatedWithOrderIdOfThePreviousOrder() {
        // given
        String previousOrderId = "previousOrderId";

        // when
        OrderCancelReplaceRequest request = new OrderCancelReplaceRequest(
            previousOrderId,
            CLIENT_ID,
            "id-of-my-prev-order-than-has-to-be-cancelled-now",
            newOrderSingleBuilder().setClOrderId(CLIENT_ORDER_ID).build());

        tradingService.cancelReplaceOrder(request, CLIENT_ID, previousOrderId, ApiType.GRAPHQL);

        // then
        ArgumentCaptor<String> generatedId = ArgumentCaptor.forClass(String.class);
        verify(registry).register(eq(CLIENT_ID), eq(CLIENT_ORDER_ID), generatedId.capture());

        ArgumentCaptor<ClientRequest> publishedReq = ArgumentCaptor.forClass(ClientRequest.class);
        verify(publisher).emit(publishedReq.capture());
        assertThat(publishedReq.getValue().getOrderId()).isEqualTo(generatedId.getValue());
        assertThat(publishedReq.getValue().getOrigOrderId()).isEqualTo(previousOrderId);
        verify(clientRequestRegistry).register(eq(generatedId.getValue()), eq(publishedReq.getValue()));
    }

    @Test
    void orderStatusRequestShouldBePropagatedWithOrderId() {
        // given
        String orderId = "15";
        when(registry.findOrderId(CLIENT_ID, CLIENT_ORDER_ID)).thenReturn(Optional.of(orderId));
        // when
        tradingService.orderStatusRequest(new OrderStatusRequest(null, null, CLIENT_ORDER_ID), CLIENT_ID, orderId, ApiType.GRAPHQL);
        // then
        ArgumentCaptor<ClientRequest> publishedReq = ArgumentCaptor.forClass(ClientRequest.class);
        verify(publisher).emitStatusRequest(publishedReq.capture());
        assertThat(publishedReq.getValue().getOrderId()).isEqualTo(orderId);
    }

    @ParameterizedTest(name = "closedOrderShouldBeMarkedForLaterEviction({0})")
    @EnumSource(names = {"FILLED", "CANCELED", "REPLACED", "REJECTED", "EXPIRED"})
    void closedOrderShouldBeMarkedForLaterEviction(ClientOrderStatus clientOrderStatus) {
        // given
        responseConsumer = new ClientResponseHandler(tradingService, registry, clientRequestRegistry, telemetryMock, TTL_SEC);

        ClientResponse msg = ClientResponse.newBuilder()
            .setResponseType(ClientResponseType.EXECUTION_REPORT)
            .setClientId(CLIENT_ID)
            .setClOrderId(CLIENT_ORDER_ID)
            .setOrderStatus(clientOrderStatus)
            .build();

        // when
        responseConsumer.consume(msg, mock(AMQP.BasicProperties.class));

        // then
        verify(registry).markForEviction(CLIENT_ID, CLIENT_ORDER_ID, TTL_SEC);
    }

    @ParameterizedTest(name = "openOrderShouldNotBeMarkedForLaterEviction({0})")
    @EnumSource(names = {
        "FILLED", "CANCELED", "REPLACED", "REJECTED", "EXPIRED",
        "UNRECOGNIZED"}, // exclude this one too, since proto-generated code won't allow to set it explicitly
                mode = EXCLUDE)
    void openOrderShouldNotBeMarkedForLaterEviction(ClientOrderStatus clientOrderStatus) {
        // given
        responseConsumer = new ClientResponseHandler(tradingService, registry, clientRequestRegistry, telemetryMock, TTL_SEC);

        ClientResponse msg = ClientResponse.newBuilder()
            .setResponseType(ClientResponseType.EXECUTION_REPORT)
            .setClientId(CLIENT_ID)
            .setClOrderId(CLIENT_ORDER_ID)
            .setOrderStatus(clientOrderStatus)
            .build();

        // when
        responseConsumer.consume(msg, mock(AMQP.BasicProperties.class));

        // then
        verify(registry, never()).markForEviction(CLIENT_ID, CLIENT_ORDER_ID, TTL_SEC);
    }

    @Test
    void shouldSubscribeExecutionReport_singleSubscriberOnOneClientId() {
        StepVerifier.create(tradingService.subscribeRealtimeExecutionReport(user(CLIENT_ID)))
            .expectSubscription()
            .expectNextMatches(executionReportResponse -> executionReportResponse.getReason().contains("Handshake"))
            .then(() -> executionReportResponseSinkManager.accept(CLIENT_ID, PORTFOLIO_ID, VENUE_ACCOUNT, testExecutionReportResponse(CLIENT_ID, CLIENT_ORDER_ID)))
            .expectNext(testExecutionReportResponse(CLIENT_ID, CLIENT_ORDER_ID))
            .thenCancel()
            .verify();

        assertThat(executionReportResponseSinkManager.count()).isEqualTo(0);
    }

    @Test
    void shouldSubscribeCancelReject_singleSubscriberOnOneClientId() {
        StepVerifier.create(tradingService.subscribeCancelReject(user(CLIENT_ID)))
            .expectSubscription()
            .expectNextMatches(cancelRejectResponse -> cancelRejectResponse.getCancelRejectReason().contains("Handshake"))
            .then(() -> cancelRejectResponseSinkManager.accept(CLIENT_ID, PORTFOLIO_ID, VENUE_ACCOUNT, testCancelRejectResponse(CLIENT_ID, CLIENT_ORDER_ID)))
            .expectNext(testCancelRejectResponse(CLIENT_ID, CLIENT_ORDER_ID))
            .thenCancel()
            .verify();

        assertThat(cancelRejectResponseSinkManager.count()).isEqualTo(0);
    }

    @Test
    void shouldSubscribeExecutionReport_multipleSubscribersOnOneClientId() {
        // given
        Disposable subscribe1 = tradingService.subscribeRealtimeExecutionReport(user(CLIENT_ID)).subscribe();
        Disposable subscribe2 = tradingService.subscribeRealtimeExecutionReport(user(CLIENT_ID)).subscribe();
        assertThat(executionReportResponseSinkManager.count()).isEqualTo(1);

        // when
        subscribe1.dispose();

        // then
        assertThat(executionReportResponseSinkManager.count()).isEqualTo(1);

        // when
        subscribe2.dispose();

        // then
        assertThat(executionReportResponseSinkManager.count()).isEqualTo(0);
    }

    @Test
    void shouldSubscribeCancelReject_multipleSubscribersOnOneClientId() {
        // given
        Disposable subscribe1 = tradingService.subscribeCancelReject(user(CLIENT_ID)).subscribe();
        Disposable subscribe2 = tradingService.subscribeCancelReject(user(CLIENT_ID)).subscribe();
        assertThat(cancelRejectResponseSinkManager.count()).isEqualTo(1);

        // when
        subscribe1.dispose();

        // then
        assertThat(cancelRejectResponseSinkManager.count()).isEqualTo(1);

        // when
        subscribe2.dispose();

        // then
        assertThat(cancelRejectResponseSinkManager.count()).isEqualTo(0);
    }

    @Test
    void shouldSubscribeExecutionReport_multipleSubscribersOnOneClientIdStreaming() {
        Flux<ExecutionReportResponse> flux1 = tradingService.subscribeRealtimeExecutionReport(user(CLIENT_ID));
        Flux<ExecutionReportResponse> flux2 = tradingService.subscribeRealtimeExecutionReport(user(CLIENT_ID));

        StepVerifier.create(Flux.merge(flux1, flux2))
            .expectSubscription()
            .expectNextMatches(executionReportResponse -> executionReportResponse.getReason().contains("Handshake"))
            .expectNextMatches(executionReportResponse -> executionReportResponse.getReason().contains("Handshake"))
            .then(() -> executionReportResponseSinkManager.accept(CLIENT_ID, PORTFOLIO_ID, VENUE_ACCOUNT, testExecutionReportResponse(CLIENT_ID, CLIENT_ORDER_ID)))
            .expectNext(testExecutionReportResponse(CLIENT_ID, CLIENT_ORDER_ID))
            .expectNext(testExecutionReportResponse(CLIENT_ID, CLIENT_ORDER_ID))
            .thenCancel()
            .verify();

        assertThat(executionReportResponseSinkManager.count()).isEqualTo(0);
    }

    @Test
    void shouldSubscribeCancelReject_multipleSubscribersOnOneClientIdStreaming() {
        Flux<CancelRejectResponse> flux1 = tradingService.subscribeCancelReject(user(CLIENT_ID));
        Flux<CancelRejectResponse> flux2 = tradingService.subscribeCancelReject(user(CLIENT_ID));

        StepVerifier.create(Flux.merge(flux1, flux2))
            .expectSubscription()
            .expectNextMatches(cancelRejectResponse -> cancelRejectResponse.getCancelRejectReason().contains("Handshake"))
            .expectNextMatches(cancelRejectResponse -> cancelRejectResponse.getCancelRejectReason().contains("Handshake"))
            .then(() -> cancelRejectResponseSinkManager.accept(CLIENT_ID, PORTFOLIO_ID, VENUE_ACCOUNT, testCancelRejectResponse(CLIENT_ID, CLIENT_ORDER_ID)))
            .expectNext(testCancelRejectResponse(CLIENT_ID, CLIENT_ORDER_ID))
            .expectNext(testCancelRejectResponse(CLIENT_ID, CLIENT_ORDER_ID))
            .thenCancel()
            .verify();

        assertThat(cancelRejectResponseSinkManager.count()).isEqualTo(0);
    }

    @Test
    void shouldSubscribeExecutionReport_multipleSubscribersOnSeparateClientIds() {
        Flux<ExecutionReportResponse> flux1 = tradingService.subscribeRealtimeExecutionReport(user(CLIENT_ID));
        Flux<ExecutionReportResponse> flux2 = tradingService.subscribeRealtimeExecutionReport(user(CLIENT_ID2));

        when(tradingPermissionValidator.checkSubscribePermissions(any(), any(), any(AccessService.User.class), any())).thenReturn(false);

        StepVerifier.create(Flux.merge(flux1, flux2))
            .expectSubscription()
            .then(() -> assertThat(executionReportResponseSinkManager.count()).isEqualTo(2))
            .expectNextMatches(executionReportResponse -> executionReportResponse.getReason().contains("Handshake"))
            .expectNextMatches(executionReportResponse -> executionReportResponse.getReason().contains("Handshake"))
            .then(() -> executionReportResponseSinkManager.accept(CLIENT_ID, PORTFOLIO_ID, VENUE_ACCOUNT, testExecutionReportResponse(CLIENT_ID, CLIENT_ORDER_ID)))
            .expectNext(testExecutionReportResponse(CLIENT_ID, CLIENT_ORDER_ID))
            .then(() -> executionReportResponseSinkManager.accept(CLIENT_ID2, PORTFOLIO_ID, VENUE_ACCOUNT, testExecutionReportResponse(CLIENT_ID2, CLIENT_ORDER_ID2)))
            .expectNext(testExecutionReportResponse(CLIENT_ID2, CLIENT_ORDER_ID2))
            .thenCancel()
            .verify();

        assertThat(executionReportResponseSinkManager.count()).isEqualTo(0);
    }

    @Test
    void shouldSubscribeCancelReject_multipleSubscribersOnSeparateClientIds() {
        Flux<CancelRejectResponse> flux1 = tradingService.subscribeCancelReject(user(CLIENT_ID));
        Flux<CancelRejectResponse> flux2 = tradingService.subscribeCancelReject(user(CLIENT_ID2));

        when(tradingPermissionValidator.checkSubscribePermissions(any(), any(), any(AccessService.User.class), any())).thenReturn(false);

        StepVerifier.create(Flux.merge(flux1, flux2))
            .expectSubscription()
            .then(() -> assertThat(cancelRejectResponseSinkManager.count()).isEqualTo(2))
            .expectNextMatches(cancelRejectResponse -> cancelRejectResponse.getCancelRejectReason().contains("Handshake"))
            .expectNextMatches(cancelRejectResponse -> cancelRejectResponse.getCancelRejectReason().contains("Handshake"))
            .then(() -> cancelRejectResponseSinkManager.accept(CLIENT_ID, PORTFOLIO_ID, VENUE_ACCOUNT, testCancelRejectResponse(CLIENT_ID, CLIENT_ORDER_ID)))
            .expectNext(testCancelRejectResponse(CLIENT_ID, CLIENT_ORDER_ID))
            .then(() -> cancelRejectResponseSinkManager.accept(CLIENT_ID2, PORTFOLIO_ID2, VENUE_ACCOUNT2, testCancelRejectResponse(CLIENT_ID2, CLIENT_ORDER_ID2)))
            .expectNext(testCancelRejectResponse(CLIENT_ID2, CLIENT_ORDER_ID2))
            .thenCancel()
            .verify();

        assertThat(cancelRejectResponseSinkManager.count()).isEqualTo(0);
    }

    @NotNull
    private static NewOrderSingleRequest.Builder newOrderSingleBuilder() {
        return NewOrderSingleRequest.newBuilder()
            .setOrderType(SharedModel.OrderType.MARKET)
            .setQuantity(BigDecimal.ONE)
            .setTif(SharedModel.TIF.GTC)
            .setSide(SharedModel.Side.BUY)
            .setVenueAccounts(List.of("wherever"))
            .setInstrumentId("BTCUSD");
    }

    @NotNull
    private static ExecutionReportResponse testExecutionReportResponse(String clientId, String clientOrderId) {
        ExecutionReportResponse executionReportResponse = new ExecutionReportResponse();
        executionReportResponse.setClientId(clientId);
        executionReportResponse.setClOrderId(clientOrderId);
        executionReportResponse.setTargetVenueAccount(VENUE_ACCOUNT);
        executionReportResponse.setPortfolioId(PORTFOLIO_ID);
        return executionReportResponse;
    }

    @NotNull
    private static CancelRejectResponse testCancelRejectResponse(String clientId, String clientOrderId) {
        CancelRejectResponse cancelRejectResponse = new CancelRejectResponse();
        cancelRejectResponse.setClientId(clientId);
        cancelRejectResponse.setClOrderId(clientOrderId);
        cancelRejectResponse.setTargetVenueAccount(VENUE_ACCOUNT);
        cancelRejectResponse.setPortfolioId(PORTFOLIO_ID);
        return cancelRejectResponse;
    }
}
