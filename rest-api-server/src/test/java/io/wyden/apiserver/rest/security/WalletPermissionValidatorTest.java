package io.wyden.apiserver.rest.security;

import io.wyden.accessgateway.client.permission.dto.PermissionDto;
import io.wyden.apiserver.rest.security.model.WydenAuthenticationToken;
import io.wyden.apiserver.rest.walletaccount.model.WalletAccountCreateRequest;
import io.wyden.apiserver.rest.walletaccount.model.WalletAccountSearch;
import io.wyden.apiserver.rest.walletaccount.model.WalletTypeDto;

import org.junit.jupiter.api.Test;

import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;

class WalletPermissionValidatorTest {

    AccessGatewayMockClient accessGatewayClient = new AccessGatewayMockClient(Set.of());
    AccessService accessService = new AccessService(accessGatewayClient);
    WalletPermissionValidator sut = new WalletPermissionValidator(accessService);

    @Test
    void emptyRequestWithoutPermissions_authorizedRequestShouldBeNull() {
        accessGatewayClient.revokePermissions();

        WalletAccountSearch walletAccountSearch = WalletAccountSearch.empty();
        WalletAccountSearch authorizedRequest = sut.authorizeRequest(walletAccountSearch, new WydenAuthenticationToken("TEST_USER"));

        assertThat(authorizedRequest).isNull();
    }

    @Test
    void emptyRequestWithStaticWalletReadPermissions_shouldReturnEmptyRequest() {
        accessGatewayClient.grantPermissions(new PermissionDto("wallet", "read", null));

        WalletAccountSearch walletAccountSearch = WalletAccountSearch.empty();
        WalletAccountSearch authorizedRequest = sut.authorizeRequest(walletAccountSearch, new WydenAuthenticationToken("TEST_USER"));

        assertThat(authorizedRequest).isNotNull();
        assertThat(authorizedRequest.walletTypeDto()).isNull();
    }

    @Test
    void emptyRequestWithStaticWalletNostroAndVostroReadPermissions_shouldReturnEmptyRequest() {
        accessGatewayClient.grantPermissions(new PermissionDto("wallet.nostro", "read", null));
        accessGatewayClient.grantPermissions(new PermissionDto("wallet.vostro", "read", null));

        WalletAccountSearch walletAccountSearch = WalletAccountSearch.empty();
        WalletAccountSearch authorizedRequest = sut.authorizeRequest(walletAccountSearch, new WydenAuthenticationToken("TEST_USER"));

        assertThat(authorizedRequest).isNotNull();
        assertThat(authorizedRequest.walletTypeDto()).isNull();
    }

    @Test
    void emptyRequestWithStaticWalletVostroReadPermissions_shouldReturnRequestWithVostroType() {
        accessGatewayClient.grantPermissions(new PermissionDto("wallet.vostro", "read", null));

        WalletAccountSearch walletAccountSearch = WalletAccountSearch.empty();
        WalletAccountSearch authorizedRequest = sut.authorizeRequest(walletAccountSearch, new WydenAuthenticationToken("TEST_USER"));

        assertThat(authorizedRequest).isNotNull();
        assertThat(authorizedRequest.walletTypeDto().name()).isEqualToIgnoringCase("vostro");
    }

    @Test
    void emptyRequestWithStaticWalletNostroReadPermissions_shouldReturnRequestWithNostroType() {
        accessGatewayClient.grantPermissions(new PermissionDto("wallet.nostro", "read", null));

        WalletAccountSearch walletAccountSearch = WalletAccountSearch.empty();
        WalletAccountSearch authorizedRequest = sut.authorizeRequest(walletAccountSearch, new WydenAuthenticationToken("TEST_USER"));

        assertThat(authorizedRequest).isNotNull();
        assertThat(authorizedRequest.walletTypeDto().name()).isEqualToIgnoringCase("nostro");
    }

    @Test
    void createWalletWithoutPermission_shouldHaveNoPermission() {

        WalletAccountCreateRequest walletAccountCreateRequest = new WalletAccountCreateRequest("1", "name", WalletTypeDto.NOSTRO, "cio");
        boolean hasPermission = sut.checkWalletCreate(walletAccountCreateRequest, new WydenAuthenticationToken("TEST_USER"), null);

        assertThat(hasPermission).isFalse();
    }

    @Test
    void createNostroWalletWithWalletCreatePermission_shouldHavePermission() {
        accessGatewayClient.grantPermissions(new PermissionDto("wallet", "create", null));

        WalletAccountCreateRequest walletAccountCreateRequest = new WalletAccountCreateRequest("1", "name", WalletTypeDto.NOSTRO, "cio");
        boolean hasPermission = sut.checkWalletCreate(walletAccountCreateRequest, new WydenAuthenticationToken("TEST_USER"), null);

        assertThat(hasPermission).isTrue();
    }

    @Test
    void createNostroWalletWithNostroCreatePermission_shouldHavePermission() {
        accessGatewayClient.grantPermissions(new PermissionDto("wallet.nostro", "create", null));

        WalletAccountCreateRequest walletAccountCreateRequest = new WalletAccountCreateRequest("1", "name", WalletTypeDto.NOSTRO, "cio");
        boolean hasPermission = sut.checkWalletCreate(walletAccountCreateRequest, new WydenAuthenticationToken("TEST_USER"), null);

        assertThat(hasPermission).isTrue();
    }

    @Test
    void createNostroWalletWithVostroCreatePermission_shouldHaveNoPermission() {
        accessGatewayClient.grantPermissions(new PermissionDto("wallet.vostro", "create", null));

        WalletAccountCreateRequest walletAccountCreateRequest = new WalletAccountCreateRequest("1", "name", WalletTypeDto.NOSTRO, "cio");
        boolean hasPermission = sut.checkWalletCreate(walletAccountCreateRequest, new WydenAuthenticationToken("TEST_USER"), null);

        assertThat(hasPermission).isFalse();
    }

}