package io.wyden.apiserver.rest.security;

import io.wyden.apiserver.rest.referencedata.portfolio.service.PortfolioRepository;
import io.wyden.apiserver.rest.referencedata.venueaccount.VenueAccountRepository;
import io.wyden.apiserver.rest.security.model.WydenAuthenticationToken;
import io.wyden.apiserver.rest.trading.model.NewOrderSingleRequest;
import io.wyden.published.referencedata.AccountType;
import io.wyden.published.referencedata.VenueAccount;
import io.wyden.published.referencedata.WalletType;

import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Optional;

import static io.wyden.accessgateway.client.permission.Permission.WALLET_NOSTRO_TRADE;
import static io.wyden.accessgateway.client.permission.Permission.WALLET_TRADE;
import static io.wyden.accessgateway.client.permission.Permission.WALLET_VOSTRO_TRADE;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class TradingPermissionValidatorTest {

    AccessService authService = mock(AccessService.class);
    VenueAccountRepository venueAccountRepository = mock(VenueAccountRepository.class);
    PortfolioRepository portfolioRepository = mock(PortfolioRepository.class);
    WydenAuthenticationToken authToken = mock(WydenAuthenticationToken.class);
    NewOrderSingleRequest orderRequest = mock(NewOrderSingleRequest.class);

    TradingPermissionValidator validator = new TradingPermissionValidator(authService, portfolioRepository, venueAccountRepository);

    @Test
    void testWalletAccountTradingPermissions_shouldReturnTrue() {

        VenueAccount venueAccount = VenueAccount.newBuilder().setAccountType(AccountType.WALLET).setWalletType(WalletType.WALLET_NOSTRO).build();

        when(authService.hasPermission(any(AccessService.User.class), eq(WALLET_TRADE))).thenReturn(true);
        when(orderRequest.getVenueAccounts()).thenReturn(List.of("v1"));
        when(venueAccountRepository.find("v1")).thenReturn(Optional.of(venueAccount));

        // Act
        boolean result = validator.checkTradePermissions(orderRequest, authToken, null);

        // Assert
        assertTrue(result);
    }

    @Test
    void testWalletNostroAccountTradingPermissions_shouldReturnTrue() {

        VenueAccount venueAccount = VenueAccount.newBuilder().setAccountType(AccountType.WALLET).setWalletType(WalletType.WALLET_NOSTRO).build();

        when(authService.hasPermission(any(AccessService.User.class), eq(WALLET_TRADE))).thenReturn(false);
        when(authService.hasPermission(any(AccessService.User.class), eq(WALLET_NOSTRO_TRADE))).thenReturn(true);
        when(orderRequest.getVenueAccounts()).thenReturn(List.of("v1"));
        when(venueAccountRepository.find("v1")).thenReturn(Optional.of(venueAccount));

        // Act
        boolean result = validator.checkTradePermissions(orderRequest, authToken, null);

        // Assert
        assertTrue(result);
    }

    @Test
    void testWalletNostroAccountTradingPermissions_shouldReturnFalse() {

        VenueAccount venueAccount = VenueAccount.newBuilder().setAccountType(AccountType.WALLET).setWalletType(WalletType.WALLET_NOSTRO).build();

        when(authService.hasPermission(any(AccessService.User.class), eq(WALLET_TRADE))).thenReturn(false);
        when(authService.hasPermission(any(AccessService.User.class), eq(WALLET_NOSTRO_TRADE))).thenReturn(false);
        when(orderRequest.getVenueAccounts()).thenReturn(List.of("v1"));
        when(venueAccountRepository.find("v1")).thenReturn(Optional.of(venueAccount));

        // Act
        boolean result = validator.checkTradePermissions(orderRequest, authToken, null);

        // Assert
        assertFalse(result);
    }

    @Test
    void testWalletNostroAccountTradingPermissions_whenAccountIsNoWallet_shouldReturnFalse() {

        VenueAccount venueAccount = VenueAccount.newBuilder().setAccountType(AccountType.EXCHANGE).build();

        when(authService.hasPermission(any(AccessService.User.class), eq(WALLET_TRADE))).thenReturn(true);
        when(authService.hasPermission(any(AccessService.User.class), eq(WALLET_NOSTRO_TRADE))).thenReturn(true);
        when(authService.hasPermission(any(AccessService.User.class), eq(WALLET_VOSTRO_TRADE))).thenReturn(true);
        when(orderRequest.getVenueAccounts()).thenReturn(List.of("v1"));
        when(venueAccountRepository.find("v1")).thenReturn(Optional.of(venueAccount));

        // Act
        boolean result = validator.checkTradePermissions(orderRequest, authToken, null);

        // Assert
        assertFalse(result);
    }

}