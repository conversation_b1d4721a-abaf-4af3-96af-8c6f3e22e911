package io.wyden.apiserver.rest.apiui;

import io.wyden.apiserver.rest.common.InputValidationException;
import io.wyden.apiserver.rest.referencedata.exception.FieldValidationException;
import io.wyden.apiserver.rest.referencedata.exception.MissingVenueAccountException;
import io.wyden.apiserver.rest.security.model.Scope;
import io.wyden.apiserver.rest.security.model.WydenAuthenticationToken;
import io.wyden.apiserver.rest.targetregistry.VenueAccountTemplateValidator;
import io.wyden.apiserver.rest.trading.model.MutationSubmittedResponse;
import io.wyden.apiserver.rest.venueaccount.SecuredVenueAccountService;
import io.wyden.apiserver.rest.venueaccount.VenueAccountService;
import io.wyden.apiserver.rest.venueaccount.model.ActivateVenueAccountRequest;
import io.wyden.apiserver.rest.venueaccount.model.CreateVenueAccountRequest;
import io.wyden.apiserver.rest.venueaccount.model.DeactivateVenueAccountRequest;
import io.wyden.apiserver.rest.venueaccount.model.UpdateVenueAccountRequest;
import io.wyden.apiserver.rest.venueaccount.model.ValidationResponse;
import io.wyden.apiserver.rest.venueaccount.model.VenueAccountDetailsInput;
import io.wyden.apiserver.rest.venueaccount.model.VenueAccountDetailsResponse;
import io.wyden.apiserver.rest.venueaccount.model.VenueAccountsPerVenue;
import io.wyden.published.referencedata.VenueAccount;
import io.wyden.referencedata.client.VenueAccountCacheFacade;

import graphql.schema.DataFetchingEnvironment;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.graphql.data.method.annotation.Argument;
import org.springframework.graphql.data.method.annotation.MutationMapping;
import org.springframework.graphql.data.method.annotation.QueryMapping;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import reactor.core.publisher.Mono;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static io.wyden.accessgateway.client.permission.Permission.Scopes.MANAGE;
import static io.wyden.accessgateway.client.permission.Permission.Scopes.READ;
import static io.wyden.accessgateway.client.permission.Permission.Scopes.TRADE;

@Controller
public class VenueAccountGraphQLController {

    private static final Logger LOGGER = LoggerFactory.getLogger(VenueAccountGraphQLController.class);

    @Deprecated
    private final VenueAccountService venueAccountService;
    @Deprecated
    private final VenueAccountCacheFacade venueAccountCacheFacade;
    private final SecuredVenueAccountService securedVenueAccountService;
    private final VenueAccountTemplateValidator templateValidator;

    public VenueAccountGraphQLController(VenueAccountService venueAccountService, VenueAccountCacheFacade venueAccountCacheFacade, SecuredVenueAccountService securedVenueAccountService, VenueAccountTemplateValidator templateValidator) {
        this.venueAccountService = venueAccountService;
        this.venueAccountCacheFacade = venueAccountCacheFacade;
        this.securedVenueAccountService = securedVenueAccountService;
        this.templateValidator = templateValidator;
    }

    @QueryMapping("venueAccountById")
    public Mono<VenueAccountsPerVenue.VenueAccount> getVenueAccountById(@Argument String venueAccountId, WydenAuthenticationToken token) {
        return Mono.justOrEmpty(asOptionalVenueAccount(securedVenueAccountService.find(token, venueAccountId), token));
    }

    @QueryMapping("venueAccounts")
    public Set<VenueAccountsPerVenue> findAllVenueAccounts(WydenAuthenticationToken token) {
        LOGGER.debug("Requesting all venue accounts");
        return securedVenueAccountService.findAllByVenue(token, Set.of(READ, TRADE, MANAGE)).entrySet().stream()
            .map(entry -> asQueryResponse(entry, token))
            .collect(Collectors.toSet());
    }

    private VenueAccountsPerVenue asQueryResponse(Map.Entry<String, List<VenueAccount>> entry, WydenAuthenticationToken token) {
        Set<VenueAccountsPerVenue.VenueAccount> accounts = entry.getValue().stream()
            .map(va -> asVenueAccount(va, token))
            .collect(Collectors.toSet());
        return new VenueAccountsPerVenue(entry.getKey(), accounts);
    }

    private Optional<VenueAccountsPerVenue.VenueAccount> asOptionalVenueAccount(Optional<VenueAccount> va, WydenAuthenticationToken token) {
        return va.map(v -> asVenueAccount(v, token));
    }

    private VenueAccountsPerVenue.VenueAccount asVenueAccount(VenueAccount va, WydenAuthenticationToken token) {
        return new VenueAccountsPerVenue.VenueAccount(
            va.getId(),
            StringUtils.firstNonBlank(va.getVenueAccountName(), va.getId()),
            va.getSuspendedAt(),
            va.getCreatedAt(),
            va.getArchivedAt(),
            toScopes(securedVenueAccountService.getScopes(token, va.getId())),
            toScopes(securedVenueAccountService.getDynamicScopes(token, va.getId()))
        );
    }

    private static Set<Scope> toScopes(Set<String> scopes) {
        return scopes.stream().map(Scope::parse).collect(Collectors.toSet());
    }

    @QueryMapping("checkAccountName")
    public Mono<ValidationResponse> checkAccountNameUniquenessInput(WydenAuthenticationToken token, @Argument String name) {
        Collection<VenueAccount> byName = venueAccountCacheFacade.findByName(name);
        if (!byName.isEmpty()) {
            return Mono.just(ValidationResponse.violation("name", "Account name " + name + " is not unique"));
        }
        return Mono.just(ValidationResponse.OK);
    }

    @QueryMapping("venueAccountDetails")
    @PreAuthorize("@defaultPermissionValidator.hasPermission(#request.venueAccount(), 'venue.account', 'manage', #token, #env)")
    public Mono<VenueAccountDetailsResponse> findVenueAccountDetails(WydenAuthenticationToken token, @Argument VenueAccountDetailsInput request, DataFetchingEnvironment env) {
        String venueAccountId = request.venueAccount();
        if (StringUtils.isBlank(venueAccountId) || !venueAccountCacheFacade.exists(venueAccountId)) {
            throw new MissingVenueAccountException(venueAccountId);
        }

        LOGGER.debug("Requesting venue account: {} details", venueAccountId);
        Set<Scope> scopes = toScopes(securedVenueAccountService.getScopes(token, venueAccountId));
        Set<Scope> dynamicScopes = toScopes(securedVenueAccountService.getDynamicScopes(token, venueAccountId));

        VenueAccount venueAccount = venueAccountCacheFacade.find(venueAccountId)
            .orElseThrow(() -> new IllegalArgumentException("Venue account %s not found".formatted(venueAccountId)));

        return venueAccountService.getVenueAccountDetails(venueAccountId)
            .map(registryDto ->
                new VenueAccountDetailsResponse(
                    registryDto.venueAccountId(),
                    venueAccount.getVenueAccountName(),
                    registryDto.venueName(),
                    registryDto.keyValues(),
                    scopes,
                    dynamicScopes))
            .onErrorResume(throwable -> Mono.just(
                new VenueAccountDetailsResponse(
                    venueAccount.getId(),
                    venueAccount.getVenueAccountName(),
                    venueAccount.getVenueName(),
                    Set.of(),
                    scopes,
                    dynamicScopes))
            );
    }

    @MutationMapping("createVenueAccount")
    @PreAuthorize("@defaultPermissionValidator.hasPermission('venue.account', 'create', #token, #env)")
    public Mono<MutationSubmittedResponse> createVenueAccount(@Argument CreateVenueAccountRequest request, WydenAuthenticationToken token, DataFetchingEnvironment env) {
        LOGGER.debug("Requesting creation of venue account: {}", request);
        String venueAccountId = request.venueAccountId();
        if (StringUtils.isNotBlank(venueAccountId) && venueAccountCacheFacade.exists(venueAccountId)) {
            throw new FieldValidationException("Venue account id: " + venueAccountId + " already exists in system, please create different id.", "venueAccountId");
        }
        if (!venueAccountCacheFacade.findByName(request.venueAccountName()).isEmpty()) {
            throw new FieldValidationException("Venue account name: " + request.venueAccountName() + " already exists in system, please use different name.", "venueAccountName");
        }

        return templateValidator.validateConnectorTemplate(request.venueName(), request.keyValues())
            .flatMap(validationErrors -> {
                if (validationErrors.isEmpty()) {
                    return venueAccountService.emitCreateVenueAccountEvent(request, token.getClientId())
                        .then(Mono.just(new MutationSubmittedResponse("OK")));
                }

                return Mono.error(new InputValidationException(validationErrors));
            });
    }

    @MutationMapping("updateVenueAccount")
    @PreAuthorize("@defaultPermissionValidator.hasPermission(#request.venueAccountId(), 'venue.account', 'manage', #token, #env)")
    public Mono<MutationSubmittedResponse> updateVenueAccount(@Argument UpdateVenueAccountRequest request, WydenAuthenticationToken token, DataFetchingEnvironment env) {
        LOGGER.debug("Requesting update of venue account: {}", request);
        String venueAccountId = request.venueAccountId();
        if (StringUtils.isBlank(venueAccountId)) {
            throw new MissingVenueAccountException(venueAccountId);
        }
        VenueAccount venueAccount = venueAccountCacheFacade.find(venueAccountId)
            .orElseThrow(() -> new MissingVenueAccountException(venueAccountId));

        if (StringUtils.isEmpty(venueAccount.getSuspendedAt()) && request.archived()) {
            throw new FieldValidationException("Venue account " + request.venueAccountName() + " can't be archived, please deactivate it first.", "archived");
        }

        Collection<VenueAccount> sameNameVenueAccounts = venueAccountCacheFacade.findByName(request.venueAccountName());
        for (VenueAccount v : sameNameVenueAccounts) {
            if (!v.getId().equals(venueAccountId)) {
                throw new FieldValidationException("Venue account name: " + request.venueAccountName() + " already exists in system, please use different name.", "venueAccountName");
            }
        }

        UpdateVenueAccountRequest request2 = new UpdateVenueAccountRequest(request.venueAccountId(), request.venueAccountName(), request.keyValues(), request.archived());

        return templateValidator.validateConnectorTemplate(venueAccount.getVenueName(), request.keyValues())
            .flatMap(validationErrors -> {
                if (validationErrors.isEmpty()) {
                    return venueAccountService.sendRequestToUpdateVenueAccount(request2)
                        .then(Mono.just(new MutationSubmittedResponse("OK")));
                }

                return Mono.error(new InputValidationException(validationErrors));
            });
    }

    @MutationMapping("deactivateVenueAccount")
    @PreAuthorize("@defaultPermissionValidator.hasPermission(#request.venueAccountId(), 'venue.account', 'manage', #token, #env)")
    public Mono<MutationSubmittedResponse> deactivateVenueAccount(@Argument DeactivateVenueAccountRequest request, WydenAuthenticationToken token, DataFetchingEnvironment env) {
        LOGGER.debug("Requesting deactivation of venue account: {}", request.venueAccountId());
        String venueAccountId = request.venueAccountId();
        if (StringUtils.isBlank(venueAccountId) || !venueAccountCacheFacade.exists(venueAccountId)) {
            throw new MissingVenueAccountException(venueAccountId);
        }
        return venueAccountService.sendRequestToDeactivateVenueAccount(request, token.getClientId())
            .then(Mono.just(new MutationSubmittedResponse("OK")));
    }

    @MutationMapping("activateVenueAccount")
    @PreAuthorize("@defaultPermissionValidator.hasPermission(#request.venueAccountId(), 'venue.account', 'manage', #token, #env)")
    public Mono<MutationSubmittedResponse> activateVenueAccount(@Argument ActivateVenueAccountRequest request, WydenAuthenticationToken token, DataFetchingEnvironment env) {
        LOGGER.debug("Requesting activation of venue account: {}", request.venueAccountId());
        String venueAccountId = request.venueAccountId();
        if (StringUtils.isBlank(venueAccountId) || !venueAccountCacheFacade.exists(venueAccountId)) {
            throw new MissingVenueAccountException(venueAccountId);
        }
        return venueAccountService.sendRequestToActivateVenueAccount(request, token.getClientId())
            .then(Mono.just(new MutationSubmittedResponse("OK")));
    }
}
