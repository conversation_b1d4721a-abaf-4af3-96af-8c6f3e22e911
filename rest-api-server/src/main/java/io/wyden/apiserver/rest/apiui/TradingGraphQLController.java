package io.wyden.apiserver.rest.apiui;

import io.wyden.apiserver.rest.security.model.WydenAuthenticationToken;
import io.wyden.apiserver.rest.trading.SecuredTradingService;
import io.wyden.apiserver.rest.trading.TradingService;
import io.wyden.apiserver.rest.trading.model.CancelRejectResponse;
import io.wyden.apiserver.rest.trading.model.ExecutionReportResponse;
import io.wyden.apiserver.rest.trading.model.MutationReturnValue;
import io.wyden.apiserver.rest.trading.model.NewOrderSingleRequest;
import io.wyden.apiserver.rest.trading.model.OrderCancelReplaceRequest;
import io.wyden.apiserver.rest.trading.model.OrderCancelRequest;
import io.wyden.apiserver.rest.utils.MetricsUtils;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.cloudutils.telemetry.tracing.otl.TracingConv;

import graphql.schema.DataFetchingEnvironment;
import io.micrometer.core.instrument.MeterRegistry;
import io.opentelemetry.api.trace.SpanKind;
import org.apache.commons.lang3.ObjectUtils;
import org.reactivestreams.Publisher;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.graphql.data.method.annotation.Argument;
import org.springframework.graphql.data.method.annotation.MutationMapping;
import org.springframework.graphql.data.method.annotation.SubscriptionMapping;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import reactor.core.publisher.Mono;

import java.util.Map;

import static io.wyden.apiserver.rest.security.AccessService.User.user;
import static io.wyden.published.common.ApiType.GRAPHQL;
import static org.apache.commons.lang3.StringUtils.EMPTY;

@Controller
public class TradingGraphQLController {

    private static final Logger LOGGER = LoggerFactory.getLogger(TradingGraphQLController.class);
    private final Tracing otlTracing;
    private final MeterRegistry meterRegistry;
    private final TradingService tradingService;
    private final SecuredTradingService securedTradingService;


    public TradingGraphQLController(Telemetry telemetry,
                                    TradingService tradingService,
                                    SecuredTradingService securedTradingService) {
        this.otlTracing = telemetry.getTracing();
        this.meterRegistry = telemetry.getMeterRegistry();
        this.tradingService = tradingService;
        this.securedTradingService = securedTradingService;
    }

    @MutationMapping
    @PreAuthorize("@tradingPermissionValidator.checkTradePermissions(#request, #token, #env)")
    public Mono<MutationReturnValue> sendOrder(@Argument NewOrderSingleRequest request, WydenAuthenticationToken token, DataFetchingEnvironment env) {
        Map<String, String> baggage = Map.of(
            TracingConv.API_SERVER, TracingConv.GRAPHQL_API_SERVER,
            TracingConv.CLIENT_ID, token.getClientIdOrEmpty(),
            TracingConv.CL_ORDER_ID, ObjectUtils.firstNonNull(request.getClOrderId(), EMPTY),
            TracingConv.INSTRUMENT_ID, ObjectUtils.firstNonNull(request.getInstrumentId(), EMPTY),
            TracingConv.VENUE_ACCOUNT, getVenueAccount(request),
            TracingConv.EVENT_DOMAIN, TracingConv.TRADING_DOMAIN,
            TracingConv.EVENT_NAME, TracingConv.NEW_ORDER_EVENT
        );
        try (var ignored = otlTracing.createBaggage(baggage)) {
            try (var ignored2 = otlTracing.createSpan("order.consume", SpanKind.SERVER)) {
                LOGGER.info("SendOrder requested for clientId: {}\n{}", token.getClientId(), request);
                MetricsUtils.updateMetrics(meterRegistry, request);
                tradingService.sendOrder(request, token.getClientId(), GRAPHQL);
                return Mono.just(new MutationReturnValue(token.getClientId()));
            }
        }
    }

    @MutationMapping
    public Mono<MutationReturnValue> cancelOrder(@Argument OrderCancelRequest request, WydenAuthenticationToken token) {
        Map<String, String> baggage = Map.of(
            TracingConv.API_SERVER, TracingConv.REST_API_SERVER,
            TracingConv.CLIENT_ID, token.getClientIdOrEmpty(),
            TracingConv.CL_ORDER_ID, ObjectUtils.firstNonNull(request.clOrderId(), EMPTY),
            TracingConv.ORDER_ID, ObjectUtils.firstNonNull(request.orderId(), EMPTY),
            TracingConv.ORIG_CLIENT_ID, ObjectUtils.firstNonNull(request.origClientId(), EMPTY),
            TracingConv.ORIG_CL_ORDER_ID, ObjectUtils.firstNonNull(request.origClOrderId(), EMPTY),
            TracingConv.EVENT_DOMAIN, TracingConv.TRADING_DOMAIN,
            TracingConv.EVENT_NAME, TracingConv.CANCEL_EVENT
        );
        try (var ignored = otlTracing.createBaggage(baggage)) {
            try (var ignored2 = otlTracing.createSpan("cancel.consume", SpanKind.SERVER)) {
                LOGGER.info("CancelOrder requested for clientId: {}\n{}", token.getClientId(), request);
                MetricsUtils.updateMetrics(meterRegistry, request);
                return Mono.fromRunnable(() -> securedTradingService.cancelOrder(request, GRAPHQL, token))
                    .thenReturn(new MutationReturnValue(token.getClientId()));
            }
        }
    }

    @MutationMapping
    @PreAuthorize("@tradingPermissionValidator.checkCancelReplaceOrderPermissions(#request, #token, #env)")
    public Mono<MutationReturnValue> cancelReplaceOrder(@Argument OrderCancelReplaceRequest request, WydenAuthenticationToken token, DataFetchingEnvironment env) {
        Map<String, String> baggage = Map.of(
            TracingConv.API_SERVER, TracingConv.REST_API_SERVER,
            TracingConv.CLIENT_ID, token.getClientIdOrEmpty(),
            TracingConv.ORDER_ID, ObjectUtils.firstNonNull(request.orderId(), EMPTY),
            TracingConv.ORIG_CLIENT_ID, ObjectUtils.firstNonNull(request.origClientId(), EMPTY),
            TracingConv.ORIG_CL_ORDER_ID, ObjectUtils.firstNonNull(request.origClOrderId(), EMPTY),
            TracingConv.CL_ORDER_ID, ObjectUtils.firstNonNull(request.replacingOrder().getClOrderId(), EMPTY),
            TracingConv.INSTRUMENT_ID, ObjectUtils.firstNonNull(request.replacingOrder().getInstrumentId(), EMPTY),
            TracingConv.VENUE_ACCOUNT, getVenueAccount(request.replacingOrder()),
            TracingConv.EVENT_DOMAIN, TracingConv.TRADING_DOMAIN,
            TracingConv.EVENT_NAME, TracingConv.CANCEL_REPLACE_EVENT
        );
        try (var ignored = otlTracing.createBaggage(baggage)) {
            try (var ignored2 = otlTracing.createSpan("cancelreplace.consume", SpanKind.SERVER)) {
                LOGGER.info("CancelReplaceOrder requested for clientId: {}\n{}", token.getClientId(), request);
                MetricsUtils.updateMetrics(meterRegistry, request);
                return Mono.fromRunnable(() -> securedTradingService.cancelReplaceOrder(request, GRAPHQL, token))
                    .thenReturn(new MutationReturnValue(token.getClientId()));

            }
        }
    }

    @SubscriptionMapping
    public Publisher<CancelRejectResponse> cancelReject(WydenAuthenticationToken token) {
        LOGGER.info("Requested stream of CancelReject, client id: " + token.getClientId());
        return tradingService.subscribeCancelReject(user(token));
    }

    @SubscriptionMapping
    public Publisher<ExecutionReportResponse> executionReport(WydenAuthenticationToken token) {
        LOGGER.info("Requested stream of ExecutionReport, client id: " + token.getClientId());
        return tradingService.subscribeRealtimeExecutionReport(user(token));
    }

    private static String getVenueAccount(NewOrderSingleRequest request) {
        if (request.isSOR()) {
            return "SOR";
        }

        if (request.getVenueAccounts().size() == 1) {
            return request.getVenueAccounts().get(0);
        }

        return EMPTY;
    }
}
