package io.wyden.apiserver.rest.apiui;

import io.wyden.apiserver.rest.referencedata.exception.FieldValidationException;
import io.wyden.apiserver.rest.security.WalletPermissionValidator;
import io.wyden.apiserver.rest.security.model.Scope;
import io.wyden.apiserver.rest.security.model.WydenAuthenticationToken;
import io.wyden.apiserver.rest.trading.model.MutationSubmittedResponse;
import io.wyden.apiserver.rest.venueaccount.VenueAccountService;
import io.wyden.apiserver.rest.walletaccount.SecuredWalletService;
import io.wyden.apiserver.rest.walletaccount.WalletTypeMapper;
import io.wyden.apiserver.rest.walletaccount.model.WalletAccountCreateRequest;
import io.wyden.apiserver.rest.walletaccount.model.WalletAccountSearch;
import io.wyden.apiserver.rest.walletaccount.model.WalletAccountSearchInput;
import io.wyden.apiserver.rest.walletaccount.model.WalletAccountSearchResponse;
import io.wyden.cloud.utils.rest.pagination.PaginationModel;
import io.wyden.published.referencedata.AccountType;
import io.wyden.published.referencedata.VenueAccount;
import io.wyden.referencedata.client.VenueAccountCacheFacade;

import graphql.schema.DataFetchingEnvironment;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.graphql.data.method.annotation.Argument;
import org.springframework.graphql.data.method.annotation.MutationMapping;
import org.springframework.graphql.data.method.annotation.QueryMapping;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Controller
public class WalletAccountGraphQLController {

    private static final Logger LOGGER = LoggerFactory.getLogger(WalletAccountGraphQLController.class);
    private static final int DEFAULT_WALLET_PAGE_SIZE = 100;

    private final VenueAccountService venueAccountService;
    private final VenueAccountCacheFacade venueAccountCacheFacade;
    private final SecuredWalletService securedWalletService;
    private final WalletPermissionValidator walletPermissionValidator;

    public WalletAccountGraphQLController(VenueAccountService venueAccountService,
                                          VenueAccountCacheFacade venueAccountCacheFacade,
                                          SecuredWalletService securedWalletService,
                                          WalletPermissionValidator walletPermissionValidator) {
        this.venueAccountService = venueAccountService;
        this.venueAccountCacheFacade = venueAccountCacheFacade;
        this.securedWalletService = securedWalletService;
        this.walletPermissionValidator = walletPermissionValidator;
    }

    @MutationMapping("createWalletAccount")
    @PreAuthorize("@walletPermissionValidator.checkWalletCreate(#request, #token, #env)")
    public Mono<MutationSubmittedResponse> createWalletAccount(@Argument WalletAccountCreateRequest request, WydenAuthenticationToken token, DataFetchingEnvironment env) {
        LOGGER.debug("Requesting creation of wallet account: {}", request);
        String venueAccountId = request.id();
        if (StringUtils.isNotBlank(venueAccountId) && venueAccountCacheFacade.exists(venueAccountId)) {
            throw new FieldValidationException("Venue account " + venueAccountId + " already exists in system, please create different id.", "venueAccountId");
        }

        if (!venueAccountCacheFacade.findByName(request.name()).isEmpty()) {
            throw new FieldValidationException("Venue account name: " + request.name() + " already exists in system, please use different name.", "name");
        }

        return venueAccountService.emitCreateWalletAccountEvent(request, token.getClientId())
            .then(Mono.just(new MutationSubmittedResponse("OK")));
    }

    @QueryMapping("walletAccountSearch")
    public Mono<PaginationModel.CursorConnection<WalletAccountSearchResponse>> walletAccountSearch(WydenAuthenticationToken token, @Argument WalletAccountSearchInput search) {
        LOGGER.info("Executing walletAccountSearch: {}", search);

        WalletAccountSearch searchInternal = search == null
            ? new WalletAccountSearch(null, null, DEFAULT_WALLET_PAGE_SIZE, null, null)
            : new WalletAccountSearch(search.scopes(), null, search.first(), search.after(), search.name());

        WalletAccountSearch authorizedSearch = walletPermissionValidator.authorizeRequest(searchInternal, token);
        if (authorizedSearch == null) {
            return Mono.just(PaginationModel.emptyCursorConnection());
        }

        VenueAccountCacheFacade.VenueAccountQuery query = new VenueAccountCacheFacade.VenueAccountQuery(AccountType.WALLET,
            Set.of(),
            authorizedSearch.name(),
            authorizedSearch.after(),
            WalletTypeMapper.map(authorizedSearch.walletTypeDto()));

        List<PaginationModel.CursorEdge<WalletAccountSearchResponse>> cursorEdges = venueAccountCacheFacade.findAll(query, authorizedSearch.first() == null ? DEFAULT_WALLET_PAGE_SIZE : authorizedSearch.first())
            .stream()
            .map(va -> toWalletResponse(va, token))
            .map(wallet-> new PaginationModel.CursorEdge<>(wallet, wallet.createdAt()))
            .toList();
        String endCursor = cursorEdges.isEmpty() ? null : cursorEdges.get(cursorEdges.size() - 1).cursor();

        long total = venueAccountCacheFacade.countAll(query);
        boolean hasNextPage = total > cursorEdges.size();

        return Mono.just(new PaginationModel.CursorConnection<>(cursorEdges, new PaginationModel.PageInfo(hasNextPage, endCursor, cursorEdges.size(), null)));
    }

    private WalletAccountSearchResponse toWalletResponse(VenueAccount va, WydenAuthenticationToken token) {
        String name = StringUtils.firstNonBlank(va.getVenueAccountName(), va.getId());
        return new WalletAccountSearchResponse(va.getId(),
            name,
            WalletTypeMapper.map(va.getWalletType()),
            va.getCreatedAt(),
            toScopes(securedWalletService.getScopes(token, va.getId())),
            List.of()); //no dynamic scopes for wallets
    }

    private static List<Scope> toScopes(Set<String> scopes) {
        return scopes.stream().map(Scope::parse).collect(Collectors.toList());
    }
}
