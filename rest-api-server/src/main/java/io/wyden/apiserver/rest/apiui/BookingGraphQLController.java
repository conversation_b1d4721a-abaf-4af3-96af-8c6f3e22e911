package io.wyden.apiserver.rest.apiui;

import io.wyden.apiserver.rest.booking.BookingEngineService;
import io.wyden.apiserver.rest.booking.BookingModel;
import io.wyden.apiserver.rest.booking.BookingModel.LedgerEntryResponse;
import io.wyden.apiserver.rest.booking.BookingModel.PositionResponse;
import io.wyden.apiserver.rest.booking.BookingModel.TransactionResponse;
import io.wyden.apiserver.rest.security.model.WydenAuthenticationToken;
import io.wyden.apiserver.rest.trading.model.MutationSubmittedResponse;
import io.wyden.cloud.utils.rest.pagination.PaginationModel.CursorConnection;

import graphql.schema.DataFetchingEnvironment;
import org.springframework.graphql.data.method.annotation.Argument;
import org.springframework.graphql.data.method.annotation.MutationMapping;
import org.springframework.graphql.data.method.annotation.QueryMapping;
import org.springframework.graphql.data.method.annotation.SubscriptionMapping;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;

@Controller
public class BookingGraphQLController {

    private final BookingEngineService bookingEngineService;

    public BookingGraphQLController(BookingEngineService bookingEngineService) {
        this.bookingEngineService = bookingEngineService;
    }

    @QueryMapping("positions")
    @PreAuthorize("@bookingPermissionValidator.checkSubscribePermissions(#searchInput.portfolio(), #searchInput.venueAccount(), #token, #env)")
    public Mono<CursorConnection<PositionResponse>> positions(@Argument("search") BookingModel.PositionSearchInput searchInput, WydenAuthenticationToken token, DataFetchingEnvironment env) {
        return bookingEngineService.getPositions(searchInput, token);
    }

    @SubscriptionMapping("positionChanges")
    @PreAuthorize("@bookingPermissionValidator.checkSubscribePermissions(#searchInput.portfolio(), #searchInput.venueAccount(), #token, #env)")
    public Flux<PositionResponse> positionStream(@Argument("search") BookingModel.PositionSearchInput searchInput, WydenAuthenticationToken token, DataFetchingEnvironment env) {
        return bookingEngineService.subscribePositionStream(searchInput, token);
    }

    @QueryMapping("positionTypes")
    public Mono<List<String>> positionTypes() {
        return bookingEngineService.getPositionTypes();
    }

    @QueryMapping("ledgerEntries")
    @PreAuthorize("@bookingPermissionValidator.checkSubscribePermissions(#searchInput.portfolioId(), #searchInput.accountName(), #token, #env)")
    public Mono<CursorConnection<LedgerEntryResponse>> ledgerEntries(@Argument("search") BookingModel.LedgerEntrySearchInput searchInput, WydenAuthenticationToken token, DataFetchingEnvironment env) {
        return bookingEngineService.getLedgerEntries(searchInput, token);
    }

    @QueryMapping("transactions")
    @PreAuthorize("@bookingPermissionValidator.checkTransactionSearchPermissions(#searchInput.portfolioId(), #searchInput.accountId(), #token, #env)")
    public Mono<CursorConnection<TransactionResponse>> transactions(@Argument("search") BookingModel.TransactionSearchInput searchInput, WydenAuthenticationToken token, DataFetchingEnvironment env) {
        return bookingEngineService.getTransactions(searchInput, token);
    }

    @SubscriptionMapping("transactionsWithSnapshot")
    @PreAuthorize("@bookingPermissionValidator.checkSubscribePermissions(#searchInput.portfolioId(), #searchInput.accountId(), #token, #env)")
    public Flux<TransactionResponse> transactionStream(@Argument("search") BookingModel.TransactionSearchInput searchInput, WydenAuthenticationToken token, DataFetchingEnvironment env) {
        return bookingEngineService.subscribeTransactionStream(searchInput, token);
    }

    @MutationMapping("addTransaction")
    public Mono<MutationSubmittedResponse> addTransaction(@Argument("input") BookingModel.TransactionInput request) {
        return bookingEngineService.addTransaction(request)
            .then(Mono.just(new MutationSubmittedResponse("OK")));
    }

    @QueryMapping("transactionTypes")
    public Mono<List<String>> transactionTypes() {
        return bookingEngineService.getTransactionTypes();
    }

    @QueryMapping("systemCurrency")
    public Mono<String> systemCurrency() {
        return bookingEngineService.getSystemCurrency();
    }
}
