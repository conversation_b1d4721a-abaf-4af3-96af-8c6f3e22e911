package io.wyden.apiserver.rest.config;

import io.wyden.accessgateway.client.license.LicenseService;
import io.wyden.accessgateway.domain.license.LicenseState;

import com.hazelcast.map.IMap;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

@Configuration
public class LicenseConfiguration {

    @Bean
    LicenseService licenseService(IMap<String, LicenseState> licenseMap,
                                  RestTemplate restTemplate,
                                  @Value("${access.gateway.host}") String accessGatewayHost) {
        return new LicenseService(licenseMap, restTemplate, accessGatewayHost);
    }
}
