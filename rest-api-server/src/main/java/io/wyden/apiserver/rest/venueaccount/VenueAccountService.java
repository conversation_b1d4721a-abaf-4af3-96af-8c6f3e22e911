package io.wyden.apiserver.rest.venueaccount;

import io.wyden.apiserver.rest.targetregistry.TargetRegistryHttpClient;
import io.wyden.apiserver.rest.venueaccount.model.ActivateVenueAccountRequest;
import io.wyden.apiserver.rest.venueaccount.model.CreateVenueAccountRequest;
import io.wyden.apiserver.rest.venueaccount.model.DeactivateVenueAccountRequest;
import io.wyden.apiserver.rest.venueaccount.model.UpdateVenueAccountRequest;
import io.wyden.apiserver.rest.venueaccount.model.VenueAccountDesc;
import io.wyden.apiserver.rest.walletaccount.WalletTypeMapper;
import io.wyden.apiserver.rest.walletaccount.model.WalletAccountCreateRequest;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.common.KeyValue;
import io.wyden.published.common.Metadata;
import io.wyden.published.referencedata.AccountType;
import io.wyden.published.referencedata.ConnectorDetails;
import io.wyden.published.referencedata.VenueAccount;
import io.wyden.published.referencedata.VenueAccountActivateRequest;
import io.wyden.published.referencedata.VenueAccountCreateRequest;
import io.wyden.published.referencedata.VenueAccountDeactivationRequest;
import io.wyden.published.referencedata.VenueAccountUpdateRequest;
import io.wyden.referencedata.client.VenueAccountCacheFacade;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import reactor.core.publisher.Mono;

import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.apache.commons.lang3.ObjectUtils.firstNonNull;
import static org.apache.commons.lang3.ObjectUtils.isEmpty;
import static org.apache.commons.lang3.StringUtils.EMPTY;

@Service
public class VenueAccountService {

    private static final Logger LOGGER = LoggerFactory.getLogger(VenueAccountService.class);

    private final TargetRegistryHttpClient targetRegistryHttpClient;
    private final VenueAccountCacheFacade venueAccountCacheFacade;
    private final VenueAccountCreateRequestEmitter venueAccountCreateRequestEmitter;
    private final VenueAccountDeactivateRequestEmitter venueAccountDeactivateRequestEmitter;
    private final VenueAccountActivateRequestEmitter venueAccountActivateRequestEmitter;
    private final VenueAccountUpdateRequestEmitter venueAccountUpdateRequestEmitter;

    public VenueAccountService(TargetRegistryHttpClient targetRegistryHttpClient,
                               VenueAccountCacheFacade venueAccountCacheFacade,
                               VenueAccountCreateRequestEmitter VenueAccountCreateRequestEmitter,
                               VenueAccountDeactivateRequestEmitter venueAccountDeactivateRequestEmitter,
                               VenueAccountActivateRequestEmitter venueAccountActivateRequestEmitter,
                               VenueAccountUpdateRequestEmitter venueAccountUpdateRequestEmitter) {
        this.targetRegistryHttpClient = targetRegistryHttpClient;
        this.venueAccountCacheFacade = venueAccountCacheFacade;
        this.venueAccountCreateRequestEmitter = VenueAccountCreateRequestEmitter;
        this.venueAccountDeactivateRequestEmitter = venueAccountDeactivateRequestEmitter;
        this.venueAccountActivateRequestEmitter = venueAccountActivateRequestEmitter;
        this.venueAccountUpdateRequestEmitter = venueAccountUpdateRequestEmitter;
    }

    public Optional<VenueAccount> find(String venueAccountId) {
        return venueAccountCacheFacade.find(venueAccountId);
    }

    public Stream<VenueAccount> findAll() {
        return venueAccountCacheFacade.venueAccountDetails().stream()
            .filter(va -> va.getAccountType() != AccountType.WALLET);
    }

    public Stream<VenueAccount> findAll(Set<String> venueAccountIds) {
        return venueAccountCacheFacade.venueAccountDetails(venueAccountIds).stream()
            .filter(va -> va.getAccountType() != AccountType.WALLET);
    }

    public Set<String> findAllIds() {
        return findAll()
            .map(VenueAccount::getId)
            .collect(Collectors.toSet());
    }

    public Map<String, List<VenueAccount>> findAllByVenue(Set<String> venueAccountIds) {
        return findAll(venueAccountIds).collect(Collectors.groupingBy(VenueAccount::getVenueName));
    }

    public Map<String, List<VenueAccount>> findAllByVenue() {
        return findAll().collect(Collectors.groupingBy(VenueAccount::getVenueName));
    }

    public String getVenueAccountName(String venueAccountId) {
        if (StringUtils.isBlank(venueAccountId)) {
            return EMPTY;
        }

        try {
            return venueAccountCacheFacade.find(venueAccountId)
                .map(VenueAccount::getVenueAccountName)
                .map(n -> StringUtils.firstNonBlank(n, venueAccountId))
                .orElseGet(() -> {
                    LOGGER.warn("Could not get account {} name", venueAccountId);
                    return venueAccountId;
                });
        } catch (Exception ex) {
            LOGGER.warn("Could not get account {} name", venueAccountId, ex);
            return venueAccountId;
        }
    }

    public List<String> getVenueAccountNames(Collection<String> venueAccountIds) {
        if (venueAccountIds == null) {
            return List.of();
        }
        return venueAccountIds.stream().map(this::getVenueAccountName).toList();
    }

    public VenueAccountDesc getVenueAccountDesc(String venueAccountId) {
        return new VenueAccountDesc(venueAccountId, getVenueAccountName(venueAccountId));
    }

    public List<VenueAccountDesc> getVenueAccountDescs(Collection<String> venueAccountIds) {
        if (venueAccountIds == null) {
            return List.of();
        }
        return venueAccountIds.stream().map(this::getVenueAccountDesc).toList();
    }

    public Collection<VenueAccount> findByName(String venueAccountName) {
        return venueAccountCacheFacade.findByName(venueAccountName);
    }

    public boolean exists(String venueAccountId) {
        return venueAccountCacheFacade.exists(venueAccountId);
    }

    public Mono<Void> emitCreateVenueAccountEvent(CreateVenueAccountRequest request, String clientId) {
        return Mono.fromRunnable(() -> {
            VenueAccountCreateRequest rabbitRequest = VenueAccountCreateRequest.newBuilder()
                .setMessageId(UUID.randomUUID().toString())
                .setVenueAccountId(firstNonNull(request.venueAccountId(), EMPTY))
                .setVenueAccountName(request.venueAccountName())
                .setAccountType(AccountType.EXCHANGE)
                .setVenueName(request.venueName())
                .setOwnerUsername(clientId)
                .setConnectorDetails(mapConnectorDetails(request))
                .setMetadata(Metadata.newBuilder()
                    .setCreatedAt(DateUtils.toIsoUtcTime(ZonedDateTime.now()))
                    .setCorrelationObject(firstNonNull(request.correlationObject(), EMPTY))
                    .build())
                .build();
            venueAccountCreateRequestEmitter.emit(rabbitRequest);
        });
    }

    public Mono<Void> emitCreateWalletAccountEvent(WalletAccountCreateRequest request, String clientId) {
        return Mono.fromRunnable(() -> {
            VenueAccountCreateRequest rabbitRequest = VenueAccountCreateRequest.newBuilder()
                .setMessageId(UUID.randomUUID().toString())
                .setVenueAccountId(firstNonNull(request.id(), EMPTY))
                .setVenueAccountName(request.name())
                .setAccountType(AccountType.WALLET)
                .setWalletType(WalletTypeMapper.map(request.walletType()))
                .setOwnerUsername(clientId)
                .setMetadata(Metadata.newBuilder()
                    .setCreatedAt(DateUtils.toIsoUtcTime(ZonedDateTime.now()))
                    .setCorrelationObject(firstNonNull(request.correlationObject(), EMPTY))
                    .build())
                .build();
            venueAccountCreateRequestEmitter.emit(rabbitRequest);
        });
    }

    public Mono<Object> sendRequestToDeactivateVenueAccount(DeactivateVenueAccountRequest request, String clientId) {
        return Mono.fromRunnable(() -> emitDeactivateVenueAccountRequest(request, clientId));
    }

    public Mono<Object> sendRequestToActivateVenueAccount(ActivateVenueAccountRequest request, String clientId) {
        return Mono.fromRunnable(() -> emitActivateVenueAccountRequest(request, clientId));
    }

    public Mono<Object> sendRequestToUpdateVenueAccount(UpdateVenueAccountRequest request) {
        return Mono.fromRunnable(() -> emitUpdateVenueAccountRequest(request));
    }

    private void emitUpdateVenueAccountRequest(UpdateVenueAccountRequest request) {
        VenueAccountUpdateRequest proto = VenueAccountUpdateRequest.newBuilder()
            .setVenueAccountId(request.venueAccountId())
            .setVenueAccountName(request.venueAccountName())
            .setArchived(request.archived())
            .setConnectorDetails(ConnectorDetails.newBuilder()
                .addAllParameters(isEmpty(request.keyValues()) ? Set.of() : mapKV(request.keyValues()))
                .build())
            .build();
        venueAccountUpdateRequestEmitter.emit(proto);
    }

    private static Set<KeyValue> mapKV(Collection<io.wyden.apiserver.rest.venueaccount.model.KeyValue> requestKVs) {
        return requestKVs.stream()
            .map(requestKV -> KeyValue.newBuilder()
                .setKey(requestKV.key())
                .setValue(requestKV.value())
                .build())
            .collect(Collectors.toSet());
    }

    private void emitActivateVenueAccountRequest(ActivateVenueAccountRequest request, String clientId) {
        VenueAccountActivateRequest protoRequest = VenueAccountActivateRequest.newBuilder()
            .setVenueAccountId(request.venueAccountId())
            .setRequester(clientId)
            .setCorrelationObject(request.correlationObject())
            .setMessageId(UUID.randomUUID().toString())
            .build();
        venueAccountActivateRequestEmitter.emit(protoRequest);
    }

    private void emitDeactivateVenueAccountRequest(DeactivateVenueAccountRequest request, String clientId) {
        VenueAccountDeactivationRequest protoRequest = VenueAccountDeactivationRequest.newBuilder()
            .setVenueAccountId(request.venueAccountId())
            .setMetadata(Metadata.newBuilder()
                .setRequestId(UUID.randomUUID().toString())
                .setRequesterId(clientId)
                .setCreatedAt(DateUtils.toIsoUtcTime(ZonedDateTime.now()))
                .setCorrelationObject(request.correlationObject())
                .build())
            .setDeactivationReason("Venue Account " + request.venueAccountId()  +" deactivated by user.")
            .build();
        venueAccountDeactivateRequestEmitter.emit(protoRequest);
    }

    private static ConnectorDetails mapConnectorDetails(CreateVenueAccountRequest request) {

        Set<KeyValue> parameters = getKeyValues(request);
        return ConnectorDetails.newBuilder()
            .addAllParameters(parameters)
            .build();
    }

    private static Set<KeyValue> getKeyValues(CreateVenueAccountRequest request) {
        if (CollectionUtils.isEmpty(request.keyValues())) {
            return Set.of();
        }
        return mapKV(request.keyValues());
    }

    public Mono<TargetRegistryHttpClient.VenueAccountDetailsResponseDto> getVenueAccountDetails(String venueAccount) {
        return targetRegistryHttpClient.fetchTargetDetails(venueAccount);
    }

    public Collection<VenueAccount> venueAccountDetails(Set<String> venueAccountNames) {
        return venueAccountCacheFacade.venueAccountDetails(venueAccountNames);
    }

}
