package io.wyden.apiserver.rest.session;

import io.wyden.apiserver.rest.security.AccessService;
import io.wyden.apiserver.rest.security.TradingPermissionValidator;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.core.publisher.Flux;

import javax.annotation.Nullable;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;


public class SecuredSinkManager<T> {

    private static final Logger LOGGER = LoggerFactory.getLogger(SecuredSinkManager.class);

    private final Map<String, SinkWrapper<T>> sinkWrappers = new ConcurrentHashMap<>();
    private final AccessService accessService;
    private final TradingPermissionValidator tradingPermissionValidator;

    public SecuredSinkManager(AccessService accessService, TradingPermissionValidator tradingPermissionValidator) {
        this.accessService = accessService;
        this.tradingPermissionValidator = tradingPermissionValidator;
    }

    public int count() {
        return sinkWrappers.size();
    }

    public synchronized SinkWrapper<T> register(AccessService.User user) {
        String clientId = user.clientId();
        if (!sinkWrappers.containsKey(clientId)) {
            LOGGER.info("Registering sink wrapper for client: {}", clientId);
            sinkWrappers.put(clientId, new SinkWrapper<>(user));
        }
        return sinkWrappers.get(clientId);
    }

    public synchronized void unregister(String clientId) {
        SinkWrapper<T> wrapper = sinkWrappers.get(clientId);
        if (wrapper != null) {
            int subscribers = wrapper.getSubscriberCount();
            if (subscribers == 1) {
                LOGGER.info("Unregistering sink wrapper for client: {}", clientId);
                sinkWrappers.remove(clientId);
            } else {
                LOGGER.debug("Unregistering sink wrapper for client: {} skipped - still {} active subscribers", clientId, subscribers);
            }
        }
    }

    public void accept(String recipientClientId, @Nullable String portfolioId, String venueAccountId, T message) {
        for (Map.Entry<String, SinkWrapper<T>> entry : sinkWrappers.entrySet()) {
            String clientId = entry.getKey();
            SinkWrapper<T> wrapper = entry.getValue();
            if (StringUtils.equals(clientId, recipientClientId)) {
                wrapper.capture(message);
            } else if (tradingPermissionValidator.checkSubscribePermissions(portfolioId, venueAccountId, wrapper.getUser(), null)) {
                wrapper.capture(message);
            } else {
                LOGGER.trace("Dropping message for {} - no permissions.", clientId);
            }
        }
    }

    public Flux<T> stream(String clientId) {
        return sinkWrappers.get(clientId).stream();
    }

}
