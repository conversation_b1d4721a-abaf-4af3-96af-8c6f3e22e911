package io.wyden.apiserver.rest.config;

import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.StatusCode;
import jakarta.validation.ConstraintViolationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.LockedException;
import org.springframework.security.core.context.ReactiveSecurityContextHolder;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.NoSuchElementException;

@RestControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler(ConstraintViolationException.class)
    void constraintValidationExceptionHandler(ConstraintViolationException e) {
        LOGGER.error("constraintValidationExceptionHandler: {}", e.getMessage(),e );
        Span.current().setStatus(StatusCode.ERROR);
        Span.current().recordException(e);
    }

    @ExceptionHandler({AccessDeniedException.class})
    public Mono<ResponseEntity<String>> accessDeniedExceptionHandler(AccessDeniedException exception, ServerWebExchange exchange) {
        String path = exchange.getRequest().getURI().getPath();
        String query = exchange.getRequest().getURI().getQuery();
        Span.current().setStatus(StatusCode.ERROR);
        Span.current().recordException(exception);
        return ReactiveSecurityContextHolder.getContext()
            .map(sc -> {
                String user = sc.getAuthentication().getName();
                LOGGER.warn("Access denied for user: {}, path: {}, request: {}", user, path, query);
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body("Access denied for user: " + user + ", path: " + path + ", request: " + query);
            });
    }

    @ExceptionHandler({NoSuchElementException.class})
    public ResponseEntity<String> noSuchElementExceptionHandler(NoSuchElementException exception) {
        LOGGER.warn("New NoSuchElementException {}", exception.getMessage(), exception.getCause());
        Span.current().setStatus(StatusCode.ERROR);
        Span.current().recordException(exception);
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(exception.getMessage());
    }

    @ExceptionHandler({Exception.class})
    public ResponseEntity<String> exceptionHandler(Exception ex) {
        LOGGER.error("Exception: {}", ex.getMessage(), ex);
        Span.current().setStatus(StatusCode.ERROR);
        Span.current().recordException(ex);
        return ResponseEntity.status(500).body(ex.getMessage());
    }

    @ExceptionHandler({IllegalArgumentException.class})
    public ResponseEntity<String> illegalArgumentExceptionHandler(IllegalArgumentException exception) {
        LOGGER.error("IllegalArgumentException: {}", exception.getMessage());
        Span.current().setStatus(StatusCode.ERROR);
        Span.current().recordException(exception);
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(exception.getMessage());
    }

    @ExceptionHandler({WebClientResponseException.class})
    public ResponseEntity<String> webClientResponseExceptionHandler(WebClientResponseException exception) {
        LOGGER.warn("WebClientResponseException {}, {}", exception.getMessage(), exception.getResponseBodyAsString());
        Span.current().setStatus(StatusCode.ERROR);
        Span.current().recordException(exception);
        return ResponseEntity.status(exception.getStatusCode()).body(exception.getMessage());
    }

    @ExceptionHandler({ResponseStatusException.class})
    public ResponseEntity<String> responseStatusExceptionHandler(ResponseStatusException exception) {
        LOGGER.warn("Response status exception {}, {}", exception.getMessage(), exception.getMessage());
        Span.current().setStatus(StatusCode.ERROR);
        Span.current().recordException(exception);
        return ResponseEntity.status(exception.getStatusCode()).body(exception.getMessage());
    }

    @ResponseStatus(HttpStatus.LOCKED)
    public void lockedExceptionHandler(LockedException exception) {
        LOGGER.warn("LockedException: {}", exception.getMessage());
        Span.current().setStatus(StatusCode.ERROR);
        Span.current().recordException(exception);
    }

    @ExceptionHandler({RuntimeException.class})
    public ResponseEntity<String> runtimeExceptionHandler(RuntimeException exception) {
        LOGGER.error("RuntimeException: {}", exception.toString());
        Span.current().setStatus(StatusCode.ERROR);
        Span.current().recordException(exception);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(exception.getMessage());
    }

}
