package io.wyden.apiserver.rest.config.exception;

import io.wyden.accessgateway.client.license.InvalidLicenseException;
import io.wyden.accessgateway.client.rest.AccessDeniedException;
import io.wyden.accessgateway.client.rest.AccessForbiddenException;
import io.wyden.apiserver.rest.apiui.QueryLimitExceededException;
import io.wyden.apiserver.rest.common.InputValidationException;

import graphql.GraphQLError;
import graphql.schema.DataFetchingEnvironment;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.StatusCode;
import org.jetbrains.annotations.NotNull;
import org.springframework.graphql.execution.DataFetcherExceptionResolverAdapter;
import org.springframework.graphql.execution.ErrorType;
import org.springframework.security.authentication.LockedException;
import org.springframework.security.oauth2.jwt.JwtValidationException;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import java.util.Map;
import java.util.NoSuchElementException;

@Component
public class GraphqlExceptionResolver extends DataFetcherExceptionResolverAdapter {

    private static final Map<Class<? extends Throwable>, AbstractGqlExceptionHandler> handlersMap = Map.ofEntries(
        Map.entry(InvalidLicenseException.class, new DefaultGqlExceptionHandler(ErrorType.FORBIDDEN)),
        Map.entry(LockedException.class, new DefaultGqlExceptionHandler(ErrorType.FORBIDDEN)),
        Map.entry(NoSuchElementException.class, new DefaultGqlExceptionHandler(ErrorType.NOT_FOUND)),
        Map.entry(WebClientResponseException.NotFound.class, new DefaultGqlExceptionHandler(ErrorType.NOT_FOUND)),
        Map.entry(AccessDeniedException.class, new AccessDeniedGqlExceptionHandler(ErrorType.UNAUTHORIZED)),
        Map.entry(org.springframework.security.access.AccessDeniedException.class, new AccessDeniedGqlExceptionHandler(ErrorType.UNAUTHORIZED)),
        Map.entry(JwtValidationException.class, new AccessDeniedGqlExceptionHandler(ErrorType.UNAUTHORIZED)),
        Map.entry(AccessForbiddenException.class, new DefaultGqlExceptionHandler(ErrorType.FORBIDDEN)),
        Map.entry(IllegalArgumentException.class, new DefaultGqlExceptionHandler(ErrorType.BAD_REQUEST)),
        Map.entry(InputValidationException.class, new InputValidationGqlExceptionHandler(ErrorType.BAD_REQUEST)),
        Map.entry(QueryLimitExceededException.class, new DefaultGqlExceptionHandler(graphql.ErrorType.DataFetchingException))
    );

    @Override
    protected GraphQLError resolveToSingleError(@NotNull Throwable ex, @NotNull DataFetchingEnvironment env) {

        Span.current().setStatus(StatusCode.ERROR);
        Span.current().recordException(ex);

        return getHandler(ex).handle(ex, env);

    }

    private AbstractGqlExceptionHandler getHandler(Throwable ex) {
        return handlersMap.entrySet().stream()
            .filter(entry -> entry.getKey().isAssignableFrom(ex.getClass()))
            .map(Map.Entry::getValue)
            .findFirst()
            .orElse(new UnknownGqlExceptionHandler());
    }
}
