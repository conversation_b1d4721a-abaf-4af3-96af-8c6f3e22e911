package io.wyden.apiserver.rest.security;

import io.wyden.apiserver.rest.referencedata.portfolio.service.PortfolioRepository;
import io.wyden.apiserver.rest.referencedata.venueaccount.VenueAccountRepository;
import io.wyden.apiserver.rest.security.model.WydenAuthenticationToken;
import io.wyden.apiserver.rest.utils.AccessDeniedLogger;
import io.wyden.published.referencedata.AccountType;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.published.referencedata.PortfolioType;
import io.wyden.published.referencedata.VenueAccount;
import io.wyden.published.referencedata.WalletType;

import graphql.schema.DataFetchingEnvironment;
import io.micrometer.common.util.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

import static io.wyden.accessgateway.client.permission.Permission.PORTFOLIO_NOSTRO_READ;
import static io.wyden.accessgateway.client.permission.Permission.PORTFOLIO_READ;
import static io.wyden.accessgateway.client.permission.Permission.PORTFOLIO_VOSTRO_READ;
import static io.wyden.accessgateway.client.permission.Permission.VENUE_ACCOUNT_READ;
import static io.wyden.accessgateway.client.permission.Permission.WALLET_NOSTRO_READ;
import static io.wyden.accessgateway.client.permission.Permission.WALLET_READ;
import static io.wyden.accessgateway.client.permission.Permission.WALLET_VOSTRO_READ;
import static io.wyden.apiserver.rest.security.AccessService.User.user;

@Component("bookingPermissionValidator")
public class BookingPermissionValidator {

    private final AccessService accessService;
    private final PortfolioRepository portfolioRepository;
    private final VenueAccountRepository venueAccountRepository;

    public BookingPermissionValidator(AccessService accessService,
                                      PortfolioRepository portfolioRepository,
                                      VenueAccountRepository venueAccountRepository) {
        this.accessService = accessService;
        this.portfolioRepository = portfolioRepository;
        this.venueAccountRepository = venueAccountRepository;
    }

    public boolean checkSubscribePermissions(String portfolioId, String venueAccount, WydenAuthenticationToken token, DataFetchingEnvironment env) {
        boolean hasPortfolioPermission = StringUtils.isEmpty(portfolioId)
            || accessService.hasPermission(user(token), PORTFOLIO_READ, portfolioId)
            || hasReadPermissionToNostroVostroPortfolio(user(token), portfolioId);
        AccessDeniedLogger.logIfAccessDenied(hasPortfolioPermission, env, token, PORTFOLIO_READ, portfolioId);

        boolean hasAccountPermission = StringUtils.isEmpty(venueAccount)
            || accessService.hasPermission(user(token), VENUE_ACCOUNT_READ, venueAccount)
            || hasReadPermissionToNostroVostroWallet(user(token), venueAccount);
        AccessDeniedLogger.logIfAccessDenied(hasAccountPermission, env, token, VENUE_ACCOUNT_READ, venueAccount);

        return hasPortfolioPermission && hasAccountPermission;
    }

    public boolean checkTransactionSearchPermissions(List<String> portfolioId, List<String> venueAccount, WydenAuthenticationToken token, DataFetchingEnvironment env) {
        boolean hasPortfolioPermission = portfolioId.stream().allMatch(id -> StringUtils.isEmpty(id)
            || accessService.hasPermission(user(token), PORTFOLIO_READ, id)
            || hasReadPermissionToNostroVostroPortfolio(user(token), id));
        AccessDeniedLogger.logIfAccessDenied(hasPortfolioPermission, env, token, PORTFOLIO_READ, portfolioId);

        boolean hasAccountPermission = venueAccount.stream().allMatch(id -> StringUtils.isEmpty(id)
            || accessService.hasPermission(user(token), VENUE_ACCOUNT_READ, id)
            || hasReadPermissionToNostroVostroWallet(user(token), id));
        AccessDeniedLogger.logIfAccessDenied(hasAccountPermission, env, token, VENUE_ACCOUNT_READ, venueAccount);
        return hasPortfolioPermission && hasAccountPermission;
    }

    private boolean hasReadPermissionToNostroVostroPortfolio(AccessService.User user, String portfolioId) {
        Optional<Portfolio> portfolio = portfolioRepository.find(portfolioId);
        if (portfolio.isEmpty()) {
            return false;
        }
        PortfolioType portfolioType = portfolio.get().getPortfolioType();
        return switch (portfolioType) {
            case VOSTRO -> accessService.hasPermission(user, PORTFOLIO_VOSTRO_READ);
            case NOSTRO -> accessService.hasPermission(user, PORTFOLIO_NOSTRO_READ);
            default -> false;
        };
    }

    private boolean hasReadPermissionToNostroVostroWallet(AccessService.User user, String accountId) {
        Optional<VenueAccount> venueAccount = venueAccountRepository.find(accountId);
        if (venueAccount.isEmpty()) {
            return false;
        }
        AccountType accountType = venueAccount.get().getAccountType();
        if (AccountType.WALLET.equals(accountType)) {

            if (accessService.hasPermission(user, WALLET_READ)) {
                return true;
            }

            WalletType walletType = venueAccount.get().getWalletType();
            return switch (walletType) {
                case WALLET_VOSTRO -> accessService.hasPermission(user, WALLET_VOSTRO_READ);
                case WALLET_NOSTRO -> accessService.hasPermission(user, WALLET_NOSTRO_READ);
                default -> false;
            };
        }
        return false;
    }

}
