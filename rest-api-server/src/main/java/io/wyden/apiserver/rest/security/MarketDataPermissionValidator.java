package io.wyden.apiserver.rest.security;

import io.wyden.apiserver.rest.referencedata.portfolio.service.PortfolioRepository;
import io.wyden.apiserver.rest.security.model.WydenAuthenticationToken;
import io.wyden.apiserver.rest.utils.AccessDeniedLogger;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.published.referencedata.PortfolioType;

import graphql.schema.DataFetchingEnvironment;
import io.micrometer.common.util.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static io.wyden.accessgateway.client.permission.Permission.PORTFOLIO_NOSTRO_TRADE;
import static io.wyden.accessgateway.client.permission.Permission.PORTFOLIO_TRADE;
import static io.wyden.accessgateway.client.permission.Permission.PORTFOLIO_VOSTRO_TRADE;
import static io.wyden.accessgateway.client.permission.Permission.VENUE_ACCOUNT_TRADE;
import static io.wyden.apiserver.rest.security.AccessService.User.user;

@Component("marketDataPermissionValidator")
public class MarketDataPermissionValidator {

    private final AccessService accessService;
    private final PortfolioRepository portfolioRepository;

    public MarketDataPermissionValidator(AccessService accessService,
                                         PortfolioRepository portfolioRepository) {
        this.accessService = accessService;
        this.portfolioRepository = portfolioRepository;
    }

    public boolean checkTradePermissions(String accountId, String portfolioId, WydenAuthenticationToken token, DataFetchingEnvironment env) {
        boolean hasPortfolioPermission = StringUtils.isEmpty(portfolioId)
            || accessService.hasPermission(user(token), PORTFOLIO_TRADE, portfolioId)
            || hasAccessTradeToNostroVostroPortfolio(user(token), portfolioId);
        AccessDeniedLogger.logIfAccessDenied(hasPortfolioPermission, env, token, PORTFOLIO_TRADE, portfolioId);

        boolean hasAccountPermission = StringUtils.isEmpty(accountId)
            || accessService.hasPermission(user(token), VENUE_ACCOUNT_TRADE, accountId);
        AccessDeniedLogger.logIfAccessDenied(hasAccountPermission, env, token, VENUE_ACCOUNT_TRADE,accountId);

        return hasPortfolioPermission && hasAccountPermission;
    }

    private boolean hasAccessTradeToNostroVostroPortfolio(AccessService.User user, String portfolioId) {

        Optional<Portfolio> portfolio = portfolioRepository.find(portfolioId);
        if (portfolio.isEmpty()) {
            return false;
        }

        PortfolioType portfolioType = portfolio.get().getPortfolioType();
        return switch(portfolioType) {
            case VOSTRO -> accessService.hasPermission(user, PORTFOLIO_VOSTRO_TRADE);
            case NOSTRO -> accessService.hasPermission(user, PORTFOLIO_NOSTRO_TRADE);
            default -> false;
        };
    }
}
