package io.wyden.apiserver.rest.config;

import io.wyden.apiserver.rest.security.AccessService;
import io.wyden.apiserver.rest.security.model.WydenAuthenticationToken;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.PermissionEvaluator;
import org.springframework.security.core.Authentication;

import java.io.Serializable;
import java.util.Collection;
import javax.annotation.Nullable;

import static io.wyden.apiserver.rest.security.AccessService.User.user;

public class CustomPermissionEvaluator implements PermissionEvaluator {

    private static final Logger LOGGER = LoggerFactory.getLogger(CustomPermissionEvaluator.class);

    private final AccessService accessService;

    public CustomPermissionEvaluator(AccessService accessService) {
        this.accessService = accessService;
    }

    @Override
    public boolean hasPermission(Authentication authentication, Object resourceObject, Object scopeObject) {
        try {
            WydenAuthenticationToken token = toWydenToken(authentication);
            String resource = toResource(resourceObject);
            String scope = toScope(scopeObject);
            return accessService.hasPermission(user(token), resource, scope);
        } catch (Exception ex) {
            LOGGER.warn("Permission check failed, returning Access Denied", ex);
            return false;
        }
    }

    // TODO: Check if #portfolioId and #venueAccountId are null in constructions
    // hasPermission(#portfolioId, 'portfolio', 'trade')) && (hasPermission(#venueAccount, 'venue.account', 'trade')
    // Then remove targetId == null returning true (should return false)
    @Override
    public boolean hasPermission(Authentication authentication, @Nullable Serializable targetId, String resource, Object scopeObject) {
        try {
            if (targetId == null) {
                return true;
            } else if (targetId instanceof Collection<?> collection) {
                return collection.stream()
                    .allMatch(t -> hasPermission(authentication, toSerializable(t), resource, scopeObject));
            } else {
                WydenAuthenticationToken token = toWydenToken(authentication);
                String scope = toScope(scopeObject);
                String resourceId = String.valueOf(targetId);
                return accessService.hasPermission(user(token), resource, scope, resourceId);
            }
        } catch (Exception ex) {
            LOGGER.warn("Permission check failed, returning Access Denied", ex);
            return false;
        }
    }

    private String toResource(Object resourceObject) {
        return objectToString("Resource", resourceObject);
    }

    private String toScope(Object scopeObject) {
        return objectToString("Scope", scopeObject);
    }

    private String objectToString(String name, Object resourceObject) {
        if (resourceObject == null) {
            throw new IllegalArgumentException("%s cannot be null".formatted(name));
        } else if (resourceObject instanceof String resourceString) {
            return resourceString;
        } else {
            throw new IllegalArgumentException("%s %s is not a string".formatted(name, resourceObject.getClass().getSimpleName()));
        }
    }

    private WydenAuthenticationToken toWydenToken(Authentication authentication) {
        if (authentication == null) {
            throw new IllegalArgumentException("Authentication cannot be null");
        } else if (authentication instanceof WydenAuthenticationToken wydenAuthenticationToken) {
            return wydenAuthenticationToken;
        } else {
            throw new IllegalArgumentException("Authentication object %s is not a WydenAuthenticationToken".formatted(authentication.getClass().getSimpleName()));
        }
    }

    private Serializable toSerializable(Object object) {
        if (object == null) {
            throw new IllegalArgumentException("ResourceId cannot be null");
        } else if (object instanceof Serializable serializable) {
            return serializable;
        } else {
            throw new IllegalArgumentException("ResourceId %s is not a Serializable".formatted(object.getClass().getSimpleName()));
        }
    }
}
