package io.wyden.apiserver.rest.security;

import io.wyden.apiserver.rest.security.accessgateway.AddOrRemoveGroupPermissionsRequestDto;
import io.wyden.apiserver.rest.security.accessgateway.AddOrRemoveUserPermissionsRequestDto;
import io.wyden.apiserver.rest.security.model.AuthorityDto;
import io.wyden.apiserver.rest.security.model.WydenAuthenticationToken;
import io.wyden.apiserver.rest.utils.AccessDeniedLogger;

import graphql.schema.DataFetchingEnvironment;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Set;

import static io.wyden.accessgateway.client.permission.Permission.Scopes.MANAGE;
import static io.wyden.apiserver.rest.security.AccessService.User.user;

@Service
public class PermissionValidator {

    private static final Logger LOGGER = LoggerFactory.getLogger(PermissionValidator.class);

    private final AccessService accessService;

    public PermissionValidator(AccessService accessService) {
        this.accessService = accessService;
    }

    public boolean canChangePermissions(AddOrRemoveUserPermissionsRequestDto request, WydenAuthenticationToken token, DataFetchingEnvironment env ) {
        boolean result = canChangePermissions(request.permissions(), token, env);
        LOGGER.info("User {} trying to change permission for user {} with access status: {}", token.getClientId(), request.username(), result);
        return result;
    }

    public boolean canChangePermissions(AddOrRemoveGroupPermissionsRequestDto request, WydenAuthenticationToken token, DataFetchingEnvironment env ) {
        boolean result = canChangePermissions(request.permissions(), token, env);
        LOGGER.info("User {} trying to change permission for group {} with access status: {}", token.getClientId(), request.groupName(), result);
        return result;
    }

    private boolean canChangePermissions(Set<AuthorityDto> authorities, WydenAuthenticationToken token, DataFetchingEnvironment env) {
        return authorities.stream().allMatch(auth -> canChangePermissions(auth, token, env));
    }

    private boolean canChangePermissions(AuthorityDto authority, WydenAuthenticationToken token, DataFetchingEnvironment env) {
        String resource = authority.getResource().toAgDomain();
        String resourceId = authority.getResourceId();
        if (StringUtils.isBlank(resourceId)) {
            boolean result = accessService.hasPermission(user(token), resource, MANAGE);
            AccessDeniedLogger.logIfAccessDenied(result, env, token, resource + "." + MANAGE, "");
            return result;
        } else {
            boolean result = accessService.hasPermission(user(token), resource, MANAGE, resourceId);
            AccessDeniedLogger.logIfAccessDenied(result, env, token, resource + "." + MANAGE, resourceId);
            return result;
        }
    }
}
