package io.wyden.apiserver.rest.config.exception;

import io.wyden.apiserver.rest.security.model.WydenAuthenticationToken;

import graphql.ErrorClassification;
import graphql.GraphQLError;
import graphql.schema.DataFetchingEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.graphql.server.WebGraphQlRequest;

import java.util.Optional;

import static io.wyden.apiserver.rest.security.SecurityAndRequestContextInterceptor.GQL_REQUEST_AUTHENTICATION_KEY;
import static io.wyden.apiserver.rest.security.SecurityAndRequestContextInterceptor.GQL_REQUEST_CONTEXT_KEY;

public abstract class AbstractGqlExceptionHandler {

    protected static final Logger LOGGER = LoggerFactory.getLogger(AbstractGqlExceptionHandler.class);
    protected final ErrorClassification errorClassification;

    protected AbstractGqlExceptionHandler(ErrorClassification errorClassification) {
        this.errorClassification = errorClassification;
    }

    public abstract GraphQLError handle(Throwable ex, DataFetchingEnvironment env);

    protected Optional<WydenAuthenticationToken> getToken(DataFetchingEnvironment env) {
        return Optional.ofNullable(env.getGraphQlContext().get(GQL_REQUEST_AUTHENTICATION_KEY));
    }

    protected Optional<WebGraphQlRequest> getRequest(DataFetchingEnvironment env) {
        return Optional.ofNullable(env.getGraphQlContext().get(GQL_REQUEST_CONTEXT_KEY));
    }
}
