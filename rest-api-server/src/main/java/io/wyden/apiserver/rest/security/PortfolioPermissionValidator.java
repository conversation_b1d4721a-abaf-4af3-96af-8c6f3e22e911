package io.wyden.apiserver.rest.security;

import io.wyden.accessgateway.client.permission.PermissionCache;
import io.wyden.apiserver.rest.referencedata.portfolio.model.CreatePortfolioDto;
import io.wyden.apiserver.rest.referencedata.portfolio.model.PortfolioSearchInputDto;
import io.wyden.apiserver.rest.referencedata.portfolio.model.PortfolioTypeDto;
import io.wyden.apiserver.rest.referencedata.portfolio.model.UpdatePortfolioDto;
import io.wyden.apiserver.rest.security.model.WydenAuthenticationToken;
import io.wyden.apiserver.rest.utils.AccessDeniedLogger;

import graphql.schema.DataFetchingEnvironment;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import java.util.List;
import java.util.Optional;

import static io.wyden.accessgateway.client.permission.Permission.PORTFOLIO_CREATE;
import static io.wyden.accessgateway.client.permission.Permission.PORTFOLIO_MANAGE;
import static io.wyden.accessgateway.client.permission.Permission.PORTFOLIO_NOSTRO_CREATE;
import static io.wyden.accessgateway.client.permission.Permission.PORTFOLIO_NOSTRO_MANAGE;
import static io.wyden.accessgateway.client.permission.Permission.PORTFOLIO_NOSTRO_READ;
import static io.wyden.accessgateway.client.permission.Permission.PORTFOLIO_READ;
import static io.wyden.accessgateway.client.permission.Permission.PORTFOLIO_VOSTRO_CREATE;
import static io.wyden.accessgateway.client.permission.Permission.PORTFOLIO_VOSTRO_MANAGE;
import static io.wyden.accessgateway.client.permission.Permission.PORTFOLIO_VOSTRO_READ;
import static io.wyden.accessgateway.client.permission.Permission.Resources.PORTFOLIO;
import static io.wyden.accessgateway.client.permission.Permission.Scopes.READ;
import static io.wyden.apiserver.rest.security.AccessService.User.user;

@Component
public class PortfolioPermissionValidator {

    private final AccessService accessService;
    private final PermissionCache permissionCache;
    private final int queryLimit;

    public PortfolioPermissionValidator(AccessService accessService,
                                        PermissionCache permissionCache,
                                        @Value("${rest-api-server.query-limits.portfolio:100}") int queryLimit) {
        this.accessService = accessService;
        this.permissionCache = permissionCache;
        this.queryLimit = queryLimit;
    }

    /**
     * Filters search through permissions
     *
     * @return input with empty portfolioIds if all portfolios are permitted
     * input with filled portfolioIds if subset of portfolios are permitted
     * null if no portfolio is permitted
     */
    @Nullable
    public PortfolioSearchInputDto authorizeRequest(PortfolioSearchInputDto search, WydenAuthenticationToken token) {

        PortfolioSearchInputDto result = Optional.ofNullable(search).orElse(PortfolioSearchInputDto.empty());

        if (accessService.hasPermission(user(token), PORTFOLIO_READ)) {
            return result;
        }

        boolean hasPortfolioVostroReadStaticPermission = accessService.hasPermission(user(token), PORTFOLIO_VOSTRO_READ);
        boolean hasPortfolioNostroReadStaticPermission = accessService.hasPermission(user(token), PORTFOLIO_NOSTRO_READ);

        if (hasPortfolioVostroReadStaticPermission && hasPortfolioNostroReadStaticPermission) {
            return result;
        }

        if (hasPortfolioNostroReadStaticPermission) {
            result = result.withPortfolioType(PortfolioTypeDto.NOSTRO);
        }

        if (hasPortfolioVostroReadStaticPermission) {
            result = result.withPortfolioType(PortfolioTypeDto.VOSTRO);
        }

        if (result.portfolioIds().isEmpty()) {

            if (hasPortfolioNostroReadStaticPermission || hasPortfolioVostroReadStaticPermission) {
                return result;
            }

            List<String> authorizedPortfolioIds = permissionCache.getPermittedResourceIds(token.getGroups(), token.getClientId(), PORTFOLIO, READ, queryLimit).stream().toList();
            return authorizedPortfolioIds.isEmpty() ? null : result.withPortfolioIds(authorizedPortfolioIds);
        }

        List<String> authorizedIds = result.portfolioIds().stream()
            .filter(i -> accessService.hasPermission(user(token), PORTFOLIO_READ, i))
            .toList();
        return authorizedIds.isEmpty() ? null : result.withPortfolioIds(authorizedIds);
    }

    public boolean checkPortfolioCreate(CreatePortfolioDto request, WydenAuthenticationToken token, DataFetchingEnvironment env) {
        boolean result = accessService.hasPermission(user(token), PORTFOLIO_CREATE)
            || accessService.hasPermission(user(token), PORTFOLIO_VOSTRO_CREATE) && request.portfolioType() != null && request.portfolioType().equals(PortfolioTypeDto.VOSTRO)
            || accessService.hasPermission(user(token), PORTFOLIO_NOSTRO_CREATE) && request.portfolioType() != null && request.portfolioType().equals(PortfolioTypeDto.NOSTRO);
        AccessDeniedLogger.logIfAccessDenied(result, env, token, PORTFOLIO_CREATE,"");
        return result;
    }

    public boolean checkPortfolioUpdate(UpdatePortfolioDto request, WydenAuthenticationToken token, DataFetchingEnvironment env) {
        boolean result =  accessService.hasPermission(user(token), PORTFOLIO_MANAGE, request.id())
            || accessService.hasPermission(user(token), PORTFOLIO_VOSTRO_MANAGE) && request.portfolioType() != null && request.portfolioType().equals(PortfolioTypeDto.VOSTRO)
            || accessService.hasPermission(user(token), PORTFOLIO_NOSTRO_MANAGE) && request.portfolioType() != null && request.portfolioType().equals(PortfolioTypeDto.NOSTRO);
        AccessDeniedLogger.logIfAccessDenied(result, env, token, PORTFOLIO_MANAGE,request.id());
        return result;
    }
}
