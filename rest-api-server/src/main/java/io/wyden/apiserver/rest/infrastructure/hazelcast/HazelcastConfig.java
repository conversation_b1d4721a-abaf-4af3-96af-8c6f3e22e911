package io.wyden.apiserver.rest.infrastructure.hazelcast;

import com.hazelcast.client.HazelcastClient;
import com.hazelcast.client.config.ClientConfig;
import com.hazelcast.client.config.ClientNetworkConfig;
import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import io.wyden.accessgateway.domain.permission.PermissionGroupMapConfig;
import io.wyden.accessgateway.domain.permission.PermissionUserMapConfig;
import io.wyden.apiserver.domain.ClientRequestMapConfig;
import io.wyden.apiserver.domain.ClientToOrderIdMapConfig;
import io.wyden.apiserver.domain.OrderStateMapConfig;
import io.wyden.cloudutils.hazelcast.HazelcastMapConfig;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.published.client.ClientRequest;
import io.wyden.published.reporting.OrderState;
import io.wyden.rate.client.RateDataProvider;
import io.wyden.rate.client.RatesCacheFacade;
import io.wyden.rate.domain.map.RateMapConfig;
import io.wyden.referencedata.client.CurrencyCacheFacade;
import io.wyden.referencedata.client.InstrumentSymbolsCacheFacade;
import io.wyden.referencedata.client.InstrumentsCacheFacade;
import io.wyden.referencedata.client.PortfoliosCacheFacade;
import io.wyden.referencedata.client.ReferenceDataProvider;
import io.wyden.referencedata.client.VenueAccountCacheFacade;
import io.wyden.referencedata.client.VenuesCacheFacade;
import io.wyden.referencedata.domain.CurrencyMapConfig;
import io.wyden.referencedata.domain.InstrumentMapConfig;
import io.wyden.referencedata.domain.InstrumentSymbolMapConfig;
import io.wyden.referencedata.domain.PortfolioMapConfig;
import io.wyden.referencedata.domain.VenueAccountMapConfig;
import io.wyden.referencedata.domain.VenueMapConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.List;

import static io.wyden.cloudutils.hazelcast.NearCaches.defaultEvictionConfig;
import static io.wyden.cloudutils.hazelcast.NearCaches.defaultNearCacheConfig;

@Configuration
public class HazelcastConfig {

    @Bean
    IMap<String, String> orderIdsMap(HazelcastInstance hazelcast) {
        return ClientToOrderIdMapConfig.getMap(hazelcast);
    }

    @Bean
    IMap<String, OrderState> orderStateCache(HazelcastInstance hazelcast) {
        return OrderStateMapConfig.getMap(hazelcast);
    }

    @Bean
    IMap<String, ClientRequest> clientRequestMap(HazelcastInstance hazelcast) {
        return ClientRequestMapConfig.getMap(hazelcast);
    }

    @Bean
    public ClientConfig createClientConfig(@Value("${hz.addressList}") String addressList,
                                           @Value("${hz.outboundPortDefinition}") String outboundPortDefinition,
                                           List<HazelcastMapConfig> hazelcastMaps,
                                           InstrumentMapConfig instrumentMapConfig,
                                           VenueMapConfig venueMapConfig,
                                           PermissionUserMapConfig permissionUserMapConfig,
                                           PermissionGroupMapConfig permissionGroupMapConfig) {

        ClientConfig clientConfig = new ClientConfig();
        clientConfig.getConnectionStrategyConfig().getConnectionRetryConfig().setMaxBackoffMillis(5000);

        ClientNetworkConfig networkConfig = clientConfig.getNetworkConfig();
        Arrays.stream(addressList.split(",")).forEach(networkConfig::addAddress);
        networkConfig.setSmartRouting(true);

        if (!outboundPortDefinition.isBlank()) {
            networkConfig.addOutboundPortDefinition(outboundPortDefinition);
        }

        networkConfig.setRedoOperation(true);
        networkConfig.setConnectionTimeout(5000);

        hazelcastMaps.forEach(m -> m.applyConfig(clientConfig));

        instrumentMapConfig.addNearCache(clientConfig, defaultNearCacheConfig(defaultEvictionConfig()));
        venueMapConfig.addNearCache(clientConfig, defaultNearCacheConfig(defaultEvictionConfig()));
        permissionUserMapConfig.addNearCache(clientConfig, defaultNearCacheConfig(defaultEvictionConfig()));
        permissionGroupMapConfig.addNearCache(clientConfig, defaultNearCacheConfig(defaultEvictionConfig()));

        return clientConfig;
    }

    @Bean("hazelcast")
    public HazelcastInstance createHazelcastInstance(ClientConfig clientConfig, Telemetry telemetry, List<HazelcastMapConfig> hazelcastMaps) {
        HazelcastInstance hz = HazelcastClient.newHazelcastClient(clientConfig);
        hazelcastMaps.forEach(m -> m.setupClientInstance(hz));
        hazelcastMaps.forEach(m -> m.setupMetrics(hz, telemetry.getMeterRegistry()));
        return hz;
    }

    @Bean
    public InstrumentsCacheFacade instrumentsCacheFacade(HazelcastInstance hazelcast,
                                                         Tracing otlTracing) {
        return ReferenceDataProvider.getInstrumentsCacheFacade(hazelcast, otlTracing);
    }

    @Bean
    public InstrumentSymbolsCacheFacade instrumentSymbolsCacheFacade(VenueAccountCacheFacade venueAccountCacheFacade,
                                                                     InstrumentsCacheFacade instrumentsCacheFacade,
                                                                     HazelcastInstance hazelcast,
                                                                     Tracing otlTracing) {
        return ReferenceDataProvider.getInstrumentSymbolsCacheFacade(venueAccountCacheFacade, instrumentsCacheFacade, hazelcast, otlTracing);
    }

    @Bean
    public PortfoliosCacheFacade portfoliosCacheFacade(HazelcastInstance hazelcast,
                                                       Tracing otlTracing) {
        return ReferenceDataProvider.getPortfoliosCacheFacade(hazelcast, otlTracing);
    }

    @Bean
    public VenuesCacheFacade venuesCacheFacade(HazelcastInstance hazelcast,
                                               Tracing otlTracing) {

        return ReferenceDataProvider.getVenuesCacheFacade(hazelcast, otlTracing);
    }

    @Bean
    public VenueAccountCacheFacade venueAccountCacheFacade(HazelcastInstance hazelcast,
                                                                 Tracing otlTracing) {
        return ReferenceDataProvider.getVenueAccountCacheFacade(hazelcast, otlTracing);
    }

    @Bean
    public CurrencyCacheFacade currencyCacheFacade(HazelcastInstance hazelcast,
                                                       Tracing otlTracing) {
        return ReferenceDataProvider.getCurrencyCacheFacade(hazelcast, otlTracing);
    }

    @Bean
    public RatesCacheFacade ratesCacheFacade(HazelcastInstance hazelcast,
                                             Tracing otlTracing) {
        return RateDataProvider.getRatesCacheFacade(hazelcast, otlTracing);
    }

    @Bean
    public OrderStateMapConfig orderStateMapConfig() {
        return new OrderStateMapConfig();
    }

    @Bean
    public ClientToOrderIdMapConfig clientToOrderIdMapConfig() {
        return new ClientToOrderIdMapConfig();
    }

    @Bean
    public VenueMapConfig venueMapConfig() {
        return new VenueMapConfig();
    }

    @Bean
    public VenueAccountMapConfig venueAccountMapConfig() {return new VenueAccountMapConfig();}

    @Bean
    public InstrumentMapConfig instrumentMapConfig() {
        return new InstrumentMapConfig();
    }

    @Bean
    public InstrumentSymbolMapConfig instrumentSymbolMapConfig() {
        return new InstrumentSymbolMapConfig();
    }

    @Bean
    public PortfolioMapConfig portfolioMapConfig() {
        return new PortfolioMapConfig();
    }

    @Bean
    public CurrencyMapConfig currencyMapConfig() {
        return new CurrencyMapConfig();
    }

    @Bean
    public ClientRequestMapConfig clientRequestMapConfig() {
        return new ClientRequestMapConfig();
    }

    @Bean
    public RateMapConfig rateMapConfig() {
        return new RateMapConfig();
    }
}
