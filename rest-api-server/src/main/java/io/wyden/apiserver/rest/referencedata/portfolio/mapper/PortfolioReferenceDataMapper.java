package io.wyden.apiserver.rest.referencedata.portfolio.mapper;

import io.wyden.apiserver.rest.referencedata.portfolio.model.PortfolioPredicateInputDto;
import io.wyden.apiserver.rest.referencedata.portfolio.model.PortfolioSearchInputDto;
import io.wyden.apiserver.rest.referencedata.portfolio.model.PortfolioSortBy;
import io.wyden.referencedata.domain.model.GetPortfoliosRequestDto;
import io.wyden.referencedata.domain.model.PortfolioPredicateDto;
import io.wyden.referencedata.domain.model.PortfolioPredicateTypeDto;
import org.jetbrains.annotations.NotNull;

import java.util.List;
import java.util.Objects;

import static io.wyden.apiserver.rest.referencedata.instruments.mapper.InstrumentReferenceDataMapper.toSortingOrderDTO;
import static io.wyden.apiserver.rest.referencedata.portfolio.mapper.PortfolioChangeMapper.convertPortfolioType;

public class PortfolioReferenceDataMapper {

    @NotNull
    public static GetPortfoliosRequestDto toGetPortfoliosRequest(PortfolioSearchInputDto search) {
        return new GetPortfoliosRequestDto(
            map(search.portfolioPredicate()),
            search.portfolioIds(),
            List.of(),
            search.tagValues(),
            convertPortfolioType(search.portfolioType(), null),
            search.archived(),
            toSortByDTO(search.sortBy()),
            toSortingOrderDTO(search.sortingOrder()),
            search.first(),
            search.after());
    }

    private static PortfolioPredicateDto map(PortfolioPredicateInputDto input) {
        if (Objects.nonNull(input) && Objects.nonNull(input.method()) && Objects.nonNull(input.searchType()) && Objects.nonNull(input.value())) {
            return new PortfolioPredicateDto(PortfolioPredicateTypeDto.valueOf(input.method().toString()),
                io.wyden.referencedata.domain.model.PortfolioSearchTypeDto.valueOf(input.searchType().toString()), input.value());
        }
        return null;
    }

    private static GetPortfoliosRequestDto.SortBy toSortByDTO(PortfolioSortBy sortBy) {
        if (sortBy == null) {
            return GetPortfoliosRequestDto.SortBy.PORTFOLIO_NAME;
        }
        return switch (sortBy) {
            case PORTFOLIO_NAME -> GetPortfoliosRequestDto.SortBy.PORTFOLIO_NAME;
            case PORTFOLIO_ID -> GetPortfoliosRequestDto.SortBy.PORTFOLIO_ID;
            case CREATED_AT -> GetPortfoliosRequestDto.SortBy.CREATED_AT;
        };
    }
}
