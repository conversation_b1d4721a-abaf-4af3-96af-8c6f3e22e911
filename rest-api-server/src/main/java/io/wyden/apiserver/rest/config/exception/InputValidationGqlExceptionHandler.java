package io.wyden.apiserver.rest.config.exception;

import io.wyden.apiserver.rest.common.InputValidationException;

import graphql.ErrorClassification;
import graphql.GraphQLError;
import graphql.GraphqlErrorBuilder;
import graphql.schema.DataFetchingEnvironment;

import java.util.Map;

public class InputValidationGqlExceptionHandler extends AbstractGqlExceptionHandler {

    protected InputValidationGqlExceptionHandler(ErrorClassification errorClassification) {
        super(errorClassification);
    }

    @Override
    public GraphQLError handle(Throwable ex, DataFetchingEnvironment env) {

        LOGGER.error(ex.getMessage(), ex);

        InputValidationException e = (InputValidationException)ex;
        Map<String, Object> extensions = Map.of("validation", e.getFieldErrors());

        return GraphqlErrorBuilder.newError()
            .errorType(errorClassification)
            .message(ex.getMessage())
            .path(env.getExecutionStepInfo().getPath())
            .location(env.getField().getSourceLocation())
            .extensions(extensions)
            .build();
    }
}
