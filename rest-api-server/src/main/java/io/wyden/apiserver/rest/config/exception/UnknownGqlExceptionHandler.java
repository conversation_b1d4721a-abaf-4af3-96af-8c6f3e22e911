package io.wyden.apiserver.rest.config.exception;

import graphql.GraphQLError;
import graphql.schema.DataFetchingEnvironment;

public class UnknownGqlExceptionHandler extends AbstractGqlExceptionHandler {

    protected UnknownGqlExceptionHandler() {
        super(null);
    }

    @Override
    public GraphQLError handle(Throwable ex, DataFetchingEnvironment env) {
        LOGGER.error("Caught unhandled exception in GQL Controller - propagating to user as INTERNAL_ERROR. Root: {}", ex.getMessage());
        return null;
    }
}
