package io.wyden.apiserver.rest.utils;

import io.wyden.accessgateway.client.permission.Permission;
import io.wyden.apiserver.rest.security.model.WydenAuthenticationToken;

import graphql.schema.DataFetchingEnvironment;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

public class AccessDeniedLogger {

    private AccessDeniedLogger() { //hide
    }

    private static final Logger LOGGER = LoggerFactory.getLogger(AccessDeniedLogger.class);


    public static void logIfAccessDenied(boolean permissionDecision, DataFetchingEnvironment env, WydenAuthenticationToken token, Permission permission, String resource) {
        logIfAccessDenied(permissionDecision, env, token.getClientId(), permission, resource);
    }

    public static void logIfAccessDenied(boolean permissionDecision, DataFetchingEnvironment env, WydenAuthenticationToken token, Permission permission, List<String> resources) {
        String resourcesStr = "[" + String.join(",", resources) + "]";
        logIfAccessDenied(permissionDecision, env, token.getClientId(), permission, resourcesStr);
    }

    public static void logIfAccessDenied(boolean permissionDecision, DataFetchingEnvironment env, WydenAuthenticationToken token, String permission, String resource) {
        logIfAccessDenied(permissionDecision, env, token.getClientId(), permission, resource);
    }

    public static void logIfAccessDenied(boolean permissionDecision, DataFetchingEnvironment env, String userName, Permission permission, String resource) {
        String perm = permission.getResource() + "." + permission.getScope();
        logIfAccessDenied(permissionDecision, env, userName, perm, resource);
    }

    private static void logIfAccessDenied(boolean permissionDecision, DataFetchingEnvironment env, String userName, String permission, String resource) {
        if (!permissionDecision) {
            String path = env != null ? env.getExecutionStepInfo().getPath().toString() : "";
            if (StringUtils.isEmpty(resource)) {
                LOGGER.warn("Access denied for user: {}, path: {}, error: missing {} for resource {}", userName, path, permission, resource);
            } else {
                LOGGER.warn("Access denied for user: {}, path: {}, error: missing {} ", userName, path, permission);
            }
        }
    }
}
