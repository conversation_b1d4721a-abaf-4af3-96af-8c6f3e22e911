package io.wyden.apiserver.rest.security;

import io.wyden.apiserver.rest.referencedata.portfolio.service.PortfolioRepository;
import io.wyden.apiserver.rest.referencedata.venueaccount.VenueAccountRepository;
import io.wyden.apiserver.rest.security.model.WydenAuthenticationToken;
import io.wyden.apiserver.rest.trading.model.NewOrderSingleRequest;
import io.wyden.apiserver.rest.trading.model.OrderCancelReplaceRequest;
import io.wyden.apiserver.rest.utils.AccessDeniedLogger;
import io.wyden.published.referencedata.AccountType;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.published.referencedata.PortfolioType;
import io.wyden.published.referencedata.VenueAccount;
import io.wyden.published.referencedata.WalletType;

import graphql.schema.DataFetchingEnvironment;
import io.micrometer.common.util.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static io.wyden.accessgateway.client.permission.Permission.PORTFOLIO_NOSTRO_READ;
import static io.wyden.accessgateway.client.permission.Permission.PORTFOLIO_NOSTRO_TRADE;
import static io.wyden.accessgateway.client.permission.Permission.PORTFOLIO_READ;
import static io.wyden.accessgateway.client.permission.Permission.PORTFOLIO_TRADE;
import static io.wyden.accessgateway.client.permission.Permission.PORTFOLIO_VOSTRO_READ;
import static io.wyden.accessgateway.client.permission.Permission.PORTFOLIO_VOSTRO_TRADE;
import static io.wyden.accessgateway.client.permission.Permission.VENUE_ACCOUNT_READ;
import static io.wyden.accessgateway.client.permission.Permission.VENUE_ACCOUNT_TRADE;
import static io.wyden.accessgateway.client.permission.Permission.WALLET_NOSTRO_READ;
import static io.wyden.accessgateway.client.permission.Permission.WALLET_NOSTRO_TRADE;
import static io.wyden.accessgateway.client.permission.Permission.WALLET_READ;
import static io.wyden.accessgateway.client.permission.Permission.WALLET_TRADE;
import static io.wyden.accessgateway.client.permission.Permission.WALLET_VOSTRO_READ;
import static io.wyden.accessgateway.client.permission.Permission.WALLET_VOSTRO_TRADE;
import static io.wyden.apiserver.rest.security.AccessService.User.user;

@Component("tradingPermissionValidator")
public class TradingPermissionValidator {

    private final AccessService accessService;
    private final PortfolioRepository portfolioRepository;
    private final VenueAccountRepository accountRepository;


    public TradingPermissionValidator(AccessService accessService,
                                      PortfolioRepository portfolioRepository,
                                      VenueAccountRepository accountRepository) {
        this.accessService = accessService;
        this.portfolioRepository = portfolioRepository;
        this.accountRepository = accountRepository;
    }

    public boolean checkTradePermissions(NewOrderSingleRequest request, WydenAuthenticationToken token, DataFetchingEnvironment env) {
        boolean hasPortfolioPermission = StringUtils.isEmpty(request.getPortfolioId())
            || accessService.hasPermission(user(token), PORTFOLIO_TRADE, request.getPortfolioId())
            || hasTradePermissionToNostroVostroPortfolio(user(token), request.getPortfolioId());
        AccessDeniedLogger.logIfAccessDenied(hasPortfolioPermission, env, token, PORTFOLIO_TRADE, request.getPortfolioId());

        boolean hasAccountPermission = accessService.hasPermission(user(token), VENUE_ACCOUNT_TRADE, request.getVenueAccounts())
            || request.getVenueAccounts().stream().allMatch(id -> hasTradePermissionToNostroVostroWallet(user(token), id));
        AccessDeniedLogger.logIfAccessDenied(hasAccountPermission, env, token, VENUE_ACCOUNT_TRADE,request.getVenueAccounts());

        return hasPortfolioPermission && hasAccountPermission;
    }

    public boolean checkCancelReplaceOrderPermissions(OrderCancelReplaceRequest request, WydenAuthenticationToken token, DataFetchingEnvironment env) {
        boolean hasPortfolioPermission = accessService.hasPermission(user(token), PORTFOLIO_TRADE, request.replacingOrder().getPortfolioId())
            || hasTradePermissionToNostroVostroPortfolio(user(token), request.replacingOrder().getPortfolioId());
        AccessDeniedLogger.logIfAccessDenied(hasPortfolioPermission, env, token, PORTFOLIO_TRADE, request.replacingOrder().getPortfolioId());

        boolean hasAccountPermission = accessService.hasPermission(user(token), VENUE_ACCOUNT_TRADE, request.replacingOrder().getVenueAccounts())
            || request.replacingOrder().getVenueAccounts().stream().allMatch(id -> hasTradePermissionToNostroVostroWallet(user(token), id));
        AccessDeniedLogger.logIfAccessDenied(hasAccountPermission, env, token, VENUE_ACCOUNT_TRADE, request.replacingOrder().getVenueAccounts());

        return hasPortfolioPermission && hasAccountPermission;
    }

    public boolean checkSubscribePermissions(String portfolioId, String venueAccount, WydenAuthenticationToken token, DataFetchingEnvironment env) {
        return checkSubscribePermissions(portfolioId, venueAccount, user(token), env);
    }

    public boolean checkSubscribePermissions(String portfolioId, String venueAccount, AccessService.User user, DataFetchingEnvironment env) {
        boolean hasPortfolioPermission = StringUtils.isEmpty(portfolioId)
            || accessService.hasPermission(user, PORTFOLIO_READ, portfolioId)
            || hasReadPermissionToNostroVostroPortfolio(user, portfolioId);
        AccessDeniedLogger.logIfAccessDenied(hasPortfolioPermission, env, user.clientId(), PORTFOLIO_READ, portfolioId);

        boolean hasAccountPermission = StringUtils.isEmpty(venueAccount)
            || accessService.hasPermission(user, VENUE_ACCOUNT_READ, venueAccount)
            || hasReadPermissionToNostroVostroWallet(user, venueAccount);
        AccessDeniedLogger.logIfAccessDenied(hasAccountPermission, env, user.clientId(), VENUE_ACCOUNT_READ, venueAccount);

        return hasPortfolioPermission && hasAccountPermission;
    }

    private boolean hasTradePermissionToNostroVostroPortfolio(AccessService.User user, String portfolioId) {
        Optional<Portfolio> portfolio = portfolioRepository.find(portfolioId);
        if (portfolio.isEmpty()) {
            return false;
        }
        PortfolioType portfolioType = portfolio.get().getPortfolioType();
        return switch (portfolioType) {
            case VOSTRO -> accessService.hasPermission(user, PORTFOLIO_VOSTRO_TRADE);
            case NOSTRO -> accessService.hasPermission(user, PORTFOLIO_NOSTRO_TRADE);
            default -> false;
        };
    }

    private boolean hasReadPermissionToNostroVostroPortfolio(AccessService.User user, String portfolioId) {
        Optional<Portfolio> portfolio = portfolioRepository.find(portfolioId);
        if (portfolio.isEmpty()) {
            return false;
        }
        PortfolioType portfolioType = portfolio.get().getPortfolioType();
        return switch (portfolioType) {
            case VOSTRO -> accessService.hasPermission(user, PORTFOLIO_VOSTRO_READ);
            case NOSTRO -> accessService.hasPermission(user, PORTFOLIO_NOSTRO_READ);
            default -> false;
        };
    }

    private boolean hasTradePermissionToNostroVostroWallet(AccessService.User user, String accountId) {
        Optional<VenueAccount> account = accountRepository.find(accountId);
        if (account.isEmpty()) {
            return false;
        }
        AccountType accountType = account.get().getAccountType();
        if (AccountType.WALLET.equals(accountType)) {
            if (accessService.hasPermission(user, WALLET_TRADE)) {
                return true;
            }
            WalletType walletType = account.get().getWalletType();
            return switch (walletType) {
                case WALLET_VOSTRO -> accessService.hasPermission(user, WALLET_VOSTRO_TRADE);
                case WALLET_NOSTRO -> accessService.hasPermission(user, WALLET_NOSTRO_TRADE);
                default -> false;
            };
        }

        return false;
    }

    private boolean hasReadPermissionToNostroVostroWallet(AccessService.User user, String accountId) {
        Optional<VenueAccount> account = accountRepository.find(accountId);
        if (account.isEmpty()) {
            return false;
        }
        AccountType accountType = account.get().getAccountType();
        if (AccountType.WALLET.equals(accountType)) {
            if (accessService.hasPermission(user, WALLET_READ)) {
                return true;
            }
            WalletType walletType = account.get().getWalletType();
            return switch (walletType) {
                case WALLET_VOSTRO -> accessService.hasPermission(user, WALLET_VOSTRO_READ);
                case WALLET_NOSTRO -> accessService.hasPermission(user, WALLET_NOSTRO_READ);
                default -> false;
            };
        }

        return false;
    }
}
