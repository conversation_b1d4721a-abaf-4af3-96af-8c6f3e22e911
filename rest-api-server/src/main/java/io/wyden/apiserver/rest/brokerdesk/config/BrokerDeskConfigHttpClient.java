package io.wyden.apiserver.rest.brokerdesk.config;

import com.google.protobuf.Message;
import io.wyden.apiserver.rest.brokerdesk.BaseHttpClient;
import io.wyden.apiserver.rest.brokerdesk.effectivevenueaccounts.EffectiveVenueAccounts;
import io.wyden.apiserver.rest.infrastructure.webclient.WebClientInstance;
import io.wyden.published.brokerdesk.AutoHedgerValidation;
import io.wyden.published.brokerdesk.ExecutionConfig;
import io.wyden.published.brokerdesk.HedgingConfig;
import io.wyden.published.brokerdesk.InstrumentConfig;
import io.wyden.published.brokerdesk.PortfolioConfig;
import io.wyden.published.brokerdesk.PortfolioConfigFlatList;
import io.wyden.published.brokerdesk.PortfolioConfigSnapshot;
import io.wyden.published.brokerdesk.PortfolioGroupConfig;
import io.wyden.published.brokerdesk.PortfolioGroupConfigFlatList;
import io.wyden.published.brokerdesk.PricingConfig;
import io.wyden.published.brokerdesk.QuotingConfig;
import io.wyden.published.brokerdesk.Validations;
import jakarta.annotation.Nullable;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.util.UriComponentsBuilder;
import reactor.core.publisher.Mono;

@Component
public class BrokerDeskConfigHttpClient extends BaseHttpClient {

    private final String getPortfolioListUrl;
    private final String getPortfolioGroupListUrl;
    private final String getPortfolioConfigUrl;
    private final String getPortfolioGroupConfigUrl;
    private final String updatePortfolioInstrumentConfigUrl;
    private final String validatePortfolioConfigUrl;
    private final String validateAutoHedgerConfigUrl;
    private final String updatePortfolioGroupExecutionConfigUrl;
    private final String updatePortfolioGroupPricingConfigUrl;
    private final String updatePortfolioGroupHedgingConfigUrl;
    private final String updatePortfolioGroupInstrumentConfigUrl;
    private final String updatePortfolioQuotingConfigUrl;
    private final String updatePortfolioExecutionConfigUrl;
    private final String updatePortfolioPricingConfigUrl;
    private final String updatePortfolioHedgingConfigUrl;
    private final String updatePortfolioConfigUrl;
    private final String updatePortfolioGroupConfigUrl;
    private final String deletePortfolioConfigUrl;
    private final String deletePortfolioGroupConfigUrl;
    private final String resetConfigUrl;
    private final String effectiveVenueAccountsUrl;

    public BrokerDeskConfigHttpClient(WebClientInstance webClientInstance,
                                      @Value("${broker.config.service.get-config-list-portfolio}") String getPortfolioListUrl,
                                      @Value("${broker.config.service.get-config-portfolio}") String getPortfolioConfigUrl,
                                      @Value("${broker.config.service.update-config-portfolio}") String updatePortfolioConfigUrl,
                                      @Value("${broker.config.service.update-config-portfolio-quoting}") String updatePortfolioQuotingConfigUrl,
                                      @Value("${broker.config.service.update-config-portfolio-execution}") String updatePortfolioExecutionConfigUrl,
                                      @Value("${broker.config.service.update-config-portfolio-pricing}") String updatePortfolioPricingConfigUrl,
                                      @Value("${broker.config.service.update-config-portfolio-hedging}") String updatePortfolioHedgingConfigUrl,
                                      @Value("${broker.config.service.update-config-portfolio-instrument}") String updatePortfolioInstrumentConfigUrl,
                                      @Value("${broker.config.service.validate-config-portfolio-instrument}") String validatePortfolioConfigUrl,
                                      @Value("${broker.config.service.validate-auto-hedger-config}") String validateAutoHedgerConfigUrl,
                                      @Value("${broker.config.service.get-config-list-portfolio-group}") String getPortfolioGroupListUrl,
                                      @Value("${broker.config.service.get-config-portfolio-group}") String getPortfolioGroupConfigUrl,
                                      @Value("${broker.config.service.update-config-portfolio-group}") String updatePortfolioGroupConfigUrl,
                                      @Value("${broker.config.service.update-config-portfolio-group-execution}") String updatePortfolioGroupExecutionConfigUrl,
                                      @Value("${broker.config.service.update-config-portfolio-group-pricing}") String updatePortfolioGroupPricingConfigUrl,
                                      @Value("${broker.config.service.update-config-portfolio-group-hedging}") String updatePortfolioGroupHedgingConfigUrl,
                                      @Value("${broker.config.service.update-config-group-instrument}") String updatePortfolioGroupInstrumentConfigUrl,
                                      @Value("${broker.config.service.delete-portfolio-config}") String deletePortfolioConfigUrl,
                                      @Value("${broker.config.service.delete-portfolio-group-config}") String deletePortfolioGroupConfigUrl,
                                      @Value("${broker.config.service.reset-config}") String resetConfigUrl,
                                      @Value("${broker.config.service.effective-venue-accounts}") String effectiveVenueAccountsUrl
    ) {
        super(webClientInstance.getWebClient());

        this.getPortfolioListUrl = getPortfolioListUrl;
        this.getPortfolioConfigUrl = getPortfolioConfigUrl;
        this.updatePortfolioConfigUrl = updatePortfolioConfigUrl;
        this.updatePortfolioQuotingConfigUrl = updatePortfolioQuotingConfigUrl;
        this.updatePortfolioExecutionConfigUrl = updatePortfolioExecutionConfigUrl;
        this.updatePortfolioPricingConfigUrl = updatePortfolioPricingConfigUrl;
        this.updatePortfolioHedgingConfigUrl = updatePortfolioHedgingConfigUrl;
        this.updatePortfolioInstrumentConfigUrl = updatePortfolioInstrumentConfigUrl;
        this.validatePortfolioConfigUrl = validatePortfolioConfigUrl;
        this.validateAutoHedgerConfigUrl = validateAutoHedgerConfigUrl;

        this.getPortfolioGroupListUrl = getPortfolioGroupListUrl;
        this.getPortfolioGroupConfigUrl = getPortfolioGroupConfigUrl;
        this.updatePortfolioGroupConfigUrl = updatePortfolioGroupConfigUrl;
        this.updatePortfolioGroupExecutionConfigUrl = updatePortfolioGroupExecutionConfigUrl;
        this.updatePortfolioGroupPricingConfigUrl = updatePortfolioGroupPricingConfigUrl;
        this.updatePortfolioGroupHedgingConfigUrl = updatePortfolioGroupHedgingConfigUrl;
        this.updatePortfolioGroupInstrumentConfigUrl = updatePortfolioGroupInstrumentConfigUrl;
        this.deletePortfolioConfigUrl = deletePortfolioConfigUrl;
        this.deletePortfolioGroupConfigUrl = deletePortfolioGroupConfigUrl;
        this.resetConfigUrl = resetConfigUrl;
        this.effectiveVenueAccountsUrl = effectiveVenueAccountsUrl;
    }

    public Mono<PortfolioConfigFlatList> getPortfolioConfigList() {
        return getResourceEntity(getPortfolioListUrl, PortfolioConfigFlatList.class);
    }

    public Mono<PortfolioGroupConfigFlatList> getPortfolioGroupConfigList() {
        return getResourceEntity(getPortfolioGroupListUrl, PortfolioGroupConfigFlatList.class);
    }

    public Mono<PortfolioConfigSnapshot> getPortfolioConfig(String portfolioId) {
        return getConfig(getPortfolioConfigUrl, portfolioId, PortfolioConfigSnapshot.class);
    }

    public Mono<PortfolioGroupConfig> getPortfolioGroupConfig(String portfolioGroupId) {
        return getConfig(getPortfolioGroupConfigUrl, portfolioGroupId, PortfolioGroupConfig.class);
    }

    public Mono<String> updatePortfolioExecutionConfig(String portfolioId, ExecutionConfig request) {
        return updateConfig(updatePortfolioExecutionConfigUrl, portfolioId, request);
    }

    public Mono<String> updatePortfolioGroupExecutionConfig(String portfolioGroupId, ExecutionConfig request) {
        return updateConfig(updatePortfolioGroupExecutionConfigUrl, portfolioGroupId, request);
    }

    public Mono<String> updatePortfolioPricingConfig(String portfolioId, PricingConfig request) {
        return updateConfig(updatePortfolioPricingConfigUrl, portfolioId, request);
    }

    public Mono<String> updatePortfolioGroupPricingConfig(String portfolioGroupId, PricingConfig request) {
        return updateConfig(updatePortfolioGroupPricingConfigUrl, portfolioGroupId, request);
    }

    public Mono<String> updatePortfolioQuotingConfig(String portfolioId, QuotingConfig request) {
        return updateConfig(updatePortfolioQuotingConfigUrl, portfolioId, request);
    }

    public Mono<String> updatePortfolioHedgingConfig(String portfolioId, HedgingConfig request) {
        return updateConfig(updatePortfolioHedgingConfigUrl, portfolioId, request);
    }

    public Mono<String> updatePortfolioGroupHedgingConfig(String portfolioGroupId, HedgingConfig request) {
        return updateConfig(updatePortfolioGroupHedgingConfigUrl, portfolioGroupId, request);
    }

    public Mono<String> updatePortfolioInstrumentConfig(String portfolioId, InstrumentConfig request) {
        return updateConfig(updatePortfolioInstrumentConfigUrl, portfolioId, request);
    }

    public Mono<Validations> validatePortfolioInstrumentConfig(String portfolioId, String instrumentId) {
        return validateConfig(validatePortfolioConfigUrl, portfolioId, instrumentId);
    }

    public Mono<String> updatePortfolioGroupInstrumentConfig(String portfolioGroupId, InstrumentConfig request) {
        return updateConfig(updatePortfolioGroupInstrumentConfigUrl, portfolioGroupId, request);
    }

    public Mono<String> updatePortfolioConfig(String portfolioId, PortfolioConfig request) {
        return updateConfig(updatePortfolioConfigUrl, portfolioId, request);
    }

    public Mono<Validations> validatePortfolioConfig(String portfolioId) {
        return validateConfig(validatePortfolioConfigUrl, portfolioId, null);
    }

    public Mono<AutoHedgerValidation> validateAutoHedgerConfig(String portfolioId, HedgingConfig hedgingConfig) {
        return validateAutoHedgerConfig(validateAutoHedgerConfigUrl, portfolioId, hedgingConfig);
    }

    public Mono<String> updatePortfolioGroupConfig(String portfolioGroupId, PortfolioGroupConfig request) {
        return updateConfig(updatePortfolioGroupConfigUrl, portfolioGroupId, request);
    }

    public Mono<Void> deletePortfolioConfiguration(String portfolioId) {
        return deleteConfig(deletePortfolioConfigUrl, portfolioId);
    }

    public Mono<Void> deletePortfolioGroupConfiguration(String portfolioId) {
        return deleteConfig(deletePortfolioGroupConfigUrl, portfolioId);
    }

    public Mono<String> resetConfig(BrokerDeskConfigModel.ResetConfigurationInput request) {
        return resetConfig(resetConfigUrl, request);
    }

    public Mono<EffectiveVenueAccounts> getEffectiveVenueAccounts(String portfolioId, String instrumentId) {
        String url = UriComponentsBuilder.fromHttpUrl(effectiveVenueAccountsUrl)
            .queryParam("portfolioId", portfolioId)
            .queryParam("instrumentId", instrumentId)
            .toUriString();

        return getResourceEntity(url, EffectiveVenueAccounts.class);
    }

    public <T> Mono<T> getConfig(String urlTemplate, String id, Class<T> clazz) {
        String url = UriComponentsBuilder.fromHttpUrl(urlTemplate)
            .buildAndExpand(id)
            .toUriString();

        return getResourceEntity(url, clazz);
    }

    public Mono<String> updateConfig(String urlTemplate, String id, Message request) {
        String url = UriComponentsBuilder.fromHttpUrl(urlTemplate)
            .buildAndExpand(id)
            .toUriString();

        return createResource(url, request);
    }

    public Mono<Void> deleteConfig(String urlTemplate, String id) {
        String url = UriComponentsBuilder.fromHttpUrl(urlTemplate)
            .buildAndExpand(id)
            .toUriString();

        return deleteResource(url).mapNotNull(HttpEntity::getBody);
    }

    public Mono<String> resetConfig(String urlTemplate, BrokerDeskConfigModel.ResetConfigurationInput request) {
        String url = UriComponentsBuilder.fromHttpUrl(urlTemplate)
            .toUriString();

        return createResource(url, request);
    }

    private Mono<Validations> validateConfig(String urlTemplate, String portfolioId, @Nullable String instrumentId) {
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(urlTemplate)
            .queryParam("portfolioId", portfolioId);

        if (instrumentId != null) {
            builder.queryParam("instrumentId", instrumentId);
        }

        return getResourceEntity(builder.toUriString(), Validations.class, MediaType.APPLICATION_PROTOBUF);
    }

    private Mono<AutoHedgerValidation> validateAutoHedgerConfig(String urlTemplate, String portfolioId, HedgingConfig hedgingConfig) {
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(urlTemplate)
            .queryParam("portfolioId", portfolioId);

        return postResourceEntity(builder.toUriString(), hedgingConfig, AutoHedgerValidation.class, MediaType.APPLICATION_PROTOBUF);
    }
}
