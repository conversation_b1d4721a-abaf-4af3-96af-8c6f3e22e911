package io.wyden.apiserver.rest.apiui;

import io.wyden.apiserver.rest.referencedata.exception.FieldValidationException;
import io.wyden.apiserver.rest.referencedata.instruments.model.dto.ResponseStatusDto;
import io.wyden.apiserver.rest.referencedata.portfolio.mapper.PortfolioEntityMapper;
import io.wyden.apiserver.rest.referencedata.portfolio.model.AggregatedPortfolioTag;
import io.wyden.apiserver.rest.referencedata.portfolio.model.CreatePortfolioDto;
import io.wyden.apiserver.rest.referencedata.portfolio.model.PortfolioResponseDto;
import io.wyden.apiserver.rest.referencedata.portfolio.model.PortfolioSearchInputDto;
import io.wyden.apiserver.rest.referencedata.portfolio.model.PortfolioTypeDto;
import io.wyden.apiserver.rest.referencedata.portfolio.model.UpdatePortfolioDto;
import io.wyden.apiserver.rest.referencedata.portfolio.service.PortfolioService;
import io.wyden.apiserver.rest.referencedata.portfolio.service.SecuredPortfolioService;
import io.wyden.apiserver.rest.security.AccessService;
import io.wyden.apiserver.rest.security.PortfolioPermissionValidator;
import io.wyden.apiserver.rest.security.model.Scope;
import io.wyden.apiserver.rest.security.model.WydenAuthenticationToken;
import io.wyden.apiserver.rest.venueaccount.model.ValidationResponse;
import io.wyden.cloud.utils.rest.pagination.PaginationModel;
import io.wyden.cloud.utils.rest.pagination.PaginationProtoToModelMapper;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.referencedata.client.PortfoliosCacheFacade;

import com.google.common.collect.Sets;
import graphql.schema.DataFetchingEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.graphql.data.method.annotation.Argument;
import org.springframework.graphql.data.method.annotation.MutationMapping;
import org.springframework.graphql.data.method.annotation.QueryMapping;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import reactor.core.publisher.Mono;

import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static io.wyden.accessgateway.client.permission.Permission.PORTFOLIO_READ;
import static io.wyden.accessgateway.client.permission.Permission.Resources.PORTFOLIO;
import static io.wyden.accessgateway.client.permission.Permission.Resources.PORTFOLIO_NOSTRO;
import static io.wyden.accessgateway.client.permission.Permission.Resources.PORTFOLIO_VOSTRO;
import static io.wyden.apiserver.rest.security.AccessService.User.user;

@Controller
public class PortfolioGraphQLController {

    private static final Logger LOGGER = LoggerFactory.getLogger(PortfolioGraphQLController.class);

    private final AccessService accessService;
    private final PortfolioService portfolioService;
    private final SecuredPortfolioService securedPortfolioService;
    private final PortfoliosCacheFacade portfoliosCacheFacade;
    private final PortfolioPermissionValidator portfolioPermissionValidator;

    public PortfolioGraphQLController(AccessService accessService,
                                      PortfolioService portfolioService,
                                      SecuredPortfolioService securedPortfolioService,
                                      PortfoliosCacheFacade portfoliosCacheFacade,
                                      PortfolioPermissionValidator portfolioPermissionValidator) {
        this.accessService = accessService;
        this.portfolioService = portfolioService;
        this.securedPortfolioService = securedPortfolioService;
        this.portfoliosCacheFacade = portfoliosCacheFacade;
        this.portfolioPermissionValidator = portfolioPermissionValidator;
    }

    @QueryMapping("portfolioById")
    public Mono<PortfolioResponseDto> getPortfolioById(@Argument String portfolioId, WydenAuthenticationToken token) {
        if (accessService.hasPermission(user(token), PORTFOLIO_READ, portfolioId)) {
            Optional<PortfolioResponseDto> result = portfolioService.findById(portfolioId)
                .map(p -> PortfolioEntityMapper.mapToDto(p,
                    toScopes(securedPortfolioService.getScopes(token, portfolioId)),
                    toScopes(securedPortfolioService.getDynamicScopes(token, portfolioId))));
            return Mono.justOrEmpty(result);
        } else {
            return Mono.empty();
        }
    }

    @QueryMapping("portfolioSearch")
    public Mono<PaginationModel.CursorConnection<PortfolioResponseDto>> portfolioSearch(@Argument PortfolioSearchInputDto search, WydenAuthenticationToken token) {
        LOGGER.info("Requesting paginated portfolios using search: {}", search);

        PortfolioSearchInputDto authorizedSearch = portfolioPermissionValidator.authorizeRequest(search, token);
        if (authorizedSearch == null) {
            return Mono.just(PaginationModel.emptyCursorConnection());
        }

        // TODO: Should call validator, but it confuses UI and needs to be reworked later
        // queryLimitValidator.validate(authorizedSearch);

        LOGGER.info("Requesting authorized portfolios using search: {}", authorizedSearch);

        AccessService.User user = user(token);
        PaginationModel.CursorConnection<PortfolioResponseDto> cursorDto = PaginationProtoToModelMapper.map(portfolioService.getPortfoliosCursor(authorizedSearch),
            node -> PortfolioEntityMapper.mapToDto(node.getPortfolio()));
        applyStaticScopes(cursorDto, user);
        applyDynamicScopes(cursorDto, user);
        return Mono.just(cursorDto);
    }

    private void applyStaticScopes(PaginationModel.CursorConnection<PortfolioResponseDto> cursorDto, AccessService.User user) {
        Set<Scope> portfolioScopes = getStaticScopes(user, PORTFOLIO);
        Set<Scope> nostroScopes = Sets.union(portfolioScopes, getStaticScopes(user, PORTFOLIO_NOSTRO));
        Set<Scope> vostroScopes = Sets.union(portfolioScopes, getStaticScopes(user, PORTFOLIO_VOSTRO));
        for (PortfolioResponseDto node : cursorDto.getAllNodes()) {
            Set<Scope> scopes = new HashSet<>(4);
            if (!PortfolioTypeDto.NOSTRO.equals(node.portfolioType())) {
                scopes.addAll(vostroScopes);
            }
            if (!PortfolioTypeDto.VOSTRO.equals(node.portfolioType())) {
                scopes.addAll(nostroScopes);
            }
            node.scopes().addAll(scopes);
        }
    }

    private void applyDynamicScopes(PaginationModel.CursorConnection<PortfolioResponseDto> cursorDto, AccessService.User user) {
        for (PortfolioResponseDto node : cursorDto.getAllNodes()) {
            Set<Scope> scopes = getDynamicScopes(user, node.id());
            node.dynamicScopes().addAll(scopes);
            List<Scope> sumScopes = Stream.concat(node.scopes().stream(), scopes.stream()).distinct().toList();
            node.scopes().clear();
            node.scopes().addAll(sumScopes);
        }
    }

    private Set<Scope> getStaticScopes(AccessService.User user, String resource) {
        return accessService.getScopes(user, resource).stream()
            .map(Scope::parse)
            .collect(Collectors.toSet());
    }

    private Set<Scope> getDynamicScopes(AccessService.User user, String resourceId) {
        return accessService.getDynamicScopes(user, PORTFOLIO, resourceId).stream()
            .map(Scope::parse)
            .collect(Collectors.toSet());
    }

    @QueryMapping
    public Mono<List<AggregatedPortfolioTag>> portfolioTags(WydenAuthenticationToken token) {
        // TODO: Workaround for ugly handling of PreCondition in UI
        if (!accessService.hasPermission(user(token), PORTFOLIO_READ)) {
            return Mono.just(Collections.emptyList());
        }
        return portfolioService.getAllPortfolioTags();
    }

    @QueryMapping("checkPortfolioName")
    public Mono<ValidationResponse> checkPortfolioNameUniquenessInput(WydenAuthenticationToken token, @Argument String name) {
        Collection<Portfolio> byName = portfoliosCacheFacade.findByName(name);
        if (!byName.isEmpty()) {
            return Mono.just(ValidationResponse.violation("name", "Portfolio name " + name + " is not unique"));
        }
        return Mono.just(ValidationResponse.OK);
    }

    @MutationMapping("createPortfolio")
    @PreAuthorize("@portfolioPermissionValidator.checkPortfolioCreate(#request, #token, #env)")
    public Mono<ResponseStatusDto> createPortfolio(@Argument CreatePortfolioDto request, WydenAuthenticationToken token, DataFetchingEnvironment env) {
        LOGGER.info("Requesting creation of portfolio: {}", request);

        if (!portfoliosCacheFacade.findByName(request.name()).isEmpty()) {
            throw new FieldValidationException("Portfolio name: " + request.name() + " already exists in system, please use different name.", "name");
        }

        String requester = token.getClientId();
        return portfolioService.sendRequestToCreatePortfolio(PortfolioEntityMapper.map(request, requester), requester, request.correlationObject())
            .then(Mono.just(new ResponseStatusDto("ACCEPTED")));
    }

    @MutationMapping("updatePortfolio")
    @PreAuthorize("@portfolioPermissionValidator.checkPortfolioUpdate(#request, #token, #env )")
    public Mono<ResponseStatusDto> updatePortfolio(@Argument UpdatePortfolioDto request, WydenAuthenticationToken token, DataFetchingEnvironment env ) {
        LOGGER.info("Requesting update of portfolio: {}", request);

        String requester = token.getClientId();
        Boolean nameChanged = portfoliosCacheFacade.find(request.id())
            .map(existingPortfolio -> !existingPortfolio.getName().equals(request.name())).orElse(false);

        if (nameChanged) {
            if (!portfoliosCacheFacade.findByName(request.name()).isEmpty()) {
                throw new FieldValidationException("Portfolio name: " + request.name() + " already exists in system, please use different name.", "name");
            }
        }

        return Mono.fromRunnable(() -> portfolioService.sendRequestToUpdatePortfolio(PortfolioEntityMapper.map(request), requester, request.correlationObject()));
    }

    private static List<Scope> toScopes(Set<String> scopes) {
        return scopes.stream().map(Scope::parse).collect(Collectors.toList());
    }
}
