package io.wyden.apiserver.rest.apiui;

import io.wyden.accessgateway.client.permission.Permission;
import io.wyden.accessgateway.client.permission.PermissionCache;
import io.wyden.accessgateway.client.permission.PermissionRestClient;
import io.wyden.accessgateway.client.permission.dto.PermissionDto;
import io.wyden.apiserver.rest.security.AccessService;
import io.wyden.apiserver.rest.security.SecurityService;
import io.wyden.apiserver.rest.security.accessgateway.AddOrRemoveGroupPermissionsRequestDto;
import io.wyden.apiserver.rest.security.accessgateway.AddOrRemoveUserPermissionsRequestDto;
import io.wyden.apiserver.rest.security.accessgateway.GroupPermissionResponseDto;
import io.wyden.apiserver.rest.security.accessgateway.GroupsScopesForResourceResponseDto;
import io.wyden.apiserver.rest.security.accessgateway.UserResponseDto;
import io.wyden.apiserver.rest.security.accessgateway.UserScopesForResourceResponseDto;
import io.wyden.apiserver.rest.security.model.AuthorityDto;
import io.wyden.apiserver.rest.security.model.Resource;
import io.wyden.apiserver.rest.security.model.Scope;
import io.wyden.apiserver.rest.security.model.WydenAuthenticationToken;
import io.wyden.apiserver.rest.trading.model.MutationReturnValue;

import graphql.schema.DataFetchingEnvironment;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.graphql.data.method.annotation.Argument;
import org.springframework.graphql.data.method.annotation.MutationMapping;
import org.springframework.graphql.data.method.annotation.QueryMapping;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import reactor.core.publisher.Mono;

import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static io.wyden.apiserver.rest.security.AccessService.User.user;

@Controller
@SuppressWarnings("unused")
public class PermissionsGraphQlController {

    private final PermissionRestClient permissionRestClient;
    private final PermissionCache permissionCache;
    private final AccessService accessService;
    private final SecurityService securityService;

    private final int queryLimit;

    public PermissionsGraphQlController(PermissionRestClient permissionRestClient,
                                        PermissionCache permissionCache,
                                        AccessService accessService,
                                        SecurityService securityService,
                                        @Value("${rest-api-server.query-limits.portfolio:100}") int queryLimit) {
        this.permissionRestClient = permissionRestClient;
        this.permissionCache = permissionCache;
        this.accessService = accessService;
        this.securityService = securityService;
        this.queryLimit = queryLimit;
    }

    @MutationMapping("addGroupPermissions")
    @PreAuthorize("@permissionValidator.canChangePermissions(#request, #token, #env)")
    public Mono<MutationReturnValue> addGroupPermissions(@Argument AddOrRemoveGroupPermissionsRequestDto request, WydenAuthenticationToken token, DataFetchingEnvironment env ) {
        permissionRestClient.addGroupPermissions(request.groupName(), toPermissionDto(request.permissions()));
        return Mono.just(new MutationReturnValue(request.groupName()));
    }

    @MutationMapping("addUserPermissions")
    @PreAuthorize("@permissionValidator.canChangePermissions(#request, #token, #env)")
    public Mono<MutationReturnValue> addUserPermissions(@Argument AddOrRemoveUserPermissionsRequestDto request, WydenAuthenticationToken token, DataFetchingEnvironment env ) {
        permissionRestClient.addUserPermissions(request.username(), toPermissionDto(request.permissions()));
        return Mono.just(new MutationReturnValue(request.username()));
    }

    @MutationMapping("removeGroupPermissions")
    @PreAuthorize("@permissionValidator.canChangePermissions(#request, #token, #env)")
    public Mono<MutationReturnValue> removeGroupPermissions(@Argument AddOrRemoveGroupPermissionsRequestDto request, WydenAuthenticationToken token, DataFetchingEnvironment env ) {
        permissionRestClient.removeGroupPermissions(request.groupName(), toPermissionDto(request.permissions()));
        return Mono.just(new MutationReturnValue(token.getClientId()));
    }

    @MutationMapping("removeUserPermissions")
    @PreAuthorize("@permissionValidator.canChangePermissions(#request, #token, #env)")
    public Mono<MutationReturnValue> removeUserPermissions(@Argument AddOrRemoveUserPermissionsRequestDto request, WydenAuthenticationToken token, DataFetchingEnvironment env ) {
        permissionRestClient.removeUserPermissions(request.username(), toPermissionDto(request.permissions()));
        return Mono.just(new MutationReturnValue(request.username()));
    }

    @QueryMapping("groupNames")
    public List<String> groupNames(WydenAuthenticationToken token) {
        return permissionRestClient.getGroupNames();
    }

    @QueryMapping("userNames")
    public List<String> userNames(WydenAuthenticationToken token) {
        return permissionRestClient.getUsersNames();
    }

    @QueryMapping("usersPermissionsForResource")
    public List<UserScopesForResourceResponseDto> usersPermissionsForResource(@Argument Resource resource, @Argument String resourceId, WydenAuthenticationToken token, DataFetchingEnvironment env ) {
        List<String> usernames = permissionRestClient.getUsersNames();
        return usernames.stream()
            .map(username -> new UserScopesForResourceResponseDto(username, toScopes(accessService.getUserScopes(username, resource.toAgDomain(), resourceId))))
            .filter(us -> !us.scopes().isEmpty())
            .toList();
    }

    @QueryMapping("groupsPermissionsForResource")
    public List<GroupsScopesForResourceResponseDto> groupsByResource(@Argument Resource resource, @Argument String resourceId, WydenAuthenticationToken token) {
        List<String> groupNames = permissionRestClient.getGroupNames();
        return groupNames.stream()
            .map(groupName -> new GroupsScopesForResourceResponseDto(groupName, toScopes(accessService.getGroupScopes(groupName, resource.toAgDomain(), resourceId))))
            .filter(gs -> !gs.scopes().isEmpty())
            .toList();
    }

    @QueryMapping("userWithPermissions")
    public UserResponseDto userWithPermissions(WydenAuthenticationToken token) {
        String username = token.getClientId();
        Set<GroupPermissionResponseDto> groupPermissionResponses = toGroupPermissionResponseDto(token.getGroups());
        Set<AuthorityDto> userAuthorities = getUserAuthorities(username);
        return new UserResponseDto(username, groupPermissionResponses, userAuthorities);
    }

    @QueryMapping("userStaticPermissions")
    public Set<AuthorityDto> userStaticPermissions(WydenAuthenticationToken token) {
        AccessService.User userToken = user(token);
        Set<AuthorityDto> result = new HashSet<>();
        for (Permission permission : Permission.values()) {
            if (accessService.hasUserPermission(userToken, permission)) {
                result.add(new AuthorityDto(permission.getResource(), permission.getScope()));
            }
        }
        return result;
    }

    @QueryMapping("groupStaticPermissions")
    public Set<AuthorityDto> groupStaticPermissions(WydenAuthenticationToken token) {
        AccessService.User userToken = user(token);
        Set<AuthorityDto> result = new HashSet<>();
        for (Permission permission : Permission.values()) {
            if (accessService.hasGroupPermission(userToken, permission)) {
                result.add(new AuthorityDto(permission.getResource(), permission.getScope()));
            }
        }
        return result;
    }

    @QueryMapping("hasOwnPermission")
    public boolean hasOwnPermission(@Argument Resource resource, @Argument Scope scope, @Argument String resourceId, WydenAuthenticationToken token) {
        if (StringUtils.isBlank(resourceId)) {
            return accessService.hasPermission(user(token), resource.toAgDomain(), scope.toAgDomain());
        } else {
            return accessService.hasPermission(user(token), resource.toAgDomain(), scope.toAgDomain(), resourceId);
        }
    }

    @QueryMapping("hasUserPermission")
    public boolean hasUserPermission(@Argument String clientId, @Argument Resource resource, @Argument Scope scope, @Argument String resourceId, WydenAuthenticationToken token) {
        AccessService.User user = user(securityService.getUserAttributes(clientId));
        if (StringUtils.isBlank(resourceId)) {
            return accessService.hasPermission(user, resource.toAgDomain(), scope.toAgDomain());
        } else {
            return accessService.hasPermission(user, resource.toAgDomain(), scope.toAgDomain(), resourceId);
        }
    }

    public Set<AuthorityDto> getUserAuthorities(String username) {
        Set<AuthorityDto> authorities = new HashSet<>();
        for (Permission permission : Permission.values()) {
            permissionCache.getUserPermittedResourceIds(username, permission.getResource(), permission.getScope(), queryLimit).forEach(rid -> {
                authorities.add(new AuthorityDto(permission.getResource(), permission.getScope(), rid));
            });
        }
        return authorities;
    }

    public Set<GroupPermissionResponseDto> toGroupPermissionResponseDto(Set<String> groupNames) {
        return groupNames.stream()
            .map(this::toGroupPermissionResponseDto)
            .collect(Collectors.toSet());
    }

    public GroupPermissionResponseDto toGroupPermissionResponseDto(String groupName) {
        Set<AuthorityDto> authorities = new HashSet<>();
        for (Permission permission : Permission.values()) {
            permissionCache.getGroupPermittedResourceIds(groupName, permission.getResource(), permission.getScope(), queryLimit).forEach(rid -> {
                authorities.add(new AuthorityDto(permission.getResource(), permission.getScope(), rid));
            });
        }
        return new GroupPermissionResponseDto(groupName, authorities);
    }

    public List<Scope> toScopes(Set<String> scopes) {
        return scopes.stream()
            .map(Scope::parse)
            .collect(Collectors.toList());
    }

    public Set<PermissionDto> toPermissionDto(Collection<AuthorityDto> authorityList) {
        return authorityList.stream()
            .map(adto -> new PermissionDto(adto.getResource().toAgDomain(), adto.getScope().toAgDomain(), adto.getResourceId()))
            .collect(Collectors.toSet());
    }
}
