package io.wyden.apiserver.rest.security;

import io.wyden.apiserver.rest.security.model.WydenAuthenticationToken;
import io.wyden.apiserver.rest.utils.AccessDeniedLogger;
import io.wyden.apiserver.rest.walletaccount.model.WalletAccountCreateRequest;
import io.wyden.apiserver.rest.walletaccount.model.WalletAccountSearch;
import io.wyden.apiserver.rest.walletaccount.model.WalletTypeDto;

import graphql.schema.DataFetchingEnvironment;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import java.util.Optional;

import static io.wyden.accessgateway.client.permission.Permission.WALLET_CREATE;
import static io.wyden.accessgateway.client.permission.Permission.WALLET_NOSTRO_CREATE;
import static io.wyden.accessgateway.client.permission.Permission.WALLET_NOSTRO_READ;
import static io.wyden.accessgateway.client.permission.Permission.WALLET_READ;
import static io.wyden.accessgateway.client.permission.Permission.WALLET_VOSTRO_CREATE;
import static io.wyden.accessgateway.client.permission.Permission.WALLET_VOSTRO_READ;
import static io.wyden.apiserver.rest.security.AccessService.User.user;

@Component("walletPermissionValidator")
public class WalletPermissionValidator {

    private final AccessService accessService;

    public WalletPermissionValidator(AccessService accessService) {
        this.accessService = accessService;
    }

    @Nullable
    public WalletAccountSearch authorizeRequest(WalletAccountSearch search, WydenAuthenticationToken token) {

        WalletAccountSearch result = Optional.ofNullable(search).orElse(WalletAccountSearch.empty());

        if (accessService.hasPermission(user(token), WALLET_READ)) {
            return result;
        }

        boolean hasWalletVostroReadStaticPermission = accessService.hasPermission(user(token), WALLET_VOSTRO_READ);
        boolean hasWalletNostroReadStaticPermission = accessService.hasPermission(user(token), WALLET_NOSTRO_READ);

        if (hasWalletVostroReadStaticPermission && hasWalletNostroReadStaticPermission) {
            return result;
        }

        if (hasWalletNostroReadStaticPermission) {
            return result.withWalletType(WalletTypeDto.NOSTRO);
        }

        if (hasWalletVostroReadStaticPermission) {
            return result.withWalletType(WalletTypeDto.VOSTRO);
        }

        //no dynamic permissions -> no resourceId validation
        return null;
    }

    public boolean checkWalletCreate(WalletAccountCreateRequest request, WydenAuthenticationToken token, DataFetchingEnvironment env) {
        boolean result =  accessService.hasPermission(user(token), WALLET_CREATE)
            || accessService.hasPermission(user(token), WALLET_VOSTRO_CREATE) && request.walletType() != null && request.walletType().equals(WalletTypeDto.VOSTRO)
            || accessService.hasPermission(user(token), WALLET_NOSTRO_CREATE) && request.walletType() != null && request.walletType().equals(WalletTypeDto.NOSTRO);

        AccessDeniedLogger.logIfAccessDenied(result, env, token, WALLET_CREATE, request.id());
        return result;
    }
}
