package io.wyden.apiserver.rest.apiui;

import graphql.schema.DataFetchingEnvironment;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.wyden.apiserver.rest.marketdata.MarketDataService;
import io.wyden.apiserver.rest.marketdata.model.MarketDataDepth;
import io.wyden.apiserver.rest.marketdata.model.MarketDataMapper;
import io.wyden.apiserver.rest.marketdata.model.MarketDataModel;
import io.wyden.apiserver.rest.marketdata.model.MdClientRequest;
import io.wyden.apiserver.rest.security.model.WydenAuthenticationToken;
import io.wyden.apiserver.rest.walletaccount.WalletAccountValidator;
import io.wyden.cloudutils.telemetry.Telemetry;
import org.reactivestreams.Publisher;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.graphql.data.method.annotation.Argument;
import org.springframework.graphql.data.method.annotation.SubscriptionMapping;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;

import java.util.UUID;
import javax.annotation.Nullable;

@Controller
public class MarketDataGraphQLController {

    private static final Logger LOGGER = LoggerFactory.getLogger(MarketDataGraphQLController.class);

    private final MarketDataService marketDataService;
    private final MarketDataMapper mapper;
    private final MeterRegistry meterRegistry;
    private final WalletAccountValidator walletValidator;

    public MarketDataGraphQLController(MarketDataService marketDataService, MarketDataMapper mapper, Telemetry telemetry,
                                       WalletAccountValidator walletValidator) {
        this.marketDataService = marketDataService;
        this.mapper = mapper;
        this.meterRegistry = telemetry.getMeterRegistry();
        this.walletValidator = walletValidator;
    }

    @SubscriptionMapping
    @PreAuthorize("@marketDataPermissionValidator.checkTradePermissions(#venueAccount, #portfolioId, #jwtAuthenticationToken, #env)")
    public Publisher<MarketDataModel.MarketDataResponse> tick(@Argument @Nullable String venueAccount, // conditionally required - direct connector access only
                                                              @Argument @Nullable String portfolioId, // conditionally required - when venueAccount is null
                                                              @Argument String instrumentId,
                                                              WydenAuthenticationToken jwtAuthenticationToken,
                                                              DataFetchingEnvironment env) {
        LOGGER.info("Requested tick stream: venueAccount={} , instrumentId={} , portfolioId={}", venueAccount, instrumentId, portfolioId);
        updateMetrics("wyden.market-data.subscribe-incoming", instrumentId, MarketDataDepth.L1.name());

        walletValidator.validateAccountIsNotWallet(venueAccount);

        return marketDataService.subscribeMarketDataTick(venueAccount, instrumentId, portfolioId, new MdClientRequest(jwtAuthenticationToken.getClientId(), UUID.randomUUID().toString()))
            .doOnNext(l1Event -> updateMetrics("wyden.market-data.events-outgoing", l1Event.identifier().instrumentId(), MarketDataDepth.L1.name(), "REST"))
            .map(mapper::fromDto)
            .doFinally(s -> updateMetrics("wyden.market-data.unsubscribe-incoming", instrumentId, MarketDataDepth.L1.name()));
    }

    @SubscriptionMapping
    @PreAuthorize("@marketDataPermissionValidator.checkTradePermissions(#venueAccount, #portfolioId, #jwtAuthenticationToken, #env)")
    public Publisher<MarketDataModel.OrderBookResponse> orderBook(@Argument @Nullable String venueAccount, // conditionally required - direct connector access only
                                                                  @Argument @Nullable String portfolioId, // conditionally required - when venueAccount is null
                                                                  @Argument String instrumentId,
                                                                  WydenAuthenticationToken jwtAuthenticationToken,
                                                                  DataFetchingEnvironment env) {
        LOGGER.info("Requested order book stream: venueAccount={} , instrumentId={} , portfolioId={}", venueAccount, instrumentId, portfolioId);
        updateMetrics("wyden.market-data.subscribe-incoming", instrumentId, MarketDataDepth.L2.name());

        walletValidator.validateAccountIsNotWallet(venueAccount);

        return marketDataService.subscribeL2(venueAccount, instrumentId, portfolioId, new MdClientRequest(jwtAuthenticationToken.getClientId(), UUID.randomUUID().toString()))
            .doOnNext(l2Event -> updateMetrics("wyden.market-data.events-outgoing", l2Event.identifier().instrumentId(), MarketDataDepth.L2.name(), "REST"))
            .map(mapper::fromDto)
            .doFinally(s -> updateMetrics("wyden.market-data.unsubscribe-incoming", instrumentId, MarketDataDepth.L2.name()));
    }

    private void updateMetrics(String name, String instrumentId, String depth) {
        updateMetrics(name, instrumentId, depth, null);
    }

    private void updateMetrics(String name, String instrumentId, String depth, @Nullable String source) {
        try {
            Tags tags = Tags.of(
                "instrumentId", instrumentId,
                "depth", depth,
                "source", "REST"
            );
            if (source != null) {
                tags = Tags.concat(tags, "source", source);
            }
            this.meterRegistry.counter(name, tags).increment();
        } catch (Exception ex) {
            LOGGER.warn("Metrics update failed", ex);
        }
    }
}
