package io.wyden.apiserver.rest.apiui.mocks.targetregistry;

import io.wyden.apiserver.rest.targetregistry.ConnectorTemplateField;
import io.wyden.apiserver.rest.trading.model.MutationSubmittedResponse;
import io.wyden.cloudutils.tools.DateUtils;

import org.springframework.graphql.data.method.annotation.Argument;
import org.springframework.graphql.data.method.annotation.MutationMapping;
import org.springframework.graphql.data.method.annotation.QueryMapping;
import org.springframework.graphql.data.method.annotation.SubscriptionMapping;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Random;

@Controller
public class ConnectorGraphQLController {

    private final ConnectorMockService connectorService;

    public ConnectorGraphQLController(ConnectorMockService connectorService) {
        this.connectorService = connectorService;
    }

    @QueryMapping("connectorList")
    @PreAuthorize("hasPermission('connector', 'read')")
    public Mono<List<ConnectorResponse>> getConnectorList() {
        return Mono.just(connectorService.getConnectors());
    }

    @QueryMapping("connectorById")
    @PreAuthorize("hasPermission('connector', 'read')")
    public Mono<ConnectorResponse> getConnectorById(@Argument String id) {
        return Mono.justOrEmpty(connectorService.getConnectorById(id));
    }

    @QueryMapping("connectorDetails")
    @PreAuthorize("hasPermission('connector', 'read')")
    public Mono<ConnectorDetailsResponse> getConnectorDetailsById(@Argument String id) {
        return Mono.justOrEmpty(connectorService.getConnectorDetailsById(id));
    }

    @QueryMapping("connectorNameCheck")
    public Mono<Boolean> connectorNameCheck(@Argument String name) {
        return Mono.just(connectorService.nameCheck(name));
    }

    @QueryMapping("connectorTypeList")
    public Mono<List<String>> getConnectorTypeList() {
        return Mono.just(connectorService.getConnectroTypeList());
    }

    @QueryMapping("connectorTemplateByVenue")
    @PreAuthorize("hasPermission('connector', 'read')")
    public Mono<List<ConnectorTemplateField>> getConnectorTemplate(@Argument String venue) {
        return Mono.just(connectorService.getConnectorTemplate(venue));
    }

    @MutationMapping("connectorCreate")
    @PreAuthorize("hasPermission('connector', 'create')")
    public Mono<MutationSubmittedResponse> connectorCreate(@Argument ConnectorDetailsRequest input) {
        return Mono.just(new MutationSubmittedResponse("ok"));
    }

    @MutationMapping("connectorUpdate")
    @PreAuthorize("hasPermission('connector', 'manage')")
    public Mono<MutationSubmittedResponse> connectorUpdate(@Argument ConnectorDetailsRequest input) {
        return Mono.just(new MutationSubmittedResponse("ok"));
    }

    @MutationMapping("connectorRemove")
    @PreAuthorize("hasPermission('connector', 'manage')")
    public Mono<MutationSubmittedResponse> connectorRemove(@Argument String id) {
        return Mono.just(new MutationSubmittedResponse("ok"));
    }

    @MutationMapping("connectorAction")
    @PreAuthorize("hasPermission('connector', 'manage')")
    public Mono<MutationSubmittedResponse> connectorAction(@Argument String id, @Argument ConnectorActionType action) {
        return Mono.just(new MutationSubmittedResponse("ok"));
    }

    @SubscriptionMapping("connectorStates")
    @PreAuthorize("hasPermission('connector', 'read')")
    public Flux<ConnectorStatesResponse> connectorStatesSubscription() {
        Random random = new Random();
        return Flux.interval(Duration.ofSeconds(10))
            .flatMap(tick -> Flux.fromIterable(connectorService.getConnectors()))
            .map(c -> {
                int r = random.nextInt(100);
                List<ConnectorStatesResponse.CapabilityState> states = List.of(
                    new ConnectorStatesResponse.CapabilityState("TRADING",
                        r > 70 ? "LIVE" : "DEAD",
                        "some message",
                        DateUtils.toIsoUtcTime(ZonedDateTime.now())),
                    new ConnectorStatesResponse.CapabilityState("MARKET_DATA",
                        r > 70 ? "LIVE" : "DEAD",
                        "some message",
                        DateUtils.toIsoUtcTime(ZonedDateTime.now()))
                );
                return new ConnectorStatesResponse(c.connectorId(), c.connectorName() + r, states);
            });
    }
}
