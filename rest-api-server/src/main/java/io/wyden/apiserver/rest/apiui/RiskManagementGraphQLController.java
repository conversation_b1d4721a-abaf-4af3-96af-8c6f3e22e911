package io.wyden.apiserver.rest.apiui;

import io.wyden.apiserver.rest.referencedata.portfolio.service.SecuredPortfolioService;
import io.wyden.apiserver.rest.risk.RiskManagementService;
import io.wyden.apiserver.rest.risk.mapper.PreTradeCheckDtoMapper;
import io.wyden.apiserver.rest.risk.mapper.PreTradeCheckSchemaDtoMapper;
import io.wyden.apiserver.rest.risk.model.PreTradeCheckModel.PreTradeCheckInputDto;
import io.wyden.apiserver.rest.risk.model.PreTradeCheckModel.PreTradeCheckResponseDto;
import io.wyden.apiserver.rest.risk.model.PreTradeCheckModel.PreTradeCheckSchemaDto;
import io.wyden.apiserver.rest.security.model.Scope;
import io.wyden.apiserver.rest.security.model.WydenAuthenticationToken;
import io.wyden.apiserver.rest.trading.model.MutationReturnValue;
import io.wyden.published.risk.PreTradeCheckSchemaList;
import io.wyden.published.risk.PreTradeChecksList;

import graphql.schema.DataFetchingEnvironment;
import org.springframework.graphql.data.method.annotation.Argument;
import org.springframework.graphql.data.method.annotation.MutationMapping;
import org.springframework.graphql.data.method.annotation.QueryMapping;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.stream.Collectors;

@Controller
public class RiskManagementGraphQLController {

    private final RiskManagementService riskManagementService;
    private final PreTradeCheckDtoMapper mapper;
    private final SecuredPortfolioService securedPortfolioService;

    public RiskManagementGraphQLController(RiskManagementService riskManagementService, PreTradeCheckDtoMapper mapper, SecuredPortfolioService securedPortfolioService) {
        this.riskManagementService = riskManagementService;
        this.mapper = mapper;
        this.securedPortfolioService = securedPortfolioService;
    }

    @QueryMapping("preTradeChecks")
    @PreAuthorize("@defaultPermissionValidator.hasPermission('risk', 'read', #token, #env)")
    public Mono<List<PreTradeCheckResponseDto>> getPreTradeChecks(WydenAuthenticationToken token, DataFetchingEnvironment env) {
        return riskManagementService.getPreTradeChecks()
            .flatMapIterable(PreTradeChecksList::getItemsList)
            .map(preTradeCheck -> mapper.map(preTradeCheck,
                portfolioId -> getScopes(token, portfolioId),
                portfolioId -> getDynamicScopes(token, portfolioId)))
            .collectList();
    }

    @MutationMapping("savePreTradeCheck")
    @PreAuthorize("@defaultPermissionValidator.hasPermission('risk', 'manage', #token, #env)")
    public Mono<MutationReturnValue> savePreTradeCheck(@Argument PreTradeCheckInputDto request, WydenAuthenticationToken token, DataFetchingEnvironment env) {
        return riskManagementService.savePreTradeCheck(PreTradeCheckDtoMapper.map(request))
            .thenReturn(new MutationReturnValue(token.getClientId()));
    }

    @MutationMapping("deletePreTradeCheck")
    @PreAuthorize("@defaultPermissionValidator.hasPermission('risk', 'manage', #token, #env)")
    public Mono<MutationReturnValue> deletePreTradeCheck(@Argument String preTradeCheckId, WydenAuthenticationToken token, DataFetchingEnvironment env) {
        return riskManagementService.deletePreTradeCheck(preTradeCheckId)
            .thenReturn(new MutationReturnValue(token.getClientId()));
    }

    @QueryMapping("preTradeCheckFormSchema")
    @PreAuthorize("@defaultPermissionValidator.hasPermission('risk', 'manage', #token, #env)")
    public Mono<List<PreTradeCheckSchemaDto>> getPreTradeCheckFormSchema(WydenAuthenticationToken token, DataFetchingEnvironment env) {
        return riskManagementService.getPreTradeCheckSchema()
            .flatMapIterable(PreTradeCheckSchemaList::getItemsList)
            .map(PreTradeCheckSchemaDtoMapper::map)
            .collectList();
    }

    private List<Scope> getScopes(WydenAuthenticationToken token, String portfolioId) {
        return securedPortfolioService.getScopes(token, portfolioId).stream()
            .map(Scope::parse)
            .collect(Collectors.toList());
    }

    private List<Scope> getDynamicScopes(WydenAuthenticationToken token, String portfolioId) {
        return securedPortfolioService.getDynamicScopes(token, portfolioId).stream()
            .map(Scope::parse)
            .collect(Collectors.toList());
    }
}
