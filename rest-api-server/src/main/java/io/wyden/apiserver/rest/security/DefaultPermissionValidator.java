package io.wyden.apiserver.rest.security;

import io.wyden.apiserver.rest.security.model.WydenAuthenticationToken;
import io.wyden.apiserver.rest.utils.AccessDeniedLogger;

import graphql.schema.DataFetchingEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import java.io.Serializable;
import java.util.Collection;

import static io.wyden.apiserver.rest.security.AccessService.User.user;

@Component("defaultPermissionValidator")
public class DefaultPermissionValidator {

    private static final Logger LOGGER = LoggerFactory.getLogger(DefaultPermissionValidator.class);

    private final AccessService accessService;

    public DefaultPermissionValidator(AccessService accessService) {
        this.accessService = accessService;
    }

    public boolean hasPermission(Object resourceObject, Object scopeObject, WydenAuthenticationToken token, DataFetchingEnvironment env) {
        try {
            String resource = toResource(resourceObject);
            String scope = toScope(scopeObject);
            boolean result = accessService.hasPermission(user(token), resource, scope);
            AccessDeniedLogger.logIfAccessDenied(result, env, token, resource + "." +scope, "");
            return result;
        } catch (Exception ex) {
            LOGGER.warn("Permission check failed, returning Access Denied", ex);
            return false;
        }
    }

    public boolean hasPermission(@Nullable Serializable targetId, String resource, Object scopeObject, WydenAuthenticationToken token, DataFetchingEnvironment env) {
        try {
            if (targetId == null) {
                String scope = toScope(scopeObject);
                boolean result = accessService.hasPermission(user(token), resource, scope);
                AccessDeniedLogger.logIfAccessDenied(result, env, token, resource + "." +scope, "");
                return result;
            } else if (targetId instanceof Collection<?> collection) {
                return collection.stream()
                    .allMatch(t -> hasPermission(toSerializable(t), resource, scopeObject, token, env));
            } else {
                String scope = toScope(scopeObject);
                String resourceId = String.valueOf(targetId);
                boolean result =  accessService.hasPermission(user(token), resource, scope, resourceId);
                AccessDeniedLogger.logIfAccessDenied(result, env, token, resource + "." +scope, resourceId);
                return result;
            }
        } catch (Exception ex) {
            LOGGER.warn("Permission check failed, returning Access Denied", ex);
            return false;
        }
    }

    private String toResource(Object resourceObject) {
        return objectToString("Resource", resourceObject);
    }

    private String toScope(Object scopeObject) {
        return objectToString("Scope", scopeObject);
    }

    private String objectToString(String name, Object resourceObject) {
        if (resourceObject == null) {
            throw new IllegalArgumentException("%s cannot be null".formatted(name));
        } else if (resourceObject instanceof String resourceString) {
            return resourceString;
        } else {
            throw new IllegalArgumentException("%s %s is not a string".formatted(name, resourceObject.getClass().getSimpleName()));
        }
    }

    private Serializable toSerializable(Object object) {
        if (object == null) {
            throw new IllegalArgumentException("ResourceId cannot be null");
        } else if (object instanceof Serializable serializable) {
            return serializable;
        } else {
            throw new IllegalArgumentException("ResourceId %s is not a Serializable".formatted(object.getClass().getSimpleName()));
        }
    }
}
