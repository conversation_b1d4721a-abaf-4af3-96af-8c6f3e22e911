package io.wyden.apiserver.rest.config.exception;

import io.wyden.apiserver.rest.security.model.WydenAuthenticationToken;

import graphql.ErrorClassification;
import graphql.GraphQLError;
import graphql.GraphqlErrorBuilder;
import graphql.schema.DataFetchingEnvironment;

import java.util.Map;

public class AccessDeniedGqlExceptionHandler extends AbstractGqlExceptionHandler {

    protected AccessDeniedGqlExceptionHandler(ErrorClassification errorClassification) {
        super(errorClassification);
    }

    @Override
    public GraphQLError handle(Throwable ex, DataFetchingEnvironment env) {

        String user = getToken(env).map(WydenAuthenticationToken::getClientId).orElse("null");
        String variables = getRequest(env).map(r -> r.getVariables().toString()).orElse("null");

        LOGGER.warn("Access denied for user: {}, path: {}, request: {}", user, env.getExecutionStepInfo().getPath(), variables);

        return GraphqlErrorBuilder.newError()
            .errorType(errorClassification)
            .message(ex.getMessage())
            .path(env.getExecutionStepInfo().getPath())
            .location(env.getField().getSourceLocation())
            .extensions(Map.of("user", user, "variables", variables))
            .build();
    }

}
