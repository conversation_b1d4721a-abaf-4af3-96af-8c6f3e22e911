spring.application.name=rest-api-server

server.port=8095

springdoc.packagesToScan=io.wyden.apiserver.rest
# TODO - matching all for now, but we should improve in scope of https://algotrader.atlassian.net/browse/AC-378
springdoc.pathsToMatch=/**
springdoc.swagger-ui.urls[0].name=REST API
springdoc.swagger-ui.urls[0].url=/v3/api-docs
springdoc.swagger-ui.urls[1].name=Execution Engine - internal API
springdoc.swagger-ui.urls[1].url=http://localhost:8097/v3/api-docs
springdoc.swagger-ui.urls[2].name=FIX actor - internal API
springdoc.swagger-ui.urls[2].url=http://localhost:8090/v3/api-docs
springdoc.swagger-ui.urls[3].name=Connector actor - internal API
springdoc.swagger-ui.urls[3].url=http://localhost:8092/v3/api-docs
springdoc.swagger-ui.urls[4].name=Access Gateway - internal API
springdoc.swagger-ui.urls[4].url=http://localhost:8089/v3/api-docs
springdoc.swagger-ui.urlsPrimaryName=REST API
springdoc.swagger-ui.oauth.clientId=swagger-ui
springdoc.swagger-ui.oauth.use-pkce-with-authorization-code-grant=true
openapi.server.url=

rabbitmq.username = restapiserver
rabbitmq.password = password
rabbitmq.virtualHost = /
rabbitmq.host = localhost
# default RabbitMQ port for non-TLS connections: 5672, default port for TLS connections: 5671
rabbitmq.port = 5672
# specify a valid protocol name, e.g. "TLSv1.2" . leave empty for non-TLS connection
rabbitmq.tls =

schema.graphql.version=1.0.335
settlement.host = http://localhost:8067
settlement.graphql = ${settlement.host}/graphql
spring.graphql.websocket.path=/graphql/ws
spring.graphql.schema.printer.enabled=true
spring.graphql.graphiql.enabled=true
spring.graphql.graphiql.path=/graphiql
spring.graphql.schema.locations=classpath:graphql/${schema.graphql.version}
spring.graphql.schema.file-extensions=.graphql

marketdata.client.queueTemplatePrefix = rest-api-server-queue.market-data.%s.EVENT

keycloak.host=http://localhost:8080
access.gateway.host = http://localhost:8089

reference.data.host = http://localhost:8098
reference.data.get.portfolios.tags.by.id.url = ${reference.data.host}/portfolios/tags
reference.data.save.currency.url = ${reference.data.host}/currencies
booking.engine.host=http://localhost:8100
target.registry.host=http://localhost:8066
target.registry.target.states.url=${target.registry.host}/targets/states
target.registry.target.template.url=${target.registry.host}/targets/templates/
target.registry.target.details.url.prefix=${target.registry.host}/targets/
risk.engine.host = http://localhost:8300
risk.engine.pretradechecks.url = ${risk.engine.host}/pretradechecks
risk.engine.pretradecheckschema.url = ${risk.engine.host}/pretradecheckschema
audit.server.host=http://localhost:8030
order.history.host=http://localhost:8040
clob.gateway.host=http://localhost:8069
quoting.engine.host=http://localhost:8071
aeron.archive.host=http://localhost:8072

broker.config.service.host=http://localhost:8049
broker.config.service.get-config-list-portfolio=${broker.config.service.host}/portfolio-configurations
broker.config.service.get-config-portfolio=${broker.config.service.host}/portfolio-configurations/{portfolioId}
broker.config.service.update-config-portfolio=${broker.config.service.host}/portfolio-configurations/{portfolioId}
broker.config.service.update-config-portfolio-quoting=${broker.config.service.host}/portfolio-configurations/{portfolioId}/quoting
broker.config.service.update-config-portfolio-execution=${broker.config.service.host}/portfolio-configurations/{portfolioId}/execution
broker.config.service.update-config-portfolio-pricing=${broker.config.service.host}/portfolio-configurations/{portfolioId}/pricing
broker.config.service.update-config-portfolio-hedging=${broker.config.service.host}/portfolio-configurations/{portfolioId}/hedging
broker.config.service.update-config-portfolio-instrument=${broker.config.service.host}/portfolio-configurations/{portfolioId}/instrument
broker.config.service.validate-config-portfolio-instrument=${broker.config.service.host}/configuration/validation
broker.config.service.validate-auto-hedger-config=${broker.config.service.host}/autohedger/validate
broker.config.service.reset-config=${broker.config.service.host}/configuration/reset
broker.config.service.delete-portfolio-config=${broker.config.service.host}/portfolio-configurations/{portfolioId}

broker.config.service.get-config-portfolio-group=${broker.config.service.host}/group-configurations/{portfolioGroupId}
broker.config.service.get-config-list-portfolio-group=${broker.config.service.host}/group-configurations
broker.config.service.update-config-portfolio-group=${broker.config.service.host}/group-configurations/{portfolioGroupId}
broker.config.service.update-config-portfolio-group-execution=${broker.config.service.host}/group-configurations/{portfolioGroupId}/execution
broker.config.service.update-config-portfolio-group-pricing=${broker.config.service.host}/group-configurations/{portfolioGroupId}/pricing
broker.config.service.update-config-portfolio-group-hedging=${broker.config.service.host}/group-configurations/{portfolioGroupId}/hedging
broker.config.service.update-config-group-instrument=${broker.config.service.host}/group-configurations/{portfolioGroupId}/instrument
broker.config.service.delete-portfolio-group-config=${broker.config.service.host}/group-configurations/{portfolioGroupId}

broker.config.service.effective-venue-accounts=${broker.config.service.host}/effective-venue-accounts

rate.service.host=http://localhost:8052
rate.service.get-conversion-sources=${rate.service.host}/conversion-source
rate.service.create-conversion-source=${rate.service.host}/conversion-source
rate.service.update-conversion-source=${rate.service.host}/conversion-source
rate.service.delete-conversion-source=${rate.service.host}/conversion-source/{venueAccount}
rate.service.get-rate-subscriptions=${rate.service.host}/subscriptions
rate.service.create-rate-subscription=${rate.service.host}/subscriptions
rate.service.delete-rate-subscription=${rate.service.host}/subscriptions/{rateSubscriptionKey}

access.gateway.get-entitlements=${access.gateway.host}/license/entitlements

spring.security.oauth2.client.registration.keycloak.client-id=access-gateway-app
spring.security.oauth2.client.registration.keycloak.authorization-grant-type=authorization_code
spring.security.oauth2.client.registration.keycloak.scope=openid
spring.security.oauth2.client.provider.keycloak.issuer-uri=${keycloak.host}/realms/${keycloak.realm}
spring.security.oauth2.resourceserver.jwt.issuer-uri=${keycloak.host}/realms/${keycloak.realm}
spring.security.oauth2.client.provider.keycloak.user-name-attribute=preferred_username
security.jwt.issuer.validation.disabled=true

keycloak.realm=Wyden
swagger.keycloak.host=${keycloak.host}

# DB
db.enabled=true
# supported values: psql, oracle
db.engine=psql
spring.datasource.url = ******************************************
spring.datasource.username = ui_server
spring.datasource.password = password
spring.datasource.hikari.maximum-pool-size=5

# Flyway
spring.flyway.url=${spring.datasource.url}
spring.flyway.user=${spring.datasource.username}
spring.flyway.password=${spring.datasource.password}
spring.flyway.enabled=${db.enabled}
spring.flyway.locations=classpath:psql/migration/schema,classpath:psql/migration/data

# comma-separated list of hz member hosts
hz.addressList = localhost
hz.outboundPortDefinition =

cache.orderIds.ttlAfterCloseInSec = 600
# Trading queue
rabbitmq.trading-rest-api-server-queue = rest-api-server-queue.trading.%s.ALL

# Target registry queue
rabbitmq.target-registry-state-changed-queue = rest-api-server-queue.target-registry.%s.TARGET-STATE-CHANGED

# Queue consuming async operation responses
rabbitmq.common-async-operation-result-exchange = common-async-operation-result-exchange
rabbitmq.common-rest-api-queue.async-results-format = rest-api-server-queue.event-log.%s.EVENT
# Queue consuming position updates from booking-engine
rabbitmq.booking-engine-rest-api-queue.position-changes = rest-api-server-queue.booking.%s.POSITION-UPDATED
# Queue consuming transaction created events from booking-engine
rabbitmq.booking-engine-rest-api-queue.transaction-created = rest-api-server-queue.booking.%s.TRANSACTION-CREATED
rabbitmq.booking-engine-rest-api-queue.command-result = rest-api-server-queue.booking.%s.COMMAND-RESULT

# Queue consuming incoming client-side securities modification responses
rabbitmq.reference-data-rest-api-queue.instrument-change-event = rest-api-server-queue.reference-data.%s.INSTRUMENT-CHANGE-EVENT

# Queue consuming order states from order-history-server
rabbitmq.order-history-rest-api-server-queue = rest-api-server-queue.order-history.%s.ALL

tracing.collector.endpoint=http://localhost:4317
management.endpoints.web.exposure.include=health,prometheus,metrics,loggers
management.endpoint.health.group.liveness.include=livenessState,rabbit,diskSpace,hazelcast,clusterRunning
management.endpoint.health.group.readiness.include=readinessState,keycloakRest,accessGatewayRest,rabbit,diskSpace,hazelcast,clusterRunning
management.endpoint.health.show-details=always
management.endpoint.health.probes.enabled=true
management.endpoint.loggers.enabled=true
management.health.livenessState.enabled=true
management.health.readinessState.enabled=true
management.metrics.tags.wyden_service=rest-api-server

mdm-heartbeat-interval = 15
# ISO-8601, empty for no sampling
marketdata.tick.sampling.duration = PT1S
# ISO-8601, empty for no sampling
marketdata.orderbook.sampling.duration = PT1S
# if set to true, client side market data requests will always go through broker-config-service.
# else, requests with known streamId will be routed to pricing-service directly
marketdata.clientside.renewViaConfigService = true

booking.command.response.timeoutInSeconds = 30

server.error.include-stacktrace=never
server.error.include-message=never

rest-api-server.query-limits.portfolio=100
