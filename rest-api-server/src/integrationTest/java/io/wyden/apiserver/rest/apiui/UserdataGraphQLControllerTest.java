package io.wyden.apiserver.rest.apiui;

import io.wyden.apiserver.OracleSetupExtension;
import io.wyden.apiserver.PostgreSQLSetupExtension;
import io.wyden.apiserver.rest.WithMockCustomUser;
import io.wyden.apiserver.rest.apiui.permissions.PermissionsIntegrationTest;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.graphql.test.tester.GraphQlTester;

import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

public abstract class UserdataGraphQLControllerTest extends PermissionsIntegrationTest {

    @ExtendWith(OracleSetupExtension.class)
    @Disabled
    public static class OracleUserdataGraphQLControllerTest extends UserdataGraphQLControllerTest {
    }

    @ExtendWith(PostgreSQLSetupExtension.class)
    public static class PostgresUserdataGraphQLControllerTest extends UserdataGraphQLControllerTest {
    }

    @Test
    @WithMockCustomUser(name = "zenek")
    void saveAndFindUserData() {
        // when
        String testData = "my-test-data";
        GraphQlTester.Response saveResponse = executeFile("update-userdata", Map.of("request", testData));
        saveResponse.errors().verify();

        // then
        GraphQlTester.Response queryResponse = executeFile("query-userdata", Map.of());
        assertThat(queryResponse.path("userData.data").entity(String.class).get()).isEqualTo(testData);
    }

    @Test
    @WithMockCustomUser(name = "franek")
    void absentUserDataReturnsEmptyString() {
        // given
        // no inserts

        // when
        GraphQlTester.Response queryResponse = executeFile("query-userdata", Map.of());

        // then
        assertThat(queryResponse.path("userData.data").entity(String.class).get()).isEmpty();
    }

    @Test
    @WithMockCustomUser(name = "brajanek")
    void saveUpdateAndFindUserData() {
        // given
        String testData = "my-test-data";
        GraphQlTester.Response saveResponse = executeFile("update-userdata", Map.of("request", testData));
        saveResponse.errors().verify();

        // when
        String updatedTestData = "my-test-data-updated";
        GraphQlTester.Response saveResponse2 = executeFile("update-userdata", Map.of("request", updatedTestData));
        saveResponse2.errors().verify();

        // then
        GraphQlTester.Response queryResponse = executeFile("query-userdata", Map.of());
        assertThat(queryResponse.path("userData.data").entity(String.class).get()).isEqualTo(updatedTestData);
    }
}
