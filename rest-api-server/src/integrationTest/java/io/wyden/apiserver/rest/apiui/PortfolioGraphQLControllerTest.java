package io.wyden.apiserver.rest.apiui;

import io.wyden.apiserver.rest.WithMockCustomUser;
import io.wyden.apiserver.rest.apiui.permissions.PermissionsIntegrationTestNoDb;
import io.wyden.apiserver.rest.referencedata.portfolio.model.PortfolioResponseDto;
import io.wyden.apiserver.rest.referencedata.portfolio.model.PortfolioSearchInputDto;
import io.wyden.apiserver.rest.referencedata.portfolio.service.PortfolioService;
import io.wyden.apiserver.rest.referencedata.portfolio.service.SecuredPortfolioService;
import io.wyden.apiserver.rest.security.model.Scope;
import io.wyden.cloud.utils.rest.pagination.PaginationModel;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.common.CursorConnection;
import io.wyden.published.common.CursorEdge;
import io.wyden.published.common.CursorNode;
import io.wyden.published.common.PageInfo;
import io.wyden.published.referencedata.Portfolio;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.graphql.test.tester.GraphQlTester;
import reactor.core.publisher.Mono;

import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class PortfolioGraphQLControllerTest extends PermissionsIntegrationTestNoDb {

    @Autowired
    private PortfolioService portfolioService;

    @MockBean
    SecuredPortfolioService securedPortfolioService;

    @Test
    @WithMockCustomUser
    void givenPortfolioAndPermission_whenGetPortfolioById_PortfolioIsReturned() {
        grant("portfolio", "read", "123");
        when(portfolioService.findById("123")).thenReturn(Optional.of(createPortfolio("123")));
        GraphQlTester.Response response = executeFile("portfolio-by-id", Map.of("portfolioId", "123"));
        response.errors().verify();
        PortfolioResponseDto result = response.path("data.portfolioById").entity(PortfolioResponseDto.class).get();
        assertThat(result.id()).isEqualTo("123");
    }

    @Test
    @WithMockCustomUser
    void givenNoPortfolioAndPermission_whenGetPortfolioById_emptyIsReturned() {
        grant("portfolio", "read", "123");
        GraphQlTester.Response response = executeFile("portfolio-by-id", Map.of("portfolioId", "123"));
        response.errors().verify();
        response.path("data.portfolioById").valueIsNull();
    }

    @Test
    @WithMockCustomUser
    void givenPortfolioAndNoPermission_whenGetPortfolioById_emptyIsReturned() {
        when(portfolioService.findById("123")).thenReturn(Optional.of(createPortfolio("123")));
        GraphQlTester.Response response = executeFile("portfolio-by-id", Map.of("portfolioId", "123"));
        response.errors().verify();
        response.path("data.portfolioById").valueIsNull();
    }

    @Test
    @Disabled
    @WithMockCustomUser
    void testAllPortfoliosAreReturnedRegardlessPermissions() {
        grant("portfolio", "read", "123");
        grant("portfolio", "read", "456");
        CursorConnection portfolioConnection = createPortfolioConnection("123", "456");
        when(portfolioService.getPortfolios(any(PortfolioSearchInputDto.class))).thenReturn(Mono.just(portfolioConnection));

        // we try to access two portfolios that we have access to and one that we have no access to
        List<String> portfolioIds = List.of("123", "456", "unauthorized");
        PortfolioSearchInputDto searchAllPortfolios = new PortfolioSearchInputDto(null, portfolioIds, List.of(), List.of(), null, null, null, null, null, "");
        GraphQlTester.Response response = executeFile("portfolio-search", Map.of("search", searchAllPortfolios));

        PaginationModel.PageInfo pageInfoEntity = response.path("portfolioSearch.pageInfo").entity(PaginationModel.PageInfo.class).get();
        List<String> cursorListEntity = response.path("portfolioSearch.edges[*].cursor").entity(List.class).get();

        ArgumentCaptor<PortfolioSearchInputDto> argumentCaptor = ArgumentCaptor.forClass(PortfolioSearchInputDto.class);
        verify(portfolioService).getPortfolios(argumentCaptor.capture());
        assertThat(argumentCaptor.getValue().portfolioIds()).containsExactlyInAnyOrder("456", "123", "unauthorized");
        assertThat(pageInfoEntity.endCursor()).isEqualTo(portfolioConnection.getPageInfo().getEndCursor());
        assertThat(pageInfoEntity.hasNextPage()).isEqualTo(portfolioConnection.getPageInfo().getHasNextPage());
        assertThat(cursorListEntity).hasSameSizeAs(portfolioConnection.getEdgesList());
    }

    @Test
    @Disabled
    @WithMockCustomUser
    void testAllPortfoliosAreReturnedRegardlessOfPermsissionOrScope() {
        grant("portfolio", "read", "123");
        grant("portfolio", "manage", "456");
        CursorConnection portfolioConnection = createPortfolioConnection("456");
        when(portfolioService.getPortfolios(any(PortfolioSearchInputDto.class))).thenReturn(Mono.just(portfolioConnection));

        // we try to access two portfolios that we have access to and one that we have no access to
        List<String> portfolioIds = List.of("123", "456", "unauthorized");
        PortfolioSearchInputDto searchAllPortfolios = new PortfolioSearchInputDto(null, portfolioIds, List.of(Scope.MANAGE), List.of(), null, null,null, null, null, "");
        GraphQlTester.Response response = executeFile("portfolio-search", Map.of("search", searchAllPortfolios));

        PaginationModel.PageInfo pageInfoEntity = response.path("portfolioSearch.pageInfo").entity(PaginationModel.PageInfo.class).get();
        List<String> cursorListEntity = response.path("portfolioSearch.edges[*].cursor").entity(List.class).get();

        ArgumentCaptor<PortfolioSearchInputDto> argumentCaptor = ArgumentCaptor.forClass(PortfolioSearchInputDto.class);
        verify(portfolioService).getPortfolios(argumentCaptor.capture());
        assertThat(argumentCaptor.getValue().portfolioIds()).containsExactlyInAnyOrder("123", "456", "unauthorized");

        assertThat(pageInfoEntity.endCursor()).isEqualTo(portfolioConnection.getPageInfo().getEndCursor());
        assertThat(pageInfoEntity.hasNextPage()).isEqualTo(portfolioConnection.getPageInfo().getHasNextPage());
        assertThat(cursorListEntity).hasSameSizeAs(portfolioConnection.getEdgesList());
    }

    @Test
    @WithMockCustomUser
    void testNoPortfoliosAreReturnedForUserWithNoAccess() {
        CursorConnection portfolioConnection = createPortfolioConnection("123", "456", "789");
        when(portfolioService.getPortfolios(any(PortfolioSearchInputDto.class))).thenReturn(Mono.just(portfolioConnection));

        // we have no access to any portfolios
        PortfolioSearchInputDto searchAllPortfolios = new PortfolioSearchInputDto(null, List.of(), List.of(), List.of(), null, null, null, null, null, "");
        GraphQlTester.Response response = executeFile("portfolio-search", Map.of("search", searchAllPortfolios));

        PaginationModel.PageInfo pageInfoEntity = response.path("portfolioSearch.pageInfo").entity(PaginationModel.PageInfo.class).get();
        List<String> cursorListEntity = response.path("portfolioSearch.edges[*].cursor").entity(List.class).get();

        assertThat(pageInfoEntity.endCursor()).isBlank();
        assertThat(pageInfoEntity.hasNextPage()).isFalse();
        assertThat(cursorListEntity).isEmpty();
    }

    @Test
    @WithMockCustomUser
    void testPortfoliosWithDynamicAndStaticScopes() {
        grant("portfolio", "trade");
        grant("portfolio", "read", "456");
        CursorConnection portfolioConnection = createPortfolioConnection("456");
        when(portfolioService.getPortfoliosCursor(any(PortfolioSearchInputDto.class))).thenReturn(portfolioConnection);

        // we try to access two portfolios that we have access to and one that we have no access to
        List<String> portfolioIds = List.of("123", "456", "unauthorized");
        PortfolioSearchInputDto searchAllPortfolios = new PortfolioSearchInputDto(null, portfolioIds, List.of(Scope.MANAGE), List.of(), null, null, null, null, null, "");
        GraphQlTester.Response response = executeFile("portfolio-search", Map.of("search", searchAllPortfolios));

        response.path("data.portfolioSearch.edges[0].node.dynamicScopes").entity(List.class).get().get(0).equals("READ");
        response.path("data.portfolioSearch.edges[0].node.scopes").entity(List.class).get().get(0).equals("TRADE");
    }

    @Test
    @WithMockCustomUser
    void testArchivedPortfolio() {
        grant("portfolio", "read");
        CursorConnection portfolioConnection = createPortfolioConnection("456");
        when(portfolioService.getPortfoliosCursor(any(PortfolioSearchInputDto.class))).thenReturn(portfolioConnection);

        List<String> portfolioIds = List.of("456");
        PortfolioSearchInputDto searchAllPortfolios = new PortfolioSearchInputDto(null, portfolioIds, List.of(Scope.MANAGE), List.of(), null, true, null, null, null, "");
        GraphQlTester.Response response = executeFile("portfolio-search", Map.of("search", searchAllPortfolios));

        String s = response.path("data.portfolioSearch.edges[0].node.archivedAt").entity(String.class).get();
        assertThat(s).isNotBlank();
    }

    private Portfolio createPortfolio(String portfolioId) {
        return Portfolio.newBuilder()
            .setId(portfolioId)
            .setCreatedAt(DateUtils.toIsoUtcTime(ZonedDateTime.now()))
            .build();
    }

    private CursorConnection createPortfolioConnection(String... ids) {
        List<CursorEdge> edges = Arrays.stream(ids)
            .map(id -> Portfolio.newBuilder()
                .setId(id)
                .setName("portfolio-" + id)
                .setPortfolioCurrency("USD")
                .setCreatedAt(DateUtils.toIsoUtcTime(ZonedDateTime.now()))
                .setArchivedAt(DateUtils.toIsoUtcTime(ZonedDateTime.now()))
                .build())
            .map(portfolio -> CursorEdge.newBuilder()
                .setCursor(portfolio.getName())
                .setNode(CursorNode.newBuilder()
                    .setPortfolio(portfolio)
                    .build())
                .build())
            .toList();


        PageInfo pageInfo = PageInfo.newBuilder()
            .setEndCursor(ids[ids.length - 1])
            .setHasNextPage(false)
            .build();

        return CursorConnection.newBuilder()
            .setPageInfo(pageInfo)
            .addAllEdges(edges)
            .build();
    }
}
