package io.wyden.apiserver.rest.apiui.permissions;

import io.wyden.apiserver.rest.WithMockCustomUser;
import io.wyden.apiserver.rest.brokerdesk.config.BrokerDeskConfigModel;
import io.wyden.apiserver.rest.brokerdesk.config.BrokerDeskConfigService;
import io.wyden.published.brokerdesk.AutoHedgerValidation;
import io.wyden.published.brokerdesk.Validations;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.springframework.beans.factory.annotation.Autowired;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

public class BrokerDeskGraphQLPermissionsTest extends PermissionsIntegrationTestNoDb {

    private static final BrokerDeskConfigModel.PricingConfiguration PRICING_CONFIGURATION = new BrokerDeskConfigModel.PricingConfiguration(List.of(),null);
    private static final BrokerDeskConfigModel.PricingConfigurationInput PRICING_CONFIGURATION_INPUT = new BrokerDeskConfigModel.PricingConfigurationInput(List.of(), null);
    private static final BrokerDeskConfigModel.PortfolioConfiguration PORTFOLIO_CONFIGURATION = new BrokerDeskConfigModel.PortfolioConfiguration("id", "name", null, null, null, null, null);
    private static final BrokerDeskConfigModel.PortfolioConfigurationInput PORTFOLIO_CONFIGURATION_INPUT = new BrokerDeskConfigModel.PortfolioConfigurationInput("id", "name", null, null, null, null, null);
    private static final BrokerDeskConfigModel.ExecutionConfiguration EXECUTION_CONFIGURATION = new BrokerDeskConfigModel.ExecutionConfiguration(BrokerDeskConfigModel.TradingMode.AGENCY, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null);
    private static final BrokerDeskConfigModel.ExecutionConfigurationInput EXECUTION_CONFIGURATION_INPUT = new BrokerDeskConfigModel.ExecutionConfigurationInput(BrokerDeskConfigModel.TradingMode.AGENCY, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null);
    private static final BrokerDeskConfigModel.HedgingConfiguration HEDGING_CONFIGURATION = new BrokerDeskConfigModel.HedgingConfiguration(false, null, null, List.of(new BrokerDeskConfigModel.ThresholdConfiguration(null, null, null, null, null)));
    private static final BrokerDeskConfigModel.HedgingConfigurationInput HEDGING_CONFIGURATION_INPUT = new BrokerDeskConfigModel.HedgingConfigurationInput(false, null, List.of(new BrokerDeskConfigModel.ThresholdConfigurationInput(null, null, null, null, null)));
    private static final BrokerDeskConfigModel.PortfolioGroupConfiguration PORTFOLIO_GROUP_CONFIGURATION = new BrokerDeskConfigModel.PortfolioGroupConfiguration("id", "name", null, EXECUTION_CONFIGURATION, PRICING_CONFIGURATION, HEDGING_CONFIGURATION, List.of());
    private static final BrokerDeskConfigModel.PortfolioGroupConfigurationFlat PORTFOLIO_GROUP_CONFIGURATION_LIST = new BrokerDeskConfigModel.PortfolioGroupConfigurationFlat("id", "name", null);
    private static final BrokerDeskConfigModel.PortfolioGroupConfigurationInput PORTFOLIO_GROUP_CONFIGURATION_INPUT = new BrokerDeskConfigModel.PortfolioGroupConfigurationInput("id", "name", null, EXECUTION_CONFIGURATION_INPUT, PRICING_CONFIGURATION_INPUT, HEDGING_CONFIGURATION_INPUT, List.of());
    private static final BrokerDeskConfigModel.InstrumentPricingConfigurationInput INSTRUMENT_PRICING_CONFIGURATION_INPUT = new BrokerDeskConfigModel.InstrumentPricingConfigurationInput(List.of(), null);
    private static final BrokerDeskConfigModel.InstrumentConfigurationInput INSTRUMENT_CONFIGURATION_INPUT = new BrokerDeskConfigModel.InstrumentConfigurationInput("instrumentId", null, EXECUTION_CONFIGURATION_INPUT, INSTRUMENT_PRICING_CONFIGURATION_INPUT);
    private static final BrokerDeskConfigModel.ResetConfigurationInput RESET_CONFIGURATION_INPUT = new BrokerDeskConfigModel.ResetConfigurationInput(BrokerDeskConfigModel.ConfigurationLevel.PORTFOLIO_GROUP, BrokerDeskConfigModel.ConfigurationType.EXECUTION, "resourceId", "instrumentId");

    private static final String REQUEST = "request";
    private static final String ID = "test";
    private static final String PORTFOLIO_ID = "portfolioId";
    private static final String PORTFOLIO_GROUP_ID = "portfolioGroupId";

    private static final Mono<String> STRING_MONO = Mono.just("something");
    private static final Mono<Validations> VALIDATIONS_MONO = Mono.just(Validations.newBuilder().build());
    private static final Mono<AutoHedgerValidation> AUTO_HEDGER_VALIDATIONS_MONO = Mono.just(AutoHedgerValidation.newBuilder().build());

    @Autowired
    BrokerDeskConfigService brokerDeskConfigService;

    @BeforeEach
    void setup() {
        when(brokerDeskConfigService.getPortfolioConfiguration(anyString())).thenReturn(Mono.just(PORTFOLIO_CONFIGURATION));
        when(brokerDeskConfigService.getPortfolioGroupConfiguration(anyString())).thenReturn(Mono.just(PORTFOLIO_GROUP_CONFIGURATION));
        when(brokerDeskConfigService.getPortfolioGroupConfigList()).thenReturn(Mono.just(List.of(PORTFOLIO_GROUP_CONFIGURATION_LIST)));
        when(brokerDeskConfigService.validatePortfolioConfiguration(anyString())).thenReturn(VALIDATIONS_MONO);
        when(brokerDeskConfigService.updatePortfolioConfiguration(anyString(), any(BrokerDeskConfigModel.PortfolioConfigurationInput.class))).thenReturn(VALIDATIONS_MONO);
        when(brokerDeskConfigService.updatePortfolioConfiguration(anyString(), any(BrokerDeskConfigModel.PortfolioConfigurationInput.class))).thenReturn(VALIDATIONS_MONO);
        when(brokerDeskConfigService.updatePortfolioConfiguration(anyString(), any(BrokerDeskConfigModel.PortfolioConfigurationInput.class))).thenReturn(VALIDATIONS_MONO);
        when(brokerDeskConfigService.updatePortfolioExecutionConfiguration(anyString(), any(BrokerDeskConfigModel.ExecutionConfigurationInput.class))).thenReturn(VALIDATIONS_MONO);
        when(brokerDeskConfigService.updatePortfolioPricingConfiguration(anyString(), any(BrokerDeskConfigModel.PricingConfigurationInput.class))).thenReturn(VALIDATIONS_MONO);
        when(brokerDeskConfigService.updatePortfolioHedgingConfiguration(anyString(), any(BrokerDeskConfigModel.HedgingConfigurationInput.class))).thenReturn(AUTO_HEDGER_VALIDATIONS_MONO);
        when(brokerDeskConfigService.updatePortfolioGroupConfiguration(anyString(), any(BrokerDeskConfigModel.PortfolioGroupConfigurationInput.class))).thenReturn(STRING_MONO);
        when(brokerDeskConfigService.updatePortfolioGroupExecutionConfiguration(anyString(), any(BrokerDeskConfigModel.ExecutionConfigurationInput.class))).thenReturn(STRING_MONO);
        when(brokerDeskConfigService.updatePortfolioGroupPricingConfiguration(anyString(), any(BrokerDeskConfigModel.PricingConfigurationInput.class))).thenReturn(STRING_MONO);
        when(brokerDeskConfigService.updatePortfolioGroupHedgingConfiguration(anyString(), any(BrokerDeskConfigModel.HedgingConfigurationInput.class))).thenReturn(STRING_MONO);
        when(brokerDeskConfigService.updatePortfolioInstrumentConfiguration(anyString(), any(BrokerDeskConfigModel.InstrumentConfigurationInput.class))).thenReturn(VALIDATIONS_MONO);
        when(brokerDeskConfigService.updatePortfolioGroupInstrumentConfiguration(anyString(), any(BrokerDeskConfigModel.InstrumentConfigurationInput.class))).thenReturn(STRING_MONO);
        when(brokerDeskConfigService.resetConfiguration(any(BrokerDeskConfigModel.ResetConfigurationInput.class))).thenReturn(VALIDATIONS_MONO);
        when(brokerDeskConfigService.deletePortfolioConfiguration(anyString())).thenReturn(STRING_MONO);
        when(brokerDeskConfigService.deletePortfolioGroupConfiguration(anyString())).thenReturn(STRING_MONO);
    }

    @Disabled("TO FIX in AC-4513")
    @ParameterizedTest
    @MethodSource("brokerConfigEndpoints")
    @WithMockCustomUser
    void brokerConfigEndpoints_noPermission(String queryFileName, Map<String, Object> parameters) {
        AssertionError exception = assertThrows(AssertionError.class, () -> executeAndVerifyFile(queryFileName, parameters));
        assertThat(exception).hasMessageContaining("Access Denied");
    }

    @ParameterizedTest
    @MethodSource("brokerConfigEndpointsNeedingReadPermission")
    @WithMockCustomUser
    void brokerConfigEndpoints_withReadPermission(String queryFileName, Map<String, Object> parameters) {
        grant("broker.config", "read");
        assertDoesNotThrow(() -> executeAndVerifyFile(queryFileName, parameters));
    }

    @ParameterizedTest
    @MethodSource("brokerConfigEndpointsNeedingBrokerConfigReadAndPortfolioReadPermission")
    @WithMockCustomUser
    void brokerConfigEndpoints_withBrokerConfigReadAndPortfolioReadPermission(String queryFileName, Map<String, Object> parameters) {
        grant("broker.config", "read");
        grant("portfolio", "read", ID);
        assertDoesNotThrow(() -> executeAndVerifyFile(queryFileName, parameters));
    }

    @Disabled("TO FIX in AC-4513")
    @ParameterizedTest
    @MethodSource("brokerConfigEndpointsNeedingBrokerConfigManagePermission")
    @WithMockCustomUser
    void brokerConfigEndpoints_withManagePermission(String queryFileName, Map<String, Object> parameters) {
        grant("broker.config", "manage");
        assertDoesNotThrow(() -> executeAndVerifyFile(queryFileName, parameters));
    }

    @Disabled("TO FIX in AC-4513")
    @ParameterizedTest
    @MethodSource("brokerConfigEndpointsNeedingBrokerConfigManageAndPortfolioManagePermission")
    @WithMockCustomUser
    void brokerConfigEndpoints_withBrokerConfigManageAndPortfolioManagePermission(String queryFileName, Map<String, Object> parameters) {
        grant("broker.config", "manage");
        grant("portfolio", "manage");
        assertDoesNotThrow(() -> executeAndVerifyFile(queryFileName, parameters));
    }

    private static Stream<Arguments> brokerConfigEndpoints() {
        return Stream.of(
            brokerConfigEndpointsNeedingReadPermission(),
            brokerConfigEndpointsNeedingBrokerConfigReadAndPortfolioReadPermission(),
            brokerConfigEndpointsNeedingBrokerConfigManagePermission(),
            brokerConfigEndpointsNeedingBrokerConfigManageAndPortfolioManagePermission()
        ).flatMap(Function.identity());
    }

    private static Stream<Arguments> brokerConfigEndpointsNeedingReadPermission() {
        return Stream.of(
            Arguments.of("portfolio-group-configuration", Map.of("id", ID)),
            Arguments.of("portfolio-group-configuration-list", Map.of())
        );
    }

    private static Stream<Arguments> brokerConfigEndpointsNeedingBrokerConfigReadAndPortfolioReadPermission() {
        return Stream.of(
            Arguments.of("portfolio-configuration", Map.of("id", ID)),
            Arguments.of("portfolio-config-validation", Map.of(PORTFOLIO_ID, ID))
        );
    }

    private static Stream<Arguments> brokerConfigEndpointsNeedingBrokerConfigManagePermission() {
        return Stream.of(
            Arguments.of("update-portfolio-group-configuration", Map.of(PORTFOLIO_GROUP_ID, ID, REQUEST, PORTFOLIO_GROUP_CONFIGURATION_INPUT)),
            Arguments.of("update-portfolio-group-execution-configuration", Map.of(PORTFOLIO_GROUP_ID, ID, REQUEST, EXECUTION_CONFIGURATION_INPUT)),
            Arguments.of("update-portfolio-group-pricing-configuration", Map.of(PORTFOLIO_GROUP_ID, ID, REQUEST, PRICING_CONFIGURATION_INPUT)),
            Arguments.of("update-portfolio-group-hedging-configuration", Map.of(PORTFOLIO_GROUP_ID, ID, REQUEST, HEDGING_CONFIGURATION_INPUT)),
            Arguments.of("update-portfolio-group-instrument-configuration", Map.of(PORTFOLIO_GROUP_ID, ID, REQUEST, INSTRUMENT_CONFIGURATION_INPUT)),
            Arguments.of("reset-configuration", Map.of(REQUEST, RESET_CONFIGURATION_INPUT)),
            Arguments.of("delete-portfolio-group-configuration", Map.of(PORTFOLIO_GROUP_ID, ID))
        );
    }

    private static Stream<Arguments> brokerConfigEndpointsNeedingBrokerConfigManageAndPortfolioManagePermission() {
        return Stream.of(
            Arguments.of("update-portfolio-configuration", Map.of(PORTFOLIO_ID, ID, REQUEST, PORTFOLIO_CONFIGURATION_INPUT)),
            Arguments.of("update-portfolio-execution-configuration", Map.of(PORTFOLIO_ID, ID, REQUEST, EXECUTION_CONFIGURATION_INPUT)),
            Arguments.of("update-portfolio-pricing-configuration", Map.of(PORTFOLIO_ID, ID, REQUEST, PRICING_CONFIGURATION_INPUT)),
            Arguments.of("update-portfolio-hedging-configuration", Map.of(PORTFOLIO_ID, ID, REQUEST, HEDGING_CONFIGURATION_INPUT)),
            Arguments.of("update-portfolio-instrument-configuration", Map.of(PORTFOLIO_ID, ID, REQUEST, INSTRUMENT_CONFIGURATION_INPUT)),
            Arguments.of("delete-portfolio-configuration", Map.of(PORTFOLIO_ID, ID))
        );
    }
}
