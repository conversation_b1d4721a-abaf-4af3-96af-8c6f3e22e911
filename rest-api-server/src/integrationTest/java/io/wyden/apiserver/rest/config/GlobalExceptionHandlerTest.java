package io.wyden.apiserver.rest.config;

import io.wyden.apiserver.rest.SecurityIntegrationTestNoDb;
import io.wyden.apiserver.rest.WithMockCustomUser;
import jakarta.validation.ConstraintViolationException;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.test.autoconfigure.web.reactive.AutoConfigureWebTestClient;
import org.springframework.context.annotation.Import;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.reactive.server.WebTestClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.NoSuchElementException;

import static org.junit.jupiter.api.Assertions.assertTrue;

@WithMockCustomUser
@Import({GlobalExceptionHandler.class, GlobalExceptionHandlerTest.TestController.class})
@AutoConfigureWebTestClient
@EnableAutoConfiguration(exclude = {
    DataSourceAutoConfiguration.class
})
@TestPropertySource(properties = {
    "logging.level.org.springframework=DEBUG",
    "logging.level.org.springframework.web=TRACE"
})
class GlobalExceptionHandlerTest extends SecurityIntegrationTestNoDb {

    @Autowired
    private WebTestClient webTestClient;

    @RestController
    @RequestMapping("/test")
    public static class TestController {

        @GetMapping("/constraint-violation")
        public void throwConstraintViolationException() {
            throw new ConstraintViolationException("Validation failed", null);
        }

        @GetMapping("/access-denied")
        public void throwAccessDeniedException() {
            throw new AccessDeniedException("Access denied");
        }

        @GetMapping("/no-such-element")
        public void throwNoSuchElementException() {
            throw new NoSuchElementException("Element not found");
        }

        @GetMapping("/runtime-exception")
        public void throwRuntimeException() {
            throw new RuntimeException("Runtime exception occurred");
        }

        @GetMapping("/exception")
        public void throwGenericException() throws Exception {
            throw new Exception("Generic exception occurred");
        }

        @GetMapping("/illegal-argument")
        public void throwIllegalArgumentException() {
            throw new IllegalArgumentException("Illegal argument");
        }
    }

    @Test
    void testBadRequestException() {
        webTestClient.get()
            .uri("/test/constraint-violation")
            .exchange()
            .expectStatus().isBadRequest();
    }

    @Test
    void testAccessDeniedException() {
        webTestClient.get()
            .uri("/test/access-denied")
            .exchange()
            .expectStatus().isForbidden()
            .expectBody(String.class).value(body -> assertTrue(body.startsWith("Access denied")));
    }

    @Test
    void testNotFoundException() {
        webTestClient.get()
            .uri("/test/no-such-element")
            .exchange()
            .expectStatus().isNotFound()
            .expectBody(String.class).isEqualTo("Element not found");
    }

    @Test
    void testInternalErrorException() {
        webTestClient.get()
            .uri("/test/runtime-exception")
            .exchange()
            .expectStatus().isEqualTo(500)
            .expectBody(String.class).isEqualTo("Runtime exception occurred");
    }
}