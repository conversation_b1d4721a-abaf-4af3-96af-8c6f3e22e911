package io.wyden.apiserver.rest.apiui;

import io.wyden.apiserver.rest.WithMockCustomUser;
import io.wyden.apiserver.rest.apiui.permissions.PermissionsIntegrationTestNoDb;
import io.wyden.apiserver.rest.security.model.Scope;
import io.wyden.apiserver.rest.targetregistry.TargetRegistryHttpClient;
import io.wyden.apiserver.rest.venueaccount.VenueAccountService;
import io.wyden.apiserver.rest.venueaccount.model.ActivateVenueAccountRequest;
import io.wyden.apiserver.rest.venueaccount.model.CreateVenueAccountRequest;
import io.wyden.apiserver.rest.venueaccount.model.DeactivateVenueAccountRequest;
import io.wyden.apiserver.rest.venueaccount.model.VenueAccountDetailsInput;
import io.wyden.apiserver.rest.venueaccount.model.VenueAccountDetailsResponse;
import io.wyden.apiserver.rest.venueaccount.model.VenueAccountsPerVenue;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.referencedata.VenueAccount;
import io.wyden.referencedata.client.VenueAccountCacheFacade;
import org.assertj.core.groups.Tuple;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.graphql.test.tester.GraphQlTester;
import reactor.core.publisher.Mono;

import java.time.ZonedDateTime;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

class VenueAccountGraphQLControllerTest extends PermissionsIntegrationTestNoDb {

    @MockBean
    VenueAccountCacheFacade venueAccountCacheFacade;
    @MockBean
    VenueAccountService venueAccountService;

    @Test
    @WithMockCustomUser
    void givenVenueAccountAndPermission_whenGetVenueAccountById_VenueAccountIsReturned() {
        grant("venue.account", "read", "123");
        when(venueAccountService.find("123")).thenReturn(Optional.of(createAccount("123")));
        GraphQlTester.Response response = executeFile("venue-account-by-id", Map.of("venueAccountId", "123"));
        response.errors().verify();
        VenueAccountsPerVenue.VenueAccount result = response.path("data.venueAccountById").entity(VenueAccountsPerVenue.VenueAccount.class).get();
        assertThat(result.venueAccountId()).isEqualTo("123");
    }

    @Test
    @WithMockCustomUser
    void givenNoVenueAccountAndPermission_whenGetVenueAccountById_emptyIsReturned() {
        grant("venue.account", "read", "123");
        GraphQlTester.Response response = executeFile("venue-account-by-id", Map.of("venueAccountId", "123"));
        response.errors().verify();
        response.path("data.venueAccountById").valueIsNull();
    }

    @Test
    @WithMockCustomUser
    void givenVenueAccountAndNoPermission_whenGetVenueAccountById_emptyIsReturned() {
        when(venueAccountService.find("123")).thenReturn(Optional.of(createAccount("123")));
        GraphQlTester.Response response = executeFile("venue-account-by-id", Map.of("venueAccountId", "123"));
        response.errors().verify();
        response.path("data.venueAccountById").valueIsNull();
    }

    @WithMockCustomUser
    @Test
    void returnedVenueAccountDetailsResponseShouldContainPermissionScopesOfGivenUser() {
        // given
        grant("venue.account", "manage", "venueAccount1");
        grant("venue.account", "trade", "venueAccount1");
        grant("venue.account", "read", "venueAccount1");
        grant("venue.account", "manage", "venueAccount2");
        grant("venue.account", "read", "venueAccount3");
        String now = DateUtils.toIsoUtcTime(ZonedDateTime.now());
        when(venueAccountService.findAll())
            .thenReturn(Stream.of(
                VenueAccount.newBuilder()
                    .setId("venueAccount1")
                    .setVenueAccountName("Venue Account 1")
                    .setVenueName("VenueA")
                    .setOwnerUsername("Heniek")
                    .setCreatedAt(now)
                    .build(),
                VenueAccount.newBuilder()
                    .setId("venueAccount2")
                    .setVenueAccountName("Venue Account 2")
                    .setVenueName("VenueA")
                    .setOwnerUsername("Zdzichu")
                    .setCreatedAt(now)
                    .build(),
                VenueAccount.newBuilder()
                    .setId("venueAccount3")
                    .setVenueAccountName("Venue Account 3")
                    .setVenueName("VenueB")
                    .setOwnerUsername("Staszek")
                    .setCreatedAt(now)
                    .build()
                ));

        when(permissionChecker.hasUserPermission(any(), any(), eq("manage"), eq("venueAccount1"))).thenReturn(true);
        when(permissionChecker.hasUserPermission(any(), any(), eq("trade"), eq("venueAccount1"))).thenReturn(true);
        when(permissionChecker.hasUserPermission(any(), any(), eq("read"), eq("venueAccount1"))).thenReturn(true);
        when(permissionChecker.hasUserPermission(any(), any(), eq("manage"), eq("venueAccount2"))).thenReturn(true);
        when(permissionChecker.hasUserPermission(any(), any(), eq("read"), eq("venueAccount3"))).thenReturn(true);

        // when
        GraphQlTester.Response response = executeFile("venue-accounts", Map.of());

        // then
        List<VenueAccountsPerVenue> venueAccountsPerVenues = response.path("venueAccounts.[*]").entityList(VenueAccountsPerVenue.class).get();
        assertThat(venueAccountsPerVenues).hasSize(2);
        assertThat(venueAccountsPerVenues).extracting(VenueAccountsPerVenue::venue, v -> new HashSet<>(v.venueAccounts()))
            .containsExactlyInAnyOrder(
                Tuple.tuple("VenueA", Set.of(
                    new VenueAccountsPerVenue.VenueAccount("venueAccount1", "Venue Account 1", "", now, "", Set.of(Scope.MANAGE, Scope.TRADE, Scope.READ), Set.of(Scope.MANAGE, Scope.TRADE, Scope.READ)),
                    new VenueAccountsPerVenue.VenueAccount("venueAccount2", "Venue Account 2", "", now, "", Set.of(Scope.MANAGE), Set.of(Scope.MANAGE)))
                ), Tuple.tuple("VenueB", Set.of(
                    new VenueAccountsPerVenue.VenueAccount("venueAccount3", "Venue Account 3", "", now, "", Set.of(Scope.READ), Set.of(Scope.READ))
                ))
            );
    }

    @WithMockCustomUser
    @Test
    void returnedVenueAccountsPerVenueResponseShouldContainPermissionScopesOfGivenUserAsDynamicScopes() {
        // given
        grant("venue.account", "manage", "venueAccount1");
        grant("venue.account", "trade", "venueAccount1");
        grant("venue.account", "read", "venueAccount1");
        when(venueAccountCacheFacade.exists("venueAccount1")).thenReturn(true);
        when(venueAccountService.getVenueAccountDetails("venueAccount1"))
            .thenReturn(Mono.just(new TargetRegistryHttpClient.VenueAccountDetailsResponseDto("venueAccount1", "venue", List.of())));
        when(venueAccountCacheFacade.find("venueAccount1")).thenReturn(
            Optional.of(
                VenueAccount.newBuilder()
                    .setId("venueAccount1")
                    .setVenueAccountName("Venue Account 1")
                    .setVenueName("venue")
                    .build()
            ));
        when(permissionChecker.hasUserPermission(any(), any(), eq("manage"), eq("venueAccount1"))).thenReturn(true);
        when(permissionChecker.hasUserPermission(any(), any(), eq("trade"), eq("venueAccount1"))).thenReturn(true);
        when(permissionChecker.hasUserPermission(any(), any(), eq("read"), eq("venueAccount1"))).thenReturn(true);

        // when
        VenueAccountDetailsInput request = new VenueAccountDetailsInput("venueAccount1");
        GraphQlTester.Response response = executeFile("venue-account-details", Map.of("request", request));

        // then
        response.path("venueAccountDetails").entity(VenueAccountDetailsResponse.class).isEqualTo(
            new VenueAccountDetailsResponse("venueAccount1", "Venue Account 1", "venue", null, Set.of(Scope.MANAGE, Scope.TRADE, Scope.READ), Set.of(Scope.MANAGE, Scope.TRADE, Scope.READ))
        );
    }

    @WithMockCustomUser
    @Test
    void returnedVenueAccountsPerVenueResponseShouldContainPermissionScopesOfGivenUserAsDynamicAndStaticScopes() {
        // given
        grant("venue.account", "manage", "venueAccount1");
        grant("venue.account", "trade");
        grant("venue.account", "read", "venueAccount1");
        when(venueAccountCacheFacade.exists("venueAccount1")).thenReturn(true);
        when(venueAccountService.getVenueAccountDetails("venueAccount1"))
            .thenReturn(Mono.just(new TargetRegistryHttpClient.VenueAccountDetailsResponseDto("venueAccount1", "venue", List.of())));
        when(venueAccountCacheFacade.find("venueAccount1")).thenReturn(
            Optional.of(
                VenueAccount.newBuilder()
                    .setId("venueAccount1")
                    .setVenueAccountName("Venue Account 1")
                    .setVenueName("venue")
                    .build()
            ));
        when(permissionChecker.hasUserPermission(any(), any(), eq("manage"), eq("venueAccount1"))).thenReturn(true);
        when(permissionChecker.hasUserPermission(any(), any(), eq("read"), eq("venueAccount1"))).thenReturn(true);
        when(permissionChecker.hasPermission(any(), any(), any(),eq("venue.account"), eq("trade"))).thenReturn(true);
        when(permissionChecker.hasPermission(any(), any(), any(),eq("venue.account"), eq("trade"), eq("venueAccount1"))).thenReturn(true);

        // when
        VenueAccountDetailsInput request = new VenueAccountDetailsInput("venueAccount1");
        GraphQlTester.Response response = executeFile("venue-account-details", Map.of("request", request));

        // then
        response.path("venueAccountDetails").entity(VenueAccountDetailsResponse.class).isEqualTo(
            new VenueAccountDetailsResponse("venueAccount1", "Venue Account 1", "venue", null, Set.of(Scope.TRADE, Scope.MANAGE, Scope.READ), Set.of(Scope.MANAGE, Scope.READ))
        );
    }

    @WithMockCustomUser
    @Test
    void shouldSetFieldWhileThrowingNonUniqueVenueAccountException() {
        //given
        grant("venue.account", "create");
        String venueAccountId = "venueAccount";
        when(venueAccountCacheFacade.exists(venueAccountId)).thenReturn(true);
        //when
        GraphQlTester.Response response = executeFile("create-venue-account", Map.of("request", new CreateVenueAccountRequest(venueAccountId, "test", "test", "test", "test", "test", List.of())));
        //then
        response.errors().expect(responseError -> {
            Map<String, String> validation = (LinkedHashMap<String, String>) ((List) responseError.getExtensions().get("validation")).get(0);
            return validation.get("message").equals("Venue account id: venueAccount already exists in system, please create different id.") && validation.get("field").equals("venueAccountId");
        });
    }

    @WithMockCustomUser
    @Test
    void shouldThrowExceptionIfUserHasNoPermissionToCreateVenueAccount() {
        //given
        String venueAccountName = "venueAccountName";
        when(venueAccountCacheFacade.exists(venueAccountName)).thenReturn(true);
        //when
        GraphQlTester.Response response = executeFile("create-venue-account", Map.of("request", new CreateVenueAccountRequest(venueAccountName, "test", "test", "test", "test", "test", List.of())));
        //then
        response.errors().expect(e -> e.getMessage().equals("Access Denied"));
    }

    @WithMockCustomUser
    @Test
    void shouldNotAllowToSuspendNotExistingVA() {
        //given
        grant("venue.account", "manage", "venueAccount");
        String venueAccountId = "venueAccount";
        when(venueAccountCacheFacade.exists(venueAccountId)).thenReturn(false);
        //when
        GraphQlTester.Response response = executeFile("deactivate-venue-account", Map.of("request", new DeactivateVenueAccountRequest(venueAccountId, "test")));
        //then
        response.errors().expect(responseError -> {
            Map<String, String> validation = (LinkedHashMap<String, String>) ((List) responseError.getExtensions().get("validation")).get(0);
            return validation.get("message").equals("Unable to find venue account: venueAccount, entity missing.")
                   && validation.get("field").equals("venueAccountId");
        });
    }

    @WithMockCustomUser
    @Test
    void shouldNotAllowToActivateNotExistingVA() {
        //given
        grant("venue.account", "manage", "venueAccount");
        String venueAccountId = "venueAccount";
        when(venueAccountCacheFacade.exists(venueAccountId)).thenReturn(false);
        //when
        GraphQlTester.Response response = executeFile("activate-venue-account", Map.of("request", new ActivateVenueAccountRequest(venueAccountId, "test")));
        //then
        response.errors().expect(responseError -> {
            Map<String, String> validation = (LinkedHashMap<String, String>) ((List) responseError.getExtensions().get("validation")).get(0);
            return validation.get("message").equals("Unable to find venue account: venueAccount, entity missing.")
                   && validation.get("field").equals("venueAccountId");
        });
    }

    private VenueAccount createAccount(String venueAccountId) {
        return VenueAccount.newBuilder()
            .setId(venueAccountId)
            .setCreatedAt(DateUtils.toIsoUtcTime(ZonedDateTime.now()))
            .build();
    }
}
