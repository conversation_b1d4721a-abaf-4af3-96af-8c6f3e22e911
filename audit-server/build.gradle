plugins {
    id 'java'
    id 'jaco<PERSON>'
    alias dependencyCatalog.plugins.spring.boot
    alias dependencyCatalog.plugins.dependency.management
    alias dependencyCatalog.plugins.sonarqube
    alias dependencyCatalog.plugins.jacocoToCobertura
}

sonarqube {
    properties {
        property "sonar.projectKey", "audit-server"
        property "sonar.projectName", "Audit Server"
    }
}

dependencies {
    implementation dependencyCatalog.cloud.utils.rabbitmq
    implementation dependencyCatalog.cloud.utils.rabbitmq.destinations
    implementation dependencyCatalog.cloud.utils.telemetry
    implementation dependencyCatalog.cloud.utils.tools
    implementation dependencyCatalog.cloud.utils.spring
    implementation dependencyCatalog.cloud.utils.hazelcast
    implementation dependencyCatalog.published.language.oems
    implementation dependencyCatalog.reference.data.client
    implementation dependencyCatalog.reference.data.domain

    implementation dependencyCatalog.hazelcast
    implementation dependencyCatalog.hazelcast.jet.protobuf

    implementation dependencyCatalog.spring.boot.starter.webflux
    implementation dependencyCatalog.spring.boot.starter.actuator
    implementation dependencyCatalog.spring.boot.starter.data.jpa

    implementation dependencyCatalog.flyway.core
    implementation dependencyCatalog.postgresql
    implementation dependencyCatalog.oracle

    testImplementation dependencyCatalog.cloud.utils.test
    testImplementation dependencyCatalog.spring.boot.starter.test
    testImplementation dependencyCatalog.junit.jupiter.api
    testImplementation dependencyCatalog.reactor.test
    testImplementation(dependencyCatalog.hazelcast) { artifact { classifier = 'tests'} }

    runtimeOnly(dependencyCatalog.netty.resolver.dns.native.macos) { artifact { classifier = 'osx-aarch_64'} }

    testRuntimeOnly dependencyCatalog.junit.jupiter.engine
}

testing {
    suites {
        test {
            useJUnitJupiter()
        }

        integrationTest(JvmTestSuite) {
            dependencies {
                implementation project
                implementation project(':audit-client')
                implementation dependencyCatalog.cloud.utils.rabbitmq
                implementation dependencyCatalog.cloud.utils.rabbitmq.destinations
                implementation dependencyCatalog.cloud.utils.telemetry
                implementation dependencyCatalog.cloud.utils.tools
                implementation dependencyCatalog.cloud.utils.spring
                implementation dependencyCatalog.published.language.oems

                implementation dependencyCatalog.spring.boot.starter.webflux
                implementation dependencyCatalog.spring.boot.starter.test
                implementation dependencyCatalog.spring.boot.starter.data.jpa

                implementation dependencyCatalog.flyway.core
                implementation dependencyCatalog.postgresql
                implementation dependencyCatalog.oracle
                implementation dependencyCatalog.testcontainers
                implementation dependencyCatalog.testcontainers.junit.jupiter
                implementation dependencyCatalog.testcontainers.rabbitmq
                implementation dependencyCatalog.testcontainers.postgresql
                implementation dependencyCatalog.testcontainers.oracle
                implementation dependencyCatalog.cloud.utils.hazelcast
                implementation dependencyCatalog.hazelcast
                implementation(dependencyCatalog.hazelcast) { artifact { classifier = 'tests'} }
            }

            targets {
                all {
                    testTask.configure {
                        shouldRunAfter(test)
                    }
                }
            }
        }
    }
}

bootJar {
    manifest {
        attributes(
                "Implementation-Version": "${version}"
        )
    }
}

bootRun {
    args = ["--tracing.collector.endpoint=http://localhost:4317"]
    jvmArgs = ["-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:9030"]
    environment([
            "FLUENTD_HOST": "localhost",
            "SPRING_PROFILES_ACTIVE": "dev"
    ])
}

tasks.named('check') {
    dependsOn(testing.suites.integrationTest)
}

sonarqube {
    properties {
        property "sonar.projectKey", "audit-server"
        property "sonar.projectName", "Audit Server"
    }
}

test {
    finalizedBy jacocoTestReport
}

jacocoTestReport {
    reports {
        xml.enabled true
        csv.enabled true
    }

    getExecutionData().setFrom(fileTree(buildDir).include("/jacoco/*.exec"))
}

jacocoToCobertura {
    inputFile.set(file("$buildDir/reports/jacoco/test/jacocoTestReport.xml"))
    outputFile.set(file("$buildDir/reports/jacoco/test/cobertura.xml"))
}

plugins.withType(JacocoPlugin) {
    tasks["test"].finalizedBy 'jacocoTestReport'
    tasks["integrationTest"].finalizedBy 'jacocoTestReport'
    tasks["jacocoTestReport"].finalizedBy 'jacocoToCobertura'
    tasks["jacocoToCobertura"].dependsOn 'jacocoTestReport'
}

import org.gradle.api.tasks.testing.logging.TestExceptionFormat
import org.gradle.api.tasks.testing.logging.TestLogEvent

tasks.withType(Test) {
    testLogging {
        info {
            events TestLogEvent.FAILED,
                    TestLogEvent.PASSED,
                    TestLogEvent.SKIPPED
        }
        debug {
            events TestLogEvent.STARTED,
                    TestLogEvent.FAILED,
                    TestLogEvent.PASSED,
                    TestLogEvent.SKIPPED,
                    TestLogEvent.STANDARD_OUT,
                    TestLogEvent.STANDARD_ERROR
            exceptionFormat TestExceptionFormat.FULL
            showExceptions true
            showCauses true
            showStackTraces true
            showStandardStreams true
        }

        afterSuite { desc, result ->
            if (!desc.parent) { // will match the outermost suite
                def output = "Results: ${result.resultType} (${result.testCount} tests, ${result.successfulTestCount} passed, ${result.failedTestCount} failed, ${result.skippedTestCount} skipped)"
                def startItem = '|  ', endItem = '  |'
                def repeatLength = startItem.length() + output.length() + endItem.length()
                println('\n' + ('-' * repeatLength) + '\n' + startItem + output + endItem + '\n' + ('-' * repeatLength))
            }
        }
    }
}
