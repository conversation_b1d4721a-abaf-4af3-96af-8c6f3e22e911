plugins {
    id 'java-library'
    id 'idea'
    id 'maven-publish'
}

group 'io.wyden'

def versionPropsFile = file('version.properties')
if (versionPropsFile.canRead()) {
    Properties versionProps = new Properties()
    versionProps.load(new FileInputStream(versionPropsFile))
    def ver = versionProps['VERSION'].toString()
    project.version = ver
} else {
    throw new GradleException("Could not read version.properties!")
}

ext {
    repository_username = System.env.NEXUS_DEPLOY_USERNAME
    repository_password = System.env.NEXUS_DEPLOY_PASSWORD
}

repositories {
    mavenLocal()
    maven {
        name 'nexus-snapshots'
        url 'https://repo.wyden.io/nexus/repository/snapshots/'
        credentials {
            username repository_username
            password repository_password
        }
    }
    mavenCentral()
}

dependencies {
    implementation project(':cloud-utils-rabbitmq')
    implementation project(':cloud-utils-telemetry')
    api dependencyCatalog.protobuf.java
    api dependencyCatalog.protobuf.java.util
    api dependencyCatalog.rabbitmq.http.client
    api dependencyCatalog.testcontainers
    api dependencyCatalog.testcontainers.rabbitmq
    api dependencyCatalog.awaitility
    api dependencyCatalog.mockito.core
}

test {
    useJUnitPlatform()
}

java {
    withSourcesJar()
    withJavadocJar()
}

publishing {
    publications {
        mavenJava(MavenPublication) {
            from components.java
        }
    }
    repositories {
        maven {
            name 'nexus-snapshots'
            url 'https://repo.wyden.io/nexus/repository/snapshots/'
            credentials {
                username repository_username
                password repository_password
            }
        }
    }
}
