package io.wyden.cloud.utils.test;

import java.security.SecureRandom;
import java.util.Arrays;
import java.util.List;
import java.util.Random;

public final class RandomEnumUtils {

    private RandomEnumUtils() {
    }

    /**
     * Returns a random value from the provided enum class.
     *
     * @param enumClass The enum class to get a random value from
     * @param <T>       The type of the enum
     * @return A randomly selected enum value
     * @throws IllegalArgumentException if the enum has no constants
     */
    public static <T extends Enum<T>> T randomEnum(Class<T> enumClass) {
        T[] constants = enumClass.getEnumConstants();
        if (constants == null || constants.length == 0) {
            throw new IllegalArgumentException("Enum class " + enumClass.getSimpleName() + " has no constants");
        }

        Random random = new SecureRandom();
        int index = random.nextInt(constants.length);
        return constants[index];
    }

    /**
     * Returns a random value from the provided enum class, excluding specified values.
     *
     * @param enumClass The enum class to get a random value from
     * @param exclude Array of enum values to exclude from selection
     * @param <T> The type of the enum
     * @return A randomly selected enum value (not in the excluded list)
     * @throws IllegalArgumentException if all enum values are excluded
     */
    @SafeVarargs
    public static <T extends Enum<T>> T randomEnum(Class<T> enumClass, T... exclude) {
        T[] constants = enumClass.getEnumConstants();
        if (constants == null || constants.length == 0) {
            throw new IllegalArgumentException("Enum class " + enumClass.getSimpleName() + " has no constants");
        }

        // Create a list of available values (excluding the ones specified)
        List<T> availableValues = Arrays.stream(constants)
            .filter(value -> !Arrays.asList(exclude).contains(value))
            .toList();

        if (availableValues.isEmpty()) {
            throw new IllegalArgumentException("All values of enum " + enumClass.getSimpleName() + " are excluded");
        }

        Random random = new SecureRandom();
        int index = random.nextInt(availableValues.size());
        return availableValues.get(index);
    }
}
