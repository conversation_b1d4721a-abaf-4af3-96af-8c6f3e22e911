package io.wyden.cloud.utils.test;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Random;

import static io.wyden.cloudutils.tools.ProtobufUtils.toProtoString;

public final class RandomNumberUtils {

    private RandomNumberUtils() {
    }

    private static final Random RANDOM = new Random();
    private static final int DEFAULT_SCALE = 2;

    /**
     * Generates a random BigDecimal in the range (0, 100) exclusive.
     * The result will be greater than 0 and less than 100.
     *
     * @return A random BigDecimal between 0 and 100 (exclusive) with default scale
     */
    public static BigDecimal randomNumber() {
        return randomNumber(DEFAULT_SCALE);
    }

    /**
     * Generates a random BigDecimal in the range (0, 100) exclusive with specified scale.
     * The result will be greater than 0 and less than 100.
     *
     * @param scale The number of decimal places for the result
     * @return A random BigDecimal between 0 and 100 (exclusive)
     */
    public static BigDecimal randomNumber(int scale) {
        return randomNumber(BigDecimal.valueOf(100), scale);
    }

    /**
     * Generates a random BigDecimal in the range (0, max) exclusive.
     * The result will be greater than 0 and less than max.
     *
     * @param max The exclusive upper bound for the random number
     * @return A random BigDecimal between 0 and max (exclusive) with default scale
     */
    public static BigDecimal randomNumber(BigDecimal max) {
        return randomNumber(max, DEFAULT_SCALE);
    }

    /**
     * Generates a random BigDecimal in the range (0, max) exclusive.
     * The result will be greater than 0 and less than max.
     *
     * @param max The exclusive upper bound for the random number as a double
     * @return A random BigDecimal between 0 and max (exclusive) with default scale
     */
    public static BigDecimal randomNumber(double max) {
        return randomNumber(BigDecimal.valueOf(max), DEFAULT_SCALE);
    }

    /**
     * Generates a random BigDecimal in the range (0, max) exclusive with specified scale.
     * The result will be greater than 0 and less than max.
     *
     * @param max The exclusive upper bound for the random number as a double
     * @param scale The number of decimal places for the result
     * @return A random BigDecimal between 0 and max (exclusive)
     */
    public static BigDecimal randomNumber(double max, int scale) {
        return randomNumber(BigDecimal.valueOf(max), scale);
    }

    /**
     * Generates a random BigDecimal in the range (0, max) exclusive with specified scale.
     * The result will be greater than 0 and less than max.
     *
     * @param max The exclusive upper bound for the random number
     * @param scale The number of decimal places for the result
     * @return A random BigDecimal between 0 and max (exclusive)
     */
    public static BigDecimal randomNumber(BigDecimal max, int scale) {
        if (max.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("Maximum value must be positive");
        }

        // Generate a random double between 0 and 1 (exclusive of 1)
        double randomValue = RANDOM.nextDouble();

        // Scale to range (0, max)
        BigDecimal result = max.multiply(BigDecimal.valueOf(randomValue));

        // Set the scale (number of decimal places)
        return result.setScale(scale, RoundingMode.HALF_UP);
    }

    /**
     * Generates a random number as a Protocol Buffer-compatible string representation.
     * The random number is in the range (0, 100) exclusive with a default scale.
     *
     * @return A {@code String} representing the random number between 0 and 100 (exclusive),
     *         formatted as a Protocol Buffer-compatible string.
     */
    public static String randomProtoNumber() {
        return toProtoString(randomNumber());
    }

    /**
     * Generates a random number in the range (0, 100) exclusive with the specified scale.
     * The result is returned as a Protocol Buffer-compatible string representation.
     *
     * @param scale The number of decimal places for the result
     * @return A String representing the random number between 0 and 100 (exclusive),
     *         formatted as a Protocol Buffer-compatible string
     */
    public static String randomProtoNumber(int scale) {
        return toProtoString(randomNumber(scale));
    }

    /**
     * Generates a random number based on the provided maximum value and converts it to a proto string representation.
     *
     * @param max the maximum value for generating the random number. The value must be a positive BigDecimal.
     * @return a string representation of the randomly generated number in proto format.
     */
    public static String randomProtoNumber(BigDecimal max) {
        return toProtoString(randomNumber(max));
    }

    /**
     * Generates a random number within the range (0, max) exclusive, and converts it
     * into a Protocol Buffer-compatible string representation.
     *
     * @param max The exclusive upper bound*/
    public static String randomProtoNumber(double max) {
        return toProtoString(randomNumber(max));
    }

    /**
     * Generates a random number within the range (0, max) exclusive, with the specified scale,
     * and converts it to a Protocol Buffer-compatible string representation.
     *
     * @param max the exclusive upper bound for the random number as a double. Must be positive.
     * @param scale the number of decimal places for the result.
     * @return a string representation of the randomly generated number, formatted as a Protocol Buffer-compatible string.
     */
    public static String randomProtoNumber(double max, int scale) {
        return toProtoString(randomNumber(max, scale));
    }

    /**
     * Generates a random number within the range (0, max) exclusive, with the specified scale,
     * and converts it to a Protocol Buffer-compatible string representation.
     *
     * @param max the exclusive upper bound for the random number as a BigDecimal. Must be positive.
     * @param scale the number of decimal places for the result.
     * @return a string representation of the randomly generated number, formatted as a Protocol Buffer-compatible string.
     */
    public static String randomProtoNumber(BigDecimal max, int scale) {
        return toProtoString(randomNumber(max, scale));
    }
}
