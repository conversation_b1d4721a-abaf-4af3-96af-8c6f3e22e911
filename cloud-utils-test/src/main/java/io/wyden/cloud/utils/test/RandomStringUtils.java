package io.wyden.cloud.utils.test;

import java.security.SecureRandom;
import java.util.UUID;

/**
 * Utility methods for generating random strings.
 */
public class RandomStringUtils {
    private static final SecureRandom RANDOM = new SecureRandom();
    private static final String ALPHANUMERIC_CHARS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    private static final String NUMERIC_CHARS = "0123456789";
    private static final String ALPHABETIC_CHARS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    
    // UUID length without hyphens is 32 characters
    private static final int DEFAULT_LENGTH = 32;
    private static final int DEFAULT_SUFFIX_LENGTH = 4;

    /**
     * Generates a random string with UUID length (32 characters).
     * Uses UUID implementation for guaranteed uniqueness.
     *
     * @return A random UUID string without hyphens
     */
    public static String randomString() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * Generates a random alphanumeric string of the specified length.
     *
     * @param length The length of the string to generate
     * @return A random alphanumeric string
     */
    public static String randomString(int length) {
        return randomString(length, ALPHANUMERIC_CHARS);
    }

    /**
     * Generates a random numeric string of the specified length.
     *
     * @param length The length of the string to generate
     * @return A random numeric string
     */
    public static String randomNumeric(int length) {
        return randomString(length, NUMERIC_CHARS);
    }

    /**
     * Generates a random alphabetic string of the specified length.
     *
     * @param length The length of the string to generate
     * @return A random alphabetic string
     */
    public static String randomAlphabetic(int length) {
        return randomString(length, ALPHABETIC_CHARS);
    }

    /**
     * Generates a random string of the specified length using the provided characters.
     *
     * @param length The length of the string to generate
     * @param characters The characters to use for generation
     * @return A random string using the specified characters
     */
    public static String randomString(int length, String characters) {
        if (length <= 0) {
            throw new IllegalArgumentException("Length must be positive");
        }
        
        if (characters == null || characters.isEmpty()) {
            throw new IllegalArgumentException("Character set cannot be empty");
        }
        
        StringBuilder result = new StringBuilder(length);
        int charLength = characters.length();
        
        for (int i = 0; i < length; i++) {
            int randomIndex = RANDOM.nextInt(charLength);
            result.append(characters.charAt(randomIndex));
        }
        
        return result.toString();
    }

    /**
     * Generates a random UUID.
     *
     * @return A random UUID string with hyphens
     */
    public static String randomUuid() {
        return UUID.randomUUID().toString();
    }

    /**
     * Generates a string with the given prefix followed by a random UUID (without hyphens).
     *
     * @param prefix The prefix to prepend to the random string
     * @return A string consisting of the prefix followed by a random UUID (without hyphens)
     */
    public static String randomStringWithPrefix(String prefix) {
        if (prefix == null) {
            prefix = "";
        }
        return prefix + "-" + randomString(DEFAULT_SUFFIX_LENGTH);
    }

    /**
     * Generates a string with the given prefix followed by a random alphanumeric string
     * of the specified length.
     *
     * @param prefix The prefix to prepend to the random string
     * @param length The length of the random part of the string
     * @return A string consisting of the prefix followed by a random alphanumeric string
     */
    public static String randomStringWithPrefix(String prefix, int length) {
        if (prefix == null) {
            prefix = "";
        }
        return prefix + "-" + randomString(length);
    }

    /**
     * Generates a string with the given prefix followed by a random string
     * of the specified length using the provided characters.
     *
     * @param prefix The prefix to prepend to the random string
     * @param length The length of the random part of the string
     * @param characters The characters to use for generating the random part
     * @return A string consisting of the prefix followed by a random string using the specified characters
     */
    public static String randomStringWithPrefix(String prefix, int length, String characters) {
        if (prefix == null) {
            prefix = "";
        }
        return prefix + "-" +randomString(length, characters);
    }

    /**
     * Generates a string with the given prefix followed by a random numeric string
     * of the specified length.
     *
     * @param prefix The prefix to prepend to the random string
     * @param length The length of the random part of the string
     * @return A string consisting of the prefix followed by a random numeric string
     */
    public static String randomNumericWithPrefix(String prefix, int length) {
        if (prefix == null) {
            prefix = "";
        }
        return prefix + "-" + randomNumeric(length);
    }

    /**
     * Generates a string with the given prefix followed by a random alphabetic string
     * of the specified length.
     *
     * @param prefix The prefix to prepend to the random string
     * @param length The length of the random part of the string
     * @return A string consisting of the prefix followed by a random alphabetic string
     */
    public static String randomAlphabeticWithPrefix(String prefix, int length) {
        if (prefix == null) {
            prefix = "";
        }
        return prefix + "-" + randomAlphabetic(length);
    }

    /**
     * Generates a string with the given prefix followed by a random UUID (with hyphens).
     *
     * @param prefix The prefix to prepend to the random UUID
     * @return A string consisting of the prefix followed by a random UUID (with hyphens)
     */
    public static String randomUuidWithPrefix(String prefix) {
        if (prefix == null) {
            prefix = "";
        }
        return prefix + "-" + randomUuid();
    }
}