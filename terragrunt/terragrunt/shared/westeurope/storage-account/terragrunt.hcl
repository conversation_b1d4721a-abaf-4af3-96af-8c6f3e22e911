locals {
  location = include.root.locals.location
}

dependency "oidc-azure" {
  config_path = "../oidc-azure"
  mock_outputs = {
    vault_identity_id = "test"
    aks_identity_id = "test"
  }
}

dependencies {
  paths = ["../oidc-azure"]
}

terraform {
  source = "tfr:///getindata/storage-account/azurerm//?version=${include.root.locals.terraform-storage-account}"
}

include "root" {
  path = find_in_parent_folders()
  expose = true
}

inputs = {
  resource_group_name                  = "${include.root.locals.resource_group}"
  create_resource_group = false
  location = local.location
  use_raw_name = true
  name  = "wydencloudpoc"
  descriptor_name = "wydencloudpoc"
  storage_account_name = "wydencloudpoc"
  account_kind = "StorageV2"
  skuname = "Standard_LRS"
  containers_list = [
    { name = "backups", access_type = "private" }
  ]
  managed_identity_type = "UserAssigned"
  managed_identity_ids  = [dependency.oidc-azure.outputs.aks_identity_id]

  storage_blob_data_contributors = [dependency.oidc-azure.outputs.aks_identity_principal_id]

  lifecycles = [
    {
      prefix_match               = ["backups"]
      tier_to_cool_after_days = 0
      tier_to_archive_after_days = 1
      delete_after_days          = 30
      snapshot_delete_after_days = 30
    }
  ]

  tags = {
    Environment = "${include.root.locals.merged.environment}"
  }
}