terraform {
  source = "git::https://github.com/terraform-aws-modules/terraform-aws-alb.git?ref=${include.root.locals.terraform-aws-alb}"
}

include "root" {
  path   = find_in_parent_folders("root.hcl")
  expose = true
}

dependency "vpc" {
  config_path = "${get_parent_terragrunt_dir()}/${include.root.locals.environment}/${include.root.locals.aws_region}/vpc"
}

locals {
  namespace = "${basename(get_terragrunt_dir())}"
}

inputs = {
  name = "${local.namespace}-nlb"
  vpc_id = dependency.vpc.outputs.vpc_id
  subnets = dependency.vpc.outputs.public_subnets
  load_balancer_type = "network"
  tags = include.root.locals.default_tags

  security_group_ingress_rules = {
    rest_8093 = {
      from_port = 8093
      to_port = 8093
      ip_protocol = "tcp"
      cidr_ipv4 = "0.0.0.0/0"
    }
    fix_9876 = {
      from_port = 9876
      to_port = 9876
      ip_protocol = "tcp"
      cidr_ipv4 = "0.0.0.0/0"
    }
    rest_8086 = {
      from_port = 8086
      to_port = 8086
      ip_protocol = "tcp"
      cidr_ipv4 = "0.0.0.0/0"
    }
    fix_9877 = {
      from_port = 9877
      to_port = 9877
      ip_protocol = "tcp"
      cidr_ipv4 = "0.0.0.0/0"
    }
    rest_8085 = {
      from_port = 8085
      to_port = 8085
      ip_protocol = "tcp"
      cidr_ipv4 = "0.0.0.0/0"
    }
    fix_9878 = {
      from_port = 9878
      to_port = 9878
      ip_protocol = "tcp"
      cidr_ipv4 = "0.0.0.0/0"
    }
    rest_8084 = {
      from_port = 8084
      to_port = 8084
      ip_protocol = "tcp"
      cidr_ipv4 = "0.0.0.0/0"
    }
    fix_9879 = {
      from_port = 9879
      to_port = 9879
      ip_protocol = "tcp"
      cidr_ipv4 = "0.0.0.0/0"
    }
  }

  security_group_egress_rules = {
    all = {
      ip_protocol = "-1"
      cidr_ipv4 = "0.0.0.0/0"
    }
  }

  listeners = {
    rest_8093 = {
      port = 8093
      protocol = "TCP"
      forward = {
        target_group_key = "rest_8093"
      }
      tags = merge({
        service = "fix-api"
        type = "rest"
        port = "8093"
      }, include.root.locals.default_tags)
    }
    fix_9876 = {
      port = 9876
      protocol = "TCP"
      forward = {
        target_group_key = "fix_9876"
      }
      tags = merge({
        service = "fix-api"
        type = "fix"
        port = "9876"
      }, include.root.locals.default_tags)
    }
    rest_8086 = {
      port = 8086
      protocol = "TCP"
      forward = {
        target_group_key = "rest_8086"
      }
      tags = merge({
        service = "fix-api-custom-ohlc"
        type = "rest"
        port = "8086"
      }, include.root.locals.default_tags)
    }
    fix_9879 = {
      port = 9879
      protocol = "TCP"
      forward = {
        target_group_key = "fix_9879"
      }
      tags = merge({
        service = "fix-api-custom-ohlc"
        type = "fix"
        port = "9879"
      }, include.root.locals.default_tags)
    }
    rest_8085 = {
      port = 8085
      protocol = "TCP"
      forward = {
        target_group_key = "rest_8085"
      }
      tags = merge({
        service = "fix-api-drop-copy"
        type = "rest"
        port = "8085"
      }, include.root.locals.default_tags)
    }
    fix_9878 = {
      port = 9878
      protocol = "TCP"
      forward = {
        target_group_key = "fix_9878"
      }
      tags = merge({
        service = "fix-api-drop-copy"
        type = "fix"
        port = "9878"
      }, include.root.locals.default_tags)
    }
    rest_8084 = {
      port     = 8084
      protocol = "TCP"
      forward = {
        target_group_key = "rest_8084"
      }
      tags = merge({
        service = "fix-api-market-data"
        type = "rest"
        port = "8084"
      }, include.root.locals.default_tags)
    }
    fix_9877 = {
      port = 9877
      protocol = "TCP"
      forward = {
        target_group_key = "fix_9877"
      }
      tags = merge({
        service = "fix-api-market-data"
        type = "fix"
        port = "9877"
      }, include.root.locals.default_tags)
    }
  }

  target_groups = {
    rest_8093 = {
      name = "${local.namespace}-rest-8093"
      protocol = "TCP"
      port = 32011
      target_type = "instance"
      create_attachment = false
      tags = merge({
        service = "fix-api"
        type = "rest"
        port = "8093"
      }, include.root.locals.default_tags)
    }
    fix_9876 = {
      name = "${local.namespace}-fix-9876"
      protocol = "TCP"
      port = 32012
      target_type = "instance"
      create_attachment = false
      tags = merge({
        service = "fix-api"
        type = "fix"
        port = "9876"
      }, include.root.locals.default_tags)
    }
    rest_8086 = {
      name = "${local.namespace}-rest-8086"
      protocol = "TCP"
      port = 32013
      target_type = "instance"
      create_attachment = false
      tags = merge({
        service = "fix-api-custom-ohlc"
        type = "rest"
        port = "8086"
      }, include.root.locals.default_tags)
    }
    fix_9879 = {
      name = "${local.namespace}-fix-9879"
      protocol = "TCP"
      port = 32014
      target_type = "instance"
      create_attachment = false
      tags = merge({
        service = "fix-api-custom-ohlc"
        type = "fix"
        port = "9879"
      }, include.root.locals.default_tags)
    }
    rest_8085 = {
      name = "${local.namespace}-rest-8085"
      protocol = "TCP"
      port = 32015
      target_type = "instance"
      create_attachment = false
      tags = merge({
        service = "fix-api-drop-copy"
        type = "rest"
        port = "8085"
      }, include.root.locals.default_tags)
    }
    fix_9878 = {
      name = "${local.namespace}-fix-9878"
      protocol = "TCP"
      port = 32016
      target_type = "instance"
      create_attachment = false
      tags = merge({
        service = "fix-api-drop-copy"
        type = "fix"
        port = "9878"
      }, include.root.locals.default_tags)
    }
    rest_8084 = {
      name = "${local.namespace}-rest-8084"
      protocol = "TCP"
      port = 32017
      target_type = "instance"
      create_attachment = false
      tags = merge({
        service = "fix-api-market-data"
        type = "rest"
        port = "8084"
      }, include.root.locals.default_tags)
    }
    fix_9877 = {
      name = "${local.namespace}-fix-9877"
      protocol = "TCP"
      port = 32018
      target_type = "instance"
      create_attachment = false
      tags = merge({
        service = "fix-api-market-data"
        type = "fix"
        port = "9877"
      }, include.root.locals.default_tags)
    }
  }
}
