apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: performance
spec:
  template:
    metadata:
      labels:
        environment: performance
    spec:
      nodeClassRef:
        group: karpenter.k8s.aws
        kind: EC2NodeClass
        name: performance
      taints:
        - key: performance
          value: "true"
          effect: NoSchedule
      requirements:
        - key: "topology.kubernetes.io/zone"
          operator: In
          values: [${availability_zones}]
        - key: "karpenter.sh/capacity-type"
          operator: In
          values: ["on-demand"]
        - key: "kubernetes.io/arch"
          operator: In
          values: ["amd64"]
        - key: node.kubernetes.io/instance-type
          operator: In
          values: ["m6a.xlarge"]
      kubelet:
        systemReserved:
          cpu: 100m
          memory: 100Mi
          ephemeral-storage: 1Gi
  limits:
    cpu: 76
    memory: 286Gi
