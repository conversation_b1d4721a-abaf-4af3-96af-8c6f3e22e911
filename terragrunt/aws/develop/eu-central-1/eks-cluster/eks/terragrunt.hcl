terraform {
  source = "${get_parent_terragrunt_dir()}/modules/eks"
}

dependency "vpc" {
  config_path = "${get_parent_terragrunt_dir("root")}/${include.root.locals.environment}/${include.root.locals.aws_region}/vpc"
}

# Include all settings from the root terragrunt.hcl file
include "root" {
  path   = find_in_parent_folders("root.hcl")
  expose = true
}

inputs = {
  cluster_name    = include.root.locals.merged.eks.cluster_name
  cluster_version = "1.31"

  vpc_id              = dependency.vpc.outputs.vpc_id
  subnet_ids          = dependency.vpc.outputs.private_subnets
  node_pools_path     = "${get_terragrunt_dir()}/node-pools"
  node_templates_path = "${get_terragrunt_dir()}/node-templates"

  access_entries = {
    development = {
      kubernetes_groups = []
      principal_arn = "arn:aws:iam::${include.root.locals.account_id}:role/aws-reserved/sso.amazonaws.com/${include.root.locals.aws_region}/AWSReservedSSO_DeveloperAccess_0807dcdd2e493d47"
      policy_associations = {
        EKSEdit = {
          policy_arn = "arn:aws:eks::aws:cluster-access-policy/AmazonEKSEditPolicy"
          access_scope = {
            namespaces = ["wyden-dev", "wyden-runner", "wyden-qa", "wyden-performance", "wyden-uat", "wyden-patch"]
            type       = "namespace"
          }
        }
        EKSView = {
          policy_arn = "arn:aws:eks::aws:cluster-access-policy/AmazonEKSViewPolicy"
          access_scope = {
            type = "cluster"
          }
        }
      }
    }
    administrator = {
      kubernetes_groups= ["wyden:admin"]
      principal_arn = "arn:aws:iam::${include.root.locals.account_id}:role/aws-reserved/sso.amazonaws.com/${include.root.locals.aws_region}/AWSReservedSSO_AdministratorAccess_413bb70abdb75552"
      policy_associations = {
        EKSAdmin = {
          policy_arn = "arn:aws:eks::aws:cluster-access-policy/AmazonEKSAdminPolicy"
          access_scope = {
            type = "cluster"
          }
        }
      }
    }
  }

  eks_managed_node_groups = {
    service = {
      name            = "service"
      use_name_prefix = true

      desired_size = 3
      max_size     = 3
      min_size     = 1

      update_config = {
        max_unavailable = 1
      }

      iam_role_additional_policies = {
        AmazonSSMManagedInstanceCore = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore",
        AmazonEC2ContainerRegistryPullOnly = "arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryPullOnly",
      }

      instance_types = ["t4g.medium", "t4g.large"]
      capacity_type  = "SPOT"
      subnet_ids     = dependency.vpc.outputs.private_subnets
      tags           = merge(include.root.locals.default_tags)

      labels = {
        environment           = "service"
        lifecycle             = "Ec2Spot"
        "aws.amazon.com/spot" = "true"
        "karpenter.sh/controller" = "true"
      }
    }
  }

  tags = merge(include.root.locals.default_tags)
}
