unregisterRunners: false

runners:
  tags: "kubernetes-aws"
  runUntagged: true
  config: |
    [[runners]]
      [runners.kubernetes]
        namespace = "{{.Release.Namespace}}"
        service_account = "{{ include "gitlab-runner.fullname" . }}"
        image = "alpine"
        privileged = true
        poll_interval = 3
        poll_timeout = 300

        # build container
        cpu_limit = "3"
        cpu_request = "1"
        memory_limit = "8Gi"
        memory_request = "512Mi"

        # service containers
        service_cpu_request = "0.1"
        service_memory_request = "256Mi"
        service_cpu_limit = "0.1"
        service_memory_limit = "256Mi"

        # helper container
        helper_cpu_request = "0.25"
        helper_memory_request = "512Mi"
        helper_cpu_limit = "0.5"
        helper_memory_limit = "512Mi"

        image_pull_secrets = ["regcred"]
        helper_image_autoset_arch_and_os = true

      [runners.kubernetes.pod_annotations]
        "karpenter.sh/do-not-disrupt" = "true"
      [runners.kubernetes.node_selector]
        environment = "dev"
        "kubernetes.io/arch" = "arm64"
        "kubernetes.io/os" = "linux"
      [runners.kubernetes.node_tolerations]
        "dev=true" = "NoSchedule"

      [runners.cache]
        Type = "s3"
        Path = "runner"
        Shared = true
      [runners.cache.s3]
        ServerAddress = "s3.amazonaws.com"
        BucketName = "runner-cache-develop"
        BucketLocation = "eu-central-1"

rbac:
  create: true
  clusterWideAccess: true
  rules:
    - apiGroups:
        [
          "",
          "apps",
          "rbac.authorization.k8s.io",
          "networking.k8s.io",
          "monitoring.coreos.com",
          "policy",
          "elbv2.k8s.aws",
        ]
      resources: ["*"]
      verbs: ["*"]

nodeSelector:
  environment: dev
  kubernetes.io/arch: "arm64"
  kubernetes.io/os: "linux"

tolerations:
  - key: "dev"
    operator: "Equal"
    value: "true"
    effect: "NoSchedule"
