package io.wyden.referencedata;

import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import com.hazelcast.test.TestHazelcastInstanceFactory;
import com.rabbitmq.client.Channel;
import io.wyden.cloud.utils.test.TracingMock;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.common.CursorConnection;
import io.wyden.published.common.CursorEdge;
import io.wyden.published.common.CursorNode;
import io.wyden.published.common.PageInfo;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.referencedata.client.PortfoliosCacheFacade;
import io.wyden.referencedata.configuration.InitialPortfolioConfig;
import io.wyden.referencedata.domain.model.GetPortfoliosRequestDto;
import io.wyden.referencedata.domain.model.PortfolioPredicateDto;
import io.wyden.referencedata.domain.model.PortfolioPredicateTypeDto;
import io.wyden.referencedata.domain.model.PortfolioSearchTypeDto;
import io.wyden.referencedata.domain.model.SortingOrder;
import io.wyden.referencedata.service.portfolio.PortfolioRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.stream.IntStream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(SpringExtension.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, properties = {"spring.main.allow-bean-definition-overriding=true", "initial.config.portfolio.prefill=true"})
@ContextConfiguration(classes = PortfolioSearchTest.TestHazelcastConfiguration.class)
public class PortfolioSearchTest {

    @Autowired
    private PortfolioRepository portfolioRepository;

    @Autowired
    private PortfoliosCacheFacade portfoliosCacheFacade;

    @Autowired
    private InitialPortfolioConfig initialPortfolioConfig;

    @Test
    void testInitialPortfolios() {
        Collection<Portfolio> portfolios = portfolioRepository.findAll();

        List<String> initialPortfolioNames = initialPortfolioConfig.initialPortfolios().stream()
            .map(InitialPortfolioConfig.InitialPortfolio::name)
            .toList();

        assertThat(portfolios).hasSameSizeAs(initialPortfolioNames)
            .extracting(Portfolio::getName)
            .containsOnly(initialPortfolioNames.toArray(new String[0]));
    }

    @Test
    void testSearchByName() {
        Portfolio portfolio1 = Portfolio.newBuilder()
            .setId("1")
            .setName("LATAM")
            .setCreatedAt(ZonedDateTime.now().toString())
            .setPortfolioCurrency("USD")
            .setCreatorClientId("client-id-1")
            .putTags("fund", "crypto")
            .build();

        Portfolio portfolio2 = Portfolio.newBuilder()
            .setId("2")
            .setName("NORAM")
            .setCreatedAt(ZonedDateTime.now().toString())
            .setPortfolioCurrency("EUR")
            .setCreatorClientId("client-id-2")
            .putTags("fund", "crypto")
            .build();

        portfolioRepository.save(portfolio1);
        portfolioRepository.save(portfolio2);

        GetPortfoliosRequestDto searchByNameWithContains = new GetPortfoliosRequestDto(new PortfolioPredicateDto(PortfolioPredicateTypeDto.CONTAINS, PortfolioSearchTypeDto.NAME, "Am"), List.of(), List.of(), List.of(), null, null, null, SortingOrder.ASC, 100, "");
        assertThat(extract(portfoliosCacheFacade.search(searchByNameWithContains))).contains(portfolio1, portfolio2);

        GetPortfoliosRequestDto searchByNameWithEquals = new GetPortfoliosRequestDto(new PortfolioPredicateDto(PortfolioPredicateTypeDto.EQUALS, PortfolioSearchTypeDto.NAME, "NORAM"), List.of(), List.of(), List.of(), null, null, null, SortingOrder.ASC, 100, "");
        assertThat(extract(portfoliosCacheFacade.search(searchByNameWithEquals))).containsOnly(portfolio2);

        GetPortfoliosRequestDto searchById = new GetPortfoliosRequestDto( null, List.of("2"), List.of(), List.of(), null, null, null, SortingOrder.ASC, 100, "");
        assertThat(extract(portfoliosCacheFacade.search(searchById))).containsOnly(portfolio2);

        // use crypto tag value to filter out 'initial portfolio' created by reference-data-server on startup
        GetPortfoliosRequestDto searchByTagValues = new GetPortfoliosRequestDto(null, List.of(), List.of(), List.of("crypto"), null, null, null, SortingOrder.ASC, 100, "");
        assertThat(extract(portfoliosCacheFacade.search(searchByTagValues))).containsOnly(portfolio1, portfolio2);

        GetPortfoliosRequestDto searchOnlyFirst = new GetPortfoliosRequestDto(null, List.of(), List.of(), List.of("crypto"), null, null, null, SortingOrder.ASC, 1, "");
        assertThat(extract(portfoliosCacheFacade.search(searchOnlyFirst))).containsOnly(portfolio1);

        GetPortfoliosRequestDto searchFirstTwo = new GetPortfoliosRequestDto(null, List.of(), List.of(), List.of("crypto"), null, null, null, SortingOrder.ASC, 10, "");
        assertThat(extract(portfoliosCacheFacade.search(searchFirstTwo))).containsOnly(portfolio1, portfolio2);

        // test backward compatibility
        GetPortfoliosRequestDto searchAll = new GetPortfoliosRequestDto(null, List.of(), List.of(), List.of(), null, null, null, SortingOrder.ASC, null, "");
        assertThat(extract(portfoliosCacheFacade.search(searchAll))).hasSize(2 + initialPortfolioConfig.initialPortfolios().size());
    }

    @Test
    void testSearchWithPagination() {
        IntStream.range(0, 10)
            .mapToObj(PortfolioSearchTest::createRandomPortfolio)
            .forEach(portfolioRepository::save);

        GetPortfoliosRequestDto initialRequest = new GetPortfoliosRequestDto(null, List.of(), List.of(), List.of("crypto"), null, null, null, SortingOrder.ASC, 5, "");
        CursorConnection firstPortfolios = portfoliosCacheFacade.search(initialRequest);
        assertThat(firstPortfolios.getEdgesCount()).isEqualTo(5);

        PageInfo initialPageInfo = firstPortfolios.getPageInfo();
        assertThat(initialPageInfo.getHasNextPage()).isTrue();

        String endCursor = initialPageInfo.getEndCursor();
        CursorEdge lastPortfolio = firstPortfolios.getEdgesList().get(4);
        assertThat(endCursor).isEqualTo(lastPortfolio.getCursor());

        GetPortfoliosRequestDto finalRequest = new GetPortfoliosRequestDto( null, List.of(), List.of(), List.of("crypto"), null, null, null, SortingOrder.ASC, 5, endCursor);
        CursorConnection finalPortfolios = portfoliosCacheFacade.search(finalRequest);
        assertThat(finalPortfolios.getEdgesCount()).isEqualTo(5);

        PageInfo finalPageInfo = finalPortfolios.getPageInfo();
        assertThat(finalPageInfo.getHasNextPage()).isFalse();
    }

    private static List<Portfolio> extract(CursorConnection portfolioConnection) {
        return portfolioConnection.getEdgesList().stream()
            .map(CursorEdge::getNode)
            .map(CursorNode::getPortfolio)
            .toList();
    }

    private static Portfolio createRandomPortfolio(int id) {
        return Portfolio.newBuilder()
            .setId(String.valueOf(id))
            .setName("EuroZone")
            .setCreatedAt(DateUtils.toIsoUtcTime(LocalDate.now().atStartOfDay(DateUtils.UTC).plusHours(id)))
            .setPortfolioCurrency("EUR")
            .setCreatorClientId("client-id-" + id)
            .putTags("fund", "crypto")
            .build();
    }

    @TestConfiguration
    public static class TestHazelcastConfiguration {

        @Primary
        @Bean("hazelcast")
        HazelcastInstance hazelcastInstance() {
            return new TestHazelcastInstanceFactory().newHazelcastInstance();
        }

        @Primary
        @Bean
        RabbitIntegrator rabbitIntegrator() {
            RabbitIntegrator rabbitIntegrator = mock(RabbitIntegrator.class);
            Channel channel = mock(Channel.class);

            when(rabbitIntegrator.getConsumptionChannel()).thenReturn(channel);
            when(rabbitIntegrator.getDeclarationAndPublishChannel()).thenReturn(channel);
            when(rabbitIntegrator.getProducingExecutorService()).thenReturn(Executors.newCachedThreadPool());

            return rabbitIntegrator;
        }

        @Bean
        PortfoliosCacheFacade portfoliosCacheFacade(IMap<String, Portfolio> portfoliosMap) {
            Tracing otlTracingMock = TracingMock.createMock();
            return new PortfoliosCacheFacade(portfoliosMap, otlTracingMock);
        }
    }
}
