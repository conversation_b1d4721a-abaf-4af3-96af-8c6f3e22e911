package io.wyden.referencedata.service.venueaccount;

import com.google.protobuf.Message;
import com.hazelcast.map.IMap;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.destination.OemsHeader;
import io.wyden.published.audit.EventLogEvent;
import io.wyden.published.audit.EventLogStatus;
import io.wyden.published.common.KeyValue;
import io.wyden.published.common.Metadata;
import io.wyden.published.referencedata.AccountType;
import io.wyden.published.referencedata.ConnectorDetails;
import io.wyden.published.referencedata.Grant;
import io.wyden.published.referencedata.Venue;
import io.wyden.published.referencedata.VenueAccount;
import io.wyden.published.referencedata.VenueAccountChangeEvent;
import io.wyden.published.referencedata.VenueAccountCreateRequest;
import io.wyden.published.referencedata.VenueAccountUpdateRequest;
import io.wyden.published.referencedata.VenueType;
import io.wyden.published.targetregistry.ConnectorRequest;
import io.wyden.referencedata.exception.FailureNeedsRetryException;
import io.wyden.referencedata.service.EventLogEmitter;
import io.wyden.referencedata.service.venue.VenueRepository;
import io.wyden.referencedata.service.venue.VenueService;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.ArgumentCaptor;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.verification.VerificationMode;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Nullable;

import static io.wyden.published.referencedata.AccountType.ACCOUNT_TYPE_UNSPECIFIED;
import static io.wyden.published.referencedata.AccountType.WALLET;
import static io.wyden.published.referencedata.VenueAccountChangeEventType.VENUE_ACCOUNT_CHANGE_EVENT_TYPE_CREATED;
import static io.wyden.published.referencedata.VenueAccountChangeEventType.VENUE_ACCOUNT_CHANGE_EVENT_TYPE_UPDATED;
import static org.apache.commons.lang3.StringUtils.EMPTY;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;


@ExtendWith(MockitoExtension.class)
class VenueAccountServiceTest {

    private static final String DEFAULT_ACCOUNT_ID = randomValue("account-id");
    private static final String DEFAULT_MESSAGE_ID = randomValue("message-id");
    private static final String DEFAULT_VENUE_ACCOUNT_NAME = randomValue("venue-account");
    private static final String DEFAULT_VENUE_NAME = randomValue("venue");
    private static final String DEFAULT_OWNER_USERNAME = randomValue("username");
    private static final String DEFAULT_CORRELATION_OBJECT = randomValue("correlation-object");
    private static final AccountType DEFAULT_ACCOUNT_TYPE = AccountType.ACCOUNT_TYPE_UNSPECIFIED;
    private static final VenueType DEFAULT_VENUE_TYPE = VenueType.STREET;

    private static final Map<AccountType, Optional<Venue>> venues = new HashMap<>();

    private static final VerificationMode ONCE = times(1);

    RabbitExchange<EventLogEvent> eventLogExchange = genericMock(RabbitExchange.class);
    EventLogEmitter eventLogEmitter = new EventLogEmitter(eventLogExchange);

    IMap<String, Venue> venueMap = spy(new IMapMock<>("reference-data-venues"));
    VenueRepository venueRepository = new VenueRepository(venueMap);
    VenueService venueService = new VenueService(venueRepository, eventLogEmitter);

    RabbitExchange<VenueAccountChangeEvent> venueAccountChangeEventExchange = genericMock(RabbitExchange.class);
    VenueAccountChangeEventEmitter venueAccountChangeEventEmitter = new VenueAccountChangeEventEmitter(venueAccountChangeEventExchange);

    RabbitExchange<Message> connectorRequestExchange = genericMock(RabbitExchange.class);
    ConnectorRequestEmitter connectorRequestEmitter = new ConnectorRequestEmitter(connectorRequestExchange);

    IMap<String, VenueAccount> venueAccountMap = spy(new IMapMock<>("reference-data-venue-accounts"));
    VenueAccountRepository venueAccountRepository = new VenueAccountRepository(venueAccountMap);
    VenueAccountService venueAccountService = new VenueAccountService(
        venueAccountRepository,
        venueService,
        venueAccountChangeEventEmitter,
        eventLogEmitter,
        connectorRequestEmitter);

    @BeforeEach
    void beforeEach() {
        createVenue(DEFAULT_ACCOUNT_TYPE, DEFAULT_VENUE_NAME, DEFAULT_VENUE_TYPE);
        createVenue(WALLET, randomValue("wallet"), null);
        createVenue(AccountType.EXCHANGE, randomValue("exchange"), VenueType.STREET);
        createVenue(AccountType.CUSTODY, randomValue("custody"), null);
        createVenue(AccountType.ACCOUNT_TYPE_CLOB, randomValue("clob"), VenueType.CLOB);
    }

    @Test
    void givenId_whenCreateAccount_idIsUsed() {
        VenueAccountCreateRequest request = defaultAccountCreateRequest()
            .setVenueAccountId(DEFAULT_ACCOUNT_ID)
            .build();

        VenueAccount venueAccount = venueAccountService.createFromRequest(request);

        assertThat(venueAccount.getId()).isEqualTo(DEFAULT_ACCOUNT_ID);
    }

    @Test
    void givenIdIsNotProvided_whenCreateAccount_idIsGenerated() {
        VenueAccountCreateRequest request = defaultAccountCreateRequest().build();

        VenueAccount venueAccount = venueAccountService.createFromRequest(request);

        assertThat(venueAccount.getId()).isEqualTo("vnccn4");
    }

    @Test
    void givenIdIsNotProvidedAndGeneratedIdExists_whenCreateAccount_nextIdIsGenerated() {
        VenueAccountCreateRequest request1 = defaultAccountCreateRequest().build();

        VenueAccount venueAccount1 = venueAccountService.createFromRequest(request1);

        assertThat(venueAccount1.getId()).isEqualTo("vnccn4");

        VenueAccountCreateRequest request2 = defaultAccountCreateRequest()
            .setVenueAccountName(DEFAULT_VENUE_ACCOUNT_NAME + "1")
            .build();

        VenueAccount venueAccount2 = venueAccountService.createFromRequest(request2);

        assertThat(venueAccount2.getId()).isEqualTo("vnccn41");
    }

    @Test
    void whenCreateAccount_allFieldsAreSet() {
        VenueAccountCreateRequest request = defaultAccountCreateRequest().build();

        VenueAccount venueAccount = venueAccountService.createFromRequest(request);

        assertThat(venueAccountMap.get(venueAccount.getId())).isEqualTo(venueAccount);
        assertThat(venueAccount.getVenueAccountName()).isEqualTo(DEFAULT_VENUE_ACCOUNT_NAME);
        assertThat(venueAccount.getOwnerUsername()).isEqualTo(DEFAULT_OWNER_USERNAME);
        assertThat(venueAccount.getVenueName()).isEqualTo(DEFAULT_VENUE_NAME);
        assertThat(venueAccount.getArchivedAt()).isBlank();
        assertThat(venueAccount.getSuspendedAt()).isBlank();
        assertThat(venueAccount.getCreatedAt()).satisfies(Instant::parse);
        assertThat(venueAccount.getAccountType()).isEqualTo(DEFAULT_ACCOUNT_TYPE);
    }

    @Test
    void givenNoException_whenCreateAccount_changeEventIsSent() {
        VenueAccountCreateRequest request = defaultAccountCreateRequest().build();

        VenueAccount venueAccount = venueAccountService.createFromRequest(request);

        ArgumentCaptor<VenueAccountChangeEvent> changeEventCaptor = ArgumentCaptor.forClass(VenueAccountChangeEvent.class);
        verify(venueAccountChangeEventExchange, ONCE).publish(changeEventCaptor.capture(), eq(EMPTY));

        VenueAccountChangeEvent changeEvent = changeEventCaptor.getValue();
        assertThat(changeEvent.getMetadata().getInResponseToRequestId()).isEqualTo(DEFAULT_MESSAGE_ID);
        assertThat(changeEvent.getVenueAccountChangeEventType()).isEqualTo(VENUE_ACCOUNT_CHANGE_EVENT_TYPE_CREATED);
        assertThat(changeEvent.getVenueAccount()).isEqualTo(venueAccount);
        assertThat(changeEvent.getGrantsList()).containsExactlyElementsOf(request.getGrantsList());
    }

    @Test
    void givenNonRecoverableException_whenCreateAccount_changeEventIsSkipped() {
        VenueAccountCreateRequest request = defaultAccountCreateRequest().build();

        doThrow(new NullPointerException()).when(venueAccountMap).putIfAbsent(any(), any());

        assertThatThrownBy(() -> venueAccountService.createFromRequest(request))
            .isInstanceOf(NullPointerException.class);

        verify(venueAccountChangeEventExchange, never()).publish(any(), any());
    }

    @Test
    void givenNeedsRetryException_whenCreateAccount_changeEventIsSkipped() {
        VenueAccountCreateRequest request = defaultAccountCreateRequest().build();

        doThrow(new RuntimeException()).when(venueAccountMap).putIfAbsent(any(), any());

        assertThatThrownBy(() -> venueAccountService.createFromRequest(request))
            .isInstanceOf(FailureNeedsRetryException.class);

        verify(venueAccountChangeEventExchange, never()).publish(any(), any());
    }

    @ParameterizedTest(name = "givenNoExceptionAndStreetAccountAndConnectorDetails_whenCreateAccount_connectorRequestIsSent({0})")
    @EnumSource(value = AccountType.class, names={ "EXCHANGE", "ACCOUNT_TYPE_UNSPECIFIED" }, mode = EnumSource.Mode.INCLUDE)
    void givenNoExceptionAndStreetAccountAndConnectorDetails_whenCreateAccount_connectorRequestIsSent(AccountType accountType) {
        VenueAccountCreateRequest request = defaultAccountCreateRequest(accountType).build();

        VenueAccount venueAccount = venueAccountService.createFromRequest(request);

        ArgumentCaptor<ConnectorRequest> connectorRequestCaptor = ArgumentCaptor.forClass(ConnectorRequest.class);
        verify(connectorRequestExchange, ONCE).publish(connectorRequestCaptor.capture(), eq(EMPTY));

        ConnectorRequest connectorRequest = connectorRequestCaptor.getValue();
        assertThat(connectorRequest.getParametersList()).containsExactlyElementsOf(request.getConnectorDetails().getParametersList());
        assertThat(connectorRequest.getVenueAccountId()).isEqualTo(venueAccount.getId());
        assertThat(connectorRequest.getOwner()).isEqualTo(request.getOwnerUsername());
        assertThat(connectorRequest.getSuspendedAt()).isEqualTo(venueAccount.getSuspendedAt());
        assertThat(connectorRequest.getVenueName()).isEqualTo(request.getVenueName());
    }

    @ParameterizedTest(name = "givenNoExceptionAndNonStreetAccountAndConnectorDetails_whenCreateAccount_connectorRequestIsNotSent({0})")
    @EnumSource(value = AccountType.class, names={ "EXCHANGE", "ACCOUNT_TYPE_UNSPECIFIED", "UNRECOGNIZED" }, mode = EnumSource.Mode.EXCLUDE)
    void givenNoExceptionAndNonStreetAccountAndConnectorDetails_whenCreateAccount_connectorRequestIsNotSent(AccountType accountType) {
        VenueAccountCreateRequest request = defaultAccountCreateRequest(accountType).build();

        venueAccountService.createFromRequest(request);

        verify(connectorRequestExchange, never()).publish(any(), any());
    }

    @ParameterizedTest(name = "givenNoExceptionAndStreetAccountAndNoConnectorDetails_whenCreateAccount_connectorRequestIsSent({0})")
    @EnumSource(value = AccountType.class, names={ "EXCHANGE", "ACCOUNT_TYPE_UNSPECIFIED" }, mode = EnumSource.Mode.INCLUDE)
    void givenNoExceptionAndStreetAccountAndNoConnectorDetails_whenCreateAccount_connectorRequestIsSent(AccountType accountType) {
        VenueAccountCreateRequest request = defaultAccountCreateRequest(accountType)
            .clearConnectorDetails()
            .build();

        VenueAccount created = venueAccountService.createFromRequest(request);

        ArgumentCaptor<ConnectorRequest> connectorRequestCaptor = ArgumentCaptor.forClass(ConnectorRequest.class);
        verify(connectorRequestExchange, ONCE).publish(connectorRequestCaptor.capture(), eq(EMPTY));

        ConnectorRequest connectorRequest = connectorRequestCaptor.getValue();
        assertThat(connectorRequest.getParametersList()).containsExactlyElementsOf(request.getConnectorDetails().getParametersList());
        assertThat(connectorRequest.getVenueAccountId()).isEqualTo(created.getId());
        assertThat(connectorRequest.getOwner()).isEqualTo(created.getOwnerUsername());
        assertThat(connectorRequest.getSuspendedAt()).isEqualTo(created.getSuspendedAt());
        assertThat(connectorRequest.getVenueName()).isEqualTo(created.getVenueName());
    }

    @ParameterizedTest(name = "givenNonRecoverableExceptionAndStreetAccountAndConnectorDetails_whenCreateAccount_connectorRequestIsNotSent({0})")
    @EnumSource(value = AccountType.class, names={ "EXCHANGE", "ACCOUNT_TYPE_UNSPECIFIED" }, mode = EnumSource.Mode.INCLUDE)
    void givenNonRecoverableExceptionAndStreetAccountAndConnectorDetails_whenCreateAccount_connectorRequestIsNotSent(AccountType accountType) {
        VenueAccountCreateRequest request = defaultAccountCreateRequest(accountType).build();

        doThrow(new NullPointerException()).when(venueAccountMap).putIfAbsent(any(), any());

        assertThatThrownBy(() -> venueAccountService.createFromRequest(request))
            .isInstanceOf(NullPointerException.class);

        verify(connectorRequestExchange, never()).publish(any(), any());
    }

    @ParameterizedTest(name = "givenNeedsRetryExceptionAndStreetAccountAndConnectorDetails_whenCreateAccount_connectorRequestIsNotSent({0})")
    @EnumSource(value = AccountType.class, names={ "EXCHANGE", "ACCOUNT_TYPE_UNSPECIFIED" }, mode = EnumSource.Mode.INCLUDE)
    void givenNeedsRetryExceptionAndStreetAccountAndConnectorDetails_whenCreateAccount_connectorRequestIsNotSent(AccountType accountType) {
        VenueAccountCreateRequest request = defaultAccountCreateRequest(accountType).build();

        doThrow(new RuntimeException()).when(venueAccountMap).putIfAbsent(any(), any());

        assertThatThrownBy(() -> venueAccountService.createFromRequest(request))
            .isInstanceOf(FailureNeedsRetryException.class);

        verify(connectorRequestExchange, never()).publish(any(), any());
    }

    @Test
    void givenNoExceptionAndMessageId_whenCreateAccount_eventLogEventIsSent() {
        VenueAccountCreateRequest request = defaultAccountCreateRequest().build();

        venueAccountService.createFromRequest(request);

        ArgumentCaptor<EventLogEvent> eventLogEventCaptor = ArgumentCaptor.forClass(EventLogEvent.class);
        @SuppressWarnings("unchecked")
        ArgumentCaptor<Map<String, String>> headersCaptor = ArgumentCaptor.forClass(Map.class);
        verify(eventLogExchange, ONCE).publishWithHeaders(eventLogEventCaptor.capture(), headersCaptor.capture());

        Map<String, String> headers = headersCaptor.getValue();
        assertThat(headers.get(OemsHeader.CLIENT_ID.getHeaderName())).isEqualTo(request.getOwnerUsername());

        EventLogEvent event = eventLogEventCaptor.getValue();
        assertThat(event.getClientId()).isEqualTo(request.getOwnerUsername());
        assertThat(event.getTimestamp()).satisfies(Instant::parse);
        assertThat(event.getDescription()).contains("Successful");
        assertThat(event.getRequestId()).isNotBlank();
        assertThat(event.getStatus()).isEqualTo(EventLogStatus.SUCCESS);
        assertThat(event.getEventType()).isEqualTo(EventLogEmitter.ACCOUNT_CREATE_REQUEST_STATUS);
        assertThat(event.getMetadata().getCreatedAt()).satisfies(Instant::parse);
        assertThat(event.getMetadata().getCorrelationObject()).isEqualTo(request.getMetadata().getCorrelationObject());
    }

    @Test
    void givenNoExceptionAndNoMessageId_whenCreateAccount_eventLogEventIsNotSent() {
        VenueAccountCreateRequest request = defaultAccountCreateRequest()
            .clearMessageId()
            .build();

        venueAccountService.createFromRequest(request);

        verify(eventLogExchange, never()).publishWithHeaders(any(), any());
    }

    @Test
    void givenFatalExceptionAndMessageId_whenCreateAccount_eventLogIsSent() {
        VenueAccountCreateRequest request = defaultAccountCreateRequest()
            .setVenueAccountName(EMPTY)
            .build();

        assertThatThrownBy(() -> venueAccountService.createFromRequest(request))
            .isInstanceOf(IllegalArgumentException.class);

        ArgumentCaptor<EventLogEvent> eventLogEventCaptor = ArgumentCaptor.forClass(EventLogEvent.class);
        @SuppressWarnings("unchecked")
        ArgumentCaptor<Map<String, String>> headersCaptor = ArgumentCaptor.forClass(Map.class);
        verify(eventLogExchange, ONCE).publishWithHeaders(eventLogEventCaptor.capture(), headersCaptor.capture());

        Map<String, String> headers = headersCaptor.getValue();
        assertThat(headers.get(OemsHeader.CLIENT_ID.getHeaderName())).isEqualTo(request.getOwnerUsername());

        EventLogEvent event = eventLogEventCaptor.getValue();
        assertThat(event.getClientId()).isEqualTo(request.getOwnerUsername());
        assertThat(event.getTimestamp()).satisfies(Instant::parse);
        assertThat(event.getDescription()).contains("Failed");
        assertThat(event.getRequestId()).isNotBlank();
        assertThat(event.getStatus()).isEqualTo(EventLogStatus.FAILURE);
        assertThat(event.getEventType()).isEqualTo(EventLogEmitter.ACCOUNT_CREATE_REQUEST_STATUS);
        assertThat(event.getMetadata().getCreatedAt()).satisfies(Instant::parse);
        assertThat(event.getMetadata().getCorrelationObject()).isEqualTo(request.getMetadata().getCorrelationObject());
    }

    @Test
    void givenFatalExceptionAndNoMessageId_whenCreateAccount_eventLogEventIsNotSent() {
        VenueAccountCreateRequest request = defaultAccountCreateRequest()
            .clearVenueAccountName()
            .clearMessageId()
            .build();

        assertThatThrownBy(() -> venueAccountService.createFromRequest(request))
            .isInstanceOf(IllegalArgumentException.class);

        verify(eventLogExchange, never()).publishWithHeaders(any(), any());
    }

    @Test
    void givenNeedsRetryExceptionAndMessageId_whenCreateAccount_eventLogEventIsNotSent() {
        VenueAccountCreateRequest request = defaultAccountCreateRequest().build();

        doThrow(new RuntimeException()).when(venueAccountMap).putIfAbsent(any(), any());

        assertThatThrownBy(() -> venueAccountService.createFromRequest(request))
            .isInstanceOf(FailureNeedsRetryException.class);

        verify(eventLogExchange, never()).publishWithHeaders(any(), any());
    }

    @Test
    void givenRabbitFailures_whenCreateAccount_errorsAreIgnored() {
        doThrow(new RuntimeException()).when(venueAccountChangeEventExchange).publish(any(), any());
        doThrow(new RuntimeException()).when(connectorRequestExchange).publish(any(), any());
        doThrow(new RuntimeException()).when(eventLogExchange).publishWithHeaders(any(), any());

        VenueAccountCreateRequest request = defaultAccountCreateRequest().build();
        VenueAccount venueAccount = venueAccountService.createFromRequest(request);

        assertThat(venueAccountMap.get(venueAccount.getId())).isEqualTo(venueAccount);
        verify(venueAccountChangeEventExchange, ONCE).publish(any(), any());
        verify(connectorRequestExchange, ONCE).publish(any(), any());
        verify(eventLogExchange, ONCE).publishWithHeaders(any(), any());
    }

    @Test
    void givenBlankAccountId_whenCreateAccount_validationFails() {
        VenueAccountCreateRequest request = defaultAccountCreateRequest()
            .setVenueAccountId(" ")
            .build();

        assertThatThrownBy(() -> venueAccountService.createFromRequest(request))
            .isInstanceOf(IllegalArgumentException.class);

        assertThat(venueAccountMap.size()).isZero();
    }

    @ParameterizedTest(name = "givenCorrectFormatAccountId_whenCreateAccount_validationSucceeds({0})")
    @ValueSource(strings = {"foo", "foo.bar", "foo-bar", "foo.bar-baz", "foo-bar.baz", "foo.bar-baz.qux", "01234567890123456789012345678901"})
    void givenCorrectFormatAccountId_whenCreateAccount_validationSucceeds(String accountId) {
        VenueAccountCreateRequest request = defaultAccountCreateRequest()
            .setVenueAccountId(accountId)
            .build();

        VenueAccount venueAccount = venueAccountService.createFromRequest(request);

        assertThat(venueAccountMap.get(venueAccount.getId())).isEqualTo(venueAccount);
    }

    @ParameterizedTest(name = "givenWrongFormatAccountId_whenCreateAccount_validationFails({0})")
    @ValueSource(strings = {"foo,", ".foo", "foo.", "-foo", "foo-", "foo.-bar", "foo-.bar", "012345678901234567890123456789012"})
    void givenWrongFormatAccountId_whenCreateAccount_validationFails(String accountId) {
        VenueAccountCreateRequest request = defaultAccountCreateRequest()
            .setVenueAccountId(accountId)
            .build();

        assertThatThrownBy(() -> venueAccountService.createFromRequest(request))
            .isInstanceOf(IllegalArgumentException.class);

        assertThat(venueAccountMap.size()).isZero();
    }

    @Test
    void givenDuplicateAccountId_whenCreateAccount_validationFails() {
        VenueAccountCreateRequest request1 = defaultAccountCreateRequest()
            .setVenueAccountId(DEFAULT_ACCOUNT_ID)
            .build();
        venueAccountService.createFromRequest(request1);

        VenueAccountCreateRequest request2 = defaultAccountCreateRequest()
            .setVenueAccountId(DEFAULT_ACCOUNT_ID)
            .setVenueAccountName(DEFAULT_ACCOUNT_ID)
            .build();
        assertThatThrownBy(() -> venueAccountService.createFromRequest(request2))
            .hasMessageContaining("VenueAccount with id")
            .isInstanceOf(IllegalArgumentException.class);

        assertThat(venueAccountMap.size()).isEqualTo(1);
    }

    @Test
    void givenNoAccountName_whenCreateAccount_validationFails() {
        VenueAccountCreateRequest request = defaultAccountCreateRequest()
            .clearVenueAccountName()
            .build();

        assertThatThrownBy(() -> venueAccountService.createFromRequest(request))
            .isInstanceOf(IllegalArgumentException.class);

        assertThat(venueAccountMap.size()).isZero();
    }

    @Test
    void givenDuplicatedAccountName_whenCreateAccount_validationFails() {
        VenueAccount duplicateAccount = VenueAccount.newBuilder()
            .setVenueAccountName(DEFAULT_VENUE_ACCOUNT_NAME)
            .build();
        venueAccountMap.set(DEFAULT_MESSAGE_ID, duplicateAccount);

        VenueAccountCreateRequest request = defaultAccountCreateRequest().build();

        assertThatThrownBy(() -> venueAccountService.createFromRequest(request))
            .isInstanceOf(IllegalArgumentException.class);

        assertThat(venueAccountMap.size()).isEqualTo(1);
    }

    @Test
    void givenNoAccountType_whenCreateAccount_validationFails() {
        VenueAccountCreateRequest request = defaultAccountCreateRequest()
            .setAccountTypeValue(-1)
            .build();

        assertThatThrownBy(() -> venueAccountService.createFromRequest(request))
            .isInstanceOf(IllegalArgumentException.class);

        assertThat(venueAccountMap.size()).isZero();
    }

    @Test
    void givenNoOwnerUsername_whenCreateAccount_validationFails() {
        VenueAccountCreateRequest request = defaultAccountCreateRequest()
            .clearOwnerUsername()
            .build();

        assertThatThrownBy(() -> venueAccountService.createFromRequest(request))
            .isInstanceOf(IllegalArgumentException.class);

        assertThat(venueAccountMap.size()).isZero();
    }

    @ParameterizedTest(name = "givenNoVenueAccountTypeAndVenueName_whenCreateAccount_validationFails({0})")
    @EnumSource(value = AccountType.class, names = {"WALLET", "CUSTODY"}, mode = EnumSource.Mode.INCLUDE)
    void givenNoVenueAccountTypeAndVenueName_whenCreateAccount_validationFails(AccountType accountType) {
        VenueAccountCreateRequest request = defaultAccountCreateRequest(accountType)
            .setVenueName(DEFAULT_VENUE_NAME)
            .build();

        assertThatThrownBy(() -> venueAccountService.createFromRequest(request))
            .hasMessageContaining("Venue name set for non-venue account")
            .isInstanceOf(IllegalArgumentException.class);

        assertThat(venueAccountMap.size()).isZero();
    }

    @ParameterizedTest(name = "givenVenueAccountTypeAndNoVenueName_whenCreateAccount_validationFails({0})")
    @EnumSource(value = AccountType.class, names = {"WALLET", "CUSTODY", "UNRECOGNIZED"}, mode = EnumSource.Mode.EXCLUDE)
    void givenVenueAccountTypeAndNoVenueName_whenCreateAccount_validationFails(AccountType accountType) {
        VenueAccountCreateRequest request = defaultAccountCreateRequest(accountType)
            .clearVenueName()
            .build();

        assertThatThrownBy(() -> venueAccountService.createFromRequest(request))
            .hasMessage("Venue name is blank")
            .isInstanceOf(IllegalArgumentException.class);

        assertThat(venueAccountMap.size()).isZero();
    }

    @ParameterizedTest(name = "givenVenueAccountTypeAndNonExistingVenueName_whenCreateAccount_validationFails({0})")
    @EnumSource(value = AccountType.class, names = {"WALLET", "CUSTODY", "UNRECOGNIZED"}, mode = EnumSource.Mode.EXCLUDE)
    void givenVenueAccountTypeAndNonExistingVenueName_whenCreateAccount_validationFails(AccountType accountType) {
        VenueAccountCreateRequest request = defaultAccountCreateRequest(accountType)
            .setVenueName(DEFAULT_MESSAGE_ID)
            .build();

        assertThatThrownBy(() -> venueAccountService.createFromRequest(request))
            .hasMessageContaining("not found")
            .isInstanceOf(IllegalArgumentException.class);

        assertThat(venueAccountMap.size()).isZero();
    }

    @ParameterizedTest(name = "givenVenueAccountTypeAndIncorrectVenueName_whenCreateAccount_validationFails({0})")
    @EnumSource(value = AccountType.class, names = {"WALLET", "CUSTODY", "UNRECOGNIZED"}, mode = EnumSource.Mode.EXCLUDE)
    void givenVenueAccountTypeAndIncorrectVenueName_whenCreateAccount_validationFails(AccountType accountType) {
        String venueClobName = venues.get(AccountType.ACCOUNT_TYPE_CLOB).orElseThrow().getName();
        String venueExchangeName = venues.get(AccountType.EXCHANGE).orElseThrow().getName();

        VenueAccountCreateRequest request = defaultAccountCreateRequest(accountType)
            .setVenueName(accountType == AccountType.ACCOUNT_TYPE_CLOB ? venueExchangeName : venueClobName)
            .build();

        assertThatThrownBy(() -> venueAccountService.createFromRequest(request))
            .hasMessageContaining("is incorrect")
            .isInstanceOf(IllegalArgumentException.class);

        assertThat(venueAccountMap.size()).isZero();
    }

    @Test
    void whenUpdateAccount_allFieldsAreUpdated() {
        VenueAccountCreateRequest createRequest = defaultAccountCreateRequest().build();
        VenueAccount created = venueAccountService.createFromRequest(createRequest);

        Venue updateVenue = venues.get(AccountType.ACCOUNT_TYPE_CLOB).orElseThrow();
        VenueAccountUpdateRequest updateRequest = defaultAccountUpdateRequest(created.getId())
            .setVenueAccountName(randomValue("account-name"))
            .setVenueName(updateVenue.getName())
            .setOwnerUsername(randomValue("owner-username"))
            .setAccountType(AccountType.ACCOUNT_TYPE_CLOB)
            .setArchived(true)
            .build();

        VenueAccount updated = venueAccountService.updateFromRequest(updateRequest);
        assertThat(venueAccountMap.get(created.getId())).isEqualTo(updated);
        assertThat(updated.getVenueAccountName()).isEqualTo(updateRequest.getVenueAccountName());
        assertThat(updated.getVenueName()).isEqualTo(updateRequest.getVenueName());
        assertThat(updated.getOwnerUsername()).isEqualTo(updateRequest.getOwnerUsername());
        assertThat(updated.getAccountType()).isEqualTo(updateRequest.getAccountType());
        assertThat(updated.getArchivedAt()).isNotBlank();
    }

    @Test
    void givenArchivedAccount_whenUpdateAccount_archivedFlagIsRemoved() {
        VenueAccountCreateRequest createRequest = defaultAccountCreateRequest().build();
        VenueAccount created = venueAccountService.createFromRequest(createRequest);

        Venue updateVenue = venues.get(AccountType.ACCOUNT_TYPE_CLOB).orElseThrow();
        VenueAccountUpdateRequest updateRequest = defaultAccountUpdateRequest(created.getId())
            .setVenueAccountName(randomValue("account-name"))
            .setVenueName(updateVenue.getName())
            .setOwnerUsername(randomValue("owner-username"))
            .setAccountType(AccountType.ACCOUNT_TYPE_CLOB)
            .setArchived(true)
            .build();

        VenueAccount updated = venueAccountService.updateFromRequest(updateRequest);
        assertThat(updated.getArchivedAt()).isNotBlank();

        VenueAccountUpdateRequest updateRequest2 = defaultAccountUpdateRequest(created.getId())
            .setVenueAccountName(randomValue("account-name"))
            .setVenueName(updateVenue.getName())
            .setOwnerUsername(randomValue("owner-username"))
            .setAccountType(AccountType.ACCOUNT_TYPE_CLOB)
            .setArchived(false)
            .build();

        VenueAccount updated2 = venueAccountService.updateFromRequest(updateRequest2);
        assertThat(updated2.getArchivedAt()).isBlank();
    }

    @Test
    void givenSuspendedAccount_whenUpdateAccount_suspendedFlagIsRemoved() {
        VenueAccountCreateRequest createRequest = defaultAccountCreateRequest().build();
        VenueAccount created = venueAccountService.createFromRequest(createRequest);

        venueAccountService.deactivate(created.getId());
        assertThat(venueAccountMap.get(created.getId()).getSuspendedAt()).isNotBlank();

        VenueAccountUpdateRequest updateRequest = defaultAccountUpdateRequest(created.getId()).build();
        VenueAccount updated = venueAccountService.updateFromRequest(updateRequest);
        assertThat(venueAccountMap.get(created.getId())).isEqualTo(updated);
    }

    @Test
    void givenNoException_whenUpdateAccount_changeEventIsSent() {
        VenueAccountCreateRequest createRequest = defaultAccountCreateRequest().build();
        VenueAccount created = venueAccountService.createFromRequest(createRequest);
        resetMocks();

        VenueAccountUpdateRequest updateRequest = defaultAccountUpdateRequest(created.getId())
            .setOwnerUsername(randomValue("owner"))
            .build();
        VenueAccount updated = venueAccountService.updateFromRequest(updateRequest);

        ArgumentCaptor<VenueAccountChangeEvent> changeEventCaptor = ArgumentCaptor.forClass(VenueAccountChangeEvent.class);
        verify(venueAccountChangeEventExchange, ONCE).publish(changeEventCaptor.capture(), eq(EMPTY));

        VenueAccountChangeEvent changeEvent = changeEventCaptor.getValue();
        assertThat(changeEvent.getMetadata().getInResponseToRequestId()).isEqualTo(DEFAULT_MESSAGE_ID);
        assertThat(changeEvent.getVenueAccountChangeEventType()).isEqualTo(VENUE_ACCOUNT_CHANGE_EVENT_TYPE_UPDATED);
        assertThat(changeEvent.getVenueAccount()).isEqualTo(updated);
        assertThat(changeEvent.getGrantsList()).isEmpty();
    }

    @Test
    void givenNonRecoverableException_whenUpdateAccount_changeEventIsSkipped() {
        VenueAccountCreateRequest createRequest = defaultAccountCreateRequest().build();
        VenueAccount created = venueAccountService.createFromRequest(createRequest);
        resetMocks();

        doThrow(new NullPointerException()).when(venueAccountMap).replace(any(), any());

        VenueAccountUpdateRequest updateRequest = defaultAccountUpdateRequest(created.getId())
            .setOwnerUsername(randomValue("owner"))
            .build();
        assertThatThrownBy(() -> venueAccountService.updateFromRequest(updateRequest))
            .isInstanceOf(NullPointerException.class);

        verify(venueAccountChangeEventExchange, never()).publish(any(), any());
    }

    @Test
    void givenNeedsRetryException_whenUpdateAccount_changeEventIsSkipped() {
        VenueAccountCreateRequest createRequest = defaultAccountCreateRequest().build();
        VenueAccount created = venueAccountService.createFromRequest(createRequest);
        resetMocks();

        doThrow(new RuntimeException()).when(venueAccountMap).replace(any(), any());

        VenueAccountUpdateRequest updateRequest = defaultAccountUpdateRequest(created.getId())
            .setOwnerUsername(randomValue("owner"))
            .build();
        assertThatThrownBy(() -> venueAccountService.updateFromRequest(updateRequest))
            .isInstanceOf(FailureNeedsRetryException.class);

        verify(venueAccountChangeEventExchange, never()).publish(any(), any());
    }

    @ParameterizedTest(name = "givenNoExceptionAndStreetAccountAndConnectorDetails_whenUpdateAccount_connectorRequestIsSent({0})")
    @EnumSource(value = AccountType.class, names={ "EXCHANGE", "ACCOUNT_TYPE_UNSPECIFIED" }, mode = EnumSource.Mode.INCLUDE)
    void givenNoExceptionAndStreetAccountAndConnectorDetails_whenUpdateAccount_connectorRequestIsSent(AccountType accountType) {
        VenueAccountCreateRequest createRequest = defaultAccountCreateRequest().build();
        VenueAccount created = venueAccountService.createFromRequest(createRequest);
        resetMocks();

        Venue venue = venues.get(accountType).orElseThrow();
        VenueAccountUpdateRequest updateRequest = defaultAccountUpdateRequest(created.getId())
            .setVenueName(venue.getName())
            .setAccountType(accountType)
            .build();
        VenueAccount updated = venueAccountService.updateFromRequest(updateRequest);

        ArgumentCaptor<ConnectorRequest> connectorRequestCaptor = ArgumentCaptor.forClass(ConnectorRequest.class);
        verify(connectorRequestExchange, ONCE).publish(connectorRequestCaptor.capture(), eq(EMPTY));

        ConnectorRequest connectorRequest = connectorRequestCaptor.getValue();
        assertThat(connectorRequest.getParametersList()).containsExactlyElementsOf(updateRequest.getConnectorDetails().getParametersList());
        assertThat(connectorRequest.getVenueAccountId()).isEqualTo(updated.getId());
        assertThat(connectorRequest.getOwner()).isEqualTo(updated.getOwnerUsername());
        assertThat(connectorRequest.getSuspendedAt()).isEqualTo(updated.getSuspendedAt());
        assertThat(connectorRequest.getVenueName()).isEqualTo(updated.getVenueName());
    }

    @ParameterizedTest(name = "givenNoExceptionAndNonStreetAccountAndConnectorDetails_whenUpdateAccount_connectorRequestIsNotSent({0})")
    @EnumSource(value = AccountType.class, names={ "EXCHANGE", "ACCOUNT_TYPE_UNSPECIFIED", "UNRECOGNIZED" }, mode = EnumSource.Mode.EXCLUDE)
    void givenNoExceptionAndNonStreetAccountAndConnectorDetails_whenUpdateAccount_connectorRequestIsNotSent(AccountType accountType) {
        VenueAccountCreateRequest createRequest = defaultAccountCreateRequest()
            .setAccountType(WALLET)
            .clearVenueName()
            .build();
        VenueAccount created = venueAccountService.createFromRequest(createRequest);
        resetMocks();

        String venueName = venues.get(accountType).map(Venue::getName).orElse(EMPTY);
        VenueAccountUpdateRequest updateRequest = defaultAccountUpdateRequest(created.getId())
            .setVenueName(venueName)
            .setAccountType(accountType)
            .build();
        venueAccountService.updateFromRequest(updateRequest);

        verify(connectorRequestExchange, never()).publish(any(), any());
    }

    @ParameterizedTest(name = "givenNoExceptionAndStreetAccountAndNoConnectorDetails_whenUpdateAccount_connectorRequestIsSent({0})")
    @EnumSource(value = AccountType.class, names={ "EXCHANGE", "ACCOUNT_TYPE_UNSPECIFIED" }, mode = EnumSource.Mode.INCLUDE)
    void givenNoExceptionAndStreetAccountAndNoConnectorDetails_whenUpdateAccount_connectorRequestIsSent(AccountType accountType) {
        VenueAccountCreateRequest createRequest = defaultAccountCreateRequest().build();
        VenueAccount created = venueAccountService.createFromRequest(createRequest);
        resetMocks();

        Venue venue = venues.get(accountType).orElseThrow();
        VenueAccountUpdateRequest updateRequest = defaultAccountUpdateRequest(created.getId())
            .setVenueName(venue.getName())
            .setAccountType(accountType)
            .clearConnectorDetails()
            .build();
        VenueAccount updated = venueAccountService.updateFromRequest(updateRequest);

        ArgumentCaptor<ConnectorRequest> connectorRequestCaptor = ArgumentCaptor.forClass(ConnectorRequest.class);
        verify(connectorRequestExchange, ONCE).publish(connectorRequestCaptor.capture(), eq(EMPTY));

        ConnectorRequest connectorRequest = connectorRequestCaptor.getValue();
        assertThat(connectorRequest.getParametersList()).containsExactlyElementsOf(updateRequest.getConnectorDetails().getParametersList());
        assertThat(connectorRequest.getVenueAccountId()).isEqualTo(updated.getId());
        assertThat(connectorRequest.getOwner()).isEqualTo(updated.getOwnerUsername());
        assertThat(connectorRequest.getSuspendedAt()).isEqualTo(updated.getSuspendedAt());
        assertThat(connectorRequest.getVenueName()).isEqualTo(updated.getVenueName());
    }

    @ParameterizedTest(name = "givenNonRecoverableExceptionAndStreetAccountAndConnectorDetails_whenUpdateAccount_connectorRequestIsNotSent({0})")
    @EnumSource(value = AccountType.class, names={ "EXCHANGE", "ACCOUNT_TYPE_UNSPECIFIED" }, mode = EnumSource.Mode.INCLUDE)
    void givenNonRecoverableExceptionAndStreetAccountAndConnectorDetails_whenUpdateAccount_connectorRequestIsNotSent(AccountType accountType) {
        VenueAccountCreateRequest createRequest = defaultAccountCreateRequest().build();
        VenueAccount created = venueAccountService.createFromRequest(createRequest);
        resetMocks();

        doThrow(new NullPointerException()).when(venueAccountMap).replace(any(), any());

        Venue venue = venues.get(accountType).orElseThrow();
        VenueAccountUpdateRequest updateRequest = defaultAccountUpdateRequest(created.getId())
            .setVenueName(venue.getName())
            .setAccountType(accountType)
            .build();
        assertThatThrownBy(() -> venueAccountService.updateFromRequest(updateRequest))
            .isInstanceOf(NullPointerException.class);

        verify(connectorRequestExchange, never()).publish(any(), any());
    }

    @ParameterizedTest(name = "givenNeedsRetryExceptionAndStreetAccountAndConnectorDetails_whenUpdateAccount_connectorRequestIsNotSent({0})")
    @EnumSource(value = AccountType.class, names={ "EXCHANGE", "ACCOUNT_TYPE_UNSPECIFIED" }, mode = EnumSource.Mode.INCLUDE)
    void givenNeedsRetryExceptionAndStreetAccountAndConnectorDetails_whenUpdateAccount_connectorRequestIsNotSent(AccountType accountType) {
        VenueAccountCreateRequest createRequest = defaultAccountCreateRequest().build();
        VenueAccount created = venueAccountService.createFromRequest(createRequest);
        resetMocks();

        doThrow(new RuntimeException()).when(venueAccountMap).replace(any(), any());

        Venue venue = venues.get(accountType).orElseThrow();
        VenueAccountUpdateRequest updateRequest = defaultAccountUpdateRequest(created.getId())
            .setVenueName(venue.getName())
            .setAccountType(accountType)
            .build();
        assertThatThrownBy(() -> venueAccountService.updateFromRequest(updateRequest))
            .isInstanceOf(FailureNeedsRetryException.class);

        verify(connectorRequestExchange, never()).publish(any(), any());
    }

    @Test
    void givenNoExceptionAndMessageId_whenUpdateAccount_eventLogEventIsSent() {
        VenueAccountCreateRequest createRequest = defaultAccountCreateRequest().build();
        VenueAccount created = venueAccountService.createFromRequest(createRequest);
        resetMocks();

        VenueAccountUpdateRequest updateRequest = defaultAccountUpdateRequest(created.getId())
            .setOwnerUsername(randomValue("owner"))
            .build();
        venueAccountService.updateFromRequest(updateRequest);

        ArgumentCaptor<EventLogEvent> eventLogEventCaptor = ArgumentCaptor.forClass(EventLogEvent.class);
        @SuppressWarnings("unchecked")
        ArgumentCaptor<Map<String, String>> headersCaptor = ArgumentCaptor.forClass(Map.class);
        verify(eventLogExchange, ONCE).publishWithHeaders(eventLogEventCaptor.capture(), headersCaptor.capture());

        Map<String, String> headers = headersCaptor.getValue();
        assertThat(headers.get(OemsHeader.CLIENT_ID.getHeaderName())).isEqualTo(updateRequest.getOwnerUsername());

        EventLogEvent event = eventLogEventCaptor.getValue();
        assertThat(event.getClientId()).isEqualTo(updateRequest.getOwnerUsername());
        assertThat(event.getTimestamp()).satisfies(Instant::parse);
        assertThat(event.getDescription()).contains("Successful");
        assertThat(event.getRequestId()).isNotBlank();
        assertThat(event.getStatus()).isEqualTo(EventLogStatus.SUCCESS);
        assertThat(event.getEventType()).isEqualTo(EventLogEmitter.ACCOUNT_UPDATE_REQUEST_STATUS);
        assertThat(event.getMetadata().getCreatedAt()).satisfies(Instant::parse);
        assertThat(event.getMetadata().getCorrelationObject()).isEqualTo(updateRequest.getMetadata().getCorrelationObject());
    }

    @Test
    void givenNoExceptionAndNoMessageId_whenUpdateAccount_eventLogEventIsNotSent() {
        VenueAccountCreateRequest createRequest = defaultAccountCreateRequest().build();
        VenueAccount created = venueAccountService.createFromRequest(createRequest);
        resetMocks();

        VenueAccountUpdateRequest updateRequest = defaultAccountUpdateRequest(created.getId())
            .setOwnerUsername(randomValue("owner"))
            .clearMessageId()
            .build();
        venueAccountService.updateFromRequest(updateRequest);

        verify(eventLogExchange, never()).publishWithHeaders(any(), any());
    }

    @Test
    void givenFatalExceptionAndMessageId_whenUpdateAccount_eventLogIsSent() {
        VenueAccountCreateRequest createRequest = defaultAccountCreateRequest().build();
        VenueAccount created = venueAccountService.createFromRequest(createRequest);
        resetMocks();

        doThrow(new NullPointerException()).when(venueAccountMap).replace(any(), any());

        VenueAccountUpdateRequest updateRequest = defaultAccountUpdateRequest(created.getId())
            .setOwnerUsername(randomValue("owner"))
            .build();
        assertThatThrownBy(() -> venueAccountService.updateFromRequest(updateRequest))
            .isInstanceOf(NullPointerException.class);

        ArgumentCaptor<EventLogEvent> eventLogEventCaptor = ArgumentCaptor.forClass(EventLogEvent.class);
        @SuppressWarnings("unchecked")
        ArgumentCaptor<Map<String, String>> headersCaptor = ArgumentCaptor.forClass(Map.class);
        verify(eventLogExchange, ONCE).publishWithHeaders(eventLogEventCaptor.capture(), headersCaptor.capture());

        Map<String, String> headers = headersCaptor.getValue();
        assertThat(headers.get(OemsHeader.CLIENT_ID.getHeaderName())).isEqualTo(updateRequest.getOwnerUsername());

        EventLogEvent event = eventLogEventCaptor.getValue();
        assertThat(event.getClientId()).isEqualTo(updateRequest.getOwnerUsername());
        assertThat(event.getTimestamp()).satisfies(Instant::parse);
        assertThat(event.getDescription()).contains("Failed");
        assertThat(event.getRequestId()).isNotBlank();
        assertThat(event.getStatus()).isEqualTo(EventLogStatus.FAILURE);
        assertThat(event.getEventType()).isEqualTo(EventLogEmitter.ACCOUNT_UPDATE_REQUEST_STATUS);
        assertThat(event.getMetadata().getCreatedAt()).satisfies(Instant::parse);
        assertThat(event.getMetadata().getCorrelationObject()).isEqualTo(updateRequest.getMetadata().getCorrelationObject());
    }

    @Test
    void givenFatalExceptionAndNoMessageId_whenUpdateAccount_eventLogEventIsNotSent() {
        VenueAccountCreateRequest createRequest = defaultAccountCreateRequest().build();
        VenueAccount created = venueAccountService.createFromRequest(createRequest);
        resetMocks();

        doThrow(new NullPointerException()).when(venueAccountMap).replace(any(), any());

        VenueAccountUpdateRequest updateRequest = defaultAccountUpdateRequest(created.getId())
            .setOwnerUsername(randomValue("owner"))
            .clearMessageId()
            .build();
        assertThatThrownBy(() -> venueAccountService.updateFromRequest(updateRequest))
            .isInstanceOf(NullPointerException.class);

        verify(eventLogExchange, never()).publishWithHeaders(any(), any());
    }

    @Test
    void givenNeedsRetryExceptionAndMessageId_whenUpdateAccount_eventLogEventIsNotSent() {
        VenueAccountCreateRequest createRequest = defaultAccountCreateRequest().build();
        VenueAccount created = venueAccountService.createFromRequest(createRequest);
        resetMocks();

        doThrow(new RuntimeException()).when(venueAccountMap).replace(any(), any());

        VenueAccountUpdateRequest updateRequest = defaultAccountUpdateRequest(created.getId())
            .setOwnerUsername(randomValue("owner"))
            .build();
        assertThatThrownBy(() -> venueAccountService.updateFromRequest(updateRequest))
            .isInstanceOf(FailureNeedsRetryException.class);

        verify(eventLogExchange, never()).publishWithHeaders(any(), any());
    }

    @Test
    void givenRabbitFailures_whenUpdateAccount_errorsAreIgnored() {
        VenueAccountCreateRequest createRequest = defaultAccountCreateRequest().build();
        VenueAccount created = venueAccountService.createFromRequest(createRequest);
        resetMocks();

        doThrow(new RuntimeException()).when(venueAccountChangeEventExchange).publish(any(), any());
        doThrow(new RuntimeException()).when(connectorRequestExchange).publish(any(), any());
        doThrow(new RuntimeException()).when(eventLogExchange).publishWithHeaders(any(), any());

        VenueAccountUpdateRequest updateRequest = defaultAccountUpdateRequest(created.getId())
            .setOwnerUsername(randomValue("owner"))
            .build();
        VenueAccount updated = venueAccountService.updateFromRequest(updateRequest);

        assertThat(venueAccountMap.get(created.getId())).isEqualTo(updated);
        verify(venueAccountChangeEventExchange, ONCE).publish(any(), any());
        verify(connectorRequestExchange, ONCE).publish(any(), any());
        verify(eventLogExchange, ONCE).publishWithHeaders(any(), any());
    }

    @Test
    void givenBlankAccountId_whenUpdateAccount_validationFails() {
        VenueAccountUpdateRequest request = defaultAccountUpdateRequest(" ").build();

        assertThatThrownBy(() -> venueAccountService.updateFromRequest(request))
            .isInstanceOf(IllegalArgumentException.class);

        assertThat(venueAccountMap.size()).isZero();
    }

    @Test
    void givenNotExistingAccountId_whenUpdateAccount_validationFails() {
        VenueAccountUpdateRequest request = defaultAccountUpdateRequest(DEFAULT_ACCOUNT_ID).build();

        assertThatThrownBy(() -> venueAccountService.updateFromRequest(request))
            .isInstanceOf(IllegalArgumentException.class);

        assertThat(venueAccountMap.size()).isZero();
    }

    @Test
    void givenBlankAccountName_whenUpdateAccount_validationFails() {
        VenueAccountCreateRequest createRequest = defaultAccountCreateRequest().build();
        VenueAccount created = venueAccountService.createFromRequest(createRequest);
        resetMocks();

        VenueAccountUpdateRequest updateRequest = defaultAccountUpdateRequest(created.getId())
            .setVenueAccountName(" ")
            .build();

        assertThatThrownBy(() -> venueAccountService.updateFromRequest(updateRequest))
            .isInstanceOf(IllegalArgumentException.class);

        assertThat(venueAccountMap.get(created.getId()).getVenueAccountName()).isEqualTo(DEFAULT_VENUE_ACCOUNT_NAME);

    }

    @Test
    void givenDuplicatedAccountName_whenUpdateAccount_validationFails() {
        VenueAccountCreateRequest createRequest = defaultAccountCreateRequest().build();
        VenueAccount created = venueAccountService.createFromRequest(createRequest);
        resetMocks();

        VenueAccountCreateRequest duplicateRequest = defaultAccountCreateRequest()
            .setVenueAccountName(DEFAULT_VENUE_ACCOUNT_NAME + "1")
            .build();
        venueAccountService.createFromRequest(duplicateRequest);

        VenueAccountUpdateRequest updateRequest = defaultAccountUpdateRequest(created.getId())
            .setVenueAccountName(DEFAULT_VENUE_ACCOUNT_NAME + "1")
            .build();

        assertThatThrownBy(() -> venueAccountService.updateFromRequest(updateRequest))
            .isInstanceOf(IllegalArgumentException.class);

        assertThat(venueAccountMap.get(created.getId()).getVenueAccountName()).isEqualTo(DEFAULT_VENUE_ACCOUNT_NAME);
    }

    @Test
    void givenSameAccountName_whenUpdateAccount_validationSucceeds() {
        VenueAccountCreateRequest createRequest = defaultAccountCreateRequest().build();
        VenueAccount created = venueAccountService.createFromRequest(createRequest);
        resetMocks();

        VenueAccountUpdateRequest updateRequest = defaultAccountUpdateRequest(created.getId())
            .setVenueAccountName(DEFAULT_VENUE_ACCOUNT_NAME)
            .setOwnerUsername(DEFAULT_MESSAGE_ID)
            .build();

        venueAccountService.updateFromRequest(updateRequest);

        assertThat(venueAccountMap.get(created.getId()).getOwnerUsername()).isEqualTo(DEFAULT_MESSAGE_ID);
    }

    @ParameterizedTest(name = "givenNoVenueAccountTypeAndVenueName_whenUpdateAccount_validationFails({0})")
    @EnumSource(value = AccountType.class, names = {"WALLET", "CUSTODY"}, mode = EnumSource.Mode.INCLUDE)
    void givenNoVenueAccountTypeAndVenueName_whenUpdateAccount_validationFails(AccountType accountType) {
        VenueAccountCreateRequest createRequest = defaultAccountCreateRequest().build();
        VenueAccount created = venueAccountService.createFromRequest(createRequest);
        resetMocks();

        VenueAccountUpdateRequest updateRequest = defaultAccountUpdateRequest(created.getId())
            .setVenueName(DEFAULT_VENUE_NAME)
            .setAccountType(accountType)
            .build();
        assertThatThrownBy(() -> venueAccountService.updateFromRequest(updateRequest))
            .hasMessageContaining("Venue name set for non-venue account")
            .isInstanceOf(IllegalArgumentException.class);

        assertThat(venueAccountMap.get(created.getId()).getAccountType()).isEqualTo(DEFAULT_ACCOUNT_TYPE);
    }

    @ParameterizedTest(name = "givenVenueAccountTypeAndNoVenueName_whenUpdateAccount_validationFails({0})")
    @EnumSource(value = AccountType.class, names = {"ACCOUNT_TYPE_UNSPECIFIED", "WALLET", "CUSTODY", "UNRECOGNIZED"}, mode = EnumSource.Mode.EXCLUDE)
    void givenVenueAccountTypeAndNoVenueName_whenUpdateAccount_validationFails(AccountType accountType) {
        VenueAccountCreateRequest createRequest = defaultAccountCreateRequest()
            .clearVenueName()
            .setAccountType(WALLET)
            .build();
        VenueAccount created = venueAccountService.createFromRequest(createRequest);
        resetMocks();

        VenueAccountUpdateRequest updateRequest = defaultAccountUpdateRequest(created.getId())
            .setAccountType(accountType)
            .build();

        assertThatThrownBy(() -> venueAccountService.updateFromRequest(updateRequest))
            .hasMessage("Venue name is blank")
            .isInstanceOf(IllegalArgumentException.class);

        assertThat(venueAccountMap.get(created.getId()).getAccountType()).isEqualTo(WALLET);
    }

    @ParameterizedTest(name = "givenVenueAccountTypeAndNonExistingVenueName_whenUpdateAccount_validationFails({0})")
    @EnumSource(value = AccountType.class, names = {"ACCOUNT_TYPE_UNSPECIFIED", "WALLET", "CUSTODY", "UNRECOGNIZED"}, mode = EnumSource.Mode.EXCLUDE)
    void givenVenueAccountTypeAndNonExistingVenueName_whenUpdateAccount_validationFails(AccountType accountType) {
        VenueAccountCreateRequest createRequest = defaultAccountCreateRequest().build();
        VenueAccount created = venueAccountService.createFromRequest(createRequest);
        resetMocks();

        VenueAccountUpdateRequest updateRequest = defaultAccountUpdateRequest(created.getId())
            .setVenueName(DEFAULT_MESSAGE_ID)
            .setAccountType(accountType)
            .build();

        assertThatThrownBy(() -> venueAccountService.updateFromRequest(updateRequest))
            .hasMessageContaining("not found")
            .isInstanceOf(IllegalArgumentException.class);

        assertThat(venueAccountMap.get(created.getId()).getAccountType()).isEqualTo(ACCOUNT_TYPE_UNSPECIFIED);
    }

    @ParameterizedTest(name = "givenVenueAccountTypeAndIncorrectVenueName_whenUpdateAccount_validationFails({0})")
    @EnumSource(value = AccountType.class, names = {"ACCOUNT_TYPE_UNSPECIFIED", "WALLET", "CUSTODY", "UNRECOGNIZED"}, mode = EnumSource.Mode.EXCLUDE)
    void givenVenueAccountTypeAndIncorrectVenueName_whenUpdateAccount_validationFails(AccountType accountType) {
        VenueAccountCreateRequest createRequest = defaultAccountCreateRequest().build();
        VenueAccount created = venueAccountService.createFromRequest(createRequest);
        resetMocks();

        String venueClobName = venues.get(AccountType.ACCOUNT_TYPE_CLOB).orElseThrow().getName();
        String venueExchangeName = venues.get(AccountType.EXCHANGE).orElseThrow().getName();
        VenueAccountUpdateRequest updateRequest = defaultAccountUpdateRequest(created.getId())
            .setVenueName(accountType == AccountType.ACCOUNT_TYPE_CLOB ? venueExchangeName : venueClobName)
            .setAccountType(accountType)
            .build();

        assertThatThrownBy(() -> venueAccountService.updateFromRequest(updateRequest))
            .hasMessageContaining("is incorrect")
            .isInstanceOf(IllegalArgumentException.class);

        assertThat(venueAccountMap.get(created.getId()).getAccountType()).isEqualTo(ACCOUNT_TYPE_UNSPECIFIED);
    }

    @Test
    void givenWrongVenueAccountType_whenUpdateAccount_validationFails() {
        VenueAccountCreateRequest createRequest = defaultAccountCreateRequest().build();
        VenueAccount created = venueAccountService.createFromRequest(createRequest);
        resetMocks();

        VenueAccountUpdateRequest updateRequest = defaultAccountUpdateRequest(created.getId())
            .setAccountTypeValue(-1)
            .build();

        assertThatThrownBy(() -> venueAccountService.updateFromRequest(updateRequest))
            .isInstanceOf(IllegalArgumentException.class);

        assertThat(venueAccountMap.get(created.getId()).getAccountType()).isEqualTo(ACCOUNT_TYPE_UNSPECIFIED);
    }

    @Test
    void givenExistingAccount_whenDeleteById_accountIsDeleted() {
        VenueAccountCreateRequest createRequest = defaultAccountCreateRequest().build();
        VenueAccount created = venueAccountService.createFromRequest(createRequest);

        venueAccountService.delete(created.getId());

        assertThat(venueAccountMap.size()).isZero();
    }

    @Test
    void givenNonExistingAccount_whenDeleteById_NoExceptionIsThrown() {
        assertThat(venueAccountMap.size()).isZero();
        assertThatNoException().isThrownBy(() -> venueAccountService.delete(DEFAULT_ACCOUNT_ID));
    }

    @Test
    void givenExistingAccount_whenDeleteByName_accountIsDeleted() {
        VenueAccountCreateRequest createRequest = defaultAccountCreateRequest().build();
        VenueAccount created = venueAccountService.createFromRequest(createRequest);

        venueAccountService.deleteByName(created.getVenueAccountName());

        assertThat(venueAccountMap.size()).isZero();
    }

    @Test
    void givenNonExistingAccount_whenDeleteByName_NoExceptionIsThrown() {
        assertThat(venueAccountMap.size()).isZero();
        assertThatNoException().isThrownBy(() -> venueAccountService.deleteByName(DEFAULT_VENUE_ACCOUNT_NAME));
    }

    @Test
    void givenExistingAccount_whenDeactivateAndActivate_suspendedChanges() {
        VenueAccountCreateRequest createRequest = defaultAccountCreateRequest().build();
        VenueAccount created = venueAccountService.createFromRequest(createRequest);

        venueAccountService.deactivate(created.getId());
        assertThat(venueAccountMap.get(created.getId()).getSuspendedAt()).satisfies(Instant::parse);

        venueAccountService.activate(created.getId());
        assertThat(venueAccountMap.get(created.getId()).getSuspendedAt()).isEmpty();
    }

    @Test
    void givenNonExistingAccount_whenActivate_exceptionIsThrown() {
        assertThatThrownBy(() -> venueAccountService.activate(DEFAULT_ACCOUNT_ID))
            .hasMessageContaining("not found")
            .isInstanceOf(IllegalArgumentException.class);
    }

    @Test
    void givenNonExistingAccount_whenDeactivate_exceptionIsThrown() {
        assertThatThrownBy(() -> venueAccountService.deactivate(DEFAULT_ACCOUNT_ID))
            .hasMessageContaining("not found")
            .isInstanceOf(IllegalArgumentException.class);
    }

    private VenueAccountCreateRequest.Builder defaultAccountCreateRequest() {
        return defaultAccountCreateRequest(DEFAULT_ACCOUNT_TYPE);
    }

    private VenueAccountCreateRequest.Builder defaultAccountCreateRequest(AccountType accountType) {
        String venueName = venues.get(accountType).map(Venue::getName).orElse(EMPTY);
        Metadata.Builder metadataBuilder = Metadata.newBuilder()
            .setCorrelationObject(DEFAULT_CORRELATION_OBJECT);
        return VenueAccountCreateRequest.newBuilder()
            .setMessageId(DEFAULT_MESSAGE_ID)
            .setVenueAccountName(DEFAULT_VENUE_ACCOUNT_NAME)
            .setVenueName(venueName)
            .setOwnerUsername(DEFAULT_OWNER_USERNAME)
            .setMetadata(metadataBuilder)
            .setConnectorDetails(defaultConnectorDetails())
            .setAccountType(accountType)
            .addGrants(defaultGrant("scope-1"))
            .addGrants(defaultGrant("scope-2"))
            .addGrants(defaultGrant("scope-3"));
    }

    private VenueAccountUpdateRequest.Builder defaultAccountUpdateRequest(String venueAccountId) {
        Metadata.Builder metadataBuilder = Metadata.newBuilder()
            .setCorrelationObject(DEFAULT_CORRELATION_OBJECT);
        return VenueAccountUpdateRequest.newBuilder()
            .setMessageId(DEFAULT_MESSAGE_ID)
            .setVenueAccountId(venueAccountId)
            .setMetadata(metadataBuilder)
            .setConnectorDetails(defaultConnectorDetails());
    }

    private Grant.Builder defaultGrant(String scope) {
        return Grant.newBuilder()
            .setScope(randomValue(scope))
            .addGroups(randomValue("group-1"))
            .addGroups(randomValue("group-2"))
            .addGroups(randomValue("group-3"));
    }

    private ConnectorDetails.Builder defaultConnectorDetails() {
        return ConnectorDetails.newBuilder()
            .addParameters(KeyValue.newBuilder().setKey(randomValue("key-1")).setValue(randomValue("value")))
            .addParameters(KeyValue.newBuilder().setKey(randomValue("key-2")).setValue(randomValue("value")))
            .addParameters(KeyValue.newBuilder().setKey(randomValue("key-3")).setValue(randomValue("value")));
    }

    private void createVenue(AccountType accountType, String venueName, @Nullable VenueType venueType) {
        if (venueType != null) {
            Venue venue = venueService.create(venueName, venueType);
            venues.put(accountType, Optional.of(venue));
        } else {
            venues.put(accountType, Optional.empty());
        }
    }

    @SuppressWarnings("unchecked")
    private void resetMocks() {
        reset(venueAccountChangeEventExchange);
        reset(connectorRequestExchange);
        reset(eventLogExchange);
    }

    // TODO: General utility
    @SuppressWarnings({"unchecked", "SameParameterValue"})
    private static <T> T genericMock(Class<? super T> clazz) {
        return (T) mock(clazz);
    }

    private static String randomValue(String prefix) {
        return prefix + "-" + RandomStringUtils.randomAlphanumeric(4).toLowerCase();
    }
}
