package io.wyden.referencedata.service.portfolio;

import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.published.common.Metadata;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.published.referencedata.PortfolioChangeCommandType;
import io.wyden.published.referencedata.PortfolioChangeEvent;
import io.wyden.published.referencedata.PortfolioChangeEventType;
import io.wyden.published.referencedata.PortfolioChangeRequest;
import io.wyden.published.referencedata.PortfolioToModify;
import io.wyden.published.referencedata.PortfolioType;
import io.wyden.referencedata.service.EventLogEmitter;
import io.wyden.referencedata.validator.PortfolioChangeRequestValidator;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PortfolioChangeRequestConsumerTest {

    private static final String OWNER_CLIENT_ID = "ownerClientId";
    private static final String PORTFOLIO_NAME = "name";
    private static final String PORTFOLIO_CURRENCY = "USD";
    private static final String PORTFOLIO_ID = "id";
    private static final String UUID_REGEX = "^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$";
    private final RabbitIntegrator rabbitIntegrator = mock(RabbitIntegrator.class);
    private final PortfolioRepository portfolioRepository = mock(PortfolioRepository.class);
    private final PortfolioChangeRequestValidator portfolioChangeRequestValidator = mock(PortfolioChangeRequestValidator.class);
    private final RabbitExchange<PortfolioChangeRequest> portfolioChangeRequestExchange = mock(RabbitExchange.class);
    private final PortfolioChangeEventEmitter portfolioChangeEventEmitter = mock(PortfolioChangeEventEmitter.class);
    private final EventLogEmitter eventLogEmitter = mock(EventLogEmitter.class);
    private final MatchingEngineUidGenerator matchingEngineUidGenerator = mock(MatchingEngineUidGenerator.class);

    private final PortfolioChangeRequestConsumer portfolioChangeRequestConsumer = new PortfolioChangeRequestConsumer(rabbitIntegrator, portfolioRepository, portfolioChangeRequestValidator, portfolioChangeRequestExchange, portfolioChangeEventEmitter, eventLogEmitter, matchingEngineUidGenerator, "queueName", "consumerName");

    @Captor
    protected ArgumentCaptor<Portfolio> portfolioCaptor;
    @Captor
    protected ArgumentCaptor<PortfolioChangeEvent> portfolioChangeEventCaptor;

    @Test
    public void givenPortfolioToModifyWithId_whenModify_thenCreatePortfolioWithId() {
        // given
        PortfolioChangeRequest portfolioChangeRequest = PortfolioChangeRequest.newBuilder()
            .setPortfolioChangeCommandType(PortfolioChangeCommandType.PORTFOLIO_CHANGE_COMMAND_TYPE_CREATE)
            .setOwnerClientId(OWNER_CLIENT_ID)
            .setMetadata(Metadata.newBuilder().build())
            .setPortfolioToModify(getPortfolioToModifyWithId())
            .build();

        // when
        Optional<Portfolio> result = portfolioChangeRequestConsumer.modify(portfolioChangeRequest);

        // then
        assertThat(result).isPresent();
        verify(portfolioRepository).saveIfAbsent(portfolioCaptor.capture());
        Portfolio portfolio = portfolioCaptor.getValue();
        assertThat(portfolio.getId()).isEqualTo(PORTFOLIO_ID);
        assertThat(portfolio.getName()).isEqualTo(PORTFOLIO_NAME);
        assertThat(portfolio.getPortfolioType()).isEqualTo(PortfolioType.VOSTRO);
        assertThat(portfolio.getPortfolioCurrency()).isEqualTo(PORTFOLIO_CURRENCY);
        assertThat(portfolio.getArchivedAt()).isNotBlank();
    }

    @Test
    public void givenPortfolioToModifyNoId_whenModify_thenCreatePortfolioWithUuid() {
        // given
        PortfolioChangeRequest portfolioChangeRequest = PortfolioChangeRequest.newBuilder()
            .setPortfolioChangeCommandType(PortfolioChangeCommandType.PORTFOLIO_CHANGE_COMMAND_TYPE_CREATE)
            .setOwnerClientId(OWNER_CLIENT_ID)
            .setMetadata(Metadata.newBuilder().build())
            .setPortfolioToModify(getPortfolioToModifyNoId())
            .build();

        // when
        Optional<Portfolio> result = portfolioChangeRequestConsumer.modify(portfolioChangeRequest);

        // then
        assertThat(result).isPresent();
        verify(portfolioRepository).saveIfAbsent(portfolioCaptor.capture());
        Portfolio portfolio = portfolioCaptor.getValue();
        assertThat(portfolio.getId()).matches(UUID_REGEX);
        assertThat(portfolio.getName()).isEqualTo(PORTFOLIO_NAME);
        assertThat(portfolio.getPortfolioType()).isEqualTo(PortfolioType.VOSTRO);
        assertThat(portfolio.getPortfolioCurrency()).isEqualTo(PORTFOLIO_CURRENCY);
    }

    @Test
    public void givenPortfolioToModify_whenModify_thenPortfolioAlreadyExists() {
        // given
        PortfolioChangeRequest portfolioChangeRequest = PortfolioChangeRequest.newBuilder()
            .setPortfolioChangeCommandType(PortfolioChangeCommandType.PORTFOLIO_CHANGE_COMMAND_TYPE_CREATE)
            .setOwnerClientId(OWNER_CLIENT_ID)
            .setMetadata(Metadata.newBuilder().build())
            .setPortfolioToModify(getPortfolioToModifyWithId())
            .build();

        when(portfolioRepository.saveIfAbsent(any())).thenReturn(Portfolio.newBuilder().setName("already_exists").build());

        // when
        Optional<Portfolio> result = portfolioChangeRequestConsumer.modify(portfolioChangeRequest);

        // then
        assertThat(result).isEmpty();
        verify(eventLogEmitter).emit(any(), any(), any(), any(), any(), any());
        verify(portfolioChangeEventEmitter).emit(portfolioChangeEventCaptor.capture());
        PortfolioChangeEvent portfolioChangeEvent = portfolioChangeEventCaptor.getValue();
        assertThat(portfolioChangeEvent.getPortfolioChangeEventType()).isEqualTo(PortfolioChangeEventType.PORTFOLIO_CHANGE_EVENT_TYPE_CREATION_FAILED);
    }

    @Test
    public void givenPortfolioToModify_whenModify_thenUpdatePortfolio() {
        // given
        PortfolioChangeRequest portfolioChangeRequest = PortfolioChangeRequest.newBuilder()
            .setPortfolioChangeCommandType(PortfolioChangeCommandType.PORTFOLIO_CHANGE_COMMAND_TYPE_UPDATE)
            .setOwnerClientId(OWNER_CLIENT_ID)
            .setMetadata(Metadata.newBuilder().build())
            .setPortfolioToModify(getPortfolioToModifyWithId())
            .build();

        when(portfolioRepository.find(PORTFOLIO_ID)).thenReturn(Portfolio.newBuilder()
            .setId(PORTFOLIO_ID)
            .setPortfolioType(PortfolioType.NOSTRO)
            .build());

        // when
        Optional<Portfolio> result = portfolioChangeRequestConsumer.modify(portfolioChangeRequest);

        // then
        assertThat(result).isPresent();
        verify(portfolioRepository).save(result.get());
        verify(portfolioChangeEventEmitter).emit(portfolioChangeEventCaptor.capture());
        PortfolioChangeEvent portfolioChangeEvent = portfolioChangeEventCaptor.getValue();
        assertThat(portfolioChangeEvent.getPortfolioChangeEventType()).isEqualTo(PortfolioChangeEventType.PORTFOLIO_CHANGE_EVENT_TYPE_UPDATED);
    }

    private static @NotNull PortfolioToModify getPortfolioToModifyNoId() {
        return PortfolioToModify.newBuilder()
            .setName(PORTFOLIO_NAME)
            .setPortfolioCurrency(PORTFOLIO_CURRENCY)
            .setPortfolioType(PortfolioType.VOSTRO)
            .build();
    }

    private static @NotNull PortfolioToModify getPortfolioToModifyWithId() {
        return getPortfolioToModifyNoId().toBuilder()
            .setId(PORTFOLIO_ID)
            .setArchived(true)
            .build();
    }
}