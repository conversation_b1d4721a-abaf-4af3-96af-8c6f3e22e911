package io.wyden.referencedata.service.venueaccount;

import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.audit.EventLogStatus;
import io.wyden.published.common.Metadata;
import io.wyden.published.referencedata.AccountType;
import io.wyden.published.referencedata.ConnectorDetails;
import io.wyden.published.referencedata.Venue;
import io.wyden.published.referencedata.VenueAccount;
import io.wyden.published.referencedata.VenueAccountChangeEvent;
import io.wyden.published.referencedata.VenueAccountChangeEventType;
import io.wyden.published.referencedata.VenueAccountCreateRequest;
import io.wyden.published.referencedata.VenueAccountOrBuilder;
import io.wyden.published.referencedata.VenueAccountUpdateRequest;
import io.wyden.published.referencedata.VenueType;
import io.wyden.referencedata.exception.FailureNeedsRetryException;
import io.wyden.referencedata.service.EventLogEmitter;
import io.wyden.referencedata.service.venue.VenueService;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.Optional;
import java.util.UUID;
import java.util.regex.Pattern;

import static io.wyden.referencedata.service.venue.VenueService.toVenueType;
import static org.apache.commons.lang3.StringUtils.EMPTY;

@Service
public class VenueAccountService {

    private static final Logger LOGGER = LoggerFactory.getLogger(VenueAccountService.class);

    private static final int DEFAULT_VENUE_ACCOUNT_ID_LENGTH = 6;
    private static final int MAX_VENUE_ACCOUNT_ID_LENGTH = 32;
    private static final Pattern VENUE_ACCOUNT_ID_PATTERN = Pattern.compile("[a-z0-9]([-a-z0-9]*[a-z0-9])?(\\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*");
    private static final String VENUE_ACCOUNT_ID_EXCLUDED = "[^a-z0-9]+";
    private static final String VOVELS = "[aeiou]+";
    private static final String VENUE_ACCOUNT_ID_SEARCH = "bdszlgt043";
    private static final String VENUE_ACCOUNT_ID_REPLACE = "8052164OAE";

    private final VenueService venueService;
    private final VenueAccountRepository venueAccountRepository;
    private final VenueAccountChangeEventEmitter venueAccountChangeEventEmitter;
    private final EventLogEmitter eventLogEmitter;
    private final ConnectorRequestEmitter connectorRequestEmitter;

    public VenueAccountService(VenueAccountRepository venueAccountRepository,
                               VenueService venueService,
                               VenueAccountChangeEventEmitter venueAccountChangeEventEmitter,
                               EventLogEmitter eventLogEmitter,
                               ConnectorRequestEmitter connectorRequestEmitter) {
        this.venueService = venueService;
        this.venueAccountRepository = venueAccountRepository;
        this.venueAccountChangeEventEmitter = venueAccountChangeEventEmitter;
        this.eventLogEmitter = eventLogEmitter;
        this.connectorRequestEmitter = connectorRequestEmitter;
    }

    public boolean exists(String venueAccountId) {
        return venueAccountRepository.exists(venueAccountId);
    }

    public Optional<VenueAccount> find(String venueAccountId) {
        return venueAccountRepository.find(venueAccountId);
    }

    public Collection<VenueAccount> findAll() {
        return venueAccountRepository.findAll();
    }

    public Collection<VenueAccount> findByName(String venueAccountName) {
        return venueAccountRepository.findByName(venueAccountName);
    }

    public Collection<VenueAccount> findAllByName(Collection<String> venueAccountNames) {
        return venueAccountRepository.findAllByName(venueAccountNames);
    }

    public VenueAccount createFromRequest(VenueAccountCreateRequest request) {
        String venueAccountName = request.getVenueAccountName();
        LOGGER.info("Creating venue account: {}", venueAccountName);
        try {
            VenueAccount.Builder venueAccountBuilder = toVenueAccount(request);
            String venueAccountId = StringUtils.firstNonEmpty(request.getVenueAccountId(), generateUniqueVenueAccountId(venueAccountName));
            venueAccountBuilder.setId(venueAccountId);

            validateNewVenueAccount(venueAccountBuilder);

            VenueAccount venueAccount = venueAccountBuilder.build();
            if (venueAccountRepository.putIfAbsent(venueAccount).isPresent()) {
                throw new IllegalStateException("VenueAccount already exists");
            }

            emitVenueAccountChangeEvent(request, venueAccount);
            emitConnectorRequest(venueAccount, request.getConnectorDetails());

            LOGGER.info("Successfully created venue account: {}", debugString(venueAccount));
            emitEventLog(request, EventLogStatus.SUCCESS, null);

            return venueAccount;
        } catch (FailureNeedsRetryException ex) {
            throw ex;
        } catch (Exception ex) {
            emitEventLog(request, EventLogStatus.FAILURE, ex.getMessage());
            throw ex;
        }
    }

    public VenueAccount updateFromRequest(VenueAccountUpdateRequest request) {
        LOGGER.info("Updating venue account: {}({})", request.getVenueAccountName(), request.getVenueAccountId());
        try {
            VenueAccount.Builder venueAccountBuilder = toVenueAccount(request);

            String venueAccountId = venueAccountBuilder.getId();
            VenueAccount.Builder updatedBuilder = venueAccountRepository.find(venueAccountId)
                .orElseThrow(() -> new IllegalArgumentException("VenueAccount with id %s not found".formatted(venueAccountId)))
                .toBuilder()
                .mergeFrom(venueAccountBuilder.build())
                .setArchivedAt(request.getArchived() ? DateUtils.toIsoUtcTime(ZonedDateTime.now()) : "");

            validateUpdatedVenueAccount(updatedBuilder);

            VenueAccount updated = updatedBuilder.build();
            venueAccountRepository.replace(updated)
                .orElseThrow(() -> new IllegalStateException("VenueAccount does not exist"));

            emitVenueAccountChangeEvent(request, updated);
            emitConnectorRequest(updated, request.getConnectorDetails());

            LOGGER.info("Successfully updated venue account: {}", debugString(updated));
            emitEventLog(request, EventLogStatus.SUCCESS, null);
            return updated;
        } catch (FailureNeedsRetryException ex) {
            throw ex;
        } catch (Exception ex) {
            emitEventLog(request, EventLogStatus.FAILURE, ex.getMessage());
            throw ex;
        }
    }

    public void delete(String venueAccountId) {
        venueAccountRepository.find(venueAccountId)
            .ifPresentOrElse(venueAccount -> {
                LOGGER.info("Deleting venue account: {}", debugString(venueAccount));
                venueAccountRepository.delete(venueAccountId);
            }, () -> LOGGER.warn("Failed to delete venue account {}: Not found", venueAccountId));
    }

    public void deleteByName(String venueAccountName) {
        LOGGER.info("Deleting venue accounts with name: {}", venueAccountName);
        venueAccountRepository.findByName(venueAccountName)
            .forEach(venueAccount -> {
                LOGGER.info("  Deleting venue account: {}", debugString(venueAccount));
                venueAccountRepository.delete(venueAccount.getId());
            });
    }

    public void activate(String venueAccountId) {
        VenueAccount venueAccount = venueAccountRepository.find(venueAccountId)
            .orElseThrow(() -> new IllegalArgumentException("Venue account " + venueAccountId + " not found."));

        LOGGER.info("Activating venue account: {}", debugString(venueAccount));

        VenueAccount updated = venueAccount.toBuilder()
            .setSuspendedAt(EMPTY)
            .build();

        venueAccountRepository.set(updated);
    }

    public void deactivate(String venueAccountId) {
        VenueAccount venueAccount = venueAccountRepository.find(venueAccountId)
            .orElseThrow(() -> new IllegalArgumentException("Venue account " + venueAccountId + " not found."));

        LOGGER.info("Deactivating venue account: {}", debugString(venueAccount));

        VenueAccount updated = venueAccount.toBuilder()
            .setSuspendedAt(DateUtils.toIsoUtcTime(ZonedDateTime.now()))
            .build();

        venueAccountRepository.set(updated);
    }

    private void emitEventLog(VenueAccountCreateRequest request, EventLogStatus status, @Nullable String description) {
        try {
            if (StringUtils.isBlank(request.getMessageId())) return;
            String message = switch (status) {
                case SUCCESS -> "Successfully created venue account %s";
                case FAILURE -> "Failed to create venue account %s: %s";
                case ORDER_STATUS_UNSPECIFIED, UNRECOGNIZED -> "Internal error when creating venue account %s";
            };
            eventLogEmitter.emit(
                request.getOwnerUsername(),
                DateUtils.toIsoUtcTime(ZonedDateTime.now()),
                message.formatted(request.getVenueAccountName(), ObjectUtils.firstNonNull(description, EMPTY)),
                status,
                EventLogEmitter.ACCOUNT_CREATE_REQUEST_STATUS,
                request.getMetadata().getCorrelationObject()
            );
        } catch (Exception ex) {
            LOGGER.warn("Failed to emit EventLog", ex);
        }
    }

    private void emitEventLog(VenueAccountUpdateRequest request, EventLogStatus status, @Nullable String description) {
        try {
            if (StringUtils.isBlank(request.getMessageId())) return;
            String message = switch (status) {
                case SUCCESS -> "Successfully created venue account %s";
                case FAILURE -> "Failed to create venue account %s: %s";
                case ORDER_STATUS_UNSPECIFIED, UNRECOGNIZED -> "Internal error when creating venue account %s";
            };
            eventLogEmitter.emit(
                request.getOwnerUsername(),
                DateUtils.toIsoUtcTime(ZonedDateTime.now()),
                message.formatted(request.getVenueAccountName(), ObjectUtils.firstNonNull(description, EMPTY)),
                status,
                EventLogEmitter.ACCOUNT_UPDATE_REQUEST_STATUS,
                request.getMetadata().getCorrelationObject()
            );
        } catch (Exception ex) {
            LOGGER.warn("Failed to emit EventLog", ex);
        }
    }

    private void emitVenueAccountChangeEvent(VenueAccountCreateRequest request, VenueAccount venueAccount) {
        try {
            String messageId = StringUtils.firstNonBlank(request.getMessageId(), UUID.randomUUID().toString());
            VenueAccountChangeEvent venueAccountChangeEvent = VenueAccountChangeEvent.newBuilder()
                .setMetadata(Metadata.newBuilder()
                    .setInResponseToRequestId(messageId)
                    .build())
                .setVenueAccountChangeEventType(VenueAccountChangeEventType.VENUE_ACCOUNT_CHANGE_EVENT_TYPE_CREATED)
                .setVenueAccount(venueAccount)
                .addAllGrants(request.getGrantsList())
                .build();

            venueAccountChangeEventEmitter.emit(venueAccountChangeEvent);
        } catch (Exception ex) {
            LOGGER.warn("Failed to emit VenueAccountChangeEvent", ex);
        }
    }

    private void emitVenueAccountChangeEvent(VenueAccountUpdateRequest request, VenueAccount venueAccount) {
        try {
            VenueAccountChangeEvent venueAccountChangeEvent = VenueAccountChangeEvent.newBuilder()
                .setMetadata(Metadata.newBuilder()
                    .setInResponseToRequestId(request.getMessageId())
                    .build())
                .setVenueAccountChangeEventType(VenueAccountChangeEventType.VENUE_ACCOUNT_CHANGE_EVENT_TYPE_UPDATED)
                .setVenueAccount(venueAccount)
                .build();

            venueAccountChangeEventEmitter.emit(venueAccountChangeEvent);
        } catch (Exception ex) {
            LOGGER.warn("Failed to emit VenueAccountChangeEvent", ex);
        }
    }

    private void emitConnectorRequest(VenueAccount venueAccount, ConnectorDetails connectorDetails) {
        try {
            if (isExchangeAccount(venueAccount.getAccountType())) {
                connectorRequestEmitter.emit(venueAccount, connectorDetails);
            } else {
                LOGGER.debug("Connector creation skipped for account: {}, account type: {}", debugString(venueAccount), venueAccount.getAccountType().name());
            }
        } catch (Exception ex) {
            LOGGER.warn("Failed to emit ConnectorRequest", ex);
        }
    }

    private String generateUniqueVenueAccountId(String venueAccountName) {
        String venueAccountId = venueAccountName.toLowerCase()
            .replaceAll(VENUE_ACCOUNT_ID_EXCLUDED, EMPTY)
            .replaceAll(VOVELS, EMPTY);
        venueAccountId = StringUtils.replaceChars(venueAccountId, VENUE_ACCOUNT_ID_SEARCH, VENUE_ACCOUNT_ID_REPLACE);
        venueAccountId = venueAccountId + RandomStringUtils.randomAlphanumeric(DEFAULT_VENUE_ACCOUNT_ID_LENGTH);
        venueAccountId = venueAccountId.charAt(0) < 'a' ? "x" + venueAccountId : venueAccountId;
        venueAccountId = StringUtils.truncate(venueAccountId, DEFAULT_VENUE_ACCOUNT_ID_LENGTH);
        venueAccountId = venueAccountId.toLowerCase();
        int suffix = 0;
        String result = venueAccountId + hexOrEmpty(suffix);
        while (venueAccountRepository.exists(result)) {
            suffix++;
            result = venueAccountId + hexOrEmpty(suffix);
        }
        return result;
    }

    private String hexOrEmpty(int i) {
        if (i == 0) {
            return EMPTY;
        } else {
            return Integer.toHexString(i).toLowerCase();
        }
    }

    private VenueAccount.Builder toVenueAccount(VenueAccountCreateRequest request) {
        return VenueAccount.newBuilder()
            .setVenueAccountName(request.getVenueAccountName())
            .setVenueName(request.getVenueName())
            .setOwnerUsername(request.getOwnerUsername())
            .setAccountType(request.getAccountType())
            .setWalletType(request.getWalletType())
            .setCreatedAt(DateUtils.toIsoUtcTime(ZonedDateTime.now()));
    }

    private VenueAccount.Builder toVenueAccount(VenueAccountUpdateRequest request) {
        return VenueAccount.newBuilder()
            .setId(request.getVenueAccountId())
            .setVenueAccountName(request.getVenueAccountName())
            .setVenueName(request.getVenueName())
            .setOwnerUsername(request.getOwnerUsername())
            .setAccountType(request.getAccountType());
    }

    private void validateNewVenueAccount(VenueAccount.Builder venueAccount) {
        validateAccountType(venueAccount);

        validateAccountId(venueAccount);
        if (find(venueAccount.getId()).isPresent()) {
            throw new IllegalArgumentException("VenueAccount with id %s already exists".formatted(venueAccount.getId()));
        }

        validateOwnerUsername(venueAccount);
        validateAccountName(venueAccount);
        validateVenueName(venueAccount);
    }

    private void validateUpdatedVenueAccount(VenueAccount.Builder venueAccount) {
        validateAccountType(venueAccount);

        if (find(venueAccount.getId()).isEmpty()) {
            throw new IllegalArgumentException("VenueAccount with id %s doesn't exist".formatted(venueAccount.getId()));
        }

        validateOwnerUsername(venueAccount);
        validateAccountName(venueAccount);
        validateVenueName(venueAccount);
    }

    private void validateAccountType(VenueAccount.Builder venueAccount) {
        AccountType accountType = venueAccount.getAccountType();
        if (accountType == AccountType.UNRECOGNIZED) {
            throw new IllegalArgumentException("Unrecognized account type: %d".formatted(accountType.getNumber()));
        }
    }

    private void validateAccountId(VenueAccount.Builder venueAccount) {
        String venueAccountId = venueAccount.getId();
        if (StringUtils.isBlank(venueAccountId)) {
            throw new IllegalArgumentException("VenueAccount id is blank");
        }
        if (StringUtils.length(venueAccountId) > MAX_VENUE_ACCOUNT_ID_LENGTH) {
            throw new IllegalArgumentException("VenueAccount id %s is longer than %d characters".formatted(venueAccountId, MAX_VENUE_ACCOUNT_ID_LENGTH));
        }
        if (!VENUE_ACCOUNT_ID_PATTERN.matcher(venueAccountId).matches()) {
            throw new IllegalArgumentException("VenueAccount id %s doesn't match pattern".formatted(venueAccountId));
        }
    }

    private void validateOwnerUsername(VenueAccount.Builder venueAccount) {
        String username = venueAccount.getOwnerUsername();
        if (StringUtils.isBlank(username)) {
            throw new IllegalArgumentException("VenueAccount owner username is blank");
        }
    }

    private void validateAccountName(VenueAccount.Builder venueAccount) {
        String venueAccountName = venueAccount.getVenueAccountName();
        if (StringUtils.isBlank(venueAccountName)) {
            throw new IllegalArgumentException("VenueAccount name is blank");
        }
        Collection<VenueAccount> sameNameVenueAccountList = findByName(venueAccountName);
        for (VenueAccount sameNameVenueAccount : sameNameVenueAccountList) {
            if (!StringUtils.equals(sameNameVenueAccount.getId(), venueAccount.getId())) {
                throw new IllegalArgumentException("VenueAccount with name %s already exists".formatted(venueAccountName));
            }
        }
    }

    private void validateVenueName(VenueAccount.Builder venueAccount) {
        AccountType accountType = venueAccount.getAccountType();
        String venueName = venueAccount.getVenueName();
        if (hasVenue(accountType)) {
            if (venueName.isBlank()) {
                throw new IllegalArgumentException("Venue name is blank");
            }
            Optional<Venue> venue = venueService.find(venueName);
            if (venue.isEmpty()) {
                throw new IllegalArgumentException("Venue %s not found".formatted(venueName));
            }
            VenueType venueType = venue.get().getVenueType();
            if (toVenueType(accountType) != venueType) {
                throw new IllegalArgumentException("Account type %s is incorrect for venue type %s".formatted(accountType, venueType));
            }
        } else {
            if (StringUtils.isNotBlank(venueName)) {
                throw new IllegalArgumentException("Venue name set for non-venue account: %s".formatted(venueName));
            }
        }
    }

    private static boolean isExchangeAccount(AccountType accountType) {
        return switch (accountType) {
            case ACCOUNT_TYPE_UNSPECIFIED, EXCHANGE -> true;
            case UNRECOGNIZED, WALLET, CUSTODY, ACCOUNT_TYPE_CLOB -> false;
        };
    }

    private static boolean hasVenue(AccountType accountType) {
        return switch (accountType) {
            case ACCOUNT_TYPE_UNSPECIFIED, EXCHANGE, ACCOUNT_TYPE_CLOB -> true;
            case WALLET, CUSTODY, UNRECOGNIZED -> false;
        };
    }

    public static String debugString(VenueAccountOrBuilder venueAccount) {
        return "%s(%s)".formatted(venueAccount.getVenueAccountName(), venueAccount.getId());
    }
}
