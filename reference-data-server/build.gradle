plugins {
    id 'java'
    alias dependencyCatalog.plugins.spring.boot
    alias dependencyCatalog.plugins.dependency.management
    alias dependencyCatalog.plugins.sonarqube
    alias dependencyCatalog.plugins.jacocoToCobertura
}

dependencies {
    implementation project(':reference-data-domain')
    implementation project(':reference-data-client')
     implementation dependencyCatalog.spring.retry
    implementation dependencyCatalog.cloud.utils.rabbitmq
    implementation dependencyCatalog.cloud.utils.telemetry
    implementation dependencyCatalog.cloud.utils.tools
    implementation dependencyCatalog.cloud.utils.hazelcast
    implementation dependencyCatalog.cloud.utils.spring
    implementation dependencyCatalog.published.language.oems
    implementation dependencyCatalog.cloud.utils.rabbitmq.destinations
    implementation dependencyCatalog.hazelcast
    implementation dependencyCatalog.hazelcast.jet.protobuf
    implementation dependencyCatalog.spring.boot.starter.webflux
    implementation dependencyCatalog.spring.boot.starter.actuator
    implementation dependencyCatalog.spring.boot.configuration.processor

    testImplementation dependencyCatalog.cloud.utils.test
    testImplementation dependencyCatalog.spring.boot.starter.test
    testImplementation dependencyCatalog.reactor.test
    testImplementation(dependencyCatalog.hazelcast) { artifact { classifier = 'tests'} }
}

testing {
    suites {
        test {
            useJUnitJupiter()
        }

        integrationTest(JvmTestSuite) {
            dependencies {
                implementation project
                implementation project(':reference-data-domain')
                implementation project(':reference-data-client')
                implementation dependencyCatalog.cloud.utils.test
                implementation dependencyCatalog.cloud.utils.rabbitmq
                implementation dependencyCatalog.published.language.oems
                implementation dependencyCatalog.cloud.utils.rabbitmq.destinations
                implementation dependencyCatalog.testcontainers
                implementation dependencyCatalog.testcontainers.junit.jupiter
                implementation dependencyCatalog.testcontainers.rabbitmq
                implementation(dependencyCatalog.hazelcast)
                implementation(dependencyCatalog.hazelcast) { artifact { classifier = 'tests'} }
                implementation dependencyCatalog.spring.boot.starter.test
            }

            targets {
                all {
                    testTask.configure {
                        shouldRunAfter(test)
                    }
                }
            }
        }
    }
}

tasks.named('check') {
    dependsOn(testing.suites.integrationTest)
}

test {
    finalizedBy jacocoTestReport
    testLogging.showStandardStreams = true
}

jacocoTestReport {
    reports {
        xml.enabled true
        csv.enabled true
    }

    getExecutionData().setFrom(fileTree(buildDir).include("/jacoco/*.exec"))
}

jacocoToCobertura {
    inputFile.set(file("$buildDir/reports/jacoco/test/jacocoTestReport.xml"))
    outputFile.set(file("$buildDir/reports/jacoco/test/cobertura.xml"))
}

plugins.withType(JacocoPlugin) {
    tasks["test"].finalizedBy 'jacocoTestReport'
    tasks["integrationTest"].finalizedBy 'jacocoTestReport'
    tasks["jacocoTestReport"].finalizedBy 'jacocoToCobertura'
    tasks["jacocoToCobertura"].dependsOn 'jacocoTestReport'
}

bootJar {
    manifest {
        attributes(
                "Implementation-Version": "${archiveVersion}"
        )
    }
}

bootRun {
    args = ["--tracing.collector.endpoint=http://localhost:4317"]
    jvmArgs = ["-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:9098"]
    environment([
            "FLUENTD_HOST": "localhost",
            "SPRING_PROFILES_ACTIVE": "dev",
            "INITIAL_CONFIG_INSTRUMENT_PREFILL": "true",
            "INITIAL_CONFIG_PORTFOLIO_PREFILL": "true",
            "INITIAL_CONFIG_VENUEACCOUNT_PREFILL": "true"
    ])
}

sonarqube {
    properties {
        property "sonar.projectKey", "reference-data"
        property "sonar.projectName", "Reference Data"
    }
}
