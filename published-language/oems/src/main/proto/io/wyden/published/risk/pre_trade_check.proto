syntax = "proto3";

option java_multiple_files = true;

package io.wyden.published.risk;

import "io/wyden/published/common/metadata.proto";
import "io/wyden/published/risk/request_channel.proto";

message PreTradeCheck {
  io.wyden.published.common.Metadata metadata = 1;
  string id = 2;
  string type = 3;
  bool enabled = 4;
  repeated string portfolios = 5;
  map<string, PreTradeCheckPropertyValue> properties = 6;
  map<string, string> portfolio_tags = 7;
  repeated io.wyden.published.risk.RequestChannel request_channels = 8;
  PreTradeCheckLevel level = 9;
}

enum PreTradeCheckLevel {
  PRE_TRADE_CHECK_LEVEL_UNSPECIFIED = 0;
  WARN = 1;
  BLOCK = 2;
}

message PreTradeChecksList {
  io.wyden.published.common.Metadata metadata = 1;
  repeated PreTradeCheck items = 2;
}

enum PreTradeCheckPropertyType {
  PTC_PROPERTY_TYPE_UNSPECIFIED = 0;
  STRING = 1;
  DECIMAL = 2;
  STRING_ARRAY = 3;
}

message PreTradeCheckPropertyValue {
  message StringArray {
    repeated string string_value = 1;
  }

  oneof value {
    string string_value = 1;
    string decimal_value = 2;
    StringArray string_array = 3;
  }
}

message PreTradeCheckSchemaList {
  io.wyden.published.common.Metadata metadata = 1;
  repeated PreTradeCheckSchema items = 2;
}

message PreTradeCheckSchema {
  io.wyden.published.common.Metadata metadata = 1;
  string type = 2;
  map<string, PreTradeCheckPropertySchema> properties = 3;
}

enum PreTradeCheckPropertyFormat {
  FORMAT_UNSPECIFIED = 0;
  CURRENCY_PAIR = 1;
}


message PreTradeCheckPropertySchema {
  PreTradeCheckPropertyType type = 1;
  bool required = 2;
  repeated string enum_values = 3;
  PreTradeCheckPropertyFormat format = 4;
}

message PreTradeCheckChangedEvent {
  io.wyden.published.common.Metadata metadata = 1;
  string id = 2;
  Type type = 3;

  enum Type {
    TYPE_UNSPECIFIED = 0;
    ADD = 1;
    REMOVE = 2;
  }
}