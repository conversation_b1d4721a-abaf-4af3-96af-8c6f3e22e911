syntax = "proto3";

option java_multiple_files = true;

package io.wyden.published.booking;

import "io/wyden/published/common/metadata.proto";
import "io/wyden/published/common/sorting.proto";

message PositionSnapshot {
  io.wyden.published.common.Metadata metadata = 1;

  string quantity = 2;
  string booking_currency = 3;
  string net_realized_pnl = 4;
  string gross_realized_pnl = 5;
  string net_cost = 6;
  string gross_cost = 7;
  string symbol = 9; // currency symbol BTC (CashPosition) or asset symbol BTCEUR (AssetPosition)
  string currency = 10; // BTC (CashPosition) or EUR (AssetPosition)
  string portfolio = 12; // portfolioId, BANK_Portfolio
  string account = 13; // accountId, bitmex-testnet1

  string notional_quantity = 14;
  string market_value = 15;
  string market_value_sc = 16;
  string net_realized_pnl_sc = 17; // sc - system currency
  string gross_realized_pnl_sc = 18;
  string net_cost_sc = 19;
  string gross_cost_sc = 20;
  string net_average_price = 21;
  string gross_average_price = 22;
  string net_unrealized_pnl = 23;
  string gross_unrealized_pnl = 24;
  string net_unrealized_pnl_sc = 25;
  string gross_unrealized_pnl_sc = 26;
  string pending_quantity = 27;
  string available_for_trading_quantity = 28;
  string available_for_withdrawal_quantity = 29;
  string settled_quantity = 30;
  string unsettled_quantity = 31;
  string last_applied_ledger_entry_id = 32;
}

message PositionSearch {
  repeated string symbol = 1;
  repeated string currency = 2;
  repeated string account_id = 3;
  repeated string portfolio_id = 4;
  string client_id = 5;
  int32 first = 6;
  string after = 7;
  io.wyden.published.common.SortingOrder sorting_order = 8;
  string order_id = 9;
}
