syntax = "proto3";

option java_multiple_files = true;

package io.wyden.published.referencedata;

import "io/wyden/published/common/metadata.proto";
import "io/wyden/published/common/key_value.proto";
import "io/wyden/published/referencedata/venue_account_create_request.proto";
import "io/wyden/published/referencedata/venue_account.proto";

message VenueAccountUpdateRequest {
  string message_id = 1;
  string venue_account_id = 2;
  string venue_account_name = 6;
  string venue_name = 7;
  string owner_username = 9;
  io.wyden.published.common.Metadata metadata = 8;
  reserved 3;
  ConnectorDetails connector_details = 4;
  AccountType account_type= 5;
  bool archived = 10;
}
