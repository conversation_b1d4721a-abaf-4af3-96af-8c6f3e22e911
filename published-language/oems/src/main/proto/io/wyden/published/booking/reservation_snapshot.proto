syntax = "proto3";

option java_multiple_files = true;

package io.wyden.published.booking;

import "io/wyden/published/booking/fee.proto";
import "io/wyden/published/booking/transaction_snapshot.proto";
import "io/wyden/published/booking/reservation_report_with_request.proto";
import "io/wyden/published/common/metadata.proto";
import "io/wyden/published/common/sorting.proto";
import "io/wyden/published/oems/request.proto";
import "io/wyden/published/oems/enums.proto";

message ReservationSearch {
  repeated string symbol = 1;
  repeated string currency = 2;
  repeated string account_id = 3;
  repeated string portfolio_id = 4;
  repeated TransactionType transaction_type = 5;
  string reservation_ref = 6;
  string client_id = 7;
  string from = 8; // unix timestamp
  string to = 9; // unix timestamp
  int32 first = 10;
  string after = 11;
  io.wyden.published.common.SortingOrder sorting_order = 12;
}

message PendingReservation {
  io.wyden.published.common.Metadata metadata = 1;

  TransactionType transaction_type = 2;

  string reservation_ref = 3;
  string quantity = 4;
  string currency = 5; // BTC (CashPosition) or EUR (AssetPosition)
  string portfolio_id = 6; // portfolioId, BANK_Portfolio
  string account_id = 7; // accountId, bitmex-testnet1
}

message ReservationBalance {
  string quantity = 1;
  string currency = 2; // BTC (CashPosition) or EUR (AssetPosition)
  string portfolio_id = 3; // portfolioId, BANK_Portfolio
  string account_id = 4; // accountId, bitmex-testnet1
}

message ReservationBalanceList {
  io.wyden.published.common.Metadata metadata = 1;
  repeated ReservationBalance reservation_balance = 2;
}

// The following message models (ones ending with ...Request) represent requests for manually creating specific resources.
// These requests originate from the REST Management service.

message ReservationRequest {
  oneof request {
    ClientCashTradeReservationRequest client_cash_trade_reservation_request = 1;
    StreetCashTradeReservationRequest street_cash_trade_reservation_request = 2;
    DepositReservationRequest deposit_reservation_request = 3;
    WithdrawalReservationRequest withdrawal_reservation_request = 4;
    AccountCashTransferReservationRequest account_cash_transfer_reservation_request = 5;
    PortfolioCashTransferReservationRequest portfolio_cash_transfer_reservation_request = 6;
    ClientAssetTradeReservationRequest client_asset_trade_reservation_request = 7;
    StreetAssetTradeReservationRequest street_asset_trade_reservation_request = 8;
    PortfolioAssetTransferReservationRequest portfolio_asset_transfer_reservation_request = 9;
    FeeReservationRequest fee_reservation_request = 11;
  }

  io.wyden.published.common.Metadata metadata = 10;
  //  UUID of the new entity assigned on the API gateway level. Optional, to be generated if not set.
  string uuid = 20;
}

message ReservationReleaseRequest {
  string reservation_reference = 1;

  io.wyden.published.common.Metadata metadata = 10;
}

message OemsRequestWithVolatilityBuffer {
  io.wyden.published.common.Metadata metadata = 1;
  io.wyden.published.oems.OemsRequest oems_request = 2;
  string volatility_buffer = 3; // java.math.BigDecimal
  string market_price = 4; // Optional, may be back-filled after received and before persistence. java.math.BigDecimal
}

message DepositReservationRequest {
  // deprecated - use generic ReservationRequest.metadata
  io.wyden.published.common.Metadata metadata = 1 [deprecated = true];

  string reservation_ref = 2;
  TransactionType transaction_type = 3;
  string date_time = 4; // ISO 8601 format, UTC
  string currency = 5;
  string quantity = 6;
  string portfolio_id = 7;
  string account_id = 8;
  string fee_portfolio_id = 10;
  string fee_account_id = 11;
  repeated Fee reservation_fee = 9;
}

message WithdrawalReservationRequest {
  // deprecated - use generic ReservationRequest.metadata
  io.wyden.published.common.Metadata metadata = 1 [deprecated = true];

  string reservation_ref = 2;
  TransactionType transaction_type = 3;
  string date_time = 4; // ISO 8601 format, UTC
  string currency = 5;
  string quantity = 6;
  string portfolio_id = 7;
  string account_id = 8;
  string fee_portfolio_id = 10;
  string fee_account_id = 11;
  repeated Fee reservation_fee = 9;
}

message ClientCashTradeReservationRequest {
  // deprecated - use generic ReservationRequest.metadata
  io.wyden.published.common.Metadata metadata = 1 [deprecated = true];

  string reservation_ref = 2;
  TransactionType transaction_type = 3;
  string date_time = 4; // ISO 8601 format, UTC
  string currency = 5;
  string base_currency = 6;
  string quantity = 7;
  string price = 8;
  string stop_price = 11;
  string portfolio_id = 9;
  string counter_portfolio_id = 10;
  repeated Fee reservation_fee = 12;
}

message StreetCashTradeReservationRequest {
  // deprecated - use generic ReservationRequest.metadata
  io.wyden.published.common.Metadata metadata = 1 [deprecated = true];

  string reservation_ref = 2;
  TransactionType transaction_type = 3;
  string date_time = 4; // ISO 8601 format, UTC
  string currency = 5;
  string base_currency = 6;
  string quantity = 7;
  string price = 8;
  string stop_price = 11;
  string portfolio_id = 9;
  string account_id = 10;
  repeated Fee reservation_fee = 12;
}

message ClientAssetTradeReservationRequest {
  string reservation_ref = 1;
  TransactionType transaction_type = 2;
  string date_time = 3; // ISO 8601 format, UTC
  string currency = 4;
  string instrument = 5;
  string quantity = 6;
  string price = 7;
  string stop_price = 8;
  string portfolio_id = 9;
  string counter_portfolio_id = 10;
  repeated Fee reservation_fee = 11;
}

message StreetAssetTradeReservationRequest {
  string reservation_ref = 1;
  TransactionType transaction_type = 2;
  string date_time = 3; // ISO 8601 format, UTC
  string currency = 4;
  string instrument = 5;
  string quantity = 6;
  string price = 7;
  string stop_price = 8;
  string portfolio_id = 9;
  string account_id = 10;
  repeated Fee reservation_fee = 11;
}

message AccountCashTransferReservationRequest {
  // deprecated - use generic ReservationRequest.metadata
  io.wyden.published.common.Metadata metadata = 1 [deprecated = true];

  string reservation_ref = 2;
  TransactionType transaction_type = 3;
  string date_time = 4; // ISO 8601 format, UTC
  string currency = 5;
  string quantity = 6;
  string from_account_id = 7;
  string to_account_id = 8;
  string fee_account_id = 9;
  // Portfolio that will collect profits from fees.
  string fee_portfolio_id = 10;
  repeated Fee reservation_fee = 11;
  string int_transfer_id = 12;
  string ext_transfer_id = 13;
}

message PortfolioCashTransferReservationRequest {
  // deprecated - use generic ReservationRequest.metadata
  io.wyden.published.common.Metadata metadata = 1 [deprecated = true];

  string reservation_ref = 2;
  TransactionType transaction_type = 3;
  string date_time = 4; // ISO 8601 format, UTC
  string currency = 5;
  string quantity = 6;
  string from_portfolio_id = 7;
  string to_portfolio_id = 8;
  repeated Fee reservation_fee = 9;
  // Portfolio that will collect profits from fees. Optional, from_portfolio_id will be set if not used
  string fee_portfolio_id = 10;
}

message PortfolioAssetTransferReservationRequest {
  string reservation_ref = 1;
  TransactionType transaction_type = 2;
  string date_time = 3; // ISO 8601 format, UTC
  string instrument = 4;
  string quantity = 5;
  string from_portfolio_id = 6;
  string to_portfolio_id = 7;
  repeated Fee reservation_fee = 8;
  // Portfolio that will collect profits from fees. Optional, from_portfolio_id will be set if not used
  string fee_portfolio_id = 9;
}

message FeeReservationRequest {
  string reservation_ref = 1;
  TransactionType transaction_type = 2;
  string date_time = 3; // ISO 8601 format, UTC
  string currency = 4;
  string quantity = 5;
  string portfolio_id = 6;
  string account_id = 7;
}

// The following message models (ones ending with ...Snapshot) represent the internal domain model used for persisting and processing these objects in the Booking system.

message ReservationSnapshot {
  oneof reservation {
    ClientCashTradeReservationSnapshot client_cash_trade_reservation = 1;
    StreetCashTradeReservationSnapshot street_cash_trade_reservation = 2;
    DepositReservationSnapshot deposit_reservation = 3;
    WithdrawalReservationSnapshot withdrawal_reservation = 4;
    AccountCashTransferReservationSnapshot account_cash_transfer_reservation = 5;
    PortfolioCashTransferReservationSnapshot portfolio_cash_transfer_reservation = 6;
    ClientAssetTradeReservationSnapshot client_asset_trade_reservation = 7;
    StreetAssetTradeReservationSnapshot street_asset_trade_reservation = 8;
    PortfolioAssetTransferReservationSnapshot portfolio_asset_transfer_reservation = 9;
    FeeReservationSnapshot fee_reservation = 10;
  }

  //  UUID of the new entity assigned on the API gateway level. Optional, to be generated if not set.
  string uuid = 19;

  //  request_id of the original Reservation request
  optional string request_id = 20;
  optional io.wyden.published.booking.command.PositionValueCalculationInput position_value_calculation_input = 21;
  optional io.wyden.published.booking.command.PositionQuantityChangeCalculationInput position_quantity_change_calculation_input = 22;
  //  Volatility buffer to be applied on top of this Reservation
  optional string volatility_buffer = 23;
  // Current market price. Required if volatility_buffer is set.
  optional string market_price = 24;
  //  Order type that will be placed for this Reservation. Required if volatility_buffer is set.
  optional io.wyden.published.oems.OemsOrderType order_type = 25;

  // Policies:

  // Buying power policy.
  // If enabled, Reservation will not be created if
  // any of the affected Client Portfolio Positions would be reduced below the buying power threshold = 0.
  // This policy is enabled by default for all OemsRequestWithBuffer commands.
  optional bool has_buying_power_policy = 30;
}

message DepositReservationSnapshot {
  io.wyden.published.common.Metadata metadata = 1;

  string reservation_ref = 2;
  TransactionType transaction_type = 3;
  string date_time = 4; // ISO 8601 format, UTC
  string currency = 5;
  string quantity = 6;
  string portfolio_id = 7;
  string account_id = 8;
  string fee_portfolio_id = 10;
  string fee_account_id = 11;
  repeated Fee reservation_fee = 9;
}

message WithdrawalReservationSnapshot {
  io.wyden.published.common.Metadata metadata = 1;

  string reservation_ref = 2;
  TransactionType transaction_type = 3;
  string date_time = 4; // ISO 8601 format, UTC
  string currency = 5;
  string quantity = 6;
  string portfolio_id = 7;
  string account_id = 8;
  string fee_portfolio_id = 10;
  string fee_account_id = 11;
  repeated Fee reservation_fee = 9;
}

message ClientCashTradeReservationSnapshot {
  io.wyden.published.common.Metadata metadata = 1;

  string reservation_ref = 2;
  TransactionType transaction_type = 3;
  string date_time = 4; // ISO 8601 format, UTC
  string currency = 5;
  string base_currency = 6;
  string quantity = 7;
  string price = 8;
  string stop_price = 11;
  string portfolio_id = 9;
  string counter_portfolio_id = 10;
  repeated Fee reservation_fee = 12;
}

message StreetCashTradeReservationSnapshot {
  io.wyden.published.common.Metadata metadata = 1;

  string reservation_ref = 2;
  TransactionType transaction_type = 3;
  string date_time = 4; // ISO 8601 format, UTC
  string currency = 5;
  string base_currency = 6;
  string quantity = 7;
  string price = 8;
  string stop_price = 11;
  string portfolio_id = 9;
  string account_id = 10;
  repeated Fee reservation_fee = 12;
}

message ClientAssetTradeReservationSnapshot {
  io.wyden.published.common.Metadata metadata = 1;

  string reservation_ref = 2;
  TransactionType transaction_type = 3;
  string date_time = 4; // ISO 8601 format, UTC
  string currency = 5;
  string instrument = 6;
  string quantity = 7;
  string price = 8;
  string stop_price = 11;
  string portfolio_id = 9;
  string counter_portfolio_id = 10;
  repeated Fee reservation_fee = 12;
}

message StreetAssetTradeReservationSnapshot {
  io.wyden.published.common.Metadata metadata = 1;

  string reservation_ref = 2;
  TransactionType transaction_type = 3;
  string date_time = 4; // ISO 8601 format, UTC
  string currency = 5;
  string instrument = 6;
  string quantity = 7;
  string price = 8;
  string stop_price = 11;
  string portfolio_id = 9;
  string account_id = 10;
  repeated Fee reservation_fee = 12;
}

message AccountCashTransferReservationSnapshot {
  io.wyden.published.common.Metadata metadata = 1;

  string reservation_ref = 2;
  TransactionType transaction_type = 3;
  string date_time = 4; // ISO 8601 format, UTC
  string currency = 5;
  string quantity = 6;
  string from_account_id = 7;
  string to_account_id = 8;
  string fee_account_id = 9;
  string fee_portfolio_id = 10;
  repeated Fee reservation_fee = 11;
}

message PortfolioCashTransferReservationSnapshot {
  io.wyden.published.common.Metadata metadata = 1;

  string reservation_ref = 2;
  TransactionType transaction_type = 3;
  string date_time = 4; // ISO 8601 format, UTC
  string currency = 5;
  string quantity = 6;
  string from_portfolio_id = 7;
  string to_portfolio_id = 8;
  repeated Fee reservation_fee = 9;
  string fee_portfolio_id = 10;
}

message PortfolioAssetTransferReservationSnapshot {
  io.wyden.published.common.Metadata metadata = 1;

  string reservation_ref = 2;
  TransactionType transaction_type = 3;
  string date_time = 4; // ISO 8601 format, UTC
  string instrument = 5;
  string quantity = 6;
  string from_portfolio_id = 7;
  string to_portfolio_id = 8;
  repeated Fee reservation_fee = 9;
  string fee_portfolio_id = 10;
}

message FeeReservationSnapshot {
  io.wyden.published.common.Metadata metadata = 1;

  string reservation_ref = 2;
  TransactionType transaction_type = 3;
  string date_time = 4; // ISO 8601 format, UTC
  string currency = 5;
  string quantity = 6;
  string portfolio_id = 7;
  string account_id = 8;
}
