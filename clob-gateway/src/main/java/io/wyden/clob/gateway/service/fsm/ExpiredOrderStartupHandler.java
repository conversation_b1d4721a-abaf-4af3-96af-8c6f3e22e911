package io.wyden.clob.gateway.service.fsm;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Component
public class ExpiredOrderStartupHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(ExpiredOrderStartupHandler.class);

    private final OrderService orderService;

    public ExpiredOrderStartupHandler(OrderService orderService) {
        this.orderService = orderService;
    }

    @EventListener
    public void onApplicationEvent(ContextRefreshedEvent event) {
        LOGGER.info("Canceling expired orders if exists");
        orderService.handleExpiredOrders();
    }
}
