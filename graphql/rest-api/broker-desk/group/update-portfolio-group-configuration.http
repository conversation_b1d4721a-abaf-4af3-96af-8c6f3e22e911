### Update portfolio group config

GRAPHQL {{RestApiUrl}}/graphql
Authorization: Bearer {{$auth.token("admin")}}

mutation updatePortfolioGroupConfiguration {
    updatePortfolioGroupConfiguration(
        portfolioGroupId: "Crypto Group",
        request: {
            id: "Crypto Group"
            name: "Crypto Group"
            portfolioType: VOSTRO
            executionConfiguration: {
                tradingMode: PRINCIPAL
                counterPortfolio: "Bank"
                fixedFee: 10
                percentageFee: 0.5
                minFee: 10
                feeCurrency: "USD"
                feeCurrencyType: SPECIFIC_CURRENCY
                agencyTradingAccount: "BITMEX-testnet-1"
                agencyTargetInstrumentId: "XBTUSD@FOREX@BitMEX"
                chargeExchangeFee: true
                discloseTradingVenue: true
            }
            pricingConfiguration: {
                markup: 2.99999
                venueAccount: ["bitmex-testnet1", "Simulator"]
            }
            hedgingConfiguration: {
                autoHedging: true,
                autoHedgingMode: ONE_TO_ONE,
                targetAccount: "Bank",
                thresholdConfiguration: [{
                    asset: "BTC",
                    hedgeInstrument: "XBTUSD@FOREX@BitMEX",
                    highThreshold: 100,
                    lowThreshold: -100,
                    targetExposure: 0
                }, {
                    asset: "ETH",
                    hedgeInstrument: "ETHUSD@FOREX@Coinbase",
                    highThreshold: 10000,
                    lowThreshold: -10000,
                    targetExposure: 0
                }]
            }
            instrumentConfiguration: [{
                instrumentId: "XBTUSD@FOREX@BitMEX"
                tradeable: true
                executionConfiguration: {
                    tradingMode: AGENCY
                    counterPortfolio: "Bank"
                    fixedFee: 10
                    percentageFee: 0.5
                    minFee: 10
                    feeCurrency: "USD"
                    agencyTradingAccount: "BITMEX-testnet-1"
                    agencyTargetInstrumentId: "XBTUSD@FOREX@BitMEX"
                    chargeExchangeFee: true
                    discloseTradingVenue: true
                }
                pricingConfiguration: {
                    markup: 0.3
                    pricingSource: [{
                        venueAccount: "bitmex-testnet1",
                        instrumentId: "XBTUSD@FOREX@BitMEX"
                    }, {
                        venueAccount: "Simulator",
                        instrumentId: "BTCUSD@FOREX@Simulator"
                    }]
                }
            }]
        })
}
