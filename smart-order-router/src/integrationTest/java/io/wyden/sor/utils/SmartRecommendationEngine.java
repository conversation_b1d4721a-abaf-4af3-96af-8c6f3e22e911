package io.wyden.sor.utils;

import com.google.common.collect.Lists;
import io.wyden.cloud.utils.test.ExchangeObserver;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.destination.OemsHeader;
import io.wyden.published.smartrecommendationengine.BestExecutionRequest;
import io.wyden.published.smartrecommendationengine.ExecutionRecommendation;
import io.wyden.published.smartrecommendationengine.ExecutionRecommendations;
import io.wyden.published.smartrecommendationengine.ResponseStatus;
import io.wyden.sor.infrastructure.rabbit.RabbitDestinations;
import org.awaitility.core.ConditionTimeoutException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Predicate;

import static io.wyden.sor.it.SORIntegrationTestBase.BITMEX_ETH_INSTRUMENT;
import static io.wyden.sor.it.SORIntegrationTestBase.BITMEX_INSTRUMENT;
import static io.wyden.sor.it.SORIntegrationTestBase.BROADCAST;
import static io.wyden.sor.it.SORIntegrationTestBase.KRAKEN_INSTRUMENT;
import static io.wyden.sor.it.SORIntegrationTestBase.SRE_HOST_ID;
import static io.wyden.sor.it.exchangeotcmix.SORMixIntegrationTestBase.BINANCE;
import static io.wyden.sor.it.exchangeotcmix.SORMixIntegrationTestBase.BINANCE_INSTRUMENT;
import static io.wyden.sor.it.exchangeotcmix.SORMixIntegrationTestBase.OTC_INSTRUMENT;
import static io.wyden.sor.it.otc.SOROtcIntegrationTestBase.B2C2_INSTRUMENT;
import static io.wyden.sor.it.otc.SOROtcIntegrationTestBase.CF_INSTRUMENT;

public class SmartRecommendationEngine {

    private final RabbitDestinations destinations;
    private final TestingData testingData;

    private final ExchangeObserver<BestExecutionRequest> bestExecutionRequestBroadcastExchange;
    private final ExchangeObserver<BestExecutionRequest> bestExecutionRequestPrivateExchange;
    private static final Logger LOGGER = LoggerFactory.getLogger(SmartRecommendationEngine.class);

    public static final Duration DEFAULT_TIMEOUT = Duration.ofSeconds(3L);

    public SmartRecommendationEngine(TestingData testingData, RabbitDestinations destinations, RabbitIntegrator rabbitIntegrator) {
        this.destinations = destinations;
        this.testingData = testingData;

        bestExecutionRequestBroadcastExchange = ExchangeObserver.newBuilder(rabbitIntegrator, destinations.getBestExecutionRequestExchange(), (d, t) -> BestExecutionRequest.parseFrom(d), "recommendations-broadcast-queue")
            .withHeaders(Map.of(
                OemsHeader.MESSAGE_TYPE.getHeaderName(), BestExecutionRequest.class.getSimpleName(),
                OemsHeader.SRE_DESTINATION.getHeaderName(), BROADCAST

            ))
            .build();
        bestExecutionRequestBroadcastExchange.attach();

        bestExecutionRequestPrivateExchange = ExchangeObserver.newBuilder(rabbitIntegrator, destinations.getBestExecutionRequestExchange(), (d, t) -> BestExecutionRequest.parseFrom(d), "recommendations-private-queue")
            .withHeaders(Map.of(
                OemsHeader.MESSAGE_TYPE.getHeaderName(), BestExecutionRequest.class.getSimpleName(),
                OemsHeader.SRE_DESTINATION.getHeaderName(), SRE_HOST_ID
            ))
            .build();
        bestExecutionRequestPrivateExchange.attach();
    }

    public void emitExecutionRecommendationsDoneNoTradingConstraints(String recommendationSubscriptionId) {
        ExecutionRecommendation executionRecommendation = ExecutionRecommendation.newBuilder()
                .setQuantity(testingData.quantity)
                .setCost("100")
                .setInstrument(BITMEX_ETH_INSTRUMENT)
                .build();

        ExecutionRecommendations executionRecommendations = ExecutionRecommendations.newBuilder()
                .addRecommendations(executionRecommendation)
                .setRecommendationSubscriptionId(recommendationSubscriptionId)
                .setStatus(ResponseStatus.DONE)
                .setHostId(SRE_HOST_ID)
                .build();

        emitExecutionRecommendation(executionRecommendations);
    }

    public void emitExecutionRecommendationsOtcDone(String recommendationSubscriptionId) {
        ExecutionRecommendation executionRecommendation2 = ExecutionRecommendation.newBuilder()
            .setQuantity(testingData.quantity)
            .setCost("100")
            .setInstrument(CF_INSTRUMENT)
            .setIsOtc(true)
            .build();

        ExecutionRecommendation executionRecommendation1 = ExecutionRecommendation.newBuilder()
            .setQuantity(testingData.quantity)
            .setCost("200")
            .setInstrument(B2C2_INSTRUMENT)
            .setIsOtc(true)
            .build();

        ExecutionRecommendations executionRecommendations = ExecutionRecommendations.newBuilder()
            .addRecommendations(executionRecommendation2)
            .addRecommendations(executionRecommendation1)
            .setRecommendationSubscriptionId(recommendationSubscriptionId)
            .setStatus(ResponseStatus.DONE)
            .setHostId(SRE_HOST_ID)
            .build();

        emitExecutionRecommendation(executionRecommendations);
    }

    public void emitExecutionRecommendationsMixDone(String recommendationSubscriptionId) {
        ExecutionRecommendation executionRecommendation2 = ExecutionRecommendation.newBuilder()
            .setQuantity(testingData.quantity)
            .setCost("100")
            .setInstrument(BINANCE_INSTRUMENT)
            .build();

        ExecutionRecommendation executionRecommendation1 = ExecutionRecommendation.newBuilder()
            .setQuantity(testingData.quantity)
            .setCost("200")
            .setInstrument(OTC_INSTRUMENT)
            .setIsOtc(true)
            .build();

        ExecutionRecommendations executionRecommendations = ExecutionRecommendations.newBuilder()
            .addRecommendations(executionRecommendation2)
            .addRecommendations(executionRecommendation1)
            .setRecommendationSubscriptionId(recommendationSubscriptionId)
            .setStatus(ResponseStatus.DONE)
            .setHostId(SRE_HOST_ID)
            .build();

        emitExecutionRecommendation(executionRecommendations);
    }

    public void emitExecutionRecommendationsMixDoneOtcBetter(String recommendationSubscriptionId) {
        ExecutionRecommendation executionRecommendation2 = ExecutionRecommendation.newBuilder()
            .setQuantity(testingData.quantity)
            .setCost("100")
            .setInstrument(OTC_INSTRUMENT)
            .setIsOtc(true)
            .build();

        ExecutionRecommendation executionRecommendation1 = ExecutionRecommendation.newBuilder()
            .setQuantity(testingData.quantity)
            .setCost("100")
            .setInstrument(BINANCE_INSTRUMENT)
            .build();

        ExecutionRecommendations executionRecommendations = ExecutionRecommendations.newBuilder()
            .addRecommendations(executionRecommendation2)
            .addRecommendations(executionRecommendation1)
            .setRecommendationSubscriptionId(recommendationSubscriptionId)
            .setStatus(ResponseStatus.DONE)
            .setHostId(SRE_HOST_ID)
            .build();

        emitExecutionRecommendation(executionRecommendations);
    }

    public void emitExecutionRecommendationsDone(String recommendationSubscriptionId) {
        ExecutionRecommendation executionRecommendation2 = ExecutionRecommendation.newBuilder()
            .setQuantity(testingData.quantity)
            .setCost("100")
            .setInstrument(KRAKEN_INSTRUMENT)
            .build();

        ExecutionRecommendation executionRecommendation1 = ExecutionRecommendation.newBuilder()
            .setQuantity(testingData.quantity)
            .setCost("200")
            .setInstrument(BITMEX_INSTRUMENT)
            .build();

        ExecutionRecommendations executionRecommendations = ExecutionRecommendations.newBuilder()
            .addRecommendations(executionRecommendation2)
            .addRecommendations(executionRecommendation1)
            .setRecommendationSubscriptionId(recommendationSubscriptionId)
            .setStatus(ResponseStatus.DONE)
            .setHostId(SRE_HOST_ID)
            .build();

        emitExecutionRecommendation(executionRecommendations);
    }

    public void emitExecutionRecommendationsDoneWithSmallerQtyOtc(String recommendationSubscriptionId) {
        ExecutionRecommendation executionRecommendation2 = ExecutionRecommendation.newBuilder()
            .setQuantity(testingData.halfQuantity)
            .setCost("100")
            .setInstrument(CF_INSTRUMENT)
            .setIsOtc(true)
            .build();

        ExecutionRecommendation executionRecommendation1 = ExecutionRecommendation.newBuilder()
            .setQuantity(testingData.halfQuantity)
            .setCost("200")
            .setInstrument(B2C2_INSTRUMENT)
            .setIsOtc(true)
            .build();

        ExecutionRecommendations executionRecommendations = ExecutionRecommendations.newBuilder()
            .addRecommendations(executionRecommendation2)
            .addRecommendations(executionRecommendation1)
            .setRecommendationSubscriptionId(recommendationSubscriptionId)
            .setStatus(ResponseStatus.DONE)
            .setHostId(SRE_HOST_ID)
            .build();

        emitExecutionRecommendation(executionRecommendations);
    }

    public void emitExecutionRecommendationsDoneWithSmallerQtyMix(String recommendationSubscriptionId) {
        ExecutionRecommendation executionRecommendation2 = ExecutionRecommendation.newBuilder()
            .setQuantity(testingData.halfQuantity)
            .setCost("100")
            .setInstrument(BINANCE_INSTRUMENT)
            .build();

        ExecutionRecommendation executionRecommendation1 = ExecutionRecommendation.newBuilder()
            .setQuantity(testingData.halfQuantity)
            .setCost("200")
            .setInstrument(OTC_INSTRUMENT)
            .setIsOtc(true)
            .build();

        ExecutionRecommendations executionRecommendations = ExecutionRecommendations.newBuilder()
            .addRecommendations(executionRecommendation2)
            .addRecommendations(executionRecommendation1)
            .setRecommendationSubscriptionId(recommendationSubscriptionId)
            .setStatus(ResponseStatus.DONE)
            .setHostId(SRE_HOST_ID)
            .build();

        emitExecutionRecommendation(executionRecommendations);
    }

    public void emitExecutionRecommendationsDoneWithSmallerQty(String recommendationSubscriptionId) {
        ExecutionRecommendation executionRecommendation2 = ExecutionRecommendation.newBuilder()
                .setQuantity(testingData.halfQuantity)
                .setCost("100")
                .setInstrument(KRAKEN_INSTRUMENT)
                .build();

        ExecutionRecommendation executionRecommendation1 = ExecutionRecommendation.newBuilder()
                .setQuantity(testingData.halfQuantity)
                .setCost("200")
                .setInstrument(BITMEX_INSTRUMENT)
                .build();

        ExecutionRecommendations executionRecommendations = ExecutionRecommendations.newBuilder()
                .addRecommendations(executionRecommendation2)
                .addRecommendations(executionRecommendation1)
                .setRecommendationSubscriptionId(recommendationSubscriptionId)
                .setStatus(ResponseStatus.DONE)
                .setHostId(SRE_HOST_ID)
                .build();

        emitExecutionRecommendation(executionRecommendations);
    }

    public void emitExecutionRecommendationsDoneWithOneSmallerQtyOtcMix(String recommendationSubscriptionId) {
        ExecutionRecommendation executionRecommendation2 = ExecutionRecommendation.newBuilder()
            .setQuantity(testingData.halfQuantity)
            .setCost("100")
            .setInstrument(BINANCE_INSTRUMENT)
            .setIsOtc(true)
            .build();

        ExecutionRecommendation executionRecommendation1 = ExecutionRecommendation.newBuilder()
            .setQuantity(testingData.quantity)
            .setCost("200")
            .setInstrument(OTC_INSTRUMENT)
            .setIsOtc(true)
            .build();

        ExecutionRecommendations executionRecommendations = ExecutionRecommendations.newBuilder()
            .addRecommendations(executionRecommendation2)
            .addRecommendations(executionRecommendation1)
            .setRecommendationSubscriptionId(recommendationSubscriptionId)
            .setStatus(ResponseStatus.DONE)
            .setHostId(SRE_HOST_ID)
            .build();

        emitExecutionRecommendation(executionRecommendations);
    }

    public void emitExecutionRecommendationsDoneWithOneSmallerQtyOtc(String recommendationSubscriptionId) {
        ExecutionRecommendation executionRecommendation2 = ExecutionRecommendation.newBuilder()
            .setQuantity(testingData.halfQuantity)
            .setCost("100")
            .setInstrument(CF_INSTRUMENT)
            .setIsOtc(true)
            .build();

        ExecutionRecommendation executionRecommendation1 = ExecutionRecommendation.newBuilder()
            .setQuantity(testingData.quantity)
            .setCost("200")
            .setInstrument(B2C2_INSTRUMENT)
            .setIsOtc(true)
            .build();

        ExecutionRecommendations executionRecommendations = ExecutionRecommendations.newBuilder()
            .addRecommendations(executionRecommendation2)
            .addRecommendations(executionRecommendation1)
            .setRecommendationSubscriptionId(recommendationSubscriptionId)
            .setStatus(ResponseStatus.DONE)
            .setHostId(SRE_HOST_ID)
            .build();

        emitExecutionRecommendation(executionRecommendations);
    }

    public void emitExecutionRecommendationsDoneWithOneSmallerQty(String recommendationSubscriptionId) {
        ExecutionRecommendation executionRecommendation2 = ExecutionRecommendation.newBuilder()
                .setQuantity(testingData.halfQuantity)
                .setCost("100")
                .setInstrument(KRAKEN_INSTRUMENT)
                .build();

        ExecutionRecommendation executionRecommendation1 = ExecutionRecommendation.newBuilder()
                .setQuantity(testingData.quantity)
                .setCost("200")
                .setInstrument(BITMEX_INSTRUMENT)
                .build();

        ExecutionRecommendations executionRecommendations = ExecutionRecommendations.newBuilder()
                .addRecommendations(executionRecommendation2)
                .addRecommendations(executionRecommendation1)
                .setRecommendationSubscriptionId(recommendationSubscriptionId)
                .setStatus(ResponseStatus.DONE)
                .setHostId(SRE_HOST_ID)
                .build();

        emitExecutionRecommendation(executionRecommendations);
    }

    public void emitEmptyExecutionRecommendationsDone(String recommendationSubscriptionId) {
        ExecutionRecommendations executionRecommendations = ExecutionRecommendations.newBuilder()
            .setRecommendationSubscriptionId(recommendationSubscriptionId)
            .setStatus(ResponseStatus.DONE)
            .setHostId(SRE_HOST_ID)
            .build();

        emitExecutionRecommendation(executionRecommendations);
    }

    public void emitExecutionRecommendationsDoneForCF(String recommendationSubscriptionId) {
        ExecutionRecommendation executionRecommendation = ExecutionRecommendation.newBuilder()
            .setQuantity(testingData.quantity)
            .setCost("100")
            .setInstrument(CF_INSTRUMENT)
            .setIsOtc(true)
            .build();

        ExecutionRecommendations executionRecommendations = ExecutionRecommendations.newBuilder()
            .addRecommendations(executionRecommendation)
            .setRecommendationSubscriptionId(recommendationSubscriptionId)
            .setStatus(ResponseStatus.DONE)
            .setHostId(SRE_HOST_ID)
            .build();

        emitExecutionRecommendation(executionRecommendations);
    }

    public void emitExecutionRecommendationsDoneForKraken(String recommendationSubscriptionId) {
        ExecutionRecommendation executionRecommendation = ExecutionRecommendation.newBuilder()
            .setQuantity(testingData.quantity)
            .setCost("100")
            .setInstrument(KRAKEN_INSTRUMENT)
            .build();

        ExecutionRecommendations executionRecommendations = ExecutionRecommendations.newBuilder()
            .addRecommendations(executionRecommendation)
            .setRecommendationSubscriptionId(recommendationSubscriptionId)
            .setStatus(ResponseStatus.DONE)
            .setHostId(SRE_HOST_ID)
            .build();

        emitExecutionRecommendation(executionRecommendations);
    }

    public void emitExecutionRecommendationsDoneForBitmex(String recommendationSubscriptionId) {
        ExecutionRecommendation executionRecommendation = ExecutionRecommendation.newBuilder()
            .setQuantity(testingData.quantity)
            .setCost("200")
            .setInstrument(BITMEX_INSTRUMENT)
            .build();

        ExecutionRecommendations executionRecommendations = ExecutionRecommendations.newBuilder()
            .addRecommendations(executionRecommendation)
            .setRecommendationSubscriptionId(recommendationSubscriptionId)
            .setStatus(ResponseStatus.DONE)
            .setHostId(SRE_HOST_ID)
            .build();

        emitExecutionRecommendation(executionRecommendations);
    }

    public void emitExecutionRecommendationsDoneForBinance(String recommendationSubscriptionId) {
        ExecutionRecommendation executionRecommendation = ExecutionRecommendation.newBuilder()
            .setQuantity(testingData.quantity)
            .setCost("100")
            .setInstrument(BINANCE_INSTRUMENT)
            .build();

        ExecutionRecommendations executionRecommendations = ExecutionRecommendations.newBuilder()
            .addRecommendations(executionRecommendation)
            .setRecommendationSubscriptionId(recommendationSubscriptionId)
            .setStatus(ResponseStatus.DONE)
            .setHostId(SRE_HOST_ID)
            .build();

        emitExecutionRecommendation(executionRecommendations);
    }

    public void emitExecutionRecommendationsProcessing(String recommendationSubscriptionId) {
        ExecutionRecommendations executionRecommendations = ExecutionRecommendations.newBuilder()
            .setRecommendationSubscriptionId(recommendationSubscriptionId)
            .setStatus(ResponseStatus.PROCESSING)
            .setHostId(SRE_HOST_ID)
            .build();

        emitExecutionRecommendation(executionRecommendations);
    }

    private void emitExecutionRecommendation(ExecutionRecommendations executionRecommendations) {
        RabbitExchange<ExecutionRecommendations> destination = destinations.getBestExecutionRecommendationsExchange();
        Map<String, String> headers = Map.of(
            OemsHeader.MESSAGE_TYPE.getHeaderName(), ExecutionRecommendations.class.getSimpleName(),
            OemsHeader.CLIENT_ID.getHeaderName(), "SRE"
        );

        destination.publishWithHeaders(executionRecommendations, headers);
    }

    public BestExecutionRequest awaitBestExecutionBroadcastRequests(String orderId) {
        LOGGER.info("expected order id: " + orderId);
        return awaitBestExecutionBroadcastRequests(request -> orderId.equals(request.getOrderId()));
    }

    private BestExecutionRequest awaitBestExecutionBroadcastRequests(Predicate<BestExecutionRequest> filter) {
        return getBestExecutionBroadcastRequest(filter);
    }

    private BestExecutionRequest getBestExecutionBroadcastRequest(Predicate<BestExecutionRequest> predicate) {
        BestExecutionRequest request;
        try {
            request = bestExecutionRequestBroadcastExchange.awaitMessage(DEFAULT_TIMEOUT, predicate);
        } catch (ConditionTimeoutException e) {
            List<BestExecutionRequest> messages = bestExecutionRequestBroadcastExchange.getMessages();
            Optional<BestExecutionRequest> bestExecutionRequest = Lists.reverse(messages).stream().filter(predicate).findFirst();
            request = bestExecutionRequest.orElseGet(() -> bestExecutionRequestBroadcastExchange.awaitMessage(DEFAULT_TIMEOUT, predicate));
        }
        bestExecutionRequestBroadcastExchange.clearMessages();
        return request;
    }

    public BestExecutionRequest awaitBestExecutionPrivateRequests(String orderId) {
        return awaitBestExecutionPrivateRequests(request -> orderId.equals(request.getOrderId()));
    }

    private BestExecutionRequest awaitBestExecutionPrivateRequests(Predicate<BestExecutionRequest> filter) {
        return getBestExecutionPrivateRequests(filter);
    }

    private BestExecutionRequest getBestExecutionPrivateRequests(Predicate<BestExecutionRequest> predicate) {
        BestExecutionRequest request;
        try {
            request = bestExecutionRequestPrivateExchange.awaitMessage(DEFAULT_TIMEOUT, predicate);
        } catch (ConditionTimeoutException e) {
            List<BestExecutionRequest> messages = bestExecutionRequestPrivateExchange.getMessages();
            Optional<BestExecutionRequest> bestExecutionRequest = Lists.reverse(messages).stream().filter(predicate).findFirst();
            request = bestExecutionRequest.orElseGet(() -> bestExecutionRequestPrivateExchange.awaitMessage(DEFAULT_TIMEOUT, predicate));
        }
        bestExecutionRequestPrivateExchange.clearMessages();
        return request;
    }

    public void ensureNoMoreBestExecutionRequests(String orderId) {
        ensureNoMoreBestExecutionPrivateRequests(orderId);
        ensureNoMoreBestExecutionBroadcastRequests(orderId);
    }

    public void ensureNoMoreBestExecutionPrivateRequests(String orderId) {
        bestExecutionRequestPrivateExchange.ensureNoMoreMessages(DEFAULT_TIMEOUT, m -> {
            if (m != null && m.getOrderId().equals(orderId)) {
                LOGGER.info("Received unexpected message: {}", m);
                return false;
            } else {
                return true;
            }
        });
    }

    public void ensureNoMoreBestExecutionBroadcastRequests(String orderId) {
        bestExecutionRequestBroadcastExchange.ensureNoMoreMessages(DEFAULT_TIMEOUT, m -> {
            if (m != null && m.getOrderId().equals(orderId)) {
                LOGGER.info("Received unexpected message: {}", m);
                return false;
            } else {
                return true;
            }
        });
    }

    public void cleanup() {
        bestExecutionRequestBroadcastExchange.clearMessages();
        bestExecutionRequestPrivateExchange.clearMessages();

        bestExecutionRequestBroadcastExchange.detach();
        bestExecutionRequestPrivateExchange.detach();
    }
}
