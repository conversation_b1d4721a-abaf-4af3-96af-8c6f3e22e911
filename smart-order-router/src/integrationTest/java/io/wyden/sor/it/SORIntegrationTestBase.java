package io.wyden.sor.it;

import com.hazelcast.config.Config;
import com.hazelcast.core.Hazelcast;
import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import io.wyden.cloud.utils.test.ExchangeObserver;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.destination.OemsExchange;
import io.wyden.cloudutils.rabbitmq.destination.OemsHeader;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.audit.AuditEvent;
import io.wyden.published.diagnostic.Capability;
import io.wyden.published.diagnostic.DiagnosticEvent;
import io.wyden.published.diagnostic.Health;
import io.wyden.published.diagnostic.HealthStatus;
import io.wyden.published.marketdata.InstrumentKey;
import io.wyden.published.oems.OemsExecType;
import io.wyden.published.oems.OemsOrderStatus;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import io.wyden.published.referencedata.AssetClass;
import io.wyden.published.referencedata.BaseInstrument;
import io.wyden.published.referencedata.ForexSpotProperties;
import io.wyden.published.referencedata.Instrument;
import io.wyden.published.referencedata.InstrumentIdentifiers;
import io.wyden.published.referencedata.TradingConstraints;
import io.wyden.published.referencedata.VenueAccount;
import io.wyden.published.referencedata.VenueType;
import io.wyden.published.targetregistry.ConnectorCapabilities;
import io.wyden.published.targetregistry.ConnectorOrderType;
import io.wyden.published.targetregistry.ConnectorState;
import io.wyden.published.targetregistry.DefaultTIF;
import io.wyden.published.targetregistry.DefaultTIFs;
import io.wyden.referencedata.client.InstrumentsCacheFacade;
import io.wyden.referencedata.client.VenueAccountCacheFacade;
import io.wyden.referencedata.domain.VenueAccountMapConfig;
import io.wyden.sor.infrastructure.rabbit.RabbitDestinations;
import io.wyden.sor.service.fsm.SmartOrderRoutingService;
import io.wyden.sor.service.recommendation.RecommendationEngineClient;
import io.wyden.sor.service.referencedata.VenueAccountsRepository;
import io.wyden.sor.service.tracking.OrderCache;
import io.wyden.sor.utils.Collider;
import io.wyden.sor.utils.MessageScheduler;
import io.wyden.sor.utils.OrderGateway;
import io.wyden.sor.utils.SmartRecommendationEngine;
import io.wyden.sor.utils.TestingData;
import org.jetbrains.annotations.NotNull;
import org.junit.After;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.AutoConfigureWebTestClient;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.util.TestPropertyValues;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.util.ReflectionTestUtils;
import org.testcontainers.containers.Network;
import org.testcontainers.containers.RabbitMQContainer;
import org.testcontainers.shaded.org.apache.commons.lang3.RandomStringUtils;

import java.io.IOException;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

import static io.wyden.published.targetregistry.ConnectorTIF.GTC;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;

@ContextConfiguration(initializers = SORIntegrationTestBase.Initializer.class)
@SpringBootTest(properties = {"spring.main.allow-bean-definition-overriding=true"})
@TestPropertySource(locations = "classpath:integration-test.properties")
@ExtendWith(SpringExtension.class)
@AutoConfigureWebTestClient
public abstract class SORIntegrationTestBase {
    public static final String KRAKEN = "Kraken";
    public static final String BITMEX = "BitMEX";
    public static final String BTCUSD_SYMBOL= "BTCUSD";
    public static final String BTCUSD_KRAKEN_INSTRUMENT_ID = "BTCUSD@FOREX@Kraken";
    public static final String BTCUSD_BITMEX_INSTRUMENT_ID = "BTCUSD@FOREX@BitMEX";
    public static final String BTCUSD_BITMEX_TICKER = "btc_usd";
    public static final String BTCUSD_KRAKEN_TICKER = "btcusd";
    public static final String SRE_HOST_ID = "sre123";
    public static final String BROADCAST = "BROADCAST";
    public static final String AUDIT_EVENT_SOURCE = "smart-order-router";
    public static final String ETHUSD_SYMBOL= "ETHUSD";
    public static final String ETHUSD_BITMEX_INSTRUMENT_ID = "ETHUSD@FOREX@BitMEX";
    public static final String ETHUSD_BITMEX_TICKER = "eth_usd";

    public static RabbitMQContainer RABBIT_MQ;

    static {
        Network network = Network.newNetwork();
        RABBIT_MQ = new RabbitMQContainer("rabbitmq:3.12-management")
            .withNetwork(network)
            .withReuse(true);

        RABBIT_MQ.start();

        System.setProperty("HOSTNAME", "testhostname-" + RandomStringUtils.randomAlphanumeric(6));
    }


    static final Map<String, Instrument> INSTRUMENTS = Map.of(
        BTCUSD_KRAKEN_INSTRUMENT_ID, Instrument.newBuilder()
            .setBaseInstrument(BaseInstrument.newBuilder()
                .setVenueType(VenueType.STREET)
                .setVenueName(KRAKEN)
                .setSymbol(BTCUSD_SYMBOL)
                .setAssetClass(AssetClass.FOREX)
                .setQuoteCurrency("USD"))
            .setForexSpotProperties(ForexSpotProperties.newBuilder()
                .setBaseCurrency("BTC"))
            .setInstrumentIdentifiers(InstrumentIdentifiers.newBuilder()
                .setInstrumentId(BTCUSD_KRAKEN_INSTRUMENT_ID)
                .setAdapterTicker(BTCUSD_KRAKEN_TICKER)
            ).setTradingConstraints(TradingConstraints.newBuilder()
                .setMaxQty("10000")
                .setMinQty("0")
                .setQtyIncr("0.000001")
                .setQtyScale("8")
                .setPriceIncr("0.000001")
                .setMaxPrice("10000000")
                .setMinPrice("0")
                .setPriceScale("8").build())
            .build(),
        BTCUSD_BITMEX_INSTRUMENT_ID, Instrument.newBuilder()
            .setBaseInstrument(BaseInstrument.newBuilder()
                .setSymbol(BTCUSD_SYMBOL)
                .setAssetClass(AssetClass.FOREX)
                .setVenueType(VenueType.STREET)
                .setVenueName(BITMEX).setQuoteCurrency("USD"))
            .setForexSpotProperties(ForexSpotProperties.newBuilder()
                .setBaseCurrency("BTC"))
            .setInstrumentIdentifiers(InstrumentIdentifiers.newBuilder()
                .setInstrumentId(BTCUSD_BITMEX_INSTRUMENT_ID)
                .setAdapterTicker(BTCUSD_BITMEX_TICKER)
            ).setTradingConstraints(TradingConstraints.newBuilder()
                    .setMaxQty("10000")
                    .setMinQty("0")
                    .setQtyIncr("0.000001")
                    .setQtyScale("8")
                    .setPriceIncr("0.000001")
                    .setMaxPrice("10000000")
                    .setMinPrice("0")
                    .setPriceScale("8").build())
            .build(),
            ETHUSD_BITMEX_INSTRUMENT_ID, Instrument.newBuilder()
               .setBaseInstrument(BaseInstrument.newBuilder()
                    .setSymbol(ETHUSD_SYMBOL)
                    .setAssetClass(AssetClass.FOREX)
                    .setVenueType(VenueType.STREET)
                    .setVenueName(BITMEX).setQuoteCurrency("USD"))
               .setForexSpotProperties(ForexSpotProperties.newBuilder()
                     .setBaseCurrency("ETH"))
               .setInstrumentIdentifiers(InstrumentIdentifiers.newBuilder()
                      .setInstrumentId(ETHUSD_BITMEX_INSTRUMENT_ID)
                       .setAdapterTicker(ETHUSD_BITMEX_TICKER))
               .build()
    );

    static final List<VenueAccount> VENUE_ACCOUNTS = List.of(
        VenueAccount.newBuilder().setVenueName(BITMEX).setId(BITMEX).build(),
        VenueAccount.newBuilder().setVenueName(KRAKEN).setId(KRAKEN).build()
    );

    public static final InstrumentKey BITMEX_INSTRUMENT = InstrumentKey.newBuilder().setInstrumentId(BTCUSD_BITMEX_INSTRUMENT_ID).setVenueAccount(BITMEX).build();
    public static final InstrumentKey KRAKEN_INSTRUMENT = InstrumentKey.newBuilder().setInstrumentId(BTCUSD_KRAKEN_INSTRUMENT_ID).setVenueAccount(KRAKEN).build();
    public static final InstrumentKey BITMEX_ETH_INSTRUMENT = InstrumentKey.newBuilder().setInstrumentId(ETHUSD_BITMEX_INSTRUMENT_ID).setVenueAccount(BITMEX).build();

    @Autowired
    public RabbitIntegrator rabbitIntegrator;

    @Autowired
    public RabbitDestinations destinations;

    @Autowired
    public RecommendationEngineClient recommendationEngineClient;

    @Autowired
    public SmartOrderRoutingService smartOrderRoutingService;

    @Autowired
    private HazelcastInstance hazelcastInstance;

    public TestingData testingData;
    public OrderGateway orderGateway;
    public Collider collider;
    public MessageScheduler messageScheduler;

    public SmartRecommendationEngine smartRecommendationEngine;

    public ExchangeObserver<AuditEvent> auditEventExchangeObserver;

    @BeforeEach
    void setup() {
        testingData = new TestingData();
        orderGateway = new OrderGateway(destinations, rabbitIntegrator);
        collider = new Collider(testingData, destinations, rabbitIntegrator);
        smartRecommendationEngine = new SmartRecommendationEngine(testingData, destinations, rabbitIntegrator);
        messageScheduler = new MessageScheduler(rabbitIntegrator, destinations);

        for (VenueAccount venueAccount : VENUE_ACCOUNTS) {
            String venueAccountName = venueAccount.getId();
            emitDiagnosticEvent(venueAccountName, Capability.TRADING, HealthStatus.ALIVE);
        }

        RabbitExchange<AuditEvent> auditEventsExchange = OemsExchange.AuditEvents.declareAuditEventsExchange(rabbitIntegrator);
        auditEventExchangeObserver = ExchangeObserver.newBuilder(rabbitIntegrator, auditEventsExchange, (data, props) -> AuditEvent.parseFrom(data), "audit-event-queue")
            .withHeaders(Map.of(
                OemsHeader.MESSAGE_TYPE.getHeaderName(), AuditEvent.class.getSimpleName()
            ))
            .build()
            .attach();
    }

    void resetFields() {
        ReflectionTestUtils.setField(smartOrderRoutingService, "childOrderExecutionGracePeriod", 5000);
        ReflectionTestUtils.setField(smartOrderRoutingService, "childOrderSubmissionGracePeriod", 10000);
        ReflectionTestUtils.setField(smartOrderRoutingService, "smartOrderCandidateSuspensionPeriod", 8000);
        ReflectionTestUtils.setField(smartOrderRoutingService, "childOrderCancellationGracePeriod", 2000);
        ReflectionTestUtils.setField(recommendationEngineClient, "recommendationsResponseTimeout", 30000);
        ReflectionTestUtils.setField(recommendationEngineClient, "subscriptionRefreshInterval", 60);
    }

    protected void emitDiagnosticEvent(String venueAccount, Capability capability, HealthStatus status) {
        ConnectorCapabilities.TradingCapabilities tradingCapabilities = ConnectorCapabilities.TradingCapabilities.newBuilder().setDefaultTifs(DefaultTIFs.newBuilder()
            .addDefaultTif(DefaultTIF.newBuilder().setOrderType(ConnectorOrderType.MARKET).setTif(GTC).build())
            .addDefaultTif(DefaultTIF.newBuilder().setOrderType(ConnectorOrderType.STOP).setTif(GTC).build())
            .addDefaultTif(DefaultTIF.newBuilder().setOrderType(ConnectorOrderType.LIMIT).setTif(GTC).build())
            .addDefaultTif(DefaultTIF.newBuilder().setOrderType(ConnectorOrderType.STOP_LIMIT).setTif(GTC).build())
            .build()).build();

        emitDiagnosticEvent(venueAccount, tradingCapabilities, capability, status);
    }

    protected void emitDiagnosticEvent(String venueAccount, ConnectorCapabilities.TradingCapabilities tradingCapabilities, Capability capability, HealthStatus status) {
        destinations.getConnectorStateExchange().publishWithHeaders(ConnectorState.newBuilder()
            .setCapabilities(ConnectorCapabilities.newBuilder().setTradingCapabilities(tradingCapabilities))
            .addDiagnosticEvents(DiagnosticEvent.newBuilder()
                .setCapability(capability)
                .setHealth(Health.newBuilder()
                    .setStatus(status)
                    .build())
                .setVenueAccount(venueAccount)
                .setTimestamp(DateUtils.toIsoUtcTime(ZonedDateTime.now()))
                .build())
            .build(), Map.of("venueAccount", venueAccount));
    }

    @AfterEach
    void tearDown() throws IOException {
        resetFields();
        orderGateway.cleanup();
        collider.cleanup();
        smartRecommendationEngine.cleanup();
        messageScheduler.cleanup();
        auditEventExchangeObserver.detach();
    }

    @After
    public void cleanHz() {
        hazelcastInstance.getMap("instruments").clear();
    }

    protected static void assertExecReportFor(OemsResponse response, OemsRequest smartOrder) {
        assertThat(response.getResponseType()).isEqualTo(OemsResponse.OemsResponseType.EXECUTION_REPORT);
        assertThat(response.getOrderId()).isEqualTo(smartOrder.getOrderId());
        assertThat(response.getMetadata().getSourceType()).isEqualTo(smartOrder.getMetadata().getTargetType());
        assertThat(response.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(response.getOrderQty()).isEqualTo(smartOrder.getQuantity());
        assertThat(response.getClientId()).isEqualTo(smartOrder.getClientId());
        assertThat(response.getBaseCurrency()).isEqualTo(smartOrder.getBaseCurrency());
        assertThat(response.getQuoteCurrency()).isEqualTo(smartOrder.getQuoteCurrency());
    }

    protected static void assertExecReportRejectedFor(OemsResponse response, OemsRequest smartOrder, String reason) {
        assertExecReportFor(response, smartOrder);
        assertThat(response.getExecutionId()).isNotBlank();
        assertThat(response.getExecType()).isEqualTo(OemsExecType.REJECTED);
        assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_REJECTED);
        assertThat(response.getReason()).isEqualTo(reason);
        assertThat(response.getCumQty()).isEqualTo("0");
        assertThat(response.getLeavesQty()).isEqualTo("0");
        assertThat(response.getLastPrice()).isEqualTo("0");
        assertThat(response.getAvgPrice()).isEqualTo("0");
    }

    public static class Initializer implements ApplicationContextInitializer<ConfigurableApplicationContext> {

        @Override
        public void initialize(@NotNull ConfigurableApplicationContext applicationContext) {
            var values = TestPropertyValues.of(
                "rabbitmq.host=" + RABBIT_MQ.getHost(),
                "rabbitmq.port=" + RABBIT_MQ.getMappedPort(5672),
                "rabbitmq.username=" + RABBIT_MQ.getAdminUsername(),
                "rabbitmq.password=" + RABBIT_MQ.getAdminPassword()
            );

            values.applyTo(applicationContext);
        }
    }

    protected AuditEvent awaitAuditEvent() {
        return auditEventExchangeObserver.awaitMessage(Objects::nonNull);
    }

    @TestConfiguration
    static class TestHazelcastConfiguration {

        @Primary
        @Bean("hazelcast")
        HazelcastInstance createHazelcastInstance() {
            Config config = new Config();
            config.setClusterName(UUID.randomUUID().toString());
            config.getNetworkConfig().getJoin().getMulticastConfig().setEnabled(false);
            config.getNetworkConfig().getJoin().getTcpIpConfig().setEnabled(false);
            config.getAdvancedNetworkConfig().getJoin().getAwsConfig().setEnabled(false);
            return Hazelcast.newHazelcastInstance(config);
        }

        @Primary
        @Bean
        InstrumentsCacheFacade instrumentsCacheFacade(HazelcastInstance hazelcastInstance) {
            IMap<String, Instrument> instruments = hazelcastInstance.getMap("instruments");
            instruments.putAll(INSTRUMENTS);
            return new InstrumentsCacheFacade(instruments, mock(Tracing.class));
        }

        @Bean
        VenueAccountsRepository venueAccountService(HazelcastInstance hazelcastInstance) {
            IMap<String, VenueAccount> venueAccounts = VenueAccountMapConfig.getMap(hazelcastInstance);
            for (VenueAccount venueAccount : VENUE_ACCOUNTS) {
                venueAccounts.put(venueAccount.getId(), venueAccount);
            }
            return new VenueAccountsRepository(new VenueAccountCacheFacade(venueAccounts, mock(Tracing.class)));
        }

        @Primary
        @Bean
        OrderCache orderCache(HazelcastInstance hazelcastInstance) {
            return new OrderCache(hazelcastInstance, mock(Tracing.class), 60, false);
        }
    }

}
