package io.wyden.sor.service.fsm;

import io.wyden.published.oems.OemsOrderStatus;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import io.wyden.sor.model.SmartOrderState;
import io.wyden.sor.service.tracking.FailureRequeueException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Set;
import java.util.stream.Collectors;

import static io.wyden.published.oems.OemsOrderStatus.STATUS_CANCELED_VALUE;
import static io.wyden.published.oems.OemsOrderStatus.STATUS_EXPIRED_VALUE;
import static io.wyden.published.oems.OemsOrderStatus.STATUS_FILLED_VALUE;
import static io.wyden.published.oems.OemsOrderStatus.STATUS_NEW_VALUE;
import static io.wyden.published.oems.OemsOrderStatus.STATUS_PARTIALLY_FILLED_VALUE;
import static io.wyden.published.oems.OemsOrderStatus.STATUS_PENDING_CANCEL_VALUE;
import static io.wyden.published.oems.OemsOrderStatus.STATUS_REJECTED_VALUE;
import static io.wyden.sor.service.tracking.RequestValidator.validateFeeExecutionReport;

/**
 * OrderStatus descendants represent order state in state machine. Each order may exist in more than one order state,
 * the value with the highest precedence is used to handle events. State transitions are described below:
 *
 *              +-------------------------------> Rejected
 *              | +--------+---------+-------------v
 * Start -> PendingNew -> New -> PartialFill   -> Filled
 *               |         |          v ^
 *               +---------+---> PendingCancel -> Cancelled
 */
@SuppressWarnings("squid:S1172") // Remove unused method parameters
abstract class OrderStatus implements Comparable<OrderStatus> {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderStatus.class);
    private static final String DUPLICATE_ORDER_ID = "Duplicate orderId";

    enum Precedence {
        START(0),
        NEW(2),
        REJECTED(2),
        PARTIAL_FILL(3),
        CANCELLED(4),
        EXPIRED(5),
        FILLED(7),
        PENDING_CANCEL(11),
        UNRECOGNIZED(100);

        private final int value;

        Precedence(int value) {
            this.value = value;
        }

        public int getValue() {
            return this.value;
        }
    }

    abstract Precedence getPrecedence();


    void onNewOrder(OrderContext context, OemsRequest request) {
        context.rejectSmartOrderAsDuplicate(DUPLICATE_ORDER_ID);
    }

    void onReplacingOrder(OrderContext context, OemsRequest replacingSmartOrder, OrderState replacedOrderState) {
        context.rejectSmartOrderAsDuplicate(DUPLICATE_ORDER_ID);
    }

    void onCancel(OrderContext context, OemsRequest request) {
        OrderState orderState = context.getOrderState();
        context.cancelSmartOrderWrongState(request, orderState);
    }

    void onExpired(OrderContext context) {
        LOGGER.warn("Received OrderExpiredEvent inconsistent with OrderState. Current OrderState: {}", context.getOrderState());
    }

    void onChildCancelReject(OrderContext context, OemsResponse venueCancelReject) {
        if (context.getOrderState().isForceCancel()) {
            LOGGER.info("Received CancelReject '{}' is ignored for force cancelled order", venueCancelReject.getMetadata().getRequestId());
            return;
        }
        LOGGER.warn("Received CancelReject '{}' inconsistent with OrderState. Current OrderState: {}", venueCancelReject.getMetadata().getRequestId(), context.getOrderState());
    }

    void onUnspecified(OrderContext context, OemsResponse report) {
        LOGGER.debug("Received correlation message");
    }

    void onNewChildOrder(OrderContext context, OemsResponse report) {
        LOGGER.warn("Received ExecType.NEW inconsistent with OrderState. Current OrderState: {}", context.getOrderState());
    }

    void onChildRejected(OrderContext context, OemsResponse report) {
        if (context.getOrderState().isForceCancel()) {
            LOGGER.info("Received ExecType.REJECTED is ignored for force cancelled order.");
            return;
        }
        LOGGER.warn("Received ExecType.REJECTED inconsistent with OrderState. Current OrderState: {}", context.getOrderState());
    }

    void onChildTrade(OrderContext context, OemsResponse report) {
        LOGGER.warn("Received ExecType.{} inconsistent with OrderState. Current OrderState: {}", report.getExecType(), context.getOrderState());
    }

    void onChildCanceled(OrderContext context, OemsResponse report) {
        if (context.getOrderState().isForceCancel()) {
            LOGGER.info("Received ExecType.{} is ignored for force cancelled order.", report.getExecType());
            return;
        }
        LOGGER.warn("Received ExecType.{} inconsistent with OrderState. Current OrderState: {}", report.getExecType(), context.getOrderState());
    }

    void onSmartOrderFilled(OrderContext context, OemsResponse report) {
        LOGGER.warn("Received ExecType.{} inconsistent with OrderState. Current OrderState: {}", report.getExecType(), context.getOrderState());
    }

    public void onPendingCancel(OrderContext context, OemsResponse report) {
        LOGGER.debug("Received ExecType.PENDING_CANCEL message");
    }

    void onUnrecognized(OrderContext context, OemsResponse report) {
        throw new FailureRequeueException("Unrecognized ExecType.%d in received ExecutionReport.".formatted(report.getExecTypeValue()));
    }

    boolean isTerminal() {
        return false;
    }

    OemsOrderStatus toOemsOrderStatus() {
        return OemsOrderStatus.ORDER_STATUS_UNSPECIFIED;
    }

    static Set<OrderStatus> toOrderStates(SmartOrderState smartOrderState) {
        return smartOrderState.getCurrentStatusValueList().stream()
            .map(OrderStatus::toOrderStatus)
            .collect(Collectors.toSet());
    }

    static OrderStatus toOrderStatus(int venueOrderStatus) {
        return switch (venueOrderStatus) {
            case STATUS_NEW_VALUE -> StatusNew.create();
            case STATUS_PARTIALLY_FILLED_VALUE -> StatusPartialFill.create();
            case STATUS_FILLED_VALUE -> StatusFilled.create();
            case STATUS_PENDING_CANCEL_VALUE -> StatusPendingCancel.create();
            case STATUS_CANCELED_VALUE -> StatusCancelled.create();
            case STATUS_REJECTED_VALUE -> StatusRejected.create();
            case STATUS_EXPIRED_VALUE -> StatusExpired.create();
            default -> StatusUnrecognized.create(venueOrderStatus);
        };
    }

    boolean hasFill(OemsResponse report) {
        String lastQty = report.getLastQty();
        String lastPrice = report.getLastPrice();

        return !lastQty.isBlank() && !lastPrice.isBlank();
    }

    boolean hasFee(OemsResponse report) {
        return !report.getFee().isBlank();
    }

    void onFee(OrderContext context, OemsResponse report) {
        validateFeeExecutionReport(report);
        OrderState orderState = context.getOrderState();
        orderState.addFees(report.getFeeDataList());
        context.handleChildFee(report);
    }

    @Override
    public boolean equals(Object o) {
        return o != null && getClass() == o.getClass();
    }

    @Override
    public int hashCode() {
        return getClass().hashCode();
    }

    @Override
    public String toString() {
        return "%s.%d".formatted(this.getClass().getSimpleName(), this.getPrecedence().getValue());
    }

    @Override
    public int compareTo(OrderStatus o) {
        return this.getPrecedence().getValue() - o.getPrecedence().getValue();
    }
}
