package io.wyden.sor.service.tracking;

import io.wyden.published.common.Metadata;
import io.wyden.published.marketdata.InstrumentKey;
import io.wyden.published.oems.FeeData;
import io.wyden.published.oems.OemsExecType;
import io.wyden.published.oems.OemsInstrumentType;
import io.wyden.published.oems.OemsOrderType;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import io.wyden.published.oems.OemsSide;
import io.wyden.published.oems.OemsTargetType;
import io.wyden.published.referencedata.Instrument;
import io.wyden.published.referencedata.TradingConstraints;
import io.wyden.published.smartrecommendationengine.ExecutionRecommendation;
import io.wyden.sor.service.fsm.OrderState;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.List;

import static io.wyden.published.oems.OemsOrderType.LIMIT;
import static io.wyden.published.oems.OemsOrderType.MARKET;
import static io.wyden.published.oems.OemsOrderType.STOP;
import static io.wyden.published.oems.OemsOrderType.STOP_LIMIT;
import static io.wyden.published.oems.OemsSide.BUY;
import static io.wyden.published.oems.OemsSide.REDUCE_SHORT;
import static io.wyden.published.oems.OemsSide.SELL;
import static io.wyden.published.oems.OemsSide.SELL_SHORT;
import static io.wyden.sor.service.tracking.ProtobufUtils.toBigDecimalOrNull;
import static io.wyden.sor.service.tracking.ProtobufUtils.toIntegerOrNull;
import static java.math.BigDecimal.ZERO;
import static org.apache.commons.lang3.StringUtils.isBlank;

public class RequestValidator {

    private static final Logger LOGGER = LoggerFactory.getLogger(RequestValidator.class);

    private static final List<OemsSide> VALID_SIDES = List.of(BUY, SELL, REDUCE_SHORT, SELL_SHORT);
    private static final List<OemsOrderType> VALID_ORDER_TYPES = List.of(MARKET, LIMIT, STOP, STOP_LIMIT);
    private static final List<OemsInstrumentType> VALID_INSTRUMENT_TYPES = List.of(OemsInstrumentType.FOREX);

    private RequestValidator() {
        // Empty
    }

    @SuppressWarnings("squid:S3776") // Cognitive Complexity
    public static void validateOemsOrder(OemsRequest request) {

        Metadata.ServiceType targetType = request.getMetadata().getTargetType();
        if (targetType != Metadata.ServiceType.SOR) {
            throw new FailureNonRecoverableException("Smart Order Router can only handle targets of type: %s".formatted(OemsTargetType.SOR.name()));
        }

        String orderId = request.getOrderId();
        if (isBlank(orderId)) {
            throw new FailureNonRecoverableException("Field orderId is blank");
        }

        OemsSide side = request.getSide();
        if (!VALID_SIDES.contains(side)) {
            throw new FailureRequeueException("Smart order %s: unknown side: %s".formatted(orderId, request.getSide()));
        }

        String clientId = request.getClientId();
        if (isBlank(clientId)) {
            throw new FailureNonRecoverableException("Smart order %s: field clientId is blank".formatted(orderId));
        }

        if (request.getVenueAccountsCount() == 0) {
            throw new FailureNonRecoverableException("Smart order %s: venue accounts not set".formatted(orderId));
        }

        if (isBlank(request.getSymbol())) {
            throw new FailureNonRecoverableException("Smart order %s: symbol not set".formatted(orderId));
        }

        if (!VALID_INSTRUMENT_TYPES.contains(request.getInstrumentType())) {
            throw new FailureNonRecoverableException("Smart order %s: instrument type not valid: %s".formatted(orderId, request.getInstrumentType()));
        }

        OemsOrderType orderType = request.getOrderType();
        if (!VALID_ORDER_TYPES.contains(orderType)) {
            throw new FailureRequeueException("Smart order %s: unknown orderType: %s".formatted(orderId, orderType));
        }

        String quantity = request.getQuantity();
        if (isBlank(quantity)) {
            throw new FailureNonRecoverableException("Smart order %s: field quantity is blank".formatted(orderId));
        } else {
            BigDecimal quantityValue = validateNumber(quantity, "quantity", orderId);
            if (quantityValue.compareTo(ZERO) <= 0) {
                throw new FailureNonRecoverableException("Smart order %s: field quantity invalid value: %s".formatted(orderId, quantity));
            }
        }

        String price = request.getPrice();
        if (requiresPrice(orderType)) {
            if (isBlank(price)) {
                throw new FailureNonRecoverableException("Smart order %s: field price is blank, orderType %s".formatted(orderId, orderType));
            } else {
                validateNumber(price, "limit price", orderId);
            }
        }

        String stopPrice = request.getStopPrice();
        if (requiresStopPrice(orderType)) {
            if (isBlank(stopPrice)) {
                throw new FailureNonRecoverableException("Smart order %s: field stop price is blank, orderType %s".formatted(orderId, orderType));
            } else {
                validateNumber(stopPrice, "stop price", orderId);
            }
        }
    }

    public static void validateReplacingOemsOrder(OrderState replacingOrderState, OrderState replacedOrderState) {
        if (replacedOrderState.isPartiallyFilled()) {
            List<InstrumentKey> originalRoutingCandidates = replacedOrderState.getRoutingCandidates();
            List<InstrumentKey> routingCandidates = replacingOrderState.getRoutingCandidates();
            if (!routingCandidates.containsAll(originalRoutingCandidates)) {
                throw new FailureNonRecoverableException("Smart order %s: cannot modify routing candidates, because smart order has been partially filled already."
                    .formatted(replacedOrderState.getOemsRequest().getOrderId()));
            }
        }
    }

    public static void validateOemsCancel(OemsRequest request) {
        String orderId = request.getOrderId();
        if (isBlank(orderId)) {{
            throw new FailureNonRecoverableException("Field orderId is blank");
        }
        }

        String requestId = request.getMetadata().getRequestId();
        if (isBlank(requestId)) {
            throw new FailureNonRecoverableException("Smart order %s: field requestId is blank".formatted(orderId));
        }
    }

    public static void validateVenueCancelReject(OemsResponse report) {
        String venueAccount = report.getVenueAccount();
        if (isBlank(venueAccount)) {
            throw new FailureNonRecoverableException("Smart order %s: field venueAccount is blank".formatted(report.getOrderId()));
        }

        String parentOrderId = report.getParentOrderId();
        if (isBlank(parentOrderId)) {
            throw new FailureNonRecoverableException("Smart order %s: field parentOrderId is blank".formatted(report.getOrderId()));
        }
    }

    public static void validateTradeExecutionReport(OemsResponse report) {
        OemsExecType execType = report.getExecType();
        String lastQty = report.getLastQty();
        String orderId = report.getOrderId();
        if (isBlank(lastQty)) {
            throw new FailureNonRecoverableException("Smart order %s: field lastQty is blank, execType %s".formatted(report.getOrderId(), execType));
        } else {
            validateNumber(lastQty, "lastQty", orderId);
        }

        String lastPrice = report.getLastPrice();
        if (isBlank(lastPrice)) {
            throw new FailureNonRecoverableException("Smart order %s: field lastPrice is blank, execType %s".formatted(report.getOrderId(), execType));
        } else {
            validateNumber(lastPrice, "lastPrice", orderId);
        }

        String executionId = report.getExecutionId();
        if (isBlank(executionId)) {
            throw new FailureNonRecoverableException("Smart order %s: field executionId is blank, execType %s".formatted(report.getOrderId(), execType));
        }
    }

    public static void validateFeeExecutionReport(OemsResponse report) {
        OemsExecType execType = report.getExecType();

        List<FeeData> feeDataList = report.getFeeDataList();

        for (FeeData feeData : feeDataList) {
            String fee = feeData.getAmount();
            if (isBlank(fee)) {
                throw new FailureNonRecoverableException("Smart order %s: field fee is blank, execType %s".formatted(report.getOrderId(), execType));
            } else {
                validateNumber(fee, "fee", report.getOrderId());
            }

            String feeCurrency = feeData.getCurrency();
            if (isBlank(feeCurrency)) {
                    throw new FailureNonRecoverableException("Smart order %s: field feeCurrency is blank, execType %s".formatted(report.getOrderId(), execType));
            }
        }
    }

    public static void validateEmptyTradingConstraints(Instrument instrument, String smartOrderId) throws ValidationException {
        final TradingConstraints constraints = instrument.getTradingConstraints();
        final Integer priceScale = toIntegerOrNull(constraints.getPriceScale(), instrument, "priceScale", smartOrderId);
        final BigDecimal priceIncr = toBigDecimalOrNull(constraints.getPriceIncr(), instrument, "priceIncr", smartOrderId);
        final BigDecimal priceMax = toBigDecimalOrNull(constraints.getMaxPrice(), instrument, "priceMax", smartOrderId);
        final BigDecimal priceMin = toBigDecimalOrNull(constraints.getMinPrice(), instrument, "priceMin", smartOrderId);
        final Integer qtyScale = toIntegerOrNull(constraints.getQtyScale(), instrument, "qtyScale", smartOrderId);
        final BigDecimal qtyIncr = toBigDecimalOrNull(constraints.getQtyIncr(), instrument, "qtyIncr", smartOrderId);
        final BigDecimal minQty = toBigDecimalOrNull(constraints.getMinQty(), instrument, "minQty", smartOrderId);
        final BigDecimal maxQty = toBigDecimalOrNull(constraints.getMaxQty(), instrument, "maxQty", smartOrderId);

        if (allConstraintsAreEmpty(priceIncr, priceMax, priceMin, qtyIncr, minQty, maxQty) &&
                allConstraintsAreEmpty(priceScale, qtyScale)) {
            throw new ValidationException("Smart order %s: all trading constraints are empty.".formatted(smartOrderId));
        }

        if (qtyIncr == null) {
            throw new ValidationException("Smart order %s: field qtyIncr is null".formatted(smartOrderId));
        }
    }

    private static <T> boolean allConstraintsAreEmpty(T ...  constraints) {
        for (T constraint : constraints) {
            if (constraint != null) {
                return false;
            }
        }
        return true;
    }

    public static void validateCandidatePrice(OemsRequest request,
                                              Instrument instrument,
                                              String smartOrderId) throws ValidationException {

        final TradingConstraints constraints = instrument.getTradingConstraints();
        final Integer priceScale = toIntegerOrNull(constraints.getPriceScale(), instrument, "priceScale", smartOrderId);
        final BigDecimal priceIncr = toBigDecimalOrNull(constraints.getPriceIncr(), instrument, "priceIncr", smartOrderId);
        final BigDecimal priceMax = toBigDecimalOrNull(constraints.getMaxPrice(), instrument, "priceMax", smartOrderId);
        final BigDecimal priceMin = toBigDecimalOrNull(constraints.getMinPrice(), instrument, "priceMin", smartOrderId);

        if (requiresPrice(request.getOrderType())) {
            BigDecimal limitPrice = new BigDecimal(request.getPrice());

            validateGreaterThanZero(limitPrice, instrument, "limit price", smartOrderId);
            validateScale(limitPrice, priceScale, instrument, "limit price", smartOrderId);
            validateMinMax(limitPrice, priceMin, priceMax, instrument, "limit price", smartOrderId);
            validateStepIncrement(limitPrice, priceIncr, instrument, "limit price", smartOrderId);
        }

        if (requiresStopPrice(request.getOrderType())) {
            BigDecimal stopPrice = new BigDecimal(request.getStopPrice());

            validateGreaterThanZero(stopPrice, instrument, "stop price", smartOrderId);
            validateScale(stopPrice, priceScale, instrument, "stop price", smartOrderId);
            validateMinMax(stopPrice, priceMin, priceMax, instrument, "stop price", smartOrderId);
            validateStepIncrement(stopPrice, priceIncr, instrument, "stop price", smartOrderId);
        }
    }

    public static void validateCandidateQuantity(OemsRequest request,
                                                 Instrument instrument,
                                                 ExecutionRecommendation candidate,
                                                 String smartOrderId) throws ValidationException {

        final TradingConstraints constraints = instrument.getTradingConstraints();
        final Integer qtyScale = toIntegerOrNull(constraints.getQtyScale(), instrument, "qtyScale", smartOrderId);
        final BigDecimal qtyIncr = toBigDecimalOrNull(constraints.getQtyIncr(), instrument, "qtyIncr", smartOrderId);
        final BigDecimal minQty = toBigDecimalOrNull(constraints.getMinQty(), instrument, "minQty", smartOrderId);
        final BigDecimal maxQty = toBigDecimalOrNull(constraints.getMaxQty(), instrument, "maxQty", smartOrderId);
        final BigDecimal candidateQuantity = toBigDecimalOrNull(candidate.getQuantity(), instrument, "candidate quantity", smartOrderId);

        final BigDecimal requestedQuantity = new BigDecimal(request.getQuantity());

        validateGreaterThanZero(requestedQuantity, instrument, "quantity", smartOrderId);
        validateScale(requestedQuantity, qtyScale, instrument, "quantity", smartOrderId);
        validateMinMax(requestedQuantity, minQty, maxQty, instrument, "quantity", smartOrderId);
        validateStepIncrement(requestedQuantity, qtyIncr, instrument, "quantity", smartOrderId);

        if (candidateQuantity.compareTo(requestedQuantity) < 0) {
            throw new ValidationException("Smart order %s: candidate quantity = %s is smaller than requested quantity = %s for candidate %s"
                .formatted(request.getOrderId(), candidateQuantity.toPlainString(), requestedQuantity.toPlainString(), getInstrumentInfo(instrument)));
        }
    }

    public static BigDecimal adjustAndValidateCandidateQuantity(Instrument instrument,
                                                                ExecutionRecommendation candidate,
                                                                String smartOrderId) throws ValidationException {

        final BigDecimal candidateQuantity = toBigDecimalOrNull(candidate.getQuantity(), instrument, "candidate quantity", smartOrderId);

        BigDecimal candidateAdjustedQuantity = adjustCandidateQuantity(instrument, candidateQuantity, smartOrderId);
        validateCandidateAdjustedQuantity(candidateAdjustedQuantity, instrument, smartOrderId);

        return candidateAdjustedQuantity;
    }

    private static void validateCandidateAdjustedQuantity(BigDecimal candidateQuantity,
                                                          Instrument instrument,
                                                          String smartOrderId) throws ValidationException {

        final TradingConstraints constraints = instrument.getTradingConstraints();
        final BigDecimal minQty = toBigDecimalOrNull(constraints.getMinQty(), instrument, "minQty", smartOrderId);

        validateGreaterThanZero(candidateQuantity, instrument, "quantity", smartOrderId);
        validateMin(candidateQuantity, minQty, instrument, "quantity", smartOrderId);
    }

    public static void validateMinExecutableQuantity(OemsRequest request,
                                                     int minExecutableQtyPercentage,
                                                     Instrument instrument,
                                                     ExecutionRecommendation candidate,
                                                     String smartOrderId) throws ValidationException {

        final BigDecimal requestedQuantity = new BigDecimal(request.getQuantity());
        final BigDecimal candidateQuantity = toBigDecimalOrNull(candidate.getQuantity(), instrument, "candidate quantity", smartOrderId);

        BigDecimal minExecutableQty = requestedQuantity.multiply(new BigDecimal(minExecutableQtyPercentage)).scaleByPowerOfTen(-2);
        if (candidateQuantity.compareTo(minExecutableQty) < 0) {
            throw new ValidationException(("Smart order %s: candidate quantity = %s is smaller than min expected executable quantity = %s for candidate %s. " +
                "Min expected executable quantity percentage = %d").formatted(smartOrderId, candidateQuantity.toPlainString(), minExecutableQty.toPlainString(),
                    getInstrumentInfo(instrument), minExecutableQtyPercentage));
        }
    }

    private static BigDecimal adjustCandidateQuantity(Instrument instrument, BigDecimal candidateQuantity, String smartOrderId) {
        final TradingConstraints constraints = instrument.getTradingConstraints();
        final Integer qtyScale = toIntegerOrNull(constraints.getQtyScale(), instrument, "qtyScale", smartOrderId);
        final BigDecimal qtyIncr = toBigDecimalOrNull(constraints.getQtyIncr(), instrument, "qtyIncr", smartOrderId);
        final BigDecimal maxQty = toBigDecimalOrNull(constraints.getMaxQty(), instrument, "maxQty", smartOrderId);

        // lower the qty if it's bigger than maximum traded quantity
        if (maxQty != null && candidateQuantity.compareTo(maxQty) > 0) {
            LOGGER.info("Smart order {}: candidate quantity of {} is bigger than maxQuantity {}, adjusting quantity for candidate {}",
                smartOrderId, candidateQuantity.toPlainString(), maxQty.toPlainString(), getInstrumentInfo(instrument));
            candidateQuantity = maxQty;
        }

        // adjust according to qtyIncr and scale
        if (qtyScale != null & qtyIncr != null) {
            LOGGER.info("Smart order {}: adjusting candidate quantity of {} according to scale {} and increment {} for candidate {}",
                smartOrderId, candidateQuantity.toPlainString(), qtyScale, qtyIncr.toPlainString(), getInstrumentInfo(instrument));
            candidateQuantity = RoundUtil.roundDown(candidateQuantity, qtyIncr, qtyScale);
        }

        return candidateQuantity;
    }

    public static void validateStepIncrement(BigDecimal valueToCheck,
                                             BigDecimal increment,
                                             Instrument instrument,
                                             String valueName,
                                             String smartOrderId) throws ValidationException {

        if (valueToCheck == null) {
            return;
        }

        if (valueToCheck.compareTo(BigDecimal.ZERO) > 0 && increment.compareTo(BigDecimal.ZERO) > 0) {
            if (valueToCheck.remainder(increment).compareTo(BigDecimal.ZERO) != 0) {
                throw new ValidationException("Smart order %s: %s = %s - incorrect multiplication of step increment = %s for candidate %s"
                    .formatted(smartOrderId, valueName, valueToCheck.toPlainString(), increment.toPlainString(), getInstrumentInfo(instrument)));
            }
        } else {
            throw new ValidationException("Smart order %s: %s = %s - incorrect multiplication of step increment = %s - values must be positive numbers for candidate %s"
                .formatted(smartOrderId, valueName, valueToCheck.toPlainString(), increment.toPlainString(), getInstrumentInfo(instrument)));
        }
    }

    private static void validateGreaterThanZero(BigDecimal valueToCheck,
                                                Instrument instrument,
                                                String valueName,
                                                String smartOrderId) throws ValidationException {
        if (valueToCheck != null) {
            if (valueToCheck.compareTo(ZERO) <= 0) {
                throw new ValidationException("Smart order %s: %s = %s - must be greater than 0 for candidate %s"
                    .formatted(smartOrderId, valueName, valueToCheck.toPlainString(), getInstrumentInfo(instrument)));
            }
        }
    }

    private static void validateScale(BigDecimal valueToCheck,
                                      Integer maxScale,
                                      Instrument instrument,
                                      String valueName,
                                      String smartOrderId) throws ValidationException {

        if (maxScale == null) {
            return;
        }

        if (maxScale < 0) {
            // do not validate such, scale for value "100" can be either 0 or -2 depending on how the BigDecimal was constructed
            // increment step will be validated in a separate validation so this is ok
            return;
        }

        if (valueToCheck != null) {
            int scaleToCheck = valueToCheck.scale();
            if (scaleToCheck > maxScale) {
                throw new ValidationException("Smart order %s: %s = %s scale is greater than maxScale = %s for candidate %s"
                    .formatted(smartOrderId, valueName, valueToCheck.toPlainString(), maxScale, getInstrumentInfo(instrument)));
            }
        }
    }

    private static void validateMinMax(BigDecimal valueToCheck,
                                       BigDecimal min,
                                       BigDecimal max,
                                       Instrument instrument,
                                       String valueName,
                                       String smartOrderId) throws ValidationException {

        validateMin(valueToCheck, min, instrument, valueName, smartOrderId);
        validateMax(valueToCheck, max, instrument, valueName, smartOrderId);
    }

    private static void validateMin(BigDecimal valueToCheck,
                                    BigDecimal min,
                                    Instrument instrument,
                                    String valueName,
                                    String smartOrderId) throws ValidationException {

        if (min != null && valueToCheck != null) {
            if (min.compareTo(valueToCheck) > 0) {
                throw new ValidationException("Smart order %s: %s = %s cannot be lower than %s for candidate %s"
                    .formatted(smartOrderId, valueName, valueToCheck.toPlainString(), min.toPlainString(), getInstrumentInfo(instrument)));
            }
        }
    }

    private static void validateMax(BigDecimal valueToCheck,
                                    BigDecimal max,
                                    Instrument instrument,
                                    String valueName,
                                    String smartOrderId) throws ValidationException {

        if (max != null && valueToCheck != null) {
            if (max.compareTo(valueToCheck) < 0) {
                throw new ValidationException("Smart order %s: %s = %s cannot be greater than %s for candidate %s"
                    .formatted(smartOrderId, valueName, valueToCheck.toPlainString(), max.toPlainString(), getInstrumentInfo(instrument)));
            }
        }
    }

    public static boolean requiresPrice(OemsOrderType orderType) {
        return switch (orderType) {
            case LIMIT, STOP_LIMIT -> true;
            default -> false;
        };
    }

    public static boolean requiresStopPrice(OemsOrderType orderType) {
        return switch (orderType) {
            case STOP, STOP_LIMIT -> true;
            default -> false;
        };
    }

    private static BigDecimal validateNumber(String value, String fieldName, String smartOrderId) {
        try {
            return new BigDecimal(value);
        } catch (Exception ex) {
            throw new FailureNonRecoverableException("Smart order %s: field %s, number '%s' parse error".formatted(smartOrderId, fieldName, value), ex);
        }
    }

    public static String[] getInstrumentInfo(Instrument instrument) {
        return new String[] {
            instrument.getInstrumentIdentifiers().getInstrumentId(),
            instrument.getBaseInstrument().getVenueName()
        };
    }
}
