package io.wyden.sor.service.fsm;

import com.google.common.collect.Lists;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.marketdata.InstrumentKey;
import io.wyden.published.oems.OemsExecType;
import io.wyden.published.oems.OemsOrderType;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import io.wyden.published.oems.OemsTIF;
import io.wyden.published.referencedata.Instrument;
import io.wyden.published.smartrecommendationengine.ExecutionRecommendation;
import io.wyden.published.smartrecommendationengine.ExecutionRecommendations;
import io.wyden.sor.model.SmartOrderState;
import io.wyden.sor.service.audit.AuditContext;
import io.wyden.sor.service.audit.AuditService;
import io.wyden.sor.service.connectorstate.ConnectorStateService;
import io.wyden.sor.service.connectorstate.TIFResolver;
import io.wyden.sor.service.oems.outbound.CancellationCheckScheduler;
import io.wyden.sor.service.oems.outbound.CandidateSuspensionTimeoutScheduler;
import io.wyden.sor.service.oems.outbound.LimitOrdersExecutionCheckScheduler;
import io.wyden.sor.service.oems.outbound.OemsRequestEmitter;
import io.wyden.sor.service.oems.outbound.OemsResponseEmitter;
import io.wyden.sor.service.oems.outbound.OemsResponseFactory;
import io.wyden.sor.service.oems.outbound.OrderExpirationScheduler;
import io.wyden.sor.service.oems.outbound.SmartOrderRetryScheduler;
import io.wyden.sor.service.oems.outbound.SubmissionCheckScheduler;
import io.wyden.sor.service.recommendation.ExecutionRecommendationWithTIF;
import io.wyden.sor.service.recommendation.RecommendationService;
import io.wyden.sor.service.referencedata.InstrumentsRepository;
import io.wyden.sor.service.tracking.FailureRequeueException;
import io.wyden.sor.service.tracking.OrderCache;
import io.wyden.sor.service.tracking.RequestValidator;
import io.wyden.sor.service.tracking.ValidationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static io.wyden.published.oems.OemsOrderType.LIMIT;
import static io.wyden.published.oems.OemsOrderType.MARKET;
import static io.wyden.published.oems.OemsOrderType.STOP;
import static io.wyden.published.oems.OemsOrderType.STOP_LIMIT;
import static io.wyden.sor.service.fsm.OemsRequestFactory.createChildCancel;
import static io.wyden.sor.service.fsm.OemsRequestFactory.createChildOrder;
import static io.wyden.sor.service.oems.outbound.OemsResponseFactory.createBasedOnExecution;
import static io.wyden.sor.service.oems.outbound.OemsResponseFactory.createCancelRejectGenericError;
import static io.wyden.sor.service.oems.outbound.OemsResponseFactory.createCancelRejectWrongState;
import static io.wyden.sor.service.oems.outbound.OemsResponseFactory.createCanceled;
import static io.wyden.sor.service.oems.outbound.OemsResponseFactory.createExpired;
import static io.wyden.sor.service.oems.outbound.OemsResponseFactory.createNew;
import static io.wyden.sor.service.oems.outbound.OemsResponseFactory.createPendingCancel;
import static io.wyden.sor.service.oems.outbound.OemsResponseFactory.createRejectAsDuplicate;
import static io.wyden.sor.service.oems.outbound.OemsResponseFactory.createRejectAsValidationError;
import static io.wyden.sor.service.tracking.RequestValidator.adjustAndValidateCandidateQuantity;
import static io.wyden.sor.service.tracking.RequestValidator.validateCandidatePrice;
import static io.wyden.sor.service.tracking.RequestValidator.validateCandidateQuantity;
import static io.wyden.sor.service.tracking.RequestValidator.validateEmptyTradingConstraints;
import static io.wyden.sor.service.tracking.RequestValidator.validateMinExecutableQuantity;
import static io.wyden.sor.service.tracking.RequestValidator.validateReplacingOemsOrder;

@Service
public class SmartOrderRoutingService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SmartOrderRoutingService.class);
    private final RecommendationService recommendationService;
    private final ConnectorStateService connectorStateService;
    private final TIFResolver tifResolver;
    private final InstrumentsRepository instrumentsRepository;
    private final AuditService auditService;
    private int smartOrderMaxRetries;
    private int otcChildOrderMaxRetries;
    private int retrySuspensionTimeout;
    private int childOrderSubmissionGracePeriod;
    private int childOrderExecutionGracePeriod;
    private int childOrderCancellationGracePeriod;
    private int childOrderCancellationMaxRetries;
    private int minExecutableQtyPercentage;
    private int smartOrderCandidateSuspensionPeriod;

    private final OemsResponseEmitter oemsResponseEmitter;
    private final OemsRequestEmitter oemsRequestEmitter;
    private final OrderExpirationScheduler orderExpirationScheduler;
    private final SubmissionCheckScheduler submissionCheckScheduler;
    private final LimitOrdersExecutionCheckScheduler executionCheckScheduler;
    private final CancellationCheckScheduler cancellationCheckScheduler;
    private final CandidateSuspensionTimeoutScheduler candidateSuspensionTimeoutScheduler;
    private final SmartOrderRetryScheduler smartOrderRetryScheduler;

    private final OrderCache orderCache;

    private final Map<String, CompletableFuture<Void>> childCancelFutures = new ConcurrentHashMap<>(); // orderId -> cancel future

    SmartOrderRoutingService(OrderCache orderCache,
                             RecommendationService recommendationService,
                             ConnectorStateService connectorStateService,
                             TIFResolver tifResolver,
                             InstrumentsRepository instrumentsRepository,
                             AuditService auditService,
                             OemsResponseEmitter oemsResponseEmitter,
                             OemsRequestEmitter oemsRequestEmitter,
                             OrderExpirationScheduler orderExpirationScheduler,
                             SubmissionCheckScheduler submissionCheckScheduler,
                             LimitOrdersExecutionCheckScheduler executionCheckScheduler,
                             CancellationCheckScheduler cancellationCheckScheduler,
                             CandidateSuspensionTimeoutScheduler candidateSuspensionTimeoutScheduler,
                             SmartOrderRetryScheduler smartOrderRetryScheduler,
                             @Value("${smartorder.maxRetries:3}") int smartOrderMaxRetries,
                             @Value("${smartorder.retrySuspensionTimeout:10000}") int retrySuspensionTimeout,
                             @Value("${smartorder.childOrderSubmissionGracePeriod:3000}") int childOrderSubmissionGracePeriod,
                             @Value("${smartorder.childOrderExecutionGracePeriod:5000}") int childOrderExecutionGracePeriod,
                             @Value("${smartorder.childOrderCancellationGracePeriod:5000}") int childOrderCancellationGracePeriod,
                             @Value("${smartorder.childOrderCancellationMaxRetries:3}") int childOrderCancellationMaxRetries,
                             @Value("${smartorder.minExecutableQtyPercentage:60}") int minExecutableQtyPercentage,
                             @Value("${smartorder.smartOrderCandidateSuspensionPeriod:10000}") int smartOrderCandidateSuspensionPeriod,
                             @Value("${smartorder.otcChildOrderMaxRetries:3}") int otcChildOrderMaxRetries) {
        this.orderCache = orderCache;
        this.recommendationService = recommendationService;
        this.connectorStateService = connectorStateService;
        this.tifResolver = tifResolver;
        this.instrumentsRepository = instrumentsRepository;
        this.auditService = auditService;
        this.smartOrderMaxRetries = smartOrderMaxRetries;
        this.retrySuspensionTimeout = retrySuspensionTimeout;
        this.childOrderSubmissionGracePeriod = childOrderSubmissionGracePeriod;
        this.childOrderCancellationGracePeriod = childOrderCancellationGracePeriod;
        this.childOrderCancellationMaxRetries = childOrderCancellationMaxRetries;
        this.orderExpirationScheduler = orderExpirationScheduler;
        this.submissionCheckScheduler = submissionCheckScheduler;
        this.executionCheckScheduler = executionCheckScheduler;
        this.cancellationCheckScheduler = cancellationCheckScheduler;
        this.candidateSuspensionTimeoutScheduler = candidateSuspensionTimeoutScheduler;
        this.oemsResponseEmitter = oemsResponseEmitter;
        this.oemsRequestEmitter = oemsRequestEmitter;
        this.minExecutableQtyPercentage = minExecutableQtyPercentage;
        this.smartOrderCandidateSuspensionPeriod = smartOrderCandidateSuspensionPeriod;
        this.childOrderExecutionGracePeriod = childOrderExecutionGracePeriod;
        this.otcChildOrderMaxRetries = otcChildOrderMaxRetries;
        this.smartOrderRetryScheduler = smartOrderRetryScheduler;
    }

    void sendSmartOrder(OemsRequest smartOrder, OrderState orderState) {
        LOGGER.info("Smart order {}: sending.", smartOrder.getOrderId());
        addSmartOrderState(orderState);
        fetchRoutingCandidates(smartOrder, orderState);
        emitOemsResponse(createNew(orderState));
        scheduleExpiration(smartOrder);
        fetchExecutionRecommendations(smartOrder, orderState);
        updateSmartOrderState(orderState);
    }

    public void sendReplacingSmartOrder(OemsRequest replacingSmartOrder, OrderState replacingOrderState, OrderState replacedOrderState) {
        OemsRequest replacedOrder = replacedOrderState.getOemsRequest();
        LOGGER.info("Smart order {}: sending. Replacing smart order {}.", replacingSmartOrder.getOrderId(), replacedOrder.getOrderId());
        orderCache.add(replacingOrderState.toSmartOrderState());
        fetchRoutingCandidates(replacingSmartOrder, replacingOrderState);
        validateReplacingOemsOrder(replacingOrderState, replacedOrderState);
        excludeCandidatesNotUsedInReplacedOrder(replacedOrderState, replacingOrderState);
        emitOemsResponse(createNew(replacingOrderState));
        scheduleExpiration(replacingSmartOrder);
        fetchExecutionRecommendations(replacingSmartOrder, replacingOrderState);
        updateSmartOrderState(replacingOrderState);
    }

    private void excludeCandidatesNotUsedInReplacedOrder(OrderState replacedOrderState, OrderState replacingOrderState) {
        List<InstrumentKey> childInstruments = replacedOrderState.getChildOrdersWithFills().stream().map(
                childOrder -> createInstrumentKey(childOrder.getInstrumentId(), childOrder.getVenueAccount())
        ).collect(Collectors.toList());

        if (childInstruments.isEmpty()) {
            return;
        }

        List<InstrumentKey> routingCandidates = replacedOrderState.getAvailableRoutingCandidates();
        for (InstrumentKey routingCandidate : routingCandidates) {
            if (!childInstruments.contains(routingCandidate)) {
                replacingOrderState.excludeCandidate(routingCandidate);
            }
        }
    }

    private void handleInternalSmartOrderCancel(OemsRequest smartOrder, OrderState orderState, String reason) {
        if (orderState.isSmartOrderCompletedOrPendingExpire()) {
            LOGGER.info("Smart order {} won't be canceled because it's already completed", smartOrder.getOrderId());
        } else {
            cancelSmartOrder(smartOrder, orderState, reason);
        }
    }

    void cancelSmartOrder(OemsRequest smartOrder, OrderState orderState, String reason) {
        LOGGER.info("Smart order {} canceling, reason: {}.", smartOrder.getOrderId(), reason);
        if (smartOrder.getForceCancel()) {
            orderState.setStatusCanceled();
            orderState.setForceCancel(true);
            emitOemsResponse(createCanceled(orderState, reason));
            cancelActiveChildOrders(smartOrder, orderState);
        } else {
            orderState.setStatusPendingCancel(smartOrder.getMetadata().getRequestId());
            emitOemsResponse(createPendingCancel(orderState));
            cancelActiveChildOrders(smartOrder, orderState).thenRun(() -> {
                emitOemsResponse(createCanceled(orderState, reason));
                orderState.setStatusCanceled();
                updateSmartOrderState(orderState);
            });
        }
        recommendationService.cancelRecommendationRequest(orderState.getRecommendationSubscriptionId());
        orderState.setDescription(reason);
        cancelExpiration(orderState.getOemsRequest());
        updateSmartOrderState(orderState);
    }

    void handleNewChild(OemsResponse childExecutionReport, OrderState orderState) {
        String childOrderId = childExecutionReport.getOrderId();
        LOGGER.info("Smart order {}: new child order {} submitted.",
            orderState.getOemsRequest().getOrderId(), childOrderId);
        orderState.markChildNotPendingNew(childOrderId);
        if (limitOrder(orderState)) {
            scheduleExecutionCheck(childOrderId, orderState);
        }
        updateSmartOrderState(orderState);
    }

    void scheduleExpiration(OemsRequest smartOrder) {
        if (smartOrder.getTif() == OemsTIF.GTD) {
            ZonedDateTime expireTime = DateUtils.fromFixUtcTime(smartOrder.getExpireTime());
            orderExpirationScheduler.scheduleExpiration(smartOrder.getOrderId(), expireTime);
            LOGGER.info("Smart order {}: scheduled expiration, expireTime {}.", smartOrder.getOrderId(), expireTime);
        }
    }

    void cancelExpiration(OemsRequest smartOrder) {
        if (smartOrder.getTif() == OemsTIF.GTD) {
            orderExpirationScheduler.cancelExpiration(smartOrder.getOrderId());
        }
    }

    public void expireSmartOrder(OrderState orderState) {
        OemsRequest smartOrder = orderState.getOemsRequest();
        LOGGER.info("Expiring smart order {} after all child orders are canceled.", smartOrder.getOrderId());
        orderState.setPendingExpired();
        cancelActiveChildOrders(smartOrder, orderState).thenRun(() -> {
            oemsResponseEmitter.emit(createExpired(orderState));
            orderState.setStatusExpired();
            updateSmartOrderState(orderState);
        });
    }

    public void handleSmartOrderFilled(OrderState orderState) {
        List<OemsRequest> activeChildOrders = orderState.getActiveChildOrders();
        for (OemsRequest childOrder : activeChildOrders) {
            LOGGER.info("Smart order {}: filled, canceling child order {}", orderState.getOemsRequest().getOrderId(), childOrder.getOrderId());
            cancelChildOrder(childOrder, orderState);
        }
    }

    void handleChildCanceled(OemsResponse childExecutionReport, OrderState orderState) {
        orderState.markChildNotPendingCanceled(childExecutionReport.getOrderId());
        completeChildCancel(childExecutionReport.getOrderId());
        handleChildRejectedOrCanceled(childExecutionReport, orderState);
        updateSmartOrderState(orderState);
    }

    private void completeChildCancel(String childOrderId) {
        CompletableFuture<Void> future = childCancelFutures.remove(childOrderId);
        if (future != null && !future.isDone()) {
            future.complete(null);
        }
    }

    public void handleCandidateSuspensionTimeout(InstrumentKey candidate, OrderState orderState) {
        OemsRequest smartOrder = orderState.getOemsRequest();
        LOGGER.info("Smart order {}: candidate {}, {} suspension time elapsed",
            smartOrder.getOrderId(), candidate.getInstrumentId(), candidate.getVenueAccount());
        orderState.removeSuspendedCandidate(candidate);
        updateSmartOrderState(orderState);

        if (orderState.isSmartOrderIdle()) {
            LOGGER.info("Smart order {}: candidate {}, {} suspension time elapsed, retrying smart order.",
                    smartOrder.getOrderId(), candidate.getInstrumentId(), candidate.getVenueAccount());
            retrySmartOrder(smartOrder, orderState);
        }
    }

    void handleChildRejectedOrCanceled(OemsResponse childExecutionReport, OrderState orderState) {
        handleChildCompleted(childExecutionReport, orderState);

        OemsRequest smartOrder = orderState.getOemsRequest();
        if (!orderState.hasOrderStatuses(StatusPartialFill.create())) {
            InstrumentKey instrumentKey = createInstrumentKey(childExecutionReport.getInstrumentId(), childExecutionReport.getVenueAccount());

            boolean otcCandidate = orderState.isOtcCandidate(instrumentKey);

            OtcRetryResult otcRetryResult = OtcRetryResult.UNDEFINED;
            if (otcCandidate) {
                otcRetryResult = handleOtcCandidateRetry(smartOrder, orderState, instrumentKey, childExecutionReport, childExecutionReport.getExecType(). toString());
            }

            if (otcRetryResult.equals(OtcRetryResult.ONGOING)) {
                return;
            }

            if (limitOrder(orderState) || otcRetryResult.equals(OtcRetryResult.DONE)) {
                suspendCandidate(smartOrder, orderState, instrumentKey,
                    "Child order was " + childExecutionReport.getExecType());
            }
            sendNextChildOrder(smartOrder, orderState);
        }
        updateSmartOrderState(orderState);
    }

    private OtcRetryResult handleOtcCandidateRetry(OemsRequest smartOrder, OrderState orderState, InstrumentKey instrumentKey, OemsResponse childExecutionReport, String reason) {
        String instrumentId = instrumentKey.getInstrumentId();
        String venueAccount = instrumentKey.getVenueAccount();

        LOGGER.info("Smart order {}: OTC candidate {}, {} was {}. Retrying sending child order.",
            smartOrder.getOrderId(), instrumentId, venueAccount, reason);

        int otcChildRetryCount = orderState.incrementOtcChildRetryCount(instrumentKey);
        if (otcChildRetryCount > otcChildOrderMaxRetries) {
            LOGGER.info("Smart order {}: max retry count ({}) for sending OTC child order for candidate {}, {} reached.",
                smartOrder.getOrderId(), childOrderCancellationMaxRetries,  instrumentId, venueAccount);
            orderState.resetOtcCandidateRetryCount(instrumentKey);
            updateSmartOrderState(orderState);
            return OtcRetryResult.DONE;
        } else {
            LOGGER.info("Smart order {}: sending OTC child order for candidate {}, {} retry take {}/{}.", smartOrder.getOrderId(), instrumentId, venueAccount,
                otcChildRetryCount, otcChildOrderMaxRetries);

            OemsRequest childOrder = sendChildOrder(smartOrder, orderState, childExecutionReport);
            orderState.addPendingNewChild(childOrder);
            scheduleSubmissionCheck(childOrder, orderState);
            updateSmartOrderState(orderState);
            return OtcRetryResult.ONGOING;
        }
    }

    void handleChildExecuted(OemsResponse childExecutionReport, OrderState orderState) {
        handleChildCompleted(childExecutionReport, orderState);
        updateSmartOrderState(orderState);
    }

    void handleChildExecution(OemsResponse childExecutionReport, OrderState orderState) {
        emitOemsResponse(createBasedOnExecution(orderState, childExecutionReport));
        removeUnknownQuantityForChildExecution(childExecutionReport, orderState);
        orderState.markChildNotPendingNew(childExecutionReport.getOrderId());
        orderState.addChildOrderWithFills(childExecutionReport.getOrderId());
        updateSmartOrderState(orderState);
    }

    void handleChildFee(OemsResponse childExecutionReport, OrderState orderState) {
        OemsResponse oemsResponse = OemsResponseFactory.createBasedOnExecution(orderState, childExecutionReport).toBuilder()
                .setExecType(OemsExecType.CALCULATED)
                .build();
        emitOemsResponse(oemsResponse);
    }

    private void removeUnknownQuantityForChildExecution(OemsResponse childExecutionReport, OrderState orderState) {
        String childOrderId = childExecutionReport.getOrderId();
        BigDecimal childQty = new BigDecimal(childExecutionReport.getLastQty());
        orderState.subtractUnknownOrderQuantity(childOrderId, childQty);
        LOGGER.info("Smart order {}: child order {} received status {}. Marking order quantity of {} as not unknown anymore.",
            orderState.getOemsRequest().getOrderId(), childOrderId, childExecutionReport.getOrderStatus(), childQty);
        updateSmartOrderState(orderState);
    }

    protected void cancelRejectSmartOrder(OemsRequest smartOrder, String reason) {
        emitOemsResponse(createCancelRejectGenericError(smartOrder, reason));
    }

    protected void handleDuplicateOrderState(OemsRequest request, String reason) {
        SmartOrderState smartOrderState = orderCache.find(request.getOrderId());
        if (smartOrderState != null) {
            OrderState state = new OrderState(smartOrderState);
            emitOemsResponse(createRejectAsDuplicate(state, reason));
        } else {
            throw new FailureRequeueException("Inconsistent cache state. Re-queueing...");
        }
    }

    protected void rejectSmartOrderAsDuplicate(OrderState orderState, String reason) {
        rejectSmartOrder(orderState, reason);
        emitOemsResponse(createRejectAsDuplicate(orderState, reason));
    }

    protected void handleOtherNewOrderException(OemsRequest request, Exception ex) {
        emitOemsResponse(createRejectAsValidationError(request, ex.getMessage()));
    }

    protected void cancelSmartOrderWrongState(OemsRequest request, OrderState orderState) {
        String requestId = request.getMetadata().getRequestId();
        String requesterId = request.getMetadata().getRequesterId();
        emitOemsResponse(createCancelRejectWrongState(requestId, requesterId, orderState));
    }

    private void fetchRoutingCandidates(OemsRequest smartOrder, OrderState orderState) {
        List<InstrumentKey> candidates = recommendationService.findCandidatesForOrder(smartOrder);
        orderState.setRoutingCandidates(new ArrayList<>(candidates));
    }

    private void rejectSmartOrderAsValidationError(OemsRequest smartOrder, OrderState orderState, String reason) {
        rejectSmartOrder(orderState, reason);
        emitOemsResponse(createRejectAsValidationError(smartOrder, reason));
    }

    void updateSmartOrderState(OrderState orderState) {
        if (orderState != null && orderState.isDirty()) {
            SmartOrderState smartOrderState = orderState.toSmartOrderState();
            orderCache.update(smartOrderState);
            orderState.incSequenceNum();
            orderState.setDirty(false);
        } else {
            LOGGER.debug("OrderState clean - skipping update");
        }
    }

    void addSmartOrderState(OrderState orderState) {
        SmartOrderState smartOrderState;
        if (orderState != null && orderState.isDirty()) {
            smartOrderState = orderState.toSmartOrderState();
            orderCache.add(smartOrderState);
            orderState.incSequenceNum();
            orderState.setDirty(false);
        } else {
            LOGGER.debug("OrderState clean - skipping add");
        }
    }

    private void suspendCandidate(OemsRequest smartOrder, OrderState orderState, InstrumentKey instrumentKey, String reason) {
        LOGGER.info("Smart order {}: candidate {}, {} suspended for {} milliseconds. Reason: {}.",
            smartOrder.getOrderId(), instrumentKey.getInstrumentId(), instrumentKey.getVenueAccount(), smartOrderCandidateSuspensionPeriod, reason);

        ZonedDateTime candidateSuspensionTimeout = ZonedDateTime.now().plus(smartOrderCandidateSuspensionPeriod, ChronoUnit.MILLIS);
        candidateSuspensionTimeoutScheduler.scheduleCandidateSuspensionTimeout(instrumentKey, smartOrder.getOrderId(), candidateSuspensionTimeout);
        orderState.suspendCandidate(instrumentKey);
    }

    private void handleChildCompleted(OemsResponse childExecutionReport, OrderState orderState) {
        LOGGER.info("Smart order {}: child order {} received status {}.",
            orderState.getOemsRequest().getOrderId(), childExecutionReport.getOrderId(), childExecutionReport.getOrderStatus());

        if (orderState.isSmartOrderCompleted()) {
            return;
        }

        completeChildOrder(childExecutionReport, orderState);

        OemsRequest smartOrder = orderState.getOemsRequest();
        if (orderState.hasOrderStatuses(StatusPartialFill.create())) {
            LOGGER.info("Smart order {} is partially filled and child order {} status is {}. Canceling remaining quantity ({}) of order to prevent execution on multiple venues.",
                smartOrder.getOrderId(), childExecutionReport.getOrderId(), childExecutionReport.getExecType(), orderState.getRemainingQuantity());
            handleInternalSmartOrderCancel(smartOrder, orderState, "Prevent execution on multiple venues");
        }
    }

    private boolean limitOrder(OrderState orderState) {
        OemsRequest oemsRequest = orderState.getOemsRequest();
        return RequestValidator.requiresPrice(oemsRequest.getOrderType());
    }

    private void completeChildOrder(OemsResponse childExecutionReport, OrderState orderState) {
        String childOrderId = childExecutionReport.getOrderId();
        orderState.markChildOrderCompleted(childOrderId);
        removeUnknownState(childExecutionReport, orderState);
    }

    private void removeUnknownState(OemsResponse childExecutionReport, OrderState orderState) {
        String childOrderId = childExecutionReport.getOrderId();
        BigDecimal unknownQuantity = orderState.removeUnknownOrderState(childOrderId);
        boolean statusWasUnknown = unknownQuantity != null;
        if (statusWasUnknown) {
            LOGGER.info("Smart order {}: child order {} received status {}. Marking order quantity of {} as not unknown anymore.",
                orderState.getOemsRequest().getOrderId(), childOrderId, childExecutionReport.getOrderStatus(), unknownQuantity);
        }
    }

    private void fetchExecutionRecommendations(OemsRequest smartOrder, OrderState orderState) {
        String orderId = smartOrder.getOrderId();

        CompletableFuture<ExecutionRecommendations> bestExecutionRecommendationsFuture = recommendationService
            .findBestExecutionRecommendations(
                smartOrder,
                orderState,
                getAvailableRoutingCandidates(orderState, true),
                this::updateSmartOrderState
            );

        orderState.setOngoingExecutionRecommendationsRequest(bestExecutionRecommendationsFuture);

        bestExecutionRecommendationsFuture.thenAccept(executionRecommendations ->
            {
                Optional<OrderState> currentStateOpt = getCurrentOrderState(orderId);

                if (currentStateOpt.isEmpty()) {
                    LOGGER.info("OrderState not found for orderId: {}", orderId);
                    return;
                }

                OrderState currentState = currentStateOpt.get();

                if (currentState.isSmartOrderCompletedOrPendingExpire()) {
                    LOGGER.info("Smart order {} is already completed or expired, skipping execution recommendations processing.", orderId);
                    return;
                }

                List<ExecutionRecommendation> recommendationsList = executionRecommendations.getRecommendationsList();
                if (recommendationsList.isEmpty()) {
                    handleNoRecommendationAvailable(smartOrder, currentState);
                    return;
                }

                List<ExecutionRecommendation> recommendations = Lists.newArrayList(recommendationsList);
                LOGGER.info("Smart order {}: received {} execution recommendations:\n{}", orderId, recommendations.size(), recommendations);

                currentState.setExecutionRecommendations(recommendations);

                if (validateProspectCandidatesForSmartOrder(smartOrder, currentState)) {
                    pickBestCandidateAndSendChildOrder(smartOrder, currentState);
                }
            });
    }

    private Optional<OrderState> getCurrentOrderState(String orderId) {
        SmartOrderState smartOrderState = orderCache.find(orderId);
        if (smartOrderState == null) {
            return Optional.empty();
        }
        return Optional.of(new OrderState(smartOrderState));
    }

    private void handleNoCandidatesAvailable(OemsRequest smartOrder, OrderState orderState) {
        if (limitOrder(orderState) && orderState.areAllCandidatesSuspended()) {
            LOGGER.info("Smart order {}: all available candidates are suspended, smart order will retry after suspension timeout.", smartOrder.getOrderId());
        } else {
            LOGGER.info("Smart order {}: no candidates are available, canceling smart order.", smartOrder.getOrderId());
            handleInternalSmartOrderCancel(smartOrder, orderState, "No candidates available.");
        }
    }

    private void pickBestCandidateAndSendChildOrder(OemsRequest smartOrder, OrderState orderState) {
        // create audit context basing on executionRecommendations
        AuditContext auditContext = new AuditContext(orderState.getExecutionRecommendations());

        Optional<ExecutionRecommendationWithTIF> smartRoutingCandidateOpt = orderState.getBestSmartRoutingCandidate(smartRoutingCandidate -> {
            if (isAccountLiveForTrading(smartOrder, smartRoutingCandidate, auditContext)) {
                return resolveTIF(smartOrder, smartRoutingCandidate, auditContext);
            }
            return Optional.empty();
        });

        if (smartRoutingCandidateOpt.isPresent()) {
            ExecutionRecommendation smartRoutingCandidate = smartRoutingCandidateOpt.get().recommendation();
            OemsTIF tif = smartRoutingCandidateOpt.get().tif();

            LOGGER.info("Smart order {}: best candidate: {}, {}.", smartOrder.getOrderId(),
                    smartRoutingCandidate.getInstrument().getInstrumentId(), smartRoutingCandidate.getInstrument().getVenueAccount());
            OemsRequest childOrder = sendChildOrder(smartOrder, orderState, smartRoutingCandidate, tif);
            orderState.addPendingNewChild(childOrder);

            auditContext.setStatusPicked(smartRoutingCandidate);
            auditService.sendAuditEventWithRecommendations(auditContext, smartOrder.getOrderId(), orderState.getRecommendationSubscriptionId());
            scheduleSubmissionCheck(childOrder, orderState);
        } else {
            handleNoRecommendationAvailable(smartOrder, orderState);
        }
        updateSmartOrderState(orderState);
    }

    private void handleNoRecommendationAvailable(OemsRequest smartOrder, OrderState orderState) {
        LOGGER.info("Smart order {}: no candidates available.", smartOrder.getOrderId());

        if (limitOrder(orderState)) {
            if (orderState.areAllCandidatesSuspended()) {
                LOGGER.info("Smart order {}: all available candidates are suspended, " +
                        "smart order will retry after suspension timeout.", smartOrder.getOrderId());
            } else {
                scheduleOrderRetry(smartOrder, orderState);
            }
        } else {
            handleSmartMarketOrderRetry(smartOrder, orderState);
        }
    }

    private Optional<ExecutionRecommendationWithTIF> resolveTIF(OemsRequest smartOrder, ExecutionRecommendation smartRoutingCandidate, AuditContext auditContext) {
        String venueAccount = smartRoutingCandidate.getInstrument().getVenueAccount();
        Optional<OemsTIF> resolvedTif = tifResolver.resolve(smartOrder, venueAccount);
        if (resolvedTif.isEmpty()) {
            LOGGER.info("Smart order {}: child order TIF can't be resolved for venue account {}, skipping candidate: {}, {}",
                    smartOrder.getOrderId(), venueAccount, smartRoutingCandidate.getInstrument().getInstrumentId(), smartRoutingCandidate.getInstrument().getVenueAccount());
            auditContext.setStatusTifNotResolvable(smartRoutingCandidate);
            return Optional.empty();
        }
        return resolvedTif
            .map(tif -> new ExecutionRecommendationWithTIF(smartRoutingCandidate, tif));
    }

    private boolean isAccountLiveForTrading(OemsRequest smartOrder, ExecutionRecommendation smartRoutingCandidate, AuditContext auditContext) {
        String venueAccount = smartRoutingCandidate.getInstrument().getVenueAccount();
        boolean tradingAlive = connectorStateService.isTradingAlive(venueAccount);
        if (tradingAlive) {
            LOGGER.debug("Smart order {}: venue account {} is live for trading", smartOrder.getOrderId(), venueAccount);
        } else {
            LOGGER.info("Smart order {}: venue account {} is not live for trading, skipping candidate: {}, {}",
                    smartOrder.getOrderId(), venueAccount, smartRoutingCandidate.getInstrument().getInstrumentId(), smartRoutingCandidate.getInstrument().getVenueAccount());
            auditContext.setStatusAccountNotAlive(smartRoutingCandidate);
        }
        return tradingAlive;
    }

    private List<InstrumentKey> getAvailableRoutingCandidates(OrderState orderState, boolean log) {
        List<InstrumentKey> availableCandidates = new ArrayList<>(orderState.getAvailableRoutingCandidates());
        availableCandidates.removeIf(candidate -> {
            boolean suspended = orderState.isCandidateSuspended(candidate);
            if (log && suspended) {
                LOGGER.info("Smart order {}: candidate {}, {} is suspended.",
                        orderState.getOemsRequest().getOrderId(), candidate.getInstrumentId(), candidate.getVenueAccount());
            }
            return suspended;
        });

        return availableCandidates;
    }

    private OemsRequest sendChildOrder(OemsRequest parentRequest, OrderState orderState, OemsResponse oemsResponse) {
        return sendChildOrder(parentRequest, orderState, oemsResponse.getInstrumentId(), oemsResponse.getVenueAccount(), oemsResponse.getOrderQty(), oemsResponse.getTif());
    }

    private OemsRequest sendChildOrder(OemsRequest parentRequest, OrderState orderState, ExecutionRecommendation smartRoutingCandidate, OemsTIF tif) {
        return sendChildOrder(parentRequest, orderState, smartRoutingCandidate.getInstrument().getInstrumentId(), smartRoutingCandidate.getInstrument().getVenueAccount(),
            smartRoutingCandidate.getQuantity(), tif);
    }

    private OemsRequest sendChildOrder(OemsRequest smartOrder, OrderState orderState, String instrumentId, String vanueAccount, String quantity, OemsTIF tif) {
        OemsRequest childOrder = createChildOrder(smartOrder, instrumentId, vanueAccount, quantity, tif);
        String childOrderId = childOrder.getOrderId();
        LOGGER.info("Smart order {}: sending child order {}.", smartOrder.getOrderId(), childOrder.getOrderId());
        emitOemsRequest(childOrder);
        orderState.markOrderStateUnknown(childOrderId, new BigDecimal(childOrder.getQuantity()));
        LOGGER.info("Smart order {}: marking child order {} state as unknown (current total quantity in unknown state {}).",
            smartOrder.getOrderId(), childOrderId, orderState.getTotalUnknownQuantity());
        return childOrder;
    }

    private void scheduleSubmissionCheck(OemsRequest childOrder, OrderState orderState) {
        String smartOrderId = orderState.getOemsRequest().getOrderId();
        String childOrderId = childOrder.getOrderId();
        if (childOrderSubmissionGracePeriod > 0) {
            ZonedDateTime submissionCheckTime = ZonedDateTime.now().plus(childOrderSubmissionGracePeriod, ChronoUnit.MILLIS);
            submissionCheckScheduler.schedule(childOrderId, smartOrderId, submissionCheckTime);
            LOGGER.info("Smart order {}: scheduled submission check for child order {} in {} ms. In case child order will not get submitted by this time, it will be canceled.",
                    smartOrderId, childOrderId, childOrderSubmissionGracePeriod);
        } else {
            LOGGER.info("Smart order {}: submission check for child order {} skipped, grace period {} is not > 0.",
                    smartOrderId, childOrderId, childOrderSubmissionGracePeriod);
        }
        updateSmartOrderState(orderState);
    }

    private void scheduleExecutionCheck(String childOrderId, OrderState orderState) {
        String smartOrderId = orderState.getOemsRequest().getOrderId();
        if (childOrderExecutionGracePeriod > 0) {
            ZonedDateTime submissionCheckTime = ZonedDateTime.now().plus(childOrderExecutionGracePeriod, ChronoUnit.MILLIS);
            executionCheckScheduler.schedule(childOrderId, smartOrderId, submissionCheckTime);
            LOGGER.info("Smart order {}: scheduled execution check for child order {} in {} ms. In case child order will not get any executions by this time, it will be canceled.",
                    smartOrderId, childOrderId, childOrderExecutionGracePeriod);
        } else {
            LOGGER.info("Smart order {}: execution check for child order {} skipped, grace period {} is not > 0.",
                    smartOrderId, childOrderId, childOrderExecutionGracePeriod);
        }
        updateSmartOrderState(orderState);
    }

    void handleSubmissionCheck(String childOrderId, OrderState orderState) {
        String smartOrderId = orderState.getOemsRequest().getOrderId();
        LOGGER.info("Smart order {}: running a submission check for child order {}.", smartOrderId, childOrderId);
        Optional<OemsRequest> pendingChildOrder = orderState.getPendingNewChildOrder(childOrderId);
        if (pendingChildOrder.isPresent()) {
            LOGGER.info("Smart order {}: child order {} has not been reported as submitted within {} ms after sending, canceling child order.",
                    smartOrderId, childOrderId, childOrderSubmissionGracePeriod);
            cancelChildOrder(pendingChildOrder.get(), orderState);
            updateSmartOrderState(orderState);
        } else {
            LOGGER.info("Smart order {}: submission check for child order {} finished, child has been submitted.", smartOrderId, childOrderId);
        }
    }

    void handleExecutionCheck(String childOrderId, OrderState orderState) {
        String smartOrderId = orderState.getOemsRequest().getOrderId();
        LOGGER.info("Smart order {}: running an execution check for child order {}.", smartOrderId, childOrderId);
        Optional<OemsRequest> childOrderWithFill = orderState.getOrderWithFills(childOrderId);
        if (childOrderWithFill.isPresent()) {
            LOGGER.info("Smart order {}: execution check for child order {} finished, child already has some executions.", smartOrderId, childOrderId);
            return;
        }

        Optional<OemsRequest> activeChildOrder = orderState.getActiveChildOrder(childOrderId);
        if (activeChildOrder.isPresent()) {
            LOGGER.info("Smart order {}: child order {} hasn't got any executions within {} ms after submitting, canceling child order.",
                    smartOrderId, childOrderId, childOrderExecutionGracePeriod);
            // TODO-abac this shouldn't probably be here, check for sure if this is needed
            // orderState.setStatusPendingCancel(activeChildOrder.get().getOrderId());
            cancelChildOrder(activeChildOrder.get(), orderState);
            updateSmartOrderState(orderState);
        } else {
            LOGGER.info("Smart order {}: execution check for child order {} finished, child already completed.", smartOrderId, childOrderId);
        }
    }

    private void cancelChildOrder(OemsRequest childOrder, OrderState orderState) {
        cancelChildOrder(childOrder, orderState, true);
    }

    private void cancelChildOrder(OemsRequest childOrder, OrderState orderState, boolean addToPendingCancel) {
        LOGGER.info("Smart order {}: trying to cancel child order {}.", orderState.getOemsRequest().getOrderId(), childOrder.getOrderId());

        int childCancelRetryCount = orderState.incrementChildCancelRetryCount(childOrder.getOrderId());
        if (childCancelRetryCount > childOrderCancellationMaxRetries) {
            LOGGER.info("Smart order {}: max retry count ({}) for canceling child order {} reached. User should check child order manually",
                    orderState.getOemsRequest().getOrderId(), childOrderCancellationMaxRetries, childOrder.getOrderId());
        } else {
            LOGGER.info("Smart order {}: canceling child order {} retry take {}/{}.", orderState.getOemsRequest().getOrderId(), childOrder.getOrderId(),
                childCancelRetryCount, childOrderCancellationMaxRetries);
            emitOemsRequest(createChildCancel(childOrder, orderState.isForceCancel()));
            if (addToPendingCancel) {
                orderState.addPendingCanceledChild(childOrder);
            }
            orderState.setDirty(true);
            scheduleCancellationCheck(childOrder, orderState);
        }
        updateSmartOrderState(orderState);
    }

    private void scheduleCancellationCheck(OemsRequest childOrder, OrderState orderState) {
        String smartOrderId = orderState.getOemsRequest().getOrderId();
        String childOrderId = childOrder.getOrderId();
        if (childOrderCancellationGracePeriod > 0) {
            ZonedDateTime cancellationCheckTime = ZonedDateTime.now().plus(childOrderCancellationGracePeriod, ChronoUnit.MILLIS);
            cancellationCheckScheduler.schedule(childOrderId, smartOrderId, cancellationCheckTime);
            LOGGER.info("Smart order {}: scheduled cancellation check for child order {} in {} ms.",
                    smartOrderId, childOrderId, childOrderCancellationGracePeriod);
        } else {
            LOGGER.info("Smart order {}: cancellation check for child order {} skipped, grace period {} is not > 0.",
                    smartOrderId, childOrderId, childOrderCancellationGracePeriod);
        }
        updateSmartOrderState(orderState);
    }

    void handleCancellationCheck(String childOrderId, OrderState orderState) {
        String smartOrderId = orderState.getOemsRequest().getOrderId();
        LOGGER.info("Smart order {}: running a cancellation check for child order {}.", smartOrderId, childOrderId);
        Optional<OemsRequest> pendingChildOrder = orderState.getPendingCanceledChildOrder(childOrderId);
        if (pendingChildOrder.isPresent()) {
            LOGGER.info("Smart order {}: child order {} has not been reported as canceled within {} ms after canceling.",
                    smartOrderId, childOrderId, childOrderCancellationGracePeriod);
            cancelChildOrder(pendingChildOrder.get(), orderState, false);
            updateSmartOrderState(orderState);
        } else {
            LOGGER.info("Smart order {}: cancellation check for child order {} finished, child has been canceled.", smartOrderId, childOrderId);
        }
    }

    private void handleSmartMarketOrderRetry(OemsRequest smartOrder, OrderState orderState) {
        String smartOrderId = smartOrder.getOrderId();
        int retryCount = orderState.incrementSmartOrderRetryCount();
        if (retryCount > smartOrderMaxRetries) {
            LOGGER.info("Smart order {}: max retry count ({}) reached, canceling order.", smartOrderMaxRetries, smartOrderId);
            handleInternalSmartOrderCancel(smartOrder, orderState, "Max retries count reached");
        } else {
            LOGGER.info("Smart order {}: retry take scheduled {}/{}.", smartOrderId, retryCount, smartOrderMaxRetries);
            scheduleOrderRetry(smartOrder, orderState);
        }
        updateSmartOrderState(orderState);
    }

    private void sendNextChildOrder(OemsRequest request, OrderState orderState) {
        LOGGER.info("Smart order {}: trying next best candidate.", request.getOrderId());
        pickBestCandidateAndSendChildOrder(request, orderState);
    }

    private void scheduleOrderRetry(OemsRequest smartOrder, OrderState orderState) {
        if (!orderState.isRetryScheduled()) {
            orderState.setRetryScheduled(true);
            ZonedDateTime retryTimeout = ZonedDateTime.now().plus(retrySuspensionTimeout, ChronoUnit.MILLIS);
            smartOrderRetryScheduler.scheduleOrderRetry(smartOrder.getOrderId(), retryTimeout);
            LOGGER.info("Smart order {}: retry scheduled in {} milliseconds.", smartOrder.getOrderId(), retrySuspensionTimeout);
        } else {
            LOGGER.info("Smart order {}: order retry already scheduled.", smartOrder.getOrderId());
        }
    }

    public void handleSmartOrderRetry(OrderState orderState) {
        OemsRequest smartOrder = orderState.getOemsRequest();
        orderState.setRetryScheduled(false);
        if (orderState.isSmartOrderIdle()) {
            retrySmartOrder(smartOrder, orderState);
        }
    }

    private void retrySmartOrder(OemsRequest request, OrderState orderState) {
        // additional check to prevent sending multiple child orders
        if (tooManyChildOrders(request, orderState)) {
            return;
        }

        if (getAvailableRoutingCandidates(orderState, false).isEmpty()) {
            handleNoCandidatesAvailable(request, orderState);
        } else if (allRemainingQuantityInUnknownState(orderState)) {
            LOGGER.info("Smart order {}: all remaining quantity is in unknown state, " +
                    "no more child orders will be sent to prevent overfills.", request.getOrderId());
        } else {
            LOGGER.info("Smart order {}: retry started.", request.getOrderId());
            fetchExecutionRecommendations(request, orderState);
        }
    }

    private boolean tooManyChildOrders(OemsRequest request, OrderState orderState) {
        List<OemsRequest> activeChildOrders = orderState.getActiveChildOrders();

        long uniqueVenueAccounts = activeChildOrders.stream()
                .map(OemsRequest::getVenueAccount)
                .distinct()
                .count();

        if (uniqueVenueAccounts == orderState.getRoutingCandidates().size()) {
            LOGGER.info("Smart order {}: there are {} pending active child orders for all routing candidates, " +
                    "no more child orders will be sent to prevent overfills.", request.getOrderId(), uniqueVenueAccounts);
            return true;
        }

        return false;
    }

    private boolean allRemainingQuantityInUnknownState(OrderState orderState) {
        return orderState.getRemainingQuantity().subtract(orderState.getTotalUnknownQuantity()).compareTo(BigDecimal.ZERO) <= 0;
    }

    private void rejectSmartOrder(OrderState orderState, String reason) {
        LOGGER.info("Smart order {}: rejecting, reason: {}", orderState.getOemsRequest().getOrderId(), reason);
        orderState.setDescription(reason);
        orderState.setStatusRejected();
        updateSmartOrderState(orderState);
    }

    protected void emitOemsResponse(OemsResponse response) {
        oemsResponseEmitter.emit(response);
    }

    protected void emitOemsRequest(OemsRequest request) {
        oemsRequestEmitter.emit(request);
    }

    private boolean validateProspectCandidatesForSmartOrder(OemsRequest smartOrder, OrderState orderState) {
        List<ExecutionRecommendation> validatedCandidates = new ArrayList<>();
        for (ExecutionRecommendation candidate : orderState.getExecutionRecommendations()) {
            Instrument instrument = instrumentsRepository.find(candidate.getInstrument().getInstrumentId());

            ExecutionRecommendation adjustedCandidate = candidate;
            if (limitOrder(orderState)) {
                // for limit orders, if candidate has min executable quantity
                // send full qty to that candidate anyway to prevent executions on multiple exchanges
                // else suspend the candidate and skip it
                try {
                    validateMinExecutableQuantity(smartOrder, minExecutableQtyPercentage, instrument, candidate, smartOrder.getOrderId());
                    adjustedCandidate = candidate.toBuilder().setQuantity(smartOrder.getQuantity()).build();
                } catch (ValidationException e) {
                    suspendCandidate(smartOrder, orderState, candidate.getInstrument(), e.getMessage());
                    continue;
                }
            }

            try {
                ExecutionRecommendation validatedCandidate = validateProspectCandidate(adjustedCandidate, instrument, smartOrder);
                validatedCandidates.add(validatedCandidate);
            } catch (ValidationException e) {
                LOGGER.info("Smart order {}: candidate {} excluded from smart order, reason: {}.", smartOrder.getOrderId(), candidate, e.getMessage());
                orderState.excludeCandidate(candidate.getInstrument());
            }
        }

        if (orderState.getAvailableRoutingCandidates().isEmpty()) {
            // all candidates excluded
            LOGGER.info("Order {}: all candidates failed validation, rejecting order.", smartOrder.getOrderId());
            rejectSmartOrderAsValidationError(smartOrder, orderState, "All candidates failed order validation.");
        } else if (validatedCandidates.isEmpty()) {
            // not all candidates excluded, but no candidate has min executable quantity (limit / stop limit order)
            LOGGER.info("Order {}: no candidate has min executable quantity, retrying order.", smartOrder.getOrderId());
            scheduleOrderRetry(smartOrder, orderState);
        } else {
            // candidates available
            orderState.setExecutionRecommendations(validatedCandidates);
            return true;
        }

        return false;
    }

    private CompletableFuture cancelActiveChildOrders(OemsRequest smartOrder, OrderState orderState) {
        LOGGER.info("Smart order {}: canceling all active child orders.", smartOrder.getOrderId());

        List<OemsRequest> activeChildOrders = orderState.getActiveChildOrders();
        List<CompletableFuture> cancellationFutures = new ArrayList<>();
        activeChildOrders.forEach(childOrder -> {
            CompletableFuture<Void> childCancelFuture = new CompletableFuture<>();
            childCancelFutures.put(childOrder.getOrderId(), childCancelFuture);
            cancellationFutures.add(childCancelFuture);
            cancelChildOrder(childOrder, orderState);
        });

        return CompletableFuture.allOf(cancellationFutures.toArray(new CompletableFuture<?>[0]));
    }

    private ExecutionRecommendation validateProspectCandidate(ExecutionRecommendation candidate, Instrument instrument, OemsRequest smartOrder) throws ValidationException {
        String orderId = smartOrder.getOrderId();
        validateEmptyTradingConstraints(instrument, orderId);

        OemsOrderType orderType = smartOrder.getOrderType();

        if (orderType == LIMIT || orderType == STOP_LIMIT || orderType == STOP) {
            validateCandidatePrice(smartOrder, instrument, orderId);
        }

        if (orderType == MARKET || orderType == STOP) {
            validateCandidateQuantity(smartOrder, instrument, candidate, orderId);
        }

        if (orderType == LIMIT || orderType == STOP_LIMIT) {
            BigDecimal candidateAdjustedQuantity = adjustAndValidateCandidateQuantity(instrument, candidate, orderId);
            ExecutionRecommendation adjustedCandidate = candidate.toBuilder().setQuantity(candidateAdjustedQuantity.toPlainString()).build();
            return adjustedCandidate;
        }

        return candidate;
    }

    private InstrumentKey createInstrumentKey(String instrumentKey, String venueAccount) {
        return InstrumentKey.newBuilder()
                .setInstrumentId(instrumentKey)
                .setVenueAccount(venueAccount)
                .build();
    }

    private enum OtcRetryResult {
        ONGOING, DONE, UNDEFINED;
    }
}
