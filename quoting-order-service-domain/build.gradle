plugins {
    id 'idea'
    id 'maven-publish'
    alias dependencyCatalog.plugins.protobuf
    alias dependencyCatalog.plugins.sonarqube
}

def versionPropsFile = file('version.properties')
if (versionPropsFile.canRead()) {
    Properties versionProps = new Properties()
    versionProps.load(new FileInputStream(versionPropsFile))
    def ver = versionProps['VERSION'].toString()
    project.version = ver
} else {
    throw new GradleException("Could not read version.properties!")
}

ext {
    repository_username = System.env.NEXUS_DEPLOY_USERNAME
    repository_password = System.env.NEXUS_DEPLOY_PASSWORD
}

dependencies {
    implementation dependencyCatalog.published.language.oems
    implementation dependencyCatalog.cloud.utils.telemetry
    implementation dependencyCatalog.cloud.utils.hazelcast
    implementation dependencyCatalog.cloud.utils.tools

    implementation dependencyCatalog.protobuf.java
    implementation dependencyCatalog.hazelcast

    testImplementation dependencyCatalog.junit.jupiter.api

    testRuntimeOnly dependencyCatalog.junit.jupiter.engine
}

protobuf {
    protoc {
        // The artifact spec for the Protobuf Compiler
        artifact = dependencyCatalog.protobuf.protoc.get()
    }
}

sonarqube {
    properties {
        property "sonar.projectKey", "order-hedger-domain"
        property "sonar.projectName", "Quoting Order Service Domain"
    }
}

test {
    useJUnitPlatform()
}

publishing {
    publications {
        mavenJava(MavenPublication) {
            repositories {
                maven {
                    name 'nexus-snapshots'
                    url 'https://repo.wyden.io/nexus/repository/snapshots/'
                    credentials {
                        username repository_username
                        password repository_password
                    }
                }
            }
            from components.java
        }
    }
}
