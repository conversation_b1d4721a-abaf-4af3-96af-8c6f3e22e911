package io.wyden.referencedata.domain;

import io.wyden.cloudutils.hazelcast.HazelcastMapConfig;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.published.referencedata.PortfolioType;

import com.hazelcast.config.SerializationConfig;
import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import com.hazelcast.query.Predicate;
import com.hazelcast.query.Predicates;

import java.util.Collection;
import java.util.List;

import static io.wyden.cloudutils.hazelcast.Serializers.protobufSerializer;

public class PortfolioMapConfig extends HazelcastMapConfig {

    private static final String MAP_NAME = "reference-data-portfolios_0.1";

    public static IMap<String, Portfolio> getMap(HazelcastInstance hazelcastInstance) {
        return hazelcastInstance.getMap(MAP_NAME);
    }

    @Override
    public String getMapName() {
        return MAP_NAME;
    }

    public static Predicate<String, Portfolio> filterByTagValue(String tagValue) {
        return filterByTagValues(List.of(tagValue));
    }

    public static Predicate<String, Portfolio> filterByTagKeys(Collection<String> tagKeys) {
        return mapEntry -> tagKeys.stream()
            .anyMatch(tagKey -> mapEntry.getValue().getTagsMap().containsKey(tagKey));
    }

    public static Predicate<String, Portfolio> filterByTagValues(Collection<String> tagValues) {
        return mapEntry -> tagValues.stream()
            .anyMatch(tagValue -> mapEntry.getValue().getTagsMap().containsValue(tagValue));
    }

    public static Predicate<String, Portfolio> filterByTag(String tagKey, String tagValue) {
        return mapEntry -> {
            String tag = mapEntry.getValue().getTagsMap().get(tagKey);
            return tag != null && tag.equals(tagValue);
        };
    }

    public static Predicate<String, Portfolio> filterByPortfolioId(Collection<String> portfolioIds) {
        return Predicates.in(Field.PORTFOLIO_ID.getFieldCoordinate(), portfolioIds.toArray(String[]::new));
    }

    public static Predicate<String, Portfolio> filterByPortfolioName(String portfolioName) {
        return Predicates.in(IndexedField.NAME.getFieldCoordinate(), portfolioName);
    }

    public static Predicate<String, Portfolio> filterByMatchingEngineUid(long matchingEngineUid) {
        return Predicates.equal(Field.MATCHING_ENGINE_UID.getFieldCoordinate(), matchingEngineUid);
    }

    public static Predicate<String, Portfolio> filterByPortfolioType(PortfolioType portfolioType) {
        return Predicates.equal(Field.PORTFOLIO_TYPE.getFieldCoordinate(), portfolioType);
    }

    public static Predicate<String, Portfolio> filterByPortfolioNameContains(String name) {
        return Predicates.ilike(IndexedField.NAME.getFieldCoordinate(), "%%%s%%".formatted(name));
    }

    public static Predicate<String, Portfolio> filterByPortfolioIdContains(String id) {
        return Predicates.ilike(Field.PORTFOLIO_ID.getFieldCoordinate(), "%%%s%%".formatted(id));
    }

    public static Predicate<String, Portfolio> filterByPortfolioIdAfter(String id) {
        return Predicates.greaterThan(Field.PORTFOLIO_ID.getFieldCoordinate(), id);
    }

    public static Predicate<String, Portfolio> filterByPortfolioIdBefore(String id) {
        return Predicates.lessThan(Field.PORTFOLIO_ID.getFieldCoordinate(), id);
    }

    public static Predicate<String, Portfolio> filterByIsArchived(Boolean archived) {

        if (archived == null) {
            return Predicates.alwaysTrue();
        }

        if (Boolean.TRUE.equals(archived)){
            return Predicates.greaterThan(IndexedField.ARCHIVED_AT.getFieldCoordinate(), "");
        }

        return Predicates.equal(IndexedField.ARCHIVED_AT.getFieldCoordinate(), "");
    }

    public static Predicate<String, Portfolio> filterByNameAfterAndThenCreatedAtAfter(String name, String createdAt) {
        return Predicates.or(
            Predicates.and(
                Predicates.greaterThan(IndexedField.NAME.getFieldCoordinate(), name),
                Predicates.alwaysTrue()
            ),
            Predicates.and(
                Predicates.equal(IndexedField.NAME.getFieldCoordinate(), name),
                Predicates.greaterThan(Field.CREATED_AT.getFieldCoordinate(), createdAt)
            )
        );
    }

    public static Predicate<String, Portfolio> filterByNameBeforeAndThenCreatedAtBefore(String name, String createdAt) {
        return Predicates.or(
            Predicates.and(
                Predicates.lessThan(IndexedField.NAME.getFieldCoordinate(), name),
                Predicates.alwaysTrue()
            ),
            Predicates.and(
                Predicates.equal(IndexedField.NAME.getFieldCoordinate(), name),
                Predicates.lessThan(Field.CREATED_AT.getFieldCoordinate(), createdAt)
            )
        );
    }

    public static Predicate<String, Portfolio> filterByPortfolioNameContainsOrPortfolioIdContains(String symbol) {
        return Predicates.or(
            Predicates.ilike(IndexedField.NAME.getFieldCoordinate(), "%%%s%%".formatted(symbol)),
            Predicates.ilike(Field.PORTFOLIO_ID.getFieldCoordinate(), "%%%s%%".formatted(symbol))
        );
    }

    public static Predicate<String, Portfolio> filterByCreatedAtAfter(String dateTime) {
        return Predicates.greaterThan(Field.CREATED_AT.getFieldCoordinate(), dateTime);
    }

    public static Predicate<String, Portfolio> filterByCreatedAtBefore(String dateTime) {
        return Predicates.lessThan(Field.CREATED_AT.getFieldCoordinate(), dateTime);
    }

    @Override
    protected void addSerializersConfig(SerializationConfig serializationConfig) {
        serializationConfig.addSerializerConfig(protobufSerializer(Portfolio.class, Portfolio.parser()));
    }

    @Override
    protected HazelcastMapConfig.HazelcastIndex[] getIndexes() {
        return IndexedField.values();
    }

    private enum Field {

        PORTFOLIO_ID("id_"),
        CREATED_AT("createdAt_"),
        PORTFOLIO_CURRENCY("portfolioCurrency_"),
        PORTFOLIO_TYPE("portfolioType"),
        MATCHING_ENGINE_UID("matchingEngineUid_");

        private final String fieldCoordinate;

        Field(String fieldCoordinate) {
            this.fieldCoordinate = fieldCoordinate;
        }

        String getFieldCoordinate() {
            return fieldCoordinate;
        }

    }

    private enum IndexedField implements HazelcastIndex {

        NAME("name_", HASH_INDEX),
        ARCHIVED_AT("archivedAt_", HASH_INDEX),
        MATCHING_ENGINE_UID("matchingEngineUid_", HASH_INDEX);

        private final String fieldCoordinate;
        private final String indexType;

        IndexedField(String fieldCoordinate, String indexType) {
            this.fieldCoordinate = fieldCoordinate;
            this.indexType = indexType;
        }

        @Override
        public String getFieldCoordinate() {
            return fieldCoordinate;
        }

        @Override
        public String getIndexType() {
            return indexType;
        }
    }
}
