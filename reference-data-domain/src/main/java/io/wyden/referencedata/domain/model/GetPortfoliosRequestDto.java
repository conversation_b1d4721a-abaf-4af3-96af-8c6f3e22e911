package io.wyden.referencedata.domain.model;

import io.wyden.published.referencedata.Portfolio;
import io.wyden.published.referencedata.PortfolioType;

import java.io.Serializable;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import javax.annotation.Nullable;

public record GetPortfoliosRequestDto(
    PortfolioPredicateDto portfolioPredicate,
    List<String> portfolioIds,
    List<String> tagKeys,
    List<String> tagValues,
    PortfolioType portfolioType,
    Boolean archived,
    @Nullable SortBy sortBy, // defaults to SortBy.PORTFOLIO_NAME if null
    SortingOrder sortingOrder,
    Integer first,
    String after
) {

    public Comparator<Map.Entry<String, Portfolio>> sortingComparator() {
        Comparator<Map.Entry<String, Portfolio>> comparator;
        if (sortBy == null || sortBy == SortBy.PORTFOLIO_NAME) {
            comparator = new CompareByPortfolioName();
        } else if (sortBy == SortBy.PORTFOLIO_ID) {
            comparator = new CompareByPortfolioId();
        } else if (sortBy == SortBy.CREATED_AT) {
            comparator = new CompareByCreatedAt();
        } else {
            throw new IllegalArgumentException("Unknown SortBy: " + sortBy);
        }

        if (sortingOrder == null || sortingOrder == SortingOrder.ASC) {
            return comparator;
        }

        return comparator.reversed();
    }

    public int pageSize() {
        return first() != null ? first() : Integer.MAX_VALUE;
    }

    public enum SortBy {
        PORTFOLIO_NAME,
        PORTFOLIO_ID,
        CREATED_AT
    }

    private static class CompareByPortfolioName implements Comparator<Map.Entry<String, Portfolio>>, Serializable {

        @Override
        public int compare(Map.Entry<String, Portfolio> entry1, Map.Entry<String, Portfolio> entry2) {
            String name1 = entry1.getValue().getName();
            String name2 = entry2.getValue().getName();
            int result = name1.compareTo(name2);
            if (result == 0) {
                return entry1.getValue().getCreatedAt().compareTo(entry2.getValue().getCreatedAt());
            }
            return result;
        }
    }

    private static class CompareByPortfolioId implements Comparator<Map.Entry<String, Portfolio>>, Serializable {

        @Override
        public int compare(Map.Entry<String, Portfolio> entry1, Map.Entry<String, Portfolio> entry2) {
            String portfolioId1 = entry1.getValue().getId();
            String portfolioId2 = entry2.getValue().getId();
            return portfolioId1.compareTo(portfolioId2);
        }
    }

    private static class CompareByCreatedAt implements Comparator<Map.Entry<String, Portfolio>>, Serializable {

        @Override
        public int compare(Map.Entry<String, Portfolio> entry1, Map.Entry<String, Portfolio> entry2) {
            return entry1.getValue().getCreatedAt().compareTo(entry2.getValue().getCreatedAt());
        }
    }
}
