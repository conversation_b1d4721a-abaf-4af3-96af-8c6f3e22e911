INSERT INTO wyden_permission(resource, scope) VALUES ('connector', 'read');
INSERT INTO wyden_group_wyden_permission(wyden_group_id, wyden_permission_id)
SELECT wg.id,wp.id FROM wyden_group wg
    JOIN wyden_permission wp ON wp.resource =  'connector' and wp.scope = 'read'
WHERE wg.name = 'administrators';

INSERT INTO wyden_permission(resource, scope) VALUES ('connector', 'manage');
INSERT INTO wyden_group_wyden_permission(wyden_group_id, wyden_permission_id)
SELECT wg.id,wp.id FROM wyden_group wg
    JOIN wyden_permission wp ON wp.resource =  'connector' and wp.scope = 'manage'
WHERE wg.name = 'administrators';

INSERT INTO wyden_permission(resource, scope) VALUES ('connector', 'create');
INSERT INTO wyden_group_wyden_permission(wyden_group_id, wyden_permission_id)
SELECT wg.id,wp.id FROM wyden_group wg
    JOIN wyden_permission wp ON wp.resource =  'connector' and wp.scope = 'create'
WHERE wg.name = 'administrators';
