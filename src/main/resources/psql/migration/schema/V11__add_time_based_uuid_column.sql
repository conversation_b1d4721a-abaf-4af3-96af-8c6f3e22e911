-- Add time_based_uuid to order_events table
ALTER TABLE order_events
    ADD COLUMN time_based_uuid TEXT;

-- Update existing records with a time-based UUID
UPDATE order_events
SET time_based_uuid =
        CONCAT(
                LPAD(TO_HEX(EXTRACT(EPOCH FROM message_timestamp)::BIGINT & x'FFFFFFFF'::BIGINT), 8, '0'),
                '-',
                LPAD(TO_HEX((EXTRACT(EPOCH FROM message_timestamp)::BIGINT >> 32) & x'FFFF'::BIGINT), 4, '0'),
                '-8',
                LPAD(TO_HEX((EXTRACT(EPOCH FROM message_timestamp)::BIGINT >> 48) & x'FFF'::BIGINT), 3, '0'),
                '-',
                LPAD(TO_HEX((id & x'3FFF'::BIGINT) | x'8000'::BIGINT), 4, '0'),
                '-',
                LPAD(TO_HEX(id), 12, '0')
        )
WHERE time_based_uuid IS NULL;

-- Make the column NOT NULL and add unique constraint
ALTER TABLE order_events
    ALTER COLUMN time_based_uuid SET NOT NULL,
    ADD CONSTRAINT uk_order_events_time_based_uuid UNIQUE (time_based_uuid);
