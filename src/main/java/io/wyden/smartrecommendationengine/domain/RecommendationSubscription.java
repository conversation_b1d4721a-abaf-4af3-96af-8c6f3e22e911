package io.wyden.smartrecommendationengine.domain;

import io.wyden.published.marketdata.InstrumentKey;

import java.util.List;
import java.util.concurrent.ScheduledFuture;

public class RecommendationSubscription {

    private final String recommendationSubscriptionId;

    private ScheduledFuture recommendationSubscriptionTimeout;

    private ScheduledFuture subscriptionHeartbeat;

    private final List<InstrumentKey> instruments;

    private boolean stopTriggered;

    public RecommendationSubscription(String recommendationSubscriptionId, ScheduledFuture recommendationSubscriptionTimeout, ScheduledFuture subscriptionHeartbeat, List<InstrumentKey> instruments) {
        this.recommendationSubscriptionId = recommendationSubscriptionId;
        this.recommendationSubscriptionTimeout = recommendationSubscriptionTimeout;
        this.subscriptionHeartbeat = subscriptionHeartbeat;
        this.instruments = instruments;
    }

    public String getRecommendationSubscriptionId() {
        return recommendationSubscriptionId;
    }

    public void cancelSubscriptionTasks() {
        subscriptionHeartbeat.cancel(false);
        recommendationSubscriptionTimeout.cancel(false);
    }

    public void refreshSubscriptionTimeout(ScheduledFuture recommendationSubscriptionTimeout) {
        this.recommendationSubscriptionTimeout.cancel(false);
        this.recommendationSubscriptionTimeout = recommendationSubscriptionTimeout;
    }

    public List<InstrumentKey> getInstruments() {
        return instruments;
    }

    public void setStopTriggered(boolean stopTriggered) {
        this.stopTriggered = stopTriggered;
    }

    public boolean isStopTriggered() {
        return stopTriggered;
    }
}
