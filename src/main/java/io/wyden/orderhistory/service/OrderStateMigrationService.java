package io.wyden.orderhistory.service;

import com.google.protobuf.Message;
import com.rabbitmq.client.AMQP;
import io.wyden.cloudutils.rabbitmq.ConsumptionResult;
import io.wyden.orderhistory.infrastructure.rabbit.TradingResponseMessageConsumer;
import io.wyden.orderhistory.model.OrderStateEntity;
import io.wyden.orderhistory.repository.PostgresOrderStateRepository;
import io.wyden.orderhistory.service.utils.OrderStateReverseMapper;
import io.wyden.published.client.ClientRequest;
import io.wyden.published.client.ClientResponse;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.event.Level;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Stream;

@Service
public class OrderStateMigrationService {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderStateMigrationService.class);
    private static final int BATCH_SIZE = 50;
    private static final String ORDER_CATEGORY_UNSPECIFIED = "ORDER_CATEGORY_UNSPECIFIED";
    private static final String SIMPLE = "SIMPLE";
    private static final String PENDING_NEW = "PENDING_NEW";
    private static final String NEW = "NEW";
    private static final String REJECTED = "REJECTED";
    private static final String ZERO = "0";

    private final PostgresOrderStateRepository orderStateRepository;
    private final TradingResponseMessageConsumer responseMessageConsumer;

    private Set<String> orderIdsToInvestigate = new HashSet<>();
    private ClientRequest currentOrderClientRequest = null;
    private OemsRequest currentOrderOemsRequest = null;

    // Message type resolution statistics
    private int totalErrors = 0;
    private int totalUnknown = 0;
    private int totalConfident = 0;
    private int totalTentative = 0;
    private int totalRequests = 0;
    private int totalResponses = 0;

    public OrderStateMigrationService(PostgresOrderStateRepository orderStateRepository,
                                      TradingResponseMessageConsumer responseMessageConsumer) {
        this.orderStateRepository = orderStateRepository;
        this.responseMessageConsumer = responseMessageConsumer;
    }

    public boolean migrateOrderStates() {
        LOGGER.info("Starting order state migration with message type resolution. Batch size: {}", BATCH_SIZE);

        // Reset statistics
        totalErrors = 0;
        totalUnknown = 0;
        totalConfident = 0;
        totalTentative = 0;
        totalRequests = 0;
        totalResponses = 0;

        int offset = 0;
        int totalProcessed = 0;

//        List<String> toInvestigate = readLatestOrderIdsToInvestigate();
//        List<String> orderIdBatch = toInvestigate.subList(offset, Math.min(offset + BATCH_SIZE, toInvestigate.size()));

//        List<String> orderIdBatch = new ArrayList<>();
//        orderIdBatch.add("6cddc95a-0318-4f4c-b8c8-bab65eaa93e8");
        while (true) {
            List<String> orderIdBatch = getOrderIdBatch(offset);

            if (orderIdBatch.isEmpty()) {
                LOGGER.info("Migration with message type resolution completed. Total orders processed: {}", totalProcessed);
                printFinalSummary(totalProcessed);
                break;
            }

            LOGGER.info("debug batch {} - {} (offset: {}) with message type resolution", offset + 1, offset + orderIdBatch.size(), offset);

            for (String orderId : orderIdBatch) {
                try {
                    migrateOrderWithMessageTypeResolution(orderId);
                    totalProcessed++;
                } catch (Exception e) {
                    LOGGER.error("Failed to migrate order with message type resolution: {}", orderId, e);
                    totalErrors++;
                }
            }
//            orderIdBatch.clear();

            offset += BATCH_SIZE;
            LOGGER.debug("Completed batch with message type resolution. Total processed so far: {}", totalProcessed);
        }
        return true;
    }

    private void migrateOrderWithMessageTypeResolution(String orderId) {
        LOGGER.debug("Starting message type resolution for order: {}", orderId);

        List<OrderStateEntity> orderStates = getAllOrderStatesForOrder(orderId);
        if (orderStates.isEmpty()) {
            LOGGER.warn("No order states found for order: {}", orderId);
            orderIdsToInvestigate.add(orderId);
            return;
        } else if (hasDuplicateSequenceNumbers(orderStates))  {
            LOGGER.warn("Order states contains duplicate sequence numbers for order: {}", orderId);
            orderIdsToInvestigate.add(orderId);
            return;
        }/* else if (hasSequenceGaps(orderStates)) {
            LOGGER.warn("Order states contains sequence gaps for order: {}", orderId);
            orderIdsToInvestigate.add(orderId);
            return;
        }*/
        else {
            LOGGER.debug("Found {} order states for order: {}", orderStates.size(), orderId);
        }

        int orderErrorCount = 0;
        int orderUnknownCount = 0;
        int orderConfidentCount = 0;
        int orderTentativeCount = 0;
        List<Message> messagesToConsume = new ArrayList<>();

        for (OrderStateEntity orderState : orderStates) {
            try {
                Message responseMessage;
                OrderStateMessageType result = resolveMessageType(orderState, orderStates);

                switch (result.getConfidenceLevel()) {
                    case CONFIDENT:
                        orderConfidentCount++;
                        totalConfident++;
                        logResolveResult(Level.DEBUG, "CONFIDENT resolution", orderId, orderState, result);
                        responseMessage = createResponseMessage(orderState, result);
                        messagesToConsume.add(responseMessage);
                        break;
                    case TENTATIVE:
                        orderTentativeCount++;
                        totalTentative++;
                        logResolveResult(Level.INFO, "TENTATIVE resolution", orderId, orderState, result);
                        responseMessage = createResponseMessage(orderState, result);
                        messagesToConsume.add(responseMessage);
                        break;
                    case UNKNOWN:
                        orderUnknownCount++;
                        totalUnknown++;
                        logResolveResult(Level.WARN, "UNKNOWN resolution", orderId, orderState, result);
                        break;
                }

            } catch (Exception e) {
                orderErrorCount++;
                totalErrors++;
                logResolveResult(Level.ERROR, "ERROR resolving message type", orderId, orderState, null);
                if (!orderIdsToInvestigate.contains(orderId)) {
                    orderIdsToInvestigate.add(orderId);
                }
            }
        }

        LOGGER.info("Order {} summary: {} confident, {} tentative, {} unknown, {} errors", orderId, orderConfidentCount, orderTentativeCount, orderUnknownCount, orderErrorCount);
        clearCurrentOrderMessages();
        LOGGER.debug("Completed message type resolution for order: {}", orderId);

        if (!messagesToConsume.isEmpty()) {
            LOGGER.info("Sending {} messages to consumer for order: {}", messagesToConsume.size(), orderId);
            messagesToConsume.forEach(this::sendMessageToConsumer);
        }
    }

    private boolean hasSequenceGaps(List<OrderStateEntity> orderStates) {
        if (orderStates.size() < 2) {
            return orderStates.getFirst().sequenceNumber() != 1;
        }
        for (int i = 1; i < orderStates.size(); i++) {
            if (orderStates.get(i).sequenceNumber() - orderStates.get(i - 1).sequenceNumber() > 1) {
                return true;
            }
        }
        return false;
    }

    private Message createResponseMessage(OrderStateEntity orderState, OrderStateMessageType resolveMessageTypeResult) {
        switch (resolveMessageTypeResult.getMessageType()) {
            case CLIENT_REQUEST -> {
                ClientRequest clientRequest = OrderStateReverseMapper.toClientRequest(orderState);
                setCurrentOrderClientRequest(clientRequest);

                ClientResponse.Builder clientResponseBuilder = OrderStateReverseMapper.toClientResponse(orderState);
                clientResponseBuilder.setRequest(clientRequest);

                return clientResponseBuilder.build();
            }
            case CLIENT_RESPONSE -> {
                ClientResponse.Builder clientResponseBuilder = OrderStateReverseMapper.toClientResponse(orderState);
                if (currentOrderClientRequest != null) {
                    LOGGER.trace("Embedding request in response: {}", currentOrderClientRequest);
                    clientResponseBuilder.setRequest(currentOrderClientRequest);
                } else {
                    LOGGER.warn("No request found for response: {}", orderState);
                    ClientRequest syntheticRequest = createSyntheticClientRequest(orderState);
                    clientResponseBuilder.setRequest(syntheticRequest);
                    orderIdsToInvestigate.add(orderState.orderId());
                }
                return clientResponseBuilder.build();
            }
            case OEMS_REQUEST -> {
                OemsRequest oemsRequest = OrderStateReverseMapper.toOemsRequest(orderState);
                setCurrentOrderOemsRequest(oemsRequest);

                OemsResponse.Builder oemsResponseBuilder = OrderStateReverseMapper.toOemsResponse(orderState);
                oemsResponseBuilder.setRequest(oemsRequest);
                return oemsResponseBuilder.build();
            }
            case OEMS_RESPONSE -> {
                OemsResponse.Builder oemsResponseBuilder = OrderStateReverseMapper.toOemsResponse(orderState);
                if (currentOrderOemsRequest != null) {
                    LOGGER.trace("Embedding request in response: {}", currentOrderOemsRequest);
                    oemsResponseBuilder.setRequest(currentOrderOemsRequest);
                } else {
                    LOGGER.warn("No request found for response: {}", orderState);
                    OemsRequest syntheticRequest = createSyntheticOemsRequest(orderState);
                    oemsResponseBuilder.setRequest(syntheticRequest);
                    orderIdsToInvestigate.add(orderState.orderId());
                }
                return oemsResponseBuilder.build();
            }
            case UNKNOWN -> {
                return null;
            }
            default -> throw new IllegalArgumentException("Unexpected message type: " + resolveMessageTypeResult.getMessageType());
        }
    }

    private List<String> getOrderIdBatch(int offset) {
        LOGGER.debug("Fetching order ID batch with offset: {}, limit: {}", offset, OrderStateMigrationService.BATCH_SIZE);
        return orderStateRepository.findDistinctOrderIdsOrderedByOldest(offset, OrderStateMigrationService.BATCH_SIZE);
    }

    private List<OrderStateEntity> getAllOrderStatesForOrder(String orderId) {
        return orderStateRepository.findAllOrderStatesByOrderIdOrderedBySequence(orderId);
    }

    private OrderStateMessageType resolveMessageType(OrderStateEntity orderState, List<OrderStateEntity> allStatesForOrder) {
        if (isResponse(orderState)) {
            totalResponses++;
            return resolveResponse(orderState, allStatesForOrder);
        } else {
            totalRequests++;
            return resolveRequest(orderState, allStatesForOrder);
        }
    }

    private OrderStateMessageType resolveRequest(OrderStateEntity orderState, List<OrderStateEntity> allStatesForOrder) {
        int sequenceNumber = orderState.sequenceNumber();
        OrderStateEntity firstState = allStatesForOrder.getFirst();
        String orderStatus = orderState.orderStatus();
        String orderCategory = orderState.orderCategory();

        // Order conditions
        boolean isParentOrder = isParentOrder(firstState);
        boolean isPendingNew = isPendingNew(orderState);
        boolean isUserOrder = isUserOrder(firstState);

        if (sequenceNumber == 1) {
            if (isParentOrder && isPendingNew) {
                if (isUserOrder) {
                    return new OrderStateMessageType(MessageType.CLIENT_REQUEST, orderStatus, orderCategory, "Seq=1, has ORDER_CATEGORY_UNSPECIFIED, has clOrderId", ConfidenceLevel.CONFIDENT);
                } else {
                    orderIdsToInvestigate.add(orderState.orderId());
                    return new OrderStateMessageType(MessageType.UNKNOWN, orderStatus, orderCategory, "Seq=1, parent order but missing user order indicators", ConfidenceLevel.UNKNOWN);
                }
            } else if (isPendingNew) {
                if (!isUserOrder) {
                    return new OrderStateMessageType(MessageType.OEMS_REQUEST, orderStatus, orderCategory, "Seq=1, OEMS-only order, no clOrderId", ConfidenceLevel.CONFIDENT);
                } else {
                    orderIdsToInvestigate.add(orderState.orderId());
                    return new OrderStateMessageType(MessageType.UNKNOWN, orderStatus, orderCategory, "Seq=1, child order but has unexpected indicators", ConfidenceLevel.UNKNOWN);
                }
            } else {
                throw new IllegalStateException(String.format("Data integrity error: Sequence 1 order state must have PENDING_NEW status but found: %s for orderId: %s", orderState.orderStatus(), orderState.orderId()));
            }
        }

        return new OrderStateMessageType(MessageType.OEMS_REQUEST, orderStatus, orderCategory, "Seq=%d, Client order transitioning to OEMS layer".formatted(sequenceNumber), ConfidenceLevel.CONFIDENT);
    }

    private OrderStateMessageType resolveResponse(OrderStateEntity orderState, List<OrderStateEntity> allStatesForOrder) {
        OrderStateEntity firstState = allStatesForOrder.getFirst();
        Optional<OrderStateEntity> nextState = getNextState(orderState, allStatesForOrder);
        String orderStatus = orderState.orderStatus();
        String orderCategory = orderState.orderCategory();

        boolean isLastStateWithStatus = isLastStateWithStatus(orderState, nextState);
        boolean isUserOrder = isUserOrder(firstState);
        boolean hasExecutionData = hasExecutionData(orderState);

        // check is client response
        if (isLastStateWithStatus && isUserOrder) {
            return new OrderStateMessageType(MessageType.CLIENT_RESPONSE, orderStatus, orderCategory, "Last state for status, user order response", ConfidenceLevel.CONFIDENT);
        }

        ConfidenceLevel confidence = orderStatus.equals(NEW) || orderStatus.equals(REJECTED) || hasExecutionData ? ConfidenceLevel.CONFIDENT : ConfidenceLevel.TENTATIVE;
        String reason = "Last seq, OEMS-only order response";
        if (confidence.equals(ConfidenceLevel.TENTATIVE)) {
            reason += ", no execution data. OrderState details: category %s, reason %s, type %s".formatted(orderState.orderCategory(), orderState.reason(), orderState.orderType());
        }
        return new OrderStateMessageType(MessageType.OEMS_RESPONSE, orderStatus, orderCategory, reason, confidence);
    }

    private void sendMessageToConsumer(Message message) {
        AMQP.BasicProperties properties = new AMQP.BasicProperties.Builder()
            .headers(createMigrationHeaders())
            .timestamp(new Date())
            .build();

        // Invoke the consumer's consume method
        ConsumptionResult result = responseMessageConsumer.consume(message, properties);

        if (result != ConsumptionResult.consumed()) {
            throw new RuntimeException("Message not consumed successfully: " + result);
        }

        LOGGER.trace("Successfully fed {} to consumer", message.getClass().getSimpleName());
    }

    private OemsRequest createSyntheticOemsRequest(OrderStateEntity orderState) {
        return OrderStateReverseMapper.toOemsRequest(orderState);
    }

    private ClientRequest createSyntheticClientRequest(OrderStateEntity orderState) {
        return OrderStateReverseMapper.toClientRequest(orderState);
    }

    private Map<String, Object> createMigrationHeaders() {
        Map<String, Object> headers = new HashMap<>();
        headers.put("migration", "true");
        headers.put("migration-source", "OrderStateEntity");
        headers.put("migration-timestamp", new Date().toString());
        return headers;
    }

    private static Optional<OrderStateEntity> getNextState(OrderStateEntity orderState, List<OrderStateEntity> allStatesForOrder) {
        int nextIndex = orderState.sequenceNumber();
        return nextIndex < allStatesForOrder.size() ? Optional.of(allStatesForOrder.get(nextIndex)) : Optional.empty();
    }

    private boolean isResponse(OrderStateEntity state) {
        return state.lastRequestResult() != null && !state.lastRequestResult().isEmpty();
    }

    private boolean isResponse(MessageType messageType) {
        return messageType.equals(MessageType.CLIENT_RESPONSE) || messageType.equals(MessageType.OEMS_RESPONSE);
    }

    private boolean isParentOrder(OrderStateEntity firstOrderState) {
        return firstOrderState.orderId().equals(firstOrderState.rootOrderId()) && StringUtils.isEmpty(firstOrderState.parentOrderId());
    }

    private boolean isPendingNew(OrderStateEntity state) {
        return PENDING_NEW.equals(state.orderStatus());
    }

    private boolean isUserOrder(OrderStateEntity firstOrderState) {
        boolean category = ORDER_CATEGORY_UNSPECIFIED.equals(firstOrderState.orderCategory()) || SIMPLE.equals(firstOrderState.orderCategory());
        boolean clOrderIdNotEmpty = firstOrderState.clOrderId() != null && !firstOrderState.clOrderId().isEmpty();
        return category && clOrderIdNotEmpty;
    }

    private boolean isLastStateWithStatus(OrderStateEntity orderState, Optional<OrderStateEntity> nextState) {
        return nextState.isEmpty() || !nextState.get().orderStatus().equals(orderState.orderStatus());
    }

    private boolean hasExecutionData(OrderStateEntity orderState) {
        return (orderState.filledQty() != null && !orderState.filledQty().equals(ZERO)) ||
               (orderState.lastQty() != null && !orderState.lastQty().equals(ZERO)) ||
               (orderState.avgPrice() != null && !orderState.avgPrice().equals(ZERO)) ||
               (orderState.lastPrice() != null && !orderState.lastPrice().equals(ZERO)) ||
               (orderState.venueTimestamp() != null);
    }

    private boolean hasDuplicateSequenceNumbers(List<OrderStateEntity> statesList) {
        Set<Integer> seen = new HashSet<>();
        for (OrderStateEntity state : statesList) {
            if (!seen.add(state.sequenceNumber())) {
                return true;
            }
        }
        return false;
    }

    private void printFinalSummary(int totalProcessed) {
        LOGGER.info("=== FINAL MESSAGE TYPE RESOLUTION SUMMARY ===");
        LOGGER.info("Total orders processed: {}", totalProcessed);
        LOGGER.info("Total requests: {}", totalRequests);
        LOGGER.info("Total responses: {}", totalResponses);
        LOGGER.info("Message type resolutions:");
        LOGGER.info("  - CONFIDENT: {}", totalConfident);
        LOGGER.info("  - TENTATIVE: {}", totalTentative);
        LOGGER.info("  - UNKNOWN: {}", totalUnknown);
        LOGGER.info("  - ERRORS: {}", totalErrors);
        LOGGER.info("Orders requiring investigation: {} orders", orderIdsToInvestigate.size());
        if (!orderIdsToInvestigate.isEmpty()) {
            storeOrderIdsToInvestigate(orderIdsToInvestigate);
            LOGGER.warn("Order IDs to investigate: {}", orderIdsToInvestigate);
        }
    }

    private static void logResolveResult(Level level, String message, String orderId, OrderStateEntity orderState, OrderStateMessageType result) {
        if (result != null) {
            LOGGER.atLevel(level).log(message + "\norderId={}, stateId={}, seq={}, type={}, orderStatus={}, orderCategory={}, reason={}",
                orderId, orderState.id(), orderState.sequenceNumber(), result.getMessageType(), result.getOrderStatus(), result.getOrderCategory(), result.getReason());
        } else {
            LOGGER.atLevel(level).log(message + "\norderId={}, stateId={}, seq={}", orderId, orderState.id(), orderState.sequenceNumber());
        }
    }

    public enum MessageType {
        CLIENT_REQUEST,
        CLIENT_RESPONSE,
        OEMS_REQUEST,
        OEMS_RESPONSE,
        UNKNOWN
    }

    public enum ConfidenceLevel {
        CONFIDENT,
        TENTATIVE,
        UNKNOWN
    }

    public static class OrderStateMessageType {
        private final MessageType messageType;
        private final String orderStatus;
        private final String orderCategory;
        private final String reason;
        private final ConfidenceLevel confidenceLevel;

        public OrderStateMessageType(MessageType messageType, String orderStatus, String orderCategory, String reason, ConfidenceLevel confidenceLevel) {
            this.messageType = messageType;
            this.orderStatus = orderStatus;
            this.orderCategory = orderCategory;
            this.reason = reason;
            this.confidenceLevel = confidenceLevel;
        }

        public MessageType getMessageType() {
            return messageType;
        }

        public String getOrderStatus() {
            return orderStatus;
        }

        public String getOrderCategory() {
            return orderCategory;
        }

        public String getReason() {
            return reason;
        }

        public ConfidenceLevel getConfidenceLevel() {
            return confidenceLevel;
        }
    }

    private void setCurrentOrderClientRequest(ClientRequest clientRequest) {
        currentOrderClientRequest = clientRequest;
    }

    private void setCurrentOrderOemsRequest(OemsRequest oemsRequest) {
        currentOrderOemsRequest = oemsRequest;
    }

    private void clearCurrentOrderMessages() {
        currentOrderClientRequest = null;
        currentOrderOemsRequest = null;
    }

    private void storeOrderIdsToInvestigate(Set<String> orderIdsToInvestigate) {
        if (CollectionUtils.isEmpty(orderIdsToInvestigate)) {
            LOGGER.info("No order IDs to investigate");
            return;
        }

        String timestamp = java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String filename = "data_to_investigate_" + timestamp + ".txt";
        java.nio.file.Path filePath = java.nio.file.Paths.get("src/integrationTest/resources", filename);

        try {
            java.nio.file.Files.createDirectories(filePath.getParent());
            java.nio.file.Files.write(filePath, orderIdsToInvestigate, java.nio.charset.StandardCharsets.UTF_8);
            LOGGER.info("Stored {} order IDs to investigate in file: {}", orderIdsToInvestigate.size(), filePath);
        } catch (java.io.IOException e) {
            LOGGER.error("Failed to store order IDs to investigate in file: {}", filePath, e);
        }
    }

    public List<String> readLatestOrderIdsToInvestigate() {
        Path resourcesPath = Paths.get("src/integrationTest/resources");
        
        try (Stream<Path> paths = Files.list(resourcesPath)) {
            Optional<Path> latestFile = paths
                .peek(path -> LOGGER.info("Found file: {}", path.getFileName()))
                .filter(path -> path.getFileName().toString().startsWith("data_to_investigate_") && path.getFileName().toString().endsWith(".txt"))
                .max(java.util.Comparator.comparing(Path::getFileName));
                
            if (latestFile.isPresent()) {
                Path filePath = latestFile.get();
                List<String> orderIds = Files.readAllLines(filePath, StandardCharsets.UTF_8);
                LOGGER.info("Read {} order IDs from latest investigation file: {}", orderIds.size(), filePath.getFileName());
                return orderIds;
            } else {
                LOGGER.info("No investigation files found in: {}", resourcesPath);
                return new ArrayList<>();
            }
        } catch (java.io.IOException e) {
            LOGGER.error("Failed to read latest order IDs to investigate from: {}", resourcesPath, e);
            return new ArrayList<>();
        }
    }
}