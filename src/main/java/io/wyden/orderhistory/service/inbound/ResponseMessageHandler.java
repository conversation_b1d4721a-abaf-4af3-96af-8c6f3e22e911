package io.wyden.orderhistory.service.inbound;

import com.google.protobuf.Message;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.orderhistory.model.OrderEventEntity;
import io.wyden.orderhistory.repository.OrderEventRepository;
import io.wyden.orderhistory.service.AsyncOrderStateProcessor;
import io.wyden.published.client.ClientExecType;
import io.wyden.published.client.ClientResponse;
import io.wyden.published.common.Metadata;
import io.wyden.published.oems.OemsResponse;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.OffsetDateTime;
import java.util.Objects;

import static io.wyden.orderhistory.service.utils.ProtobufUtils.toMessageType;

@Component
public class ResponseMessageHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(ResponseMessageHandler.class);

    private final OrderEventRepository orderEventRepository;
    private final AsyncOrderStateProcessor asyncProcessor;

    public ResponseMessageHandler(OrderEventRepository orderEventRepository, AsyncOrderStateProcessor asyncProcessor) {
        this.orderEventRepository = orderEventRepository;
        this.asyncProcessor = asyncProcessor;
    }

    public void handleMessage(Message message) {
        if (doesResponseContainRequest(message)) {
            OrderEventEntity orderEvent = storeEvent(message);
            asyncProcessor.scheduleProcessing(orderEvent);
            LOGGER.debug("Stored event {}", orderEvent);
        } else if (isClientResponseExecTypeReplaced(message)) {
            LOGGER.debug("Processing ExecTypeReplaced message for order {}", extractOrderId(message));
            // TODO: separate logic for ExecTypeReplaced
        } else {
            LOGGER.debug("Skipping message type: {} (not a Response with Request)", message.getClass().getSimpleName());
        }
    }

    private OrderEventEntity storeEvent(Message message) {
        OrderEventEntity event = OrderEventEntity.builder()
            .orderId(extractOrderId(message))
            .messageType(toMessageType(message.getClass().getSimpleName()))
            .messageTimestamp(extractMessageTimestamp(message))
            .protoBlob(message.toByteArray())
            .build();

        return orderEventRepository.save(event);
    }

    private boolean doesResponseContainRequest(Message message) {
        if (message instanceof OemsResponse oemsResponse) {
            return oemsResponse.hasRequest();
        } else if (message instanceof ClientResponse clientResponse) {
            return clientResponse.hasRequest();
        }
        return false;
    }

    private boolean isClientResponseExecTypeReplaced(Message message) {
        if (message instanceof ClientResponse clientResponse) {
            return clientResponse.getExecType() == ClientExecType.CLIENT_EXEC_TYPE_REPLACED;
        }
        return false;
    }

    private String extractOrderId(Message message) {
        if (message instanceof OemsResponse oemsResponse) {
            return oemsResponse.getOrderId();
        } else if (message instanceof ClientResponse clientResponse) {
            return clientResponse.getOrderId();
        }
        throw new IllegalArgumentException("Unsupported message type for order ID extraction: " + message.getClass());
    }

    private OffsetDateTime extractMessageTimestamp(Message message) {
        if (message instanceof OemsResponse oemsResponse) {
            Metadata metadata = oemsResponse.getMetadata();
            String createdAt = metadata.getCreatedAt();
            String updatedAt = metadata.getUpdatedAt();

            LOGGER.debug("Extracted timestamp from OemsResponse: createdAt={}, updatedAt={}", createdAt, updatedAt);
            Instant instant = DateUtils.isoUtcTimeToInstant(StringUtils.firstNonEmpty(updatedAt, createdAt));

            if (Objects.nonNull(instant)) {
                return OffsetDateTime.ofInstant(instant, DateUtils.UTC);
            }
        } else if (message instanceof ClientResponse clientResponse) {
            Metadata metadata = clientResponse.getMetadata();
            String createdAt = metadata.getCreatedAt();
            String updatedAt = metadata.getUpdatedAt();

            LOGGER.debug("Extracted timestamp from ClientResponse: createdAt={}, updatedAt={}", createdAt, updatedAt);
            Instant instant = DateUtils.isoUtcTimeToInstant(StringUtils.firstNonEmpty(updatedAt, createdAt));

            if (Objects.nonNull(instant)) {
                return OffsetDateTime.ofInstant(instant, DateUtils.UTC);
            }
        }

        LOGGER.warn("No reliable timestamp found in message, using current time");
        return OffsetDateTime.now();
    }
}
