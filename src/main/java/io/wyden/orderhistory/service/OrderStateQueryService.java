package io.wyden.orderhistory.service;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.Message;
import io.wyden.cloud.utils.rest.pagination.PaginationWrapper;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.orderhistory.infrastructure.telemetry.RecordLatency;
import io.wyden.orderhistory.model.CollectionPredicateInput;
import io.wyden.orderhistory.model.MessageType;
import io.wyden.orderhistory.model.OrderEventEntity;
import io.wyden.orderhistory.model.OrderHistorySearchIndex;
import io.wyden.orderhistory.model.OrderHistorySearchInput;
import io.wyden.orderhistory.model.SortingField;
import io.wyden.orderhistory.model.SortingOrder;
import io.wyden.orderhistory.repository.OrderEventRepository;
import io.wyden.orderhistory.repository.OrderHistorySearchIndexRepository;
import io.wyden.orderhistory.service.utils.OrderStateMapperExtensions;
import io.wyden.orderhistory.service.utils.Paging;
import io.wyden.published.client.ClientResponse;
import io.wyden.published.common.CursorConnection;
import io.wyden.published.common.CursorNode;
import io.wyden.published.oems.OemsResponse;
import io.wyden.published.reporting.OrderState;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static io.wyden.cloudutils.tools.DateUtils.instantToEpochMicros;
import static io.wyden.orderhistory.repository.specification.OrderHistorySearchSpecificationBuilder.buildSpecification;
import static java.util.Collections.emptyList;

@Service
public class OrderStateQueryService {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderStateQueryService.class);
    private static final int DEFAULT_PAGE_SIZE = 1000;

    private final OrderEventRepository orderEventRepository;
    private final OrderHistorySearchIndexRepository searchIndexRepository;

    public OrderStateQueryService(OrderEventRepository orderEventRepository,
                                  OrderHistorySearchIndexRepository searchIndexRepository) {
        this.orderEventRepository = orderEventRepository;
        this.searchIndexRepository = searchIndexRepository;
    }

    /**
     * Get OrderState snapshots for search input
     */
    @RecordLatency("getOrderStateSnapshots")
    public List<OrderState> getOrderStateSnapshots(OrderHistorySearchInput searchInput) {
        LOGGER.debug("Getting OrderState snapshots for input: {}", searchInput);

        List<String> orderIds = findOrderIds(searchInput);
        return getLatestOrderStates(orderIds, searchInput);
    }

    /**
     * Get paged OrderState snapshots for search input
     */
    @RecordLatency("getOrderStateSnapshotsPaged")
    public CursorConnection getOrderStateSnapshotsPaged(OrderHistorySearchInput searchInput) {
        LOGGER.debug("Getting paged OrderState snapshots for input: {}", searchInput);

        List<String> orderIds = findOrderIds(searchInput);

        if (orderIds.isEmpty()) {
            return createEmptyCursorConnection();
        }

        Slice<OrderEventEntity> latestOrderEvent = getLatestOrderEventEntities(orderIds, searchInput);
        List<OrderState> latestOrderStates = mapEventEntitiesToOrderStateList(latestOrderEvent);
        LOGGER.debug("Found {} latest order events for snapshots", latestOrderEvent.getNumberOfElements());

        return createCursorConnection(latestOrderStates, latestOrderEvent.hasNext());
    }

    /**
     * Get OrderStates (all events) for search input
     */
    @RecordLatency("getOrderStates")
    public List<OrderState> getOrderStates(OrderHistorySearchInput searchInput) {
        LOGGER.debug("Getting all OrderStates for input: {}", searchInput);

        List<String> orderIds = findOrderIds(searchInput);
        return getAllOrderStates(orderIds, searchInput);
    }

    /**
     * Get paged OrderStates (all events) for search input
     */
    @RecordLatency("getOrderStatesPaged")
    public CursorConnection getOrderStatesPaged(OrderHistorySearchInput searchInput) {
        LOGGER.debug("Getting paged OrderStates (all events) for input: {}", searchInput);

        List<String> orderIds = findOrderIds(searchInput);

        if (orderIds.isEmpty()) {
            return createEmptyCursorConnection();
        }

        Page<OrderEventEntity> allEvents = getAllOrderEventEntities(orderIds, searchInput);
        List<OrderState> allOrderStates = mapEventEntitiesToOrderStateList(allEvents);
        LOGGER.debug("Found {} order events for all events", allEvents.getNumberOfElements());

        return createCursorConnection(allOrderStates, allEvents.hasNext());
    }

    /**
     * Find order IDs matching search input from index list
     */
    private List<String> findOrderIds(OrderHistorySearchInput searchInput) {
        return getIndexList(searchInput).stream()
            .map(OrderHistorySearchIndex::getOrderId)
            .toList();
    }

    /**
     * Get OrderHistorySearchIndexes matching search input
     */
    private @NotNull List<OrderHistorySearchIndex> getIndexList(OrderHistorySearchInput searchInput) {
        Specification<OrderHistorySearchIndex> searchIndexSpecification = buildSpecification(searchInput.withSortingField(SortingField.LATEST_MESSAGE_TIMESTAMP));
        return searchIndexRepository.findAll(searchIndexSpecification);
    }

    /**
     * Get latest OrderState snapshots for matching order IDs
     */
    private List<OrderState> getLatestOrderStates(List<String> orderIds, OrderHistorySearchInput searchInput) {
        Slice<OrderEventEntity> latestOrderEvents = getLatestOrderEventEntities(orderIds, searchInput);
        LOGGER.debug("Found {} latest order events for snapshots", latestOrderEvents.getNumberOfElements());

        return mapEventEntitiesToOrderStateList(latestOrderEvents);
    }

    /**
     * Get all OrderStates (all events) for matching order IDs
     */
    private List<OrderState> getAllOrderStates(List<String> orderIds, OrderHistorySearchInput searchInput) {
        Page<OrderEventEntity> allEvents = getAllOrderEventEntities(orderIds, searchInput);
        LOGGER.debug("Found {} order events", allEvents.getTotalElements());

        return mapEventEntitiesToOrderStateList(allEvents);
    }

    /**
     * Get latest OrderEventEntities for given order IDs
     */
    @RecordLatency("getLatestOrderEventEntities")
    private @NotNull Slice<OrderEventEntity> getLatestOrderEventEntities(List<String> orderIds, OrderHistorySearchInput searchInput) {
        OrderHistorySearchInput orderEventSearchInput = createOrderEventSearchInput(orderIds, searchInput);

        SortingField sortingField = Objects.requireNonNullElse(orderEventSearchInput.sortingField(), SortingField.MESSAGE_TIMESTAMP);
        Pageable pageable = buildPageable(orderEventSearchInput, sortingField.columnName()); // Because we are using a custom query, we need to sort by the column name

        return orderEventRepository.findLatestEventsByOrderIds(orderIds, pageable);
    }

    /**
     * Get all OrderEventEntities for given order IDs
     */
    @RecordLatency("getAllOrderEventEntities")
    private @NotNull Page<OrderEventEntity> getAllOrderEventEntities(List<String> orderIds, OrderHistorySearchInput searchInput) {
        OrderHistorySearchInput orderEventSearchInput = createOrderEventSearchInput(orderIds, searchInput);
        Specification<OrderEventEntity> specification = buildSpecification(orderEventSearchInput);

        SortingField sortingField = Objects.requireNonNullElse(orderEventSearchInput.sortingField(), SortingField.MESSAGE_TIMESTAMP);
        Pageable pageable = buildPageable(orderEventSearchInput, sortingField.fieldName()); // Because we are using JPA Specification, we need to sort by the field name

        return orderEventRepository.findAll(specification, pageable);
    }

    /**
     * Create OrderHistorySearchInput for OrderEventEntity - only order IDs and cursor
     */
    private static OrderHistorySearchInput createOrderEventSearchInput(List<String> orderIds, OrderHistorySearchInput searchInput) {
        CollectionPredicateInput collectionPredicateInput = new CollectionPredicateInput(CollectionPredicateInput.PredicateType.IN, CollectionPredicateInput.Field.ORDER_ID, orderIds);
        return new OrderHistorySearchInput(emptyList(), List.of(collectionPredicateInput), emptyList(), searchInput.first(), searchInput.after(), searchInput.sortingOrder(), SortingField.MESSAGE_TIMESTAMP);
    }

    /**
     * Convert Page<OrderEventEntity> to List<OrderState>
     */
    private @NotNull List<OrderState> mapEventEntitiesToOrderStateList(Slice<OrderEventEntity> allEvents) {
        return allEvents.stream()
            .map(this::buildOrderStateFromEvent)
            .flatMap(Optional::stream)
            .toList();
    }

    /**
     * Build OrderState from OrderEventEntity
     */
    @RecordLatency("buildOrderStateFromEvent")
    private Optional<OrderState> buildOrderStateFromEvent(OrderEventEntity event) {
        try {
            Message proto = deserializeProto(event.getProtoBlob(), event.getMessageType());
            return Optional.ofNullable(buildOrderStateFromResponse(proto));
        } catch (Exception e) {
            LOGGER.error("Failed to deserialize event {}", event.getId(), e);
            return Optional.empty();
        }
    }

    /**
     * Deserialize proto blob to Message
     */
    private Message deserializeProto(byte[] protoBlob, MessageType messageType) throws InvalidProtocolBufferException {
        return switch (messageType) {
            case OEMS_RESPONSE -> OemsResponse.parseFrom(protoBlob);
            case CLIENT_RESPONSE -> ClientResponse.parseFrom(protoBlob);
        };
    }

    /**
     * Build OrderState from Response proto
     */
    private OrderState buildOrderStateFromResponse(Message response) {
        if (response instanceof OemsResponse oemsResponse) {
            return OrderStateMapperExtensions.buildFromOemsRequestAndResponse(oemsResponse.getRequest(), oemsResponse);
        } else if (response instanceof ClientResponse clientResponse) {
            return OrderStateMapperExtensions.buildFromClientRequestAndResponse(clientResponse.getRequest(), clientResponse);
        }
        throw new IllegalArgumentException("Unsupported message type: " + response.getClass());
    }

    /**
     * Create CursorConnection from OrderStates
     */
    private static @NotNull CursorConnection createCursorConnection(List<OrderState> orderStates, boolean hasNextPage) {
        return Paging.wrap(orderStates,
            orderState -> instantToEpochMicros(DateUtils.isoUtcTimeToInstant(orderState.getUpdatedAt())),
            orderState -> CursorNode.newBuilder().setOrderState(orderState).build(),
            hasNextPage);
    }

    /**
     * Create empty CursorConnection
     */
    private CursorConnection createEmptyCursorConnection() {
        return PaginationWrapper.wrapToProto(List.of(),
            orderState -> "",
            orderState -> CursorNode.newBuilder().build(),
            0,
            0L
        );
    }

    /**
     * Build Pageable from OrderHistorySearchInput
     */
    private Pageable buildPageable(OrderHistorySearchInput searchInput, String sortingValue) {
        int pageSize = searchInput.first() != null ? searchInput.first() : DEFAULT_PAGE_SIZE;
        Sort.Direction direction = searchInput.sortingOrder() == SortingOrder.DESC ? Sort.Direction.DESC : Sort.Direction.ASC;

        return PageRequest.of(0, pageSize, Sort.by(direction, sortingValue));
    }
}
