package io.wyden.orderhistory.service;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.Message;
import io.wyden.orderhistory.model.MessageType;
import io.wyden.orderhistory.model.OrderEventEntity;
import io.wyden.orderhistory.model.OrderHistorySearchIndex;
import io.wyden.orderhistory.repository.OrderHistorySearchIndexRepository;
import io.wyden.orderhistory.repository.PortfolioProvider;
import io.wyden.orderhistory.service.outbound.OrderStateEmitter;
import io.wyden.orderhistory.service.utils.OrderStateMapperExtensions;
import io.wyden.published.client.ClientRequest;
import io.wyden.published.client.ClientResponse;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.published.referencedata.PortfolioType;
import io.wyden.published.reporting.OrderState;
import jakarta.annotation.Nullable;
import jakarta.annotation.PreDestroy;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.OffsetDateTime;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Service
public class AsyncOrderStateProcessor {

    private static final Logger LOGGER = LoggerFactory.getLogger(AsyncOrderStateProcessor.class);

    private final ExecutorService virtualThreadExecutor;
    private final OrderHistorySearchIndexRepository searchIndexRepository;
    private final PortfolioProvider portfolioProvider;
    private final OrderStateEmitter orderStateEmitter;

    public AsyncOrderStateProcessor(OrderHistorySearchIndexRepository searchIndexRepository,
                                    PortfolioProvider portfolioProvider,
                                    OrderStateEmitter orderStateEmitter) {
        this.portfolioProvider = portfolioProvider;
        this.virtualThreadExecutor = Executors.newVirtualThreadPerTaskExecutor();
        this.searchIndexRepository = searchIndexRepository;
        this.orderStateEmitter = orderStateEmitter;
    }

    public void scheduleProcessing(OrderEventEntity orderEvent) {
        LOGGER.trace("Scheduling processing for event: {} (orderId: {})", orderEvent.getId(), orderEvent.getOrderId());
        virtualThreadExecutor.submit(() -> processEvent(orderEvent));
    }

    /**
     * Process a single OrderEvent.
     * This method is called asynchronously by the virtual thread executor.
     */
    public void processEvent(OrderEventEntity orderEvent) {
        LOGGER.trace("Starting processEvent for event: {} (orderId: {})", orderEvent.getId(), orderEvent.getOrderId());
        try {
            // Check if this event is newer than current search index
            Optional<OrderHistorySearchIndex> currentIndex = searchIndexRepository.findByOrderId(orderEvent.getOrderId());
            LOGGER.trace("Current search index for orderId {}: {}", orderEvent.getOrderId(), currentIndex.isPresent() ? "exists" : "not found");

            if (currentIndex.isPresent() && !orderEvent.getMessageTimestamp().isAfter(currentIndex.get().getLatestMessageTimestamp())) {
                LOGGER.debug("Skipping older event {} for order {} (current index timestamp: {})", orderEvent, orderEvent.getOrderId(), currentIndex.get().getLatestMessageTimestamp());
                return;
            }

            // Build OrderState from Response (with embedded Request)
            LOGGER.trace("Deserializing proto blob for messageType: {}", orderEvent.getMessageType());
            Message message = deserializeProto(orderEvent.getProtoBlob(), orderEvent.getMessageType());
            LOGGER.trace("Building OrderState from response");
            OrderState orderState = buildOrderStateFromResponse(message);
            LOGGER.trace("Built OrderState with orderId: {}", orderState.getOrderId());

            // Update search index
            LOGGER.trace("Updating search index for orderId: {}", orderEvent.getOrderId());
            updateSearchIndex(orderEvent.getOrderId(), orderState, orderEvent);

            // Emit to RabbitMQ
            LOGGER.trace("Emitting OrderState to RabbitMQ for orderId: {}", orderState.getOrderId());
            orderStateEmitter.emit(orderState);

            LOGGER.info("Successfully processed event {} for order {}", orderEvent, orderEvent.getOrderId());
        } catch (Exception e) {
            handleProcessingError(orderEvent, e);
        }
    }

    private Message deserializeProto(byte[] protoBlob, MessageType messageType) throws InvalidProtocolBufferException {
        return switch (messageType) {
            case OEMS_RESPONSE -> OemsResponse.parseFrom(protoBlob);
            case CLIENT_RESPONSE -> ClientResponse.parseFrom(protoBlob);
        };
    }

    private OrderState buildOrderStateFromResponse(Message response) {
        if (response instanceof OemsResponse oemsResponse) {
            return applyOemsResponseOnRequest(oemsResponse.getRequest(), oemsResponse);
        } else if (response instanceof ClientResponse clientResponse) {
            return applyClientResponseOnRequest(clientResponse.getRequest(), clientResponse);
        }
        throw new IllegalArgumentException("Unsupported message type: " + response.getClass());
    }

    private OrderState applyOemsResponseOnRequest(OemsRequest request, OemsResponse response) {
        return OrderStateMapperExtensions.buildFromOemsRequestAndResponse(request, response);
    }

    private OrderState applyClientResponseOnRequest(ClientRequest request, ClientResponse response) {
        return OrderStateMapperExtensions.buildFromClientRequestAndResponse(request, response);
    }

    public void updateSearchIndex(String orderId, OrderState orderState, OrderEventEntity event) {
        Portfolio portfolio = getPortfolio(orderState.getPortfolioId());
        String[] venueAccountsArray = orderState.getVenueAccountsList().toArray(String[]::new);
        
        searchIndexRepository.upsertSearchIndex(
            orderState.getOrderId(),
            event.getMessageTimestamp(),
            event.getId(),
            orderState.getPortfolioId(),
            portfolio != null && !PortfolioType.PORTFOLIO_TYPE_UNSPECIFIED.equals(portfolio.getPortfolioType()) ? portfolio.getPortfolioType().name() : null,
            venueAccountsArray,
            orderState.getInstrumentId(),
            orderState.getClOrderId(),
            orderState.getTif().name(),
            orderState.getLastRequestResult(),
            orderState.getReason(),
            orderState.getOrderStatus().name(),
            orderState.getOrderCategory().name(),
            orderState.getParentOrderId(),
            orderState.getRootOrderId(),
            orderState.getExtOrderId(),
            StringUtils.isNotEmpty(orderState.getCreatedAt()) ? OffsetDateTime.parse(orderState.getCreatedAt()) : null,
            StringUtils.isNotEmpty(orderState.getUpdatedAt()) ? OffsetDateTime.parse(orderState.getUpdatedAt()) : null
        );
        
        LOGGER.debug("Updated search index for order {}", orderId);
        LOGGER.trace("Search index details for orderId: {}, timestamp: {}, eventId: {}", orderId, event.getMessageTimestamp(), event.getId());
    }


    private @Nullable Portfolio getPortfolio(@Nullable  String portfolioId) {
        if (StringUtils.isNotEmpty(portfolioId)) {
            return portfolioProvider.find(portfolioId).orElse(null);
        }
        return null;
    }

    private void handleProcessingError(OrderEventEntity event, Exception e) {
        LOGGER.error("Failed to process event {}: {}", event, e.getMessage(), e);
    }

    // Graceful shutdown
    @PreDestroy
    public void shutdown() {
        LOGGER.info("Shutting down AsyncOrderStateProcessor...");
        virtualThreadExecutor.shutdown();
        try {
            if (!virtualThreadExecutor.awaitTermination(30, java.util.concurrent.TimeUnit.SECONDS)) {
                LOGGER.warn("Executor did not terminate gracefully, forcing shutdown");
                virtualThreadExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            LOGGER.warn("Interrupted while waiting for executor termination");
            virtualThreadExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
}
