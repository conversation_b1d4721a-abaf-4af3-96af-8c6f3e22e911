package io.wyden.orderhistory.service.utils;

import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.orderhistory.model.OrderStateEntity;
import io.wyden.published.client.ClientInstrumentType;
import io.wyden.published.client.ClientOrderCategory;
import io.wyden.published.client.ClientOrderStatus;
import io.wyden.published.client.ClientOrderType;
import io.wyden.published.client.ClientRequest;
import io.wyden.published.client.ClientResponse;
import io.wyden.published.client.ClientSide;
import io.wyden.published.client.ClientTIF;
import io.wyden.published.client.Result;
import io.wyden.published.common.Metadata;
import io.wyden.published.oems.OemsInstrumentType;
import io.wyden.published.oems.OemsOrderCategory;
import io.wyden.published.oems.OemsOrderStatus;
import io.wyden.published.oems.OemsOrderType;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import io.wyden.published.oems.OemsSide;
import io.wyden.published.oems.OemsTIF;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.ZoneOffset;
import java.util.Objects;

import static org.apache.commons.lang3.StringUtils.EMPTY;

public final class OrderStateReverseMapper {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderStateReverseMapper.class);

    public static ClientRequest toClientRequest(OrderStateEntity orderState) {
        try {
            ClientRequest.Builder builder = ClientRequest.newBuilder()
                .setOrderId(orderState.orderId())
                .setClientId(orderState.clientId())
                .setPortfolioId(orderState.portfolioId())
                .setQuantity(orderState.orderQty())
                .setOrderType(toClientOrderType(orderState.orderType()))
                .setSide(toClientSide(orderState.side()))
                .setTif(toClientTIF(orderState.tif()));

            // Optional fields
            if (StringUtils.isNotEmpty(orderState.clOrderId())) {
                builder.setClOrderId(orderState.clOrderId());
            }
            if (StringUtils.isNotEmpty(orderState.origClOrderId())) {
                builder.setOrigClOrderId(orderState.origClOrderId());
            }
            if (StringUtils.isNotEmpty(orderState.currency())) {
                builder.setCurrency(orderState.currency());
            }
            if (StringUtils.isNotEmpty(orderState.limitPrice())) {
                builder.setPrice(orderState.limitPrice());
            }
            if (StringUtils.isNotEmpty(orderState.stopPrice())) {
                builder.setStopPrice(orderState.stopPrice());
            }
            if (StringUtils.isNotEmpty(orderState.instrumentId())) {
                builder.setInstrumentId(orderState.instrumentId());
            }
            if (StringUtils.isNotEmpty(orderState.symbol())) {
                builder.setSymbol(orderState.symbol());
            }
            if (StringUtils.isNotEmpty(orderState.instrumentType())) {
                builder.setInstrumentType(toClientInstrumentType(orderState.instrumentType()));
            }
            if (Objects.nonNull(orderState.createdAt())) {
                builder.setCreatedAt(orderState.createdAt().atZoneSameInstant(ZoneOffset.UTC).toString());
            }
            if (Objects.nonNull(orderState.expireTime())) {
                builder.setExpireTime(DateUtils.toFixUtcTime(orderState.expireTime().atZoneSameInstant(ZoneOffset.UTC)));
            }
            if (Objects.nonNull(orderState.venueAccounts()) && !orderState.venueAccounts().isEmpty()) {
                builder.addAllVenueAccounts(orderState.venueAccounts());
            }

            // Add metadata
            Metadata.Builder metadataBuilder = Metadata.newBuilder();
            if (Objects.nonNull(orderState.createdAt())) {
                metadataBuilder.setCreatedAt(orderState.createdAt().atZoneSameInstant(ZoneOffset.UTC).toString());
            }
            if (Objects.nonNull(orderState.updatedAt())) {
                metadataBuilder.setUpdatedAt(orderState.updatedAt().atZoneSameInstant(ZoneOffset.UTC).toString());
            }
            if (Objects.nonNull(orderState.sequenceNumber())) {
                metadataBuilder.setSource(orderState.sequenceNumber().toString());
            }
            builder.setMetadata(metadataBuilder.build());

            return builder.build();
        } catch (Exception e) {
            LOGGER.error("Failed to convert OrderStateEntity to ClientRequest for order: {}", orderState.orderId(), e);
            throw new IllegalArgumentException("Cannot convert OrderStateEntity to ClientRequest", e);
        }
    }

    public static ClientResponse.Builder toClientResponse(OrderStateEntity orderState) {
        try {
            ClientResponse.Builder builder = ClientResponse.newBuilder()
                .setOrderId(orderState.orderId())
                .setOrderStatus(toClientOrderStatus(orderState.orderStatus()))
                .setOrderQty(orderState.orderQty());

            // Optional fields
            if (StringUtils.isNotEmpty(orderState.currency())) {
                builder.setCurrency(orderState.currency());
            }
            if (StringUtils.isNotEmpty(orderState.filledQty())) {
                builder.setCumQty(orderState.filledQty());
            }
            if (StringUtils.isNotEmpty(orderState.remainingQty())) {
                builder.setLeavesQty(orderState.remainingQty());
            }
            if (StringUtils.isNotEmpty(orderState.lastQty())) {
                builder.setLastQty(orderState.lastQty());
            }
            if (StringUtils.isNotEmpty(orderState.avgPrice())) {
                builder.setAvgPrice(orderState.avgPrice());
            }
            if (StringUtils.isNotEmpty(orderState.lastPrice())) {
                builder.setLastPrice(orderState.lastPrice());
            }
            if (StringUtils.isNotEmpty(orderState.reason())) {
                builder.setReason(orderState.reason());
            }
            if (StringUtils.isNotEmpty(orderState.underlyingVenueAccount())) {
                builder.setUnderlyingVenueAccount(orderState.underlyingVenueAccount());
            }
            if (StringUtils.isNotEmpty(orderState.counterPortfolioId())) {
                builder.setCounterPortfolioId(orderState.counterPortfolioId());
            }
            if (Objects.nonNull(orderState.updatedAt())) {
                builder.setTimestamp(orderState.updatedAt().atZoneSameInstant(ZoneOffset.UTC).toString());
            }
            if (StringUtils.isNotEmpty(orderState.lastRequestResult())) {
                builder.setRequestResult(toResult(orderState.lastRequestResult()));
            }
            if (StringUtils.isNotEmpty(orderState.orderCategory())) {
                builder.setOrderCategory(toClientOrderCategory(orderState.orderCategory()));
            }

            // Add metadata
            Metadata.Builder metadataBuilder = Metadata.newBuilder();
            if (Objects.nonNull(orderState.createdAt())) {
                metadataBuilder.setCreatedAt(orderState.createdAt().atZoneSameInstant(ZoneOffset.UTC).toString());
            }
            if (Objects.nonNull(orderState.updatedAt())) {
                metadataBuilder.setUpdatedAt(orderState.updatedAt().atZoneSameInstant(ZoneOffset.UTC).toString());
            }
            if (Objects.nonNull(orderState.sequenceNumber())) {
                metadataBuilder.setSource(orderState.sequenceNumber().toString());
            }
            builder.setMetadata(metadataBuilder.build());

            return builder;
        } catch (Exception e) {
            LOGGER.error("Failed to convert OrderStateEntity to ClientResponse for order: {}", orderState.orderId(), e);
            throw new IllegalArgumentException("Cannot convert OrderStateEntity to ClientResponse", e);
        }
    }

    public static OemsRequest toOemsRequest(OrderStateEntity orderState) {
        try {
            OemsRequest.Builder builder = OemsRequest.newBuilder()
                .setOrderId(orderState.orderId())
                .setClientId(orderState.clientId())
                .setPortfolioId(orderState.portfolioId())
                .setOrderType(toOemsOrderType(orderState.orderType()))
                .setQuantity(orderState.orderQty())
                .setSide(toOemsSide(orderState.side()))
                .setTif(toOemsTIF(orderState.tif()));

            // Optional fields
            if (StringUtils.isNotEmpty(orderState.currency())) {
                builder.setCurrency(orderState.currency());
                builder.setBaseCurrency(orderState.currency());
            }
            if (StringUtils.isNotEmpty(orderState.limitPrice())) {
                builder.setPrice(orderState.limitPrice());
            }
            if (StringUtils.isNotEmpty(orderState.stopPrice())) {
                builder.setStopPrice(orderState.stopPrice());
            }
            if (StringUtils.isNotEmpty(orderState.instrumentId())) {
                builder.setInstrumentId(orderState.instrumentId());
            }
            if (StringUtils.isNotEmpty(orderState.symbol())) {
                builder.setSymbol(orderState.symbol());
            }
            if (StringUtils.isNotEmpty(orderState.instrumentType())) {
                builder.setInstrumentType(toOemsInstrumentType(orderState.instrumentType()));
            }
            if (Objects.nonNull(orderState.venueAccounts()) && !orderState.venueAccounts().isEmpty()) {
                builder.addAllVenueAccounts(orderState.venueAccounts());
                if (orderState.venueAccounts().size() == 1) {
                    builder.setVenueAccount(orderState.venueAccounts().getFirst());
                }
            }
            if (Objects.nonNull(orderState.expireTime())) {
                builder.setExpireTime(DateUtils.toFixUtcTime(orderState.expireTime().atZoneSameInstant(ZoneOffset.UTC)));
            }
            if (StringUtils.isNotEmpty(orderState.orderCategory())) {
                builder.setOrderCategory(toOemsOrderCategory(orderState.orderCategory()));
            }
            if (StringUtils.isNotEmpty(orderState.parentOrderId())) {
                builder.setParentOrderId(orderState.parentOrderId());
            }
            if (StringUtils.isNotEmpty(orderState.rootOrderId())) {
                builder.setRootOrderId(orderState.rootOrderId());
            }
            if (StringUtils.isNotEmpty(orderState.clOrderId())) {
                builder.setClientRootOrderId(orderState.clOrderId());
            }

            // Add metadata
            Metadata.Builder metadataBuilder = Metadata.newBuilder();
            if (Objects.nonNull(orderState.createdAt())) {
                metadataBuilder.setCreatedAt(orderState.createdAt().atZoneSameInstant(ZoneOffset.UTC).toString());
            }
            if (Objects.nonNull(orderState.updatedAt())) {
                metadataBuilder.setUpdatedAt(orderState.updatedAt().atZoneSameInstant(ZoneOffset.UTC).toString());
            }
            if (Objects.nonNull(orderState.sequenceNumber())) {
                metadataBuilder.setSource(orderState.sequenceNumber().toString());
            }
            builder.setMetadata(metadataBuilder.build());

            return builder.build();
        } catch (Exception e) {
            LOGGER.error("Failed to convert OrderStateEntity to OemsRequest for order: {}", orderState.orderId(), e);
            throw new IllegalArgumentException("Cannot convert OrderStateEntity to OemsRequest", e);
        }
    }

    public static OemsResponse.Builder toOemsResponse(OrderStateEntity orderState) {
        try {
            OemsResponse.Builder builder = OemsResponse.newBuilder()
                .setOrderId(orderState.orderId())
                .setClientId(orderState.clientId())
                .setPortfolioId(orderState.portfolioId())
                .setOrderStatus(toOemsOrderStatus(orderState.orderStatus()))
                .setOrderQty(orderState.orderQty())
                .setSide(toOemsSide(orderState.side()))
                .setTif(toOemsTIF(orderState.tif()))
                .setReason(Objects.nonNull(orderState.reason()) ? orderState.reason() : EMPTY);

            // Optional fields
            if (StringUtils.isNotEmpty(orderState.currency())) {
                builder.setCurrency(orderState.currency());
                builder.setBaseCurrency(orderState.currency());
            }
            if (StringUtils.isNotEmpty(orderState.lastQty())) {
                builder.setLastQty(orderState.lastQty());
            }
            if (StringUtils.isNotEmpty(orderState.filledQty())) {
                builder.setCumQty(orderState.filledQty());
            }
            if (StringUtils.isNotEmpty(orderState.remainingQty())) {
                builder.setLeavesQty(orderState.remainingQty());
            }
            if (StringUtils.isNotEmpty(orderState.avgPrice())) {
                builder.setAvgPrice(orderState.avgPrice());
            }
            if (StringUtils.isNotEmpty(orderState.lastPrice())) {
                builder.setLastPrice(orderState.lastPrice());
            }
            if (StringUtils.isNotEmpty(orderState.instrumentId())) {
                builder.setInstrumentId(orderState.instrumentId());
            }
            if (StringUtils.isNotEmpty(orderState.symbol())) {
                builder.setSymbol(orderState.symbol());
            }
            if (StringUtils.isNotEmpty(orderState.instrumentType())) {
                builder.setInstrumentType(toOemsInstrumentType(orderState.instrumentType()));
            }
            if (Objects.nonNull(orderState.venueAccounts()) && !orderState.venueAccounts().isEmpty()) {
                if (orderState.venueAccounts().size() == 1) {
                    builder.setVenueAccount(orderState.venueAccounts().getFirst());
                }
            }
            if (Objects.nonNull(orderState.expireTime())) {
                builder.setExpireTime(DateUtils.toFixUtcTime(orderState.expireTime().atZoneSameInstant(ZoneOffset.UTC)));
            }
            if (StringUtils.isNotEmpty(orderState.orderCategory())) {
                builder.setOrderCategory(toOemsOrderCategory(orderState.orderCategory()));
            }
            if (StringUtils.isNotEmpty(orderState.parentOrderId())) {
                builder.setParentOrderId(orderState.parentOrderId());
            }
            if (StringUtils.isNotEmpty(orderState.rootOrderId())) {
                builder.setRootOrderId(orderState.rootOrderId());
            }
            if (StringUtils.isNotEmpty(orderState.clOrderId())) {
                builder.setClientRootOrderId(orderState.clOrderId());
            }
            if (StringUtils.isNotEmpty(orderState.extOrderId())) {
                builder.setExtId(orderState.extOrderId());
            }
            if (StringUtils.isNotEmpty(orderState.lastRequestResult())) {
                builder.setRequestResult(toOemsResponseResult(orderState.lastRequestResult()));
            }
            if (Objects.nonNull(orderState.updatedAt())) {
                builder.setSystemTimestamp(orderState.updatedAt().atZoneSameInstant(ZoneOffset.UTC).toString());
            }
            if (Objects.nonNull(orderState.venueTimestamp())) {
                builder.setVenueTimestamp(orderState.venueTimestamp().atZoneSameInstant(ZoneOffset.UTC).toString());
            }

            // Add metadata
            Metadata.Builder metadataBuilder = Metadata.newBuilder();
            if (Objects.nonNull(orderState.createdAt())) {
                metadataBuilder.setCreatedAt(orderState.createdAt().atZoneSameInstant(ZoneOffset.UTC).toString());
            }
            if (Objects.nonNull(orderState.updatedAt())) {
                metadataBuilder.setUpdatedAt(orderState.updatedAt().atZoneSameInstant(ZoneOffset.UTC).toString());
            }
            if (Objects.nonNull(orderState.sequenceNumber())) {
                metadataBuilder.setSource(orderState.sequenceNumber().toString());
            }
            builder.setMetadata(metadataBuilder.build());

            return builder;
        } catch (Exception e) {
            LOGGER.error("Failed to convert OrderStateEntity to OemsResponse for order: {}", orderState.orderId(), e);
            throw new IllegalArgumentException("Cannot convert OrderStateEntity to OemsResponse", e);
        }
    }

    public static ClientOrderStatus toClientOrderStatus(String orderStatus) {
        if (StringUtils.isEmpty(orderStatus)) {
            return ClientOrderStatus.ORDER_STATUS_UNSPECIFIED;
        }

        try {
            return ClientOrderStatus.valueOf(orderStatus);
        } catch (IllegalArgumentException e) {
            LOGGER.warn("Unknown ClientOrderStatus: {}, defaulting to ORDER_STATUS_UNSPECIFIED", orderStatus);
            return ClientOrderStatus.ORDER_STATUS_UNSPECIFIED;
        }
    }

    public static OemsOrderStatus toOemsOrderStatus(String orderStatus) {
        if (StringUtils.isEmpty(orderStatus)) {
            return OemsOrderStatus.ORDER_STATUS_UNSPECIFIED;
        }

        return switch (orderStatus) {
            case "NEW" -> OemsOrderStatus.STATUS_NEW;
            case "PARTIALLY_FILLED" -> OemsOrderStatus.STATUS_PARTIALLY_FILLED;
            case "FILLED" -> OemsOrderStatus.STATUS_FILLED;
            case "CANCELED" -> OemsOrderStatus.STATUS_CANCELED;
            case "PENDING_CANCEL" -> OemsOrderStatus.STATUS_PENDING_CANCEL;
            case "REJECTED" -> OemsOrderStatus.STATUS_REJECTED;
            case "PENDING_NEW" -> OemsOrderStatus.STATUS_PENDING_NEW;
            case "EXPIRED" -> OemsOrderStatus.STATUS_EXPIRED;
            default -> {
                LOGGER.warn("Unknown OemsOrderStatus: {}, defaulting to ORDER_STATUS_UNSPECIFIED", orderStatus);
                yield OemsOrderStatus.ORDER_STATUS_UNSPECIFIED;
            }
        };
    }

    public static ClientOrderType toClientOrderType(String orderType) {
        if (StringUtils.isEmpty(orderType)) {
            return ClientOrderType.ORDER_TYPE_UNSPECIFIED;
        }

        try {
            return ClientOrderType.valueOf(orderType);
        } catch (IllegalArgumentException e) {
            LOGGER.warn("Unknown ClientOrderType: {}, defaulting to ORDER_TYPE_UNSPECIFIED", orderType);
            return ClientOrderType.ORDER_TYPE_UNSPECIFIED;
        }
    }

    public static OemsOrderType toOemsOrderType(String orderType) {
        if (StringUtils.isEmpty(orderType)) {
            return OemsOrderType.ORDER_TYPE_UNSPECIFIED;
        }

        try {
            return OemsOrderType.valueOf(orderType);
        } catch (IllegalArgumentException e) {
            LOGGER.warn("Unknown OemsOrderType: {}, defaulting to ORDER_TYPE_UNSPECIFIED", orderType);
            return OemsOrderType.ORDER_TYPE_UNSPECIFIED;
        }
    }

    public static ClientSide toClientSide(String side) {
        if (StringUtils.isEmpty(side)) {
            return ClientSide.SIDE_UNDETERMINED;
        }

        try {
            return ClientSide.valueOf(side);
        } catch (IllegalArgumentException e) {
            LOGGER.warn("Unknown ClientSide: {}, defaulting to SIDE_UNDETERMINED", side);
            return ClientSide.SIDE_UNDETERMINED;
        }
    }

    public static OemsSide toOemsSide(String side) {
        if (StringUtils.isEmpty(side)) {
            return OemsSide.SIDE_UNDETERMINED;
        }

        try {
            return OemsSide.valueOf(side);
        } catch (IllegalArgumentException e) {
            LOGGER.warn("Unknown OemsSide: {}, defaulting to SIDE_UNDETERMINED", side);
            return OemsSide.SIDE_UNDETERMINED;
        }
    }

    public static ClientTIF toClientTIF(String tif) {
        if (StringUtils.isEmpty(tif)) {
            return ClientTIF.TIF_UNSPECIFIED;
        }

        try {
            return ClientTIF.valueOf(tif);
        } catch (IllegalArgumentException e) {
            LOGGER.warn("Unknown ClientTIF: {}, defaulting to TIF_UNSPECIFIED", tif);
            return ClientTIF.TIF_UNSPECIFIED;
        }
    }

    public static OemsTIF toOemsTIF(String tif) {
        if (StringUtils.isEmpty(tif)) {
            return OemsTIF.TIF_UNSPECIFIED;
        }

        try {
            return OemsTIF.valueOf(tif);
        } catch (IllegalArgumentException e) {
            LOGGER.warn("Unknown OemsTIF: {}, defaulting to TIF_UNSPECIFIED", tif);
            return OemsTIF.TIF_UNSPECIFIED;
        }
    }

    public static ClientInstrumentType toClientInstrumentType(String instrumentType) {
        if (StringUtils.isEmpty(instrumentType)) {
            return ClientInstrumentType.INSTRUMENT_TYPE_UNDETERMINED;
        }

        try {
            return ClientInstrumentType.valueOf(instrumentType);
        } catch (IllegalArgumentException e) {
            LOGGER.warn("Unknown ClientInstrumentType: {}, defaulting to INSTRUMENT_TYPE_UNDETERMINED", instrumentType);
            return ClientInstrumentType.INSTRUMENT_TYPE_UNDETERMINED;
        }
    }

    public static OemsInstrumentType toOemsInstrumentType(String instrumentType) {
        if (StringUtils.isEmpty(instrumentType)) {
            return OemsInstrumentType.INSTRUMENT_TYPE_UNSPECIFIED;
        }

        try {
            return OemsInstrumentType.valueOf(instrumentType);
        } catch (IllegalArgumentException e) {
            LOGGER.warn("Unknown OemsInstrumentType: {}, defaulting to INSTRUMENT_TYPE_UNSPECIFIED", instrumentType);
            return OemsInstrumentType.INSTRUMENT_TYPE_UNSPECIFIED;
        }
    }

    public static ClientOrderCategory toClientOrderCategory(String orderCategory) {
        if (StringUtils.isEmpty(orderCategory)) {
            return ClientOrderCategory.ORDER_CATEGORY_UNSPECIFIED;
        }

        // Handle known mapping differences
        return switch (orderCategory) {
            case "ORDER_CATEGORY_UNSPECIFIED" -> ClientOrderCategory.ORDER_CATEGORY_UNSPECIFIED;
            case "SIMPLE" -> ClientOrderCategory.ORDER_CATEGORY_UNSPECIFIED; // Map legacy SIMPLE to UNSPECIFIED
            default -> {
                try {
                    yield ClientOrderCategory.valueOf(orderCategory);
                } catch (IllegalArgumentException e) {
                    LOGGER.warn("Unknown ClientOrderCategory: {}, defaulting to ORDER_CATEGORY_UNSPECIFIED", orderCategory);
                    yield ClientOrderCategory.ORDER_CATEGORY_UNSPECIFIED;
                }
            }
        };
    }

    public static OemsOrderCategory toOemsOrderCategory(String orderCategory) {
        if (StringUtils.isEmpty(orderCategory)) {
            return OemsOrderCategory.ORDER_CATEGORY_UNSPECIFIED;
        }

        // Handle known mapping differences
        return switch (orderCategory) {
            case "ORDER_CATEGORY_UNSPECIFIED" -> OemsOrderCategory.ORDER_CATEGORY_UNSPECIFIED;
            case "SIMPLE" -> OemsOrderCategory.ORDER_CATEGORY_UNSPECIFIED; // Map legacy SIMPLE to UNSPECIFIED
            default -> {
                try {
                    yield OemsOrderCategory.valueOf(orderCategory);
                } catch (IllegalArgumentException e) {
                    LOGGER.warn("Unknown OemsOrderCategory: {}, defaulting to ORDER_CATEGORY_UNSPECIFIED", orderCategory);
                    yield OemsOrderCategory.ORDER_CATEGORY_UNSPECIFIED;
                }
            }
        };
    }

    public static Result toResult(String result) {
        if (StringUtils.isEmpty(result)) {
            return Result.SUCCESS;
        }

        try {
            return Result.valueOf(result);
        } catch (IllegalArgumentException e) {
            LOGGER.warn("Unknown Result: {}, defaulting to SUCCESS", result);
            return Result.SUCCESS;
        }
    }

    public static OemsResponse.Result toOemsResponseResult(String result) {
        if (StringUtils.isEmpty(result)) {
            return OemsResponse.Result.SUCCESS;
        }

        try {
            return OemsResponse.Result.valueOf(result);
        } catch (IllegalArgumentException e) {
            LOGGER.warn("Unknown OemsResponse.Result: {}, defaulting to SUCCESS", result);
            return OemsResponse.Result.SUCCESS;
        }
    }
}