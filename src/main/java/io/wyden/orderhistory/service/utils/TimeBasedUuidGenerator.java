package io.wyden.orderhistory.service.utils;

import java.util.concurrent.ThreadLocalRandom;

/**
 * Utility class for generating time-based UUID v8.
 * These UUIDs are designed to be sortable by time of creation.
 */
public final class TimeBasedUuidGenerator {

    private TimeBasedUuidGenerator() {
        // Utility class
    }

    /**
     * Generates a time-based UUID v8 using System.nanoTime().
     * Format: xxxxxxxx-xxxx-8xxx-yxxx-xxxxxxxxxxxx
     * Where:
     * - First 64 bits: nanoTime
     * - Version bits: 8 (UUID version 8)
     * - Variant bits: 10 (RFC 4122 variant)
     * - Random bits for uniqueness
     * 
     * @return A time-based UUID v8 as a String
     */
    public static String generateTimeBasedUuid() {
        long nanoTime = System.nanoTime();
        ThreadLocalRandom random = ThreadLocalRandom.current();
        
        // Split nanoTime into two parts for the first two segments
        long timeLow = nanoTime & 0xFFFFFFFFL;
        long timeMid = (nanoTime >>> 32) & 0xFFFFL;
        long timeHiAndVersion = ((nanoTime >>> 48) & 0x0FFF) | 0x8000; // Version 8
        
        // Generate random bits for the rest
        long clockSeqAndVariant = (random.nextLong() & 0x3FFF) | 0x8000; // Variant 10
        long node = random.nextLong() & 0xFFFFFFFFFFFFL;
        
        return String.format("%08x-%04x-%04x-%04x-%012x",
            timeLow,
            timeMid,
            timeHiAndVersion,
            clockSeqAndVariant,
            node
        );
    }
    
    /**
     * Generates a time-based UUID v8 for migration purposes.
     * Combines timestamp with sequence number for deterministic ordering.
     * 
     * @param timestamp The timestamp in nanoseconds
     * @param sequenceNumber The sequence number for ordering
     * @return A time-based UUID v8 as a String
     */
    /*public static String generateTimeBasedUuidForMigration(long timestamp, int sequenceNumber) {
        // Use timestamp as base
        long timeLow = timestamp & 0xFFFFFFFFL;
        long timeMid = (timestamp >>> 32) & 0xFFFFL;
        long timeHiAndVersion = ((timestamp >>> 48) & 0x0FFF) | 0x8000; // Version 8
        
        // Embed sequence number in the clock sequence
        long clockSeqAndVariant = ((sequenceNumber & 0x3FFF) | 0x8000); // Variant 10
        
        // Use zeros for node to ensure consistent ordering
        long node = 0L;
        
        return String.format("%08x-%04x-%04x-%04x-%012x",
            timeLow,
            timeMid,
            timeHiAndVersion,
            clockSeqAndVariant,
            node
        );
    }*/
}
