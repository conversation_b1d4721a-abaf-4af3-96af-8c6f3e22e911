package io.wyden.orderhistory.service.utils;

import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.client.ClientRequest;
import io.wyden.published.client.ClientResponse;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import io.wyden.published.reporting.OrderState;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.ZonedDateTime;
import java.util.UUID;

import static io.wyden.orderhistory.service.utils.ProtobufUtils.toInstrumentType;
import static io.wyden.orderhistory.service.utils.ProtobufUtils.toOrderCategory;
import static io.wyden.orderhistory.service.utils.ProtobufUtils.toOrderStatus;
import static io.wyden.orderhistory.service.utils.ProtobufUtils.toOrderType;
import static io.wyden.orderhistory.service.utils.ProtobufUtils.toSide;
import static io.wyden.orderhistory.service.utils.ProtobufUtils.toTiff;

public class OrderStateMapperExtensions {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderStateMapperExtensions.class);
    private static final String EMPTY = StringUtils.EMPTY;

    /**
     * Build OrderState from OemsRequest and OemsResponse
     */
    public static OrderState buildFromOemsRequestAndResponse(OemsRequest request, OemsResponse response) {
        try {
            OrderState.Builder orderStateBuilder = OrderState.newBuilder();

            // Order details from Request
            orderStateBuilder
                .setId(UUID.randomUUID().toString())
                .setOrderId(request.getOrderId())
                .setRootOrderId(request.getRootOrderId())
                .setParentOrderId(request.getParentOrderId())
                .setClientId(request.getClientId())
                .setPortfolioId(request.getPortfolioId())
                .setOrderType(toOrderType(request.getOrderType().name()))
                .setLimitPrice(request.getPrice())
                .setStopPrice(request.getStopPrice())
                .setInstrumentId(request.getInstrumentId())
                .setSymbol(request.getSymbol())
                .setInstrumentType(toInstrumentType(request.getInstrumentType().name()))
                .addAllVenueAccounts(request.getVenueAccountsList())
                .setSide(toSide(request.getSide()))
                .setTif(toTiff(request.getTif()))
                .setExpireTime(ObjectUtils.firstNonNull(request.getExpireTime(), EMPTY))
                .setCreatedAt(request.hasMetadata() ? request.getMetadata().getCreatedAt() : getCurrentIsoTime());
            if (StringUtils.isNotEmpty(response.getVenueAccount())) {
                orderStateBuilder.setVenueAccountId(response.getVenueAccount());
            } else if (request.getVenueAccountsCount() == 1) {
                orderStateBuilder.setVenueAccountId(request.getVenueAccounts(0));
            }

            // Execution details from Response
            orderStateBuilder
                .setClOrderId(response.getClientRootOrderId())
                .setOrderStatus(toOrderStatus(response.getOrderStatus()))
                .setOrderQty(response.getOrderQty())
                .setFilledQty(response.getCumQty())
                .setRemainingQty(response.getLeavesQty())
                .setLastQty(response.getLastQty())
                .setAvgPrice(response.getAvgPrice())
                .setLastPrice(response.getLastPrice())
                .setCurrency(ObjectUtils.firstNonNull(response.getCurrency(), response.getBaseCurrency(), EMPTY))
                .setReason(response.getReason())
                .setLastRequestResult(response.getRequestResult().getValueDescriptor().getName())
                .setUpdatedAt(response.hasMetadata() ? response.getMetadata().getUpdatedAt() : response.getMetadata().getCreatedAt())
                .setOrderCategory(toOrderCategory(response.getOrderCategory().name()))
                .setVenueTimestamp(response.getVenueTimestamp())
                .setExtOrderId(response.getExtId())
                .setSequenceNumber(getSequenceNumber(response));

            return orderStateBuilder.build();
        } catch (Exception e) {
            LOGGER.error("Failed to build OrderState from OemsRequest and OemsResponse: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to build OrderState", e);
        }
    }

    /**
     * Build OrderState from ClientRequest and ClientResponse
     */
    public static OrderState buildFromClientRequestAndResponse(ClientRequest request, ClientResponse response) {
        try {
            OrderState.Builder orderStateBuilder = OrderState.newBuilder();

            // Order details from Request
            orderStateBuilder
                .setId(UUID.randomUUID().toString())
                .setOrderId(request.getOrderId())
                .setRootOrderId(request.getOrderId())
                .setClientId(request.getClientId())
                .setClOrderId(request.getClOrderId())
                .setOrigClOrderId(request.getOrigClOrderId())
                .setPortfolioId(request.getPortfolioId())
                .setLimitPrice(request.getPrice())
                .setStopPrice(request.getStopPrice())
                .setSide(toSide(request.getSide()))
                .setTif(toTiff(request.getTif()))
                .setOrderType(toOrderType(request.getOrderType().name()))
                .setExpireTime(ObjectUtils.firstNonNull(request.getExpireTime(), EMPTY))
                .addAllVenueAccounts(request.getVenueAccountsList())
                .setCreatedAt(request.hasMetadata() ? request.getMetadata().getCreatedAt() : getCurrentIsoTime());
            if (StringUtils.isNotEmpty(request.getInstrumentId())) {
                orderStateBuilder.setInstrumentId(request.getInstrumentId());
            } else if (StringUtils.isNotEmpty(request.getSymbol()) && StringUtils.isEmpty(request.getInstrumentId())) {
                orderStateBuilder.setSymbol(request.getSymbol());
                orderStateBuilder.setInstrumentType(toInstrumentType(request.getInstrumentType().name()));
            } else {
                LOGGER.warn("No instrument id or symbol provided, skipping ClientRequest");
                throw new IllegalArgumentException("No instrument id or symbol provided");
            }
            if (StringUtils.isNotEmpty(response.getVenueAccount())) {
                orderStateBuilder.setVenueAccountId(response.getVenueAccount());
            } else if (request.getVenueAccountsCount() == 1) {
                orderStateBuilder.setVenueAccountId(request.getVenueAccounts(0));
            }

            // Execution details from Response
            orderStateBuilder
                .setOrderStatus(toOrderStatus(response.getOrderStatus()))
                .setOrderQty(response.getOrderQty())
                .setFilledQty(response.getCumQty())
                .setRemainingQty(response.getLeavesQty())
                .setLastQty(response.getLastQty())
                .setAvgPrice(response.getAvgPrice())
                .setLastPrice(response.getLastPrice())
                .setCurrency(response.getCurrency())
                .setReason(response.getReason())
                .setLastRequestResult(response.getRequestResult().getValueDescriptor().getName())
                .setUpdatedAt(response.hasMetadata() ? response.getMetadata().getUpdatedAt() : response.getMetadata().getCreatedAt())
                .setOrderCategory(toOrderCategory(response.getOrderCategory().name()))
                .setVenueTimestamp(response.getTargetVenueTimestamp())
                .setUnderlyingVenueAccount(response.getUnderlyingVenueAccount())
                .setCounterPortfolioId(response.getCounterPortfolioId())
                .setSequenceNumber(getSequenceNumber(response));

            return orderStateBuilder.build();
        } catch (Exception e) {
            LOGGER.error("Failed to build OrderState from ClientRequest and ClientResponse: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to build OrderState", e);
        }
    }

    private static int getSequenceNumber(ClientResponse response) {
        String source = response.hasMetadata() ? response.getMetadata().getSource() : EMPTY;
        return NumberUtils.isCreatable(source) ? Integer.parseInt(source) : response.getSequenceNumber();
    }

    private static int getSequenceNumber(OemsResponse response) {
        String source = response.hasMetadata() ? response.getMetadata().getSource() : EMPTY;
        return NumberUtils.isCreatable(source) ? Integer.parseInt(source) : -1;
    }

    private static String getCurrentIsoTime() {
        return DateUtils.toIsoUtcTime(ZonedDateTime.now());
    }
}
