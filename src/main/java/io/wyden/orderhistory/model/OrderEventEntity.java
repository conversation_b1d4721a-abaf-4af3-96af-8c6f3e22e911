package io.wyden.orderhistory.model;

import io.wyden.orderhistory.service.utils.TimeBasedUuidGenerator;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.Table;

import java.time.OffsetDateTime;

@Entity
@Table(name = "order_events", indexes = {
    @Index(name = "idx_order_events_order_id", columnList = "order_id"),
    @Index(name = "idx_order_events_latest", columnList = "order_id, message_timestamp DESC")
})
public class OrderEventEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "order_id", nullable = false)
    private String orderId;

    @Column(name = "message_type", nullable = false)
    @Enumerated(EnumType.STRING)
    private MessageType messageType;

    @Column(name = "message_timestamp", nullable = false)
    private OffsetDateTime messageTimestamp;

    @Column(name = "proto_blob", nullable = false)
    private byte[] protoBlob;

    @Column(name = "created_at", nullable = false)
    private OffsetDateTime createdAt;

    @Column(name = "time_based_uuid", nullable = false, unique = true)
    private String timeBasedUuid;

    // Constructors
    public OrderEventEntity() {
        this.createdAt = OffsetDateTime.now();
        this.timeBasedUuid = TimeBasedUuidGenerator.generateTimeBasedUuid();
    }

    public OrderEventEntity(String orderId, MessageType messageType, OffsetDateTime messageTimestamp, byte[] protoBlob) {
        this();
        this.orderId = orderId;
        this.messageType = messageType;
        this.messageTimestamp = messageTimestamp;
        this.protoBlob = protoBlob;
    }

    public OrderEventEntity(String orderId, MessageType messageType, OffsetDateTime messageTimestamp, byte[] protoBlob, String timeBasedUuid) {
        this.orderId = orderId;
        this.messageType = messageType;
        this.messageTimestamp = messageTimestamp;
        this.protoBlob = protoBlob;
        this.createdAt = OffsetDateTime.now();
        this.timeBasedUuid = timeBasedUuid;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public MessageType getMessageType() {
        return messageType;
    }

    public void setMessageType(MessageType messageType) {
        this.messageType = messageType;
    }

    public OffsetDateTime getMessageTimestamp() {
        return messageTimestamp;
    }

    public void setMessageTimestamp(OffsetDateTime messageTimestamp) {
        this.messageTimestamp = messageTimestamp;
    }

    public byte[] getProtoBlob() {
        return protoBlob;
    }

    public void setProtoBlob(byte[] protoBlob) {
        this.protoBlob = protoBlob;
    }

    public OffsetDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(OffsetDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public String getTimeBasedUuid() {
        return timeBasedUuid;
    }

    public void setTimeBasedUuid(String timeBasedUuid) {
        this.timeBasedUuid = timeBasedUuid;
    }

    // Builder pattern
    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private String orderId;
        private MessageType messageType;
        private OffsetDateTime messageTimestamp;
        private byte[] protoBlob;
        private String timeBasedUuid;

        public Builder orderId(String orderId) {
            this.orderId = orderId;
            return this;
        }

        public Builder messageType(MessageType messageType) {
            this.messageType = messageType;
            return this;
        }

        public Builder messageTimestamp(OffsetDateTime messageTimestamp) {
            this.messageTimestamp = messageTimestamp;
            return this;
        }

        public Builder protoBlob(byte[] protoBlob) {
            this.protoBlob = protoBlob;
            return this;
        }

        public Builder timeBasedUuid(String timeBasedUuid) {
            this.timeBasedUuid = timeBasedUuid;
            return this;
        }

        public OrderEventEntity build() {
            if (timeBasedUuid != null) {
                return new OrderEventEntity(orderId, messageType, messageTimestamp, protoBlob, timeBasedUuid);
            }
            return new OrderEventEntity(orderId, messageType, messageTimestamp, protoBlob);
        }
    }

    @Override
    public String toString() {
        return "OrderEventEntity{" +
                "id=" + id +
                ", orderId='" + orderId + '\'' +
                ", messageType='" + messageType + '\'' +
                ", messageTimestamp=" + messageTimestamp +
                ", createdAt=" + createdAt +
                ", timeBasedUuid='" + timeBasedUuid + '\'' +
                '}';
    }
}
