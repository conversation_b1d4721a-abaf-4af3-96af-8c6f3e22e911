package io.wyden.oems.storage.config;

import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

public class StringRowMapper implements RowMapper<String> {

    private final String columnLabel;

    public StringRowMapper(String columnLabel) {
        this.columnLabel = columnLabel;
    }

    @Override
    public String mapRow(ResultSet rs, int rowNum) throws SQLException {
        return rs.getString(columnLabel);
    }
}
