package io.wyden.oems.storage.infra;

import org.junit.jupiter.api.extension.AfterEachCallback;
import org.junit.jupiter.api.extension.BeforeAllCallback;
import org.junit.jupiter.api.extension.BeforeEachCallback;
import org.junit.jupiter.api.extension.ExtensionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testcontainers.containers.BindMode;
import org.testcontainers.containers.Container;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.shaded.org.awaitility.Awaitility;
import org.testcontainers.utility.DockerImageName;

import java.io.IOException;
import java.time.Duration;
import java.util.UUID;
import java.util.function.Predicate;

public class PostgreSQLSetupExtension extends DatabaseSetupExtension implements BeforeAllCallback, BeforeEachCallback, AfterEachCallback {

    private static final Logger LOGGER = LoggerFactory.getLogger(PostgreSQLSetupExtension.class);

    protected static PostgreSQLContainer<?> postgreSQLContainer = new PostgreSQLContainer<>(DockerImageName.parse("docker.wyden.io/mirror/postgres:14.9")
        .asCompatibleSubstituteFor("postgres"))
        .withClasspathResourceMapping("psql/migration/data/V999_0__reset_test_data.sql", "/etc/psql/migration/data/V999_0__reset_test_data.sql", BindMode.READ_ONLY)
        .withReuse(false)
        .withDatabaseName("storage")
        .withUsername("storage")
        .withPassword("password");

    @Override
    public void beforeAll(ExtensionContext context) throws Exception {
        injectStaticSelf(context);
        postgreSQLContainer.start();

        String urlSuffix = postgreSQLContainer.getHost() + ":" + postgreSQLContainer.getFirstMappedPort()
            + "/" + postgreSQLContainer.getDatabaseName();

        System.setProperty("spring.datasource.url", "jdbc:postgresql://" + urlSuffix);
        System.setProperty("spring.flyway.locations", "classpath:psql/migration/schema,classpath:psql/migration/data");
        System.setProperty("db.engine", "psql");

        System.setProperty("hz.instanceName", UUID.randomUUID().toString());
    }

    @Override
    public void beforeEach(ExtensionContext context) throws Exception {
        injectSelf(context);
        resetDatabase();
    }

    @Override
    public void afterEach(ExtensionContext context) throws Exception {
        resetDatabase();
    }

    private void resetDatabase() throws Exception {
        LOGGER.info("Resetting DB data...");
        postgreSQLContainer.execInContainer("sh", "-c", "psql -U storage -d storage -f /etc/psql/migration/data/V999_0__reset_test_data.sql");
    }

    public String queryDb(String query) throws IOException, InterruptedException {
        String command = "psql -U storage -d storage -c '%s'".formatted(query);
        Container.ExecResult result = postgreSQLContainer.execInContainer("sh", "-c", command);
        return result.getStdout();
    }

    @Override
    public void logTableContent(String tableName) throws IOException, InterruptedException {
        String query = "SELECT * from %s".formatted(tableName);
        LOGGER.debug(queryDb(query));
    }

    @Override
    public void awaitExpectedContentInTable(String tableName, Predicate<String> predicate) {
        String query = "SELECT * FROM %s".formatted(tableName);
        Awaitility.await()
            .atMost(Duration.ofSeconds(60))
            .until(() -> {
                String stdout = queryDb(query);
                return predicate.test(stdout);
            });
    }

    @Override
    public void awaitExpectedTextInTable(String tableName, String needle) {
        awaitExpectedContentInTable(tableName, t -> t.contains(needle));
    }

    @Override
    public void awaitExpectedRecordsInTable(String tableName, int i) {
        String query = "SELECT count(*) FROM %s".formatted(tableName);
        Awaitility.await()
            .atMost(Duration.ofSeconds(60))
            .until(() -> {
                String expected = i + "\n(1 row)";
                String stdout = queryDb(query);
                return stdout.contains(expected);
            });
    }
}
