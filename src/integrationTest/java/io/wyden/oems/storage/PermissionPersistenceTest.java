package io.wyden.oems.storage;

import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import io.wyden.accessgateway.domain.permission.PermissionGroupListMapConfig;
import io.wyden.accessgateway.domain.permission.PermissionGroupMapConfig;
import io.wyden.accessgateway.domain.permission.PermissionUserListMapConfig;
import io.wyden.accessgateway.domain.permission.PermissionUserMapConfig;
import io.wyden.oems.storage.infra.DatabaseSetupExtension;
import io.wyden.oems.storage.infra.IntegrationTestBase;
import io.wyden.oems.storage.infra.OracleSetupExtension;
import io.wyden.oems.storage.infra.PostgreSQLSetupExtension;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.DirtiesContext;

import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;

public abstract class PermissionPersistenceTest extends IntegrationTestBase {

    static DatabaseSetupExtension DB;

    @Autowired
    private HazelcastInstance hazelcastInstance;

    @Test
    void shouldReflectFlywayMigrationsInHz() throws Exception {

        // given
        Set<Permission> expectedPermissions = Set.of(
            Permission.of("venue.account", "create"),
            Permission.of("portfolio", "create"),
            Permission.of("risk", "read"),
            Permission.of("risk", "manage"),
            Permission.of("broker.config", "read"),
            Permission.of("broker.config", "manage"),
            Permission.of("wallet", "create"),
            Permission.of("wallet", "manage"),
            Permission.of("wallet", "trade"),
            Permission.of("client.instrument", "create"),
            Permission.of("clob.instrument", "create"),
            Permission.of("clob.instrument", "manage"),
            Permission.of("portfolio", "manage"),
            Permission.of("venue.account", "manage"),
            Permission.of("currency", "manage"),
            Permission.of("api.key", "manage"),
            Permission.of("api.key", "create"),
            Permission.of("client.instrument", "manage"),
            Permission.of("venue", "create"),
            Permission.of("venue", "manage"),
            Permission.of("venue.account", "read"),
            Permission.of("portfolio", "read"),
            Permission.of("venue.account", "trade"),
            Permission.of("portfolio", "trade"),
            Permission.of("wallet", "read"),
            Permission.of("client.instrument", "read"),
            Permission.of("portfolio.nostro", "read"),
            Permission.of("portfolio.nostro", "manage"),
            Permission.of("portfolio.nostro", "trade"),
            Permission.of("portfolio.nostro", "create"),
            Permission.of("portfolio.vostro", "read"),
            Permission.of("portfolio.vostro", "manage"),
            Permission.of("portfolio.vostro", "trade"),
            Permission.of("portfolio.vostro", "create"),
            Permission.of("wallet.nostro", "read"),
            Permission.of("wallet.nostro", "manage"),
            Permission.of("wallet.nostro", "create"),
            Permission.of("wallet.nostro", "trade"),
            Permission.of("wallet.vostro", "read"),
            Permission.of("wallet.vostro", "manage"),
            Permission.of("wallet.vostro", "create"),
            Permission.of("wallet.vostro", "trade"),
            Permission.of("settlement", "read"),
            Permission.of("settlement", "manage"),
            Permission.of("connector", "create"),
            Permission.of("connector", "manage"),
            Permission.of("connector", "read")
        );
        // when
        IMap<String, String> groupPermissions = PermissionGroupMapConfig.getMap(hazelcastInstance);
        // then
        expectedPermissions.forEach(p -> {
            String key = PermissionGroupMapConfig.toKey("administrators", p.resource(), p.scope(), p.resourceId());
            assertThat(groupPermissions.containsKey(key)).isTrue();
        });
    }

    @Test
    void shouldReflectUserModificationInOtherMaps() {

        //given
        IMap<String, String> userPermissions = PermissionUserMapConfig.getMap(hazelcastInstance);
        IMap<String, Set<String>> userPermissionList = PermissionUserListMapConfig.getMap(hazelcastInstance);

        String key1 = PermissionUserMapConfig.toKey("trader", "venue.account", "trade", "resourceId");
        String key2 = PermissionUserMapConfig.toKey("admin", "portfolio", "read", "portfolio-id");
        String key3 = PermissionUserMapConfig.toKey("admin", "portfolio", "create", null);
        String queryKey1 = PermissionUserListMapConfig.toKey("trader", "venue.account", "trade", 100);
        String queryKey2 = PermissionUserListMapConfig.toKey("admin", "portfolio", "read", 100);
        String queryKey3 = PermissionUserListMapConfig.toKey("admin", "portfolio", "create", 100);

        //when
        userPermissions.put(key1, "true");
        userPermissions.put(key2, "true");
        userPermissions.put(key3, "true");
        userPermissionList.evictAll();

        //then
        assertThat(userPermissionList.get(queryKey1)).isEqualTo(Set.of("resourceId"));
        assertThat(userPermissionList.get(queryKey2)).isEqualTo(Set.of("portfolio-id"));
        assertThat(userPermissionList.get(queryKey3)).isEqualTo(Set.of());

        //and when
        userPermissions.delete(key1);
        userPermissions.delete(key2);
        userPermissions.delete(key3);
        userPermissionList.evictAll();

        //then
        assertThat(userPermissionList.get(queryKey1)).isEqualTo(Set.of());
        assertThat(userPermissionList.get(queryKey2)).isEqualTo(Set.of());
        assertThat(userPermissionList.get(queryKey3)).isEqualTo(Set.of());
    }

    @Test
    void shouldReflectGroupModificationInOtherMaps() {
        //given

        IMap<String, String> groupPermissions = PermissionGroupMapConfig.getMap(hazelcastInstance);
        IMap<String, Set<String>> groupPermissionList = PermissionGroupListMapConfig.getMap(hazelcastInstance);

        String key1 = PermissionGroupMapConfig.toKey("newGroup", "venue.account", "trade", "resourceId");
        String key2 = PermissionGroupMapConfig.toKey("admin", "portfolio", "read", "portfolio-id");
        String key3 = PermissionGroupMapConfig.toKey("admin", "non-existing", "create", null);
        String queryKey1 = PermissionGroupListMapConfig.toKey("newGroup", "venue.account", "trade", 100);
        String queryKey2 = PermissionGroupListMapConfig.toKey("admin", "portfolio", "read", 100);
        String queryKey3 = PermissionGroupListMapConfig.toKey("admin", "non-existing", "create", 100);

        //when
        groupPermissions.put(key1, "true");
        groupPermissions.put(key2, "true");
        groupPermissions.put(key3, "true");
        groupPermissionList.evictAll();

        //then
        assertThat(groupPermissionList.get(queryKey1)).isEqualTo(Set.of("resourceId"));
        assertThat(groupPermissionList.get(queryKey2)).isEqualTo(Set.of("portfolio-id"));
        assertThat(groupPermissionList.get(queryKey3)).isEqualTo(Set.of());

        //and when
        groupPermissions.delete(key1);
        groupPermissions.delete(key2);
        groupPermissions.delete(key3);
        groupPermissionList.evictAll();

        //then
        assertThat(groupPermissionList.get(queryKey1)).isEqualTo(Set.of());
        assertThat(groupPermissionList.get(queryKey2)).isEqualTo(Set.of());
        assertThat(groupPermissionList.get(queryKey3)).isEqualTo(Set.of());
    }

    record Permission(String resource, String scope, String resourceId) {

        static Permission of(String resource, String scope) {
            return new Permission(resource, scope, null);
        }

        static Permission of(String resource, String scope, String resourceId) {
            return new Permission(resource, scope, resourceId);
        }
    }

    @ExtendWith(OracleSetupExtension.class)
    @DirtiesContext(classMode = DirtiesContext.ClassMode.BEFORE_CLASS)
    @Disabled
    static class OracleClientOrdersPersistenceTest extends PermissionPersistenceTest {

    }

    @ExtendWith(PostgreSQLSetupExtension.class)
    @DirtiesContext(classMode = DirtiesContext.ClassMode.BEFORE_CLASS)
    static class PostgresClientOrdersPersistenceTest extends PermissionPersistenceTest {

    }
}
