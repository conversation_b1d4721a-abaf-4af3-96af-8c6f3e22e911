package io.wyden.orderhistory.service;

import com.hazelcast.core.HazelcastInstance;
import io.wyden.orderhistory.PostgreSQLSetupExtension;
import io.wyden.orderhistory.model.OrderHistorySearchInput;
import io.wyden.orderhistory.model.OrderStateEntity;
import io.wyden.orderhistory.repository.PostgresOrderStateRepository;
import io.wyden.published.reporting.OrderState;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.datasource.init.ScriptUtils;
import org.springframework.test.annotation.DirtiesContext;

import java.sql.SQLException;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.sql.DataSource;

import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;

@ExtendWith(PostgreSQLSetupExtension.class)
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_CLASS)
@MockBean(classes = { HazelcastInstance.class })
public class MigrationValidationIntegrationTest extends OrderHistoryServiceDbIntegrationTestBase {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(MigrationValidationIntegrationTest.class);

    @Autowired
    private OrderStateMigrationService orderStateMigrationService;

    @Autowired
    private OrderHistoryService deprecatedOrderHistoryService;

    @Autowired
    private OrderStateQueryService newOrderStateQueryService;

    @Autowired
    private PostgresOrderStateRepository orderStateRepository;

    @Autowired
    private DataSource dataSource;

    @BeforeEach
    void setUp() throws SQLException {
        // Load test data
//        ScriptUtils.executeSqlScript(dataSource.getConnection(), new ClassPathResource("psql/migration-test/schema/order_history_uat_order_state.sql"));
//        ScriptUtils.executeSqlScript(dataSource.getConnection(), new ClassPathResource("psql/migration-test/schema/order_history_uat_order_state_snapshot.sql"));
//        ScriptUtils.executeSqlScript(dataSource.getConnection(), new ClassPathResource("psql/migration-test/schema/test_order_state.sql"));
//        ScriptUtils.executeSqlScript(dataSource.getConnection(), new ClassPathResource("psql/migration-test/schema/test_order_state_snapshot.sql"));
        ScriptUtils.executeSqlScript(dataSource.getConnection(), new ClassPathResource("psql/migration-test/schema/test_data__order_state.sql"));
        ScriptUtils.executeSqlScript(dataSource.getConnection(), new ClassPathResource("psql/migration-test/schema/test_data__order_state_snapshot.sql"));
    }

    @Test
    void shouldValidateMigrationResultsForAllOrders() {
        // Given: UAT data
        List<OrderStateEntity> originalOrderStates = orderStateRepository.findOrderStatesBySearchInput(
            new OrderHistorySearchInput(List.of(), List.of(), List.of(), 10000, null, null, null));
        assertThat(originalOrderStates).isNotEmpty();

        // Create search input that covers all orders
        OrderHistorySearchInput searchInput = new OrderHistorySearchInput(
            List.of(), List.of(), List.of(), 10000, null, null, null);

        // When: Get results from deprecated service (before migration)
        List<OrderState> deprecatedResults = deprecatedOrderHistoryService.getOrderStates(searchInput);
        LOGGER.info("Deprecated service returned {} orders", deprecatedResults.size());
        
        // Perform migration
        LOGGER.info("Starting migration...");
        boolean migrationCompleted = orderStateMigrationService.migrateOrderStates();
        assertThat(migrationCompleted).as("Migration should complete successfully").isTrue();
        LOGGER.info("Migration completed");
        
        // Wait for async processing to complete and search index to be populated
        await().atMost(Duration.ofSeconds(10))
               .pollInterval(Duration.ofMillis(500))
               .until(() -> {
                   List<OrderState> results = newOrderStateQueryService.getOrderStates(searchInput);
                   LOGGER.debug("Polling for search index population: {} results found", results.size());
                   return !results.isEmpty();
               });
        
        // Get results from new service (after migration and async processing)
        List<OrderState> newResults = newOrderStateQueryService.getOrderStates(searchInput);
        LOGGER.info("New service returned {} orders", newResults.size());

        // Then: Migration should complete successfully
        // Note: The new service uses a different data model (OrderEventEntity + OrderHistorySearchIndex)
        // while the deprecated service uses OrderStateEntity. The migration creates events in the new format,
        // but the search index may need additional processing to be populated.
        assertThat(deprecatedResults).as("Deprecated service should return test data").isNotEmpty();

        if (!newResults.isEmpty()) {
            // Verify each order state is valid
            for (OrderState orderState : newResults) {
                assertThat(orderState.getOrderId()).as("Order ID should not be null").isNotNull();
                assertThat(orderState.getOrderStatus()).as("Order status should not be null").isNotNull();
            }
            LOGGER.info("Deprecated service returned {} orders", deprecatedResults.size());
            LOGGER.info("Validation passed for {} migrated orders", newResults.size());
        } else {
            LOGGER.info("New service returned no results - this may be expected if search index needs async processing");
        }
    }

    @Test
    void shouldReturnSameOrderStatesForDeprecatedAndNewService() {
        // Given: UAT data
        OrderHistorySearchInput historySearch = new OrderHistorySearchInput(List.of(), List.of(), List.of(), 10000, null, null, null);

        List<OrderStateEntity> originalOrderStates = orderStateRepository.findOrderStatesBySearchInput(historySearch);
        assertThat(originalOrderStates).isNotEmpty();

        // Perform migration
        LOGGER.info("Starting migration...");
        boolean migrationCompleted = orderStateMigrationService.migrateOrderStates();
        assertThat(migrationCompleted).as("Migration should complete successfully").isTrue();
        LOGGER.info("Migration completed");

        // When: Get results from deprecated service (before migration)
        List<OrderState> deprecatedResults = deprecatedOrderHistoryService.getOrderStates(historySearch);
        assertThat(deprecatedResults).as("Deprecated service should return test data").isNotEmpty();
        LOGGER.info("Deprecated service returned {} orders", deprecatedResults.size());

        // Wait for async processing to complete and get order states
        List<OrderState> newResults = await("Await for migrated orders from new service")
            .atMost(Duration.ofSeconds(5))
            .pollInterval(Duration.ofSeconds(1))
            .until(() -> newOrderStateQueryService.getOrderStates(historySearch),
                orderStates -> {
                    LOGGER.debug("Polling for search index population: {} results found", orderStates.size());
                    return orderStates.size() == deprecatedResults.size();
                });

        // Group order states by orderId for comparison
        Map<String, List<OrderState>> deprecatedByOrderId = deprecatedResults.stream()
            .collect(Collectors.groupingBy(OrderState::getOrderId));
        
        Map<String, List<OrderState>> newByOrderId = newResults.stream()
            .collect(Collectors.groupingBy(OrderState::getOrderId));
        
        LOGGER.info("Comparing {} orders from deprecated service with {} orders from new service", deprecatedByOrderId.size(), newByOrderId.size());
        
        // Verify that all orders from deprecated service exist in new service
        Set<String> deprecatedOrderIds = deprecatedByOrderId.keySet();
        Set<String> newOrderIds = newByOrderId.keySet();
        
        assertThat(newOrderIds).as("New service should contain all orders from deprecated service").containsAll(deprecatedOrderIds);
        
        // For each order, verify the order states match
        for (String orderId : deprecatedOrderIds) {
            List<OrderState> deprecatedStates = deprecatedByOrderId.get(orderId);
            List<OrderState> newStates = newByOrderId.get(orderId);
            
            LOGGER.debug("Validating order {}: deprecated has {} states, new has {} states", orderId, deprecatedStates.size(), newStates.size());

            assertThat(newStates.size()).as("Order %s: new states (%d) should have same size as deprecated states (%d)",
                orderId, newStates.size(), deprecatedStates.size())
                .isEqualTo(deprecatedStates.size());

             validateOrderStateFields(orderId, deprecatedStates, newStates);
        }
        
        LOGGER.info("Migration validation completed successfully for {} orders", deprecatedOrderIds.size());
    }


    private void validateOrderStateFields(String orderId, List<OrderState> deprecatedStates, List<OrderState> newStates) {
        deprecatedStates.sort(Comparator.comparing(OrderState::getSequenceNumber));
        newStates.sort(Comparator.comparing(OrderState::getSequenceNumber));

        for (int i = 0; i < newStates.size(); i++) {
            boolean comparisonResult = compareStates(deprecatedStates.get(i), newStates.get(i));
            assertThat(comparisonResult)
                .as("Order states from deprecated and new service doesn't match for order: %s\n Old: %s\n New: %s", orderId, deprecatedStates.get(i), newStates.get(i))
                .isTrue();
        }
        
        LOGGER.debug("Order {} field validation completed", orderId);
    }
    
    private boolean compareStates(OrderState deprecated, OrderState newState) {
        List<String> differences = new ArrayList<>();
        
        if (!deprecated.getOrderId().equals(newState.getOrderId())) {
            differences.add(String.format("OrderId: deprecated='%s' vs new='%s'", deprecated.getOrderId(), newState.getOrderId()));
        }
        if (!deprecated.getOrderStatus().equals(newState.getOrderStatus())) {
            differences.add(String.format("OrderStatus: deprecated='%s' vs new='%s'", deprecated.getOrderStatus(), newState.getOrderStatus()));
        }
        if (!deprecated.getSide().equals(newState.getSide())) {
            differences.add(String.format("Side: deprecated='%s' vs new='%s'", deprecated.getSide(), newState.getSide()));
        }
        if (!deprecated.getOrderType().equals(newState.getOrderType())) {
            differences.add(String.format("OrderType: deprecated='%s' vs new='%s'", deprecated.getOrderType(), newState.getOrderType()));
        }
        if (!deprecated.getSymbol().equals(newState.getSymbol())) {
            differences.add(String.format("Symbol: deprecated='%s' vs new='%s'", deprecated.getSymbol(), newState.getSymbol()));
        }
        if (!deprecated.getOrderQty().equals(newState.getOrderQty())) {
            differences.add(String.format("OrderQty: deprecated='%s' vs new='%s'", deprecated.getOrderQty(), newState.getOrderQty()));
        }
        if (!deprecated.getLimitPrice().equals(newState.getLimitPrice())) {
            differences.add(String.format("LimitPrice: deprecated='%s' vs new='%s'", deprecated.getLimitPrice(), newState.getLimitPrice()));
        }
        if (!deprecated.getPortfolioId().equals(newState.getPortfolioId())) {
            differences.add(String.format("PortfolioId: deprecated='%s' vs new='%s'", deprecated.getPortfolioId(), newState.getPortfolioId()));
        }
        if (!deprecated.getOrderCategory().equals(newState.getOrderCategory())) {
            differences.add(String.format("OrderCategory: deprecated='%s' vs new='%s'", deprecated.getOrderCategory(), newState.getOrderCategory()));
        }
        if (!deprecated.getClientId().equals(newState.getClientId())) {
            differences.add(String.format("ClientId: deprecated='%s' vs new='%s'", deprecated.getClientId(), newState.getClientId()));
        }
        if (!safeEquals(deprecated.getClOrderId(), newState.getClOrderId())) {
            differences.add(String.format("ClOrderId: deprecated='%s' vs new='%s'", deprecated.getClOrderId(), newState.getClOrderId()));
        }
        if (!safeEquals(deprecated.getInstrumentId(), newState.getInstrumentId())) {
            differences.add(String.format("InstrumentId: deprecated='%s' vs new='%s'", deprecated.getInstrumentId(), newState.getInstrumentId()));
        }
        if (!safeEquals(deprecated.getFilledQty(), newState.getFilledQty())) {
            differences.add(String.format("FilledQty: deprecated='%s' vs new='%s'", deprecated.getFilledQty(), newState.getFilledQty()));
        }
        if (!safeEquals(deprecated.getAvgPrice(), newState.getAvgPrice())) {
            differences.add(String.format("AvgPrice: deprecated='%s' vs new='%s'", deprecated.getAvgPrice(), newState.getAvgPrice()));
        }
        if (!safeEquals(deprecated.getVenueAccountId(), newState.getVenueAccountId())) {
            differences.add(String.format("VenueAccountId: deprecated='%s' vs new='%s'", deprecated.getVenueAccountId(), newState.getVenueAccountId()));
        }
        
        if (!differences.isEmpty()) {
            LOGGER.error("Order state comparison failed for order: {}. Differences: {}", deprecated.getOrderId(), String.join(", ", differences));
            return false;
        }
        
        return true;
    }
    
    private boolean safeEquals(String a, String b) {
        if (a == null && b == null) return true;
        if (a == null || b == null) return false;
        return a.equals(b);
    }
    

}