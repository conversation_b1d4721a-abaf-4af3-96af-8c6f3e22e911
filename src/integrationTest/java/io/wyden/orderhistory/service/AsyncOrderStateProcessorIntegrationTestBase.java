package io.wyden.orderhistory.service;

import com.google.protobuf.Message;
import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import com.hazelcast.test.TestHazelcastInstanceFactory;
import io.wyden.cloud.utils.test.TracingMock;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.queue.MatchingCondition;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueue;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueueBuilder;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.orderhistory.IntegrationTestBase;
import io.wyden.orderhistory.PostgreSQLSetupExtension;
import io.wyden.orderhistory.infrastructure.rabbit.RabbitDestinations;
import io.wyden.orderhistory.infrastructure.rabbit.TradingMessageConsumer;
import io.wyden.orderhistory.infrastructure.rabbit.TradingResponseMessageConsumer;
import io.wyden.orderhistory.repository.OrderEventRepository;
import io.wyden.orderhistory.repository.OrderHistorySearchIndexRepository;
import io.wyden.orderhistory.service.inbound.ResponseMessageHandler;
import io.wyden.published.client.ClientOrderStatus;
import io.wyden.published.client.ClientRequest;
import io.wyden.published.client.ClientResponse;
import io.wyden.published.oems.OemsOrderStatus;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.published.reporting.OrderState;
import io.wyden.referencedata.client.PortfoliosCacheFacade;
import io.wyden.referencedata.domain.PortfolioMapConfig;
import org.awaitility.Awaitility;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.test.context.ContextConfiguration;

import java.io.IOException;
import java.time.Duration;
import java.time.ZonedDateTime;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

import static io.wyden.cloudutils.rabbitmq.ConsumptionResult.consumed;
import static io.wyden.orderhistory.utils.TestingData.clientNewOrderRequest;
import static io.wyden.orderhistory.utils.TestingData.expectedExecutionReportWithTimestamp;
import static io.wyden.orderhistory.utils.TestingData.expectedOemsExecutionReport;
import static io.wyden.orderhistory.utils.TestingData.oemsRequest;
import static io.wyden.orderhistory.utils.TestingData.oemsRequestWithInstrumentId;
import static org.testcontainers.shaded.org.awaitility.Awaitility.await;

@SpringBootTest(properties = {"spring.main.allow-bean-definition-overriding=true", "message.consumer.use.v1 = false", "message.consumer.use.v2 = true"})
@ExtendWith(PostgreSQLSetupExtension.class)
@MockBean(classes = { TradingMessageConsumer.class })
@ContextConfiguration(classes = AsyncOrderStateProcessorIntegrationTestBase.TestConfig.class)
public abstract class AsyncOrderStateProcessorIntegrationTestBase extends IntegrationTestBase {

    private static final Logger LOGGER = LoggerFactory.getLogger(AsyncOrderStateProcessorIntegrationTestBase.class);

    static final Map<String, String> TRADING_HEADERS = Map.of("messageType", OemsResponse.class.getSimpleName());
    static final Duration DEFAULT_TIMEOUT = Duration.ofSeconds(3);

    @Autowired
    AsyncOrderStateProcessor asyncOrderStateProcessor;

    @Autowired
    protected RabbitIntegrator rabbitIntegrator;

    @Autowired
    protected OrderEventRepository orderEventRepository;

    @Autowired
    protected OrderHistorySearchIndexRepository searchIndexRepository;

    @Autowired
    protected RabbitDestinations rabbitDestinations;

    @Autowired
    protected RabbitExchange<Message> tradingIngressExchange;

    @Autowired
    protected ResponseMessageHandler responseMessageHandler;

    @Autowired
    protected Telemetry telemetry;

    @Autowired
    protected RabbitExchange<OrderState> exchange;

    @Autowired
    protected IMap<String, Portfolio> portfoliosMap;

    protected TradingResponseMessageConsumer consumer;
    protected String queueName = "order-history-queue.trading.responses";

    private final BlockingQueue<OrderState> consumerOrderStateQueue = new LinkedBlockingQueue<>();
    private String orderStateTag;

    @BeforeEach
    public void setUp() {
        consumer = new TradingResponseMessageConsumer(
            rabbitIntegrator,
            rabbitDestinations.tradingIngressExchange(rabbitIntegrator),
            responseMessageHandler,
            telemetry.getTracing(),
            queueName,
            "async-processor-test-consumer" + UUID.randomUUID());

        RabbitQueue<OrderState> orderStateQueue = new RabbitQueueBuilder<OrderState>(rabbitIntegrator)
            .setQueueName("temporary-order-state-queue"+ UUID.randomUUID())
            .declare();
        orderStateQueue.bindWithHeaders(exchange, MatchingCondition.ALL, Map.of());
        orderStateTag = orderStateQueue.attachConsumer(OrderState.parser(), (orderState, params) -> {
            LOGGER.info("Received order state: {}", orderState);
            consumerOrderStateQueue.add(orderState);
            return consumed();
        });
    }

    @AfterEach
    void tearDownBase() throws InterruptedException {
        Optional.ofNullable(orderStateTag)
            .ifPresent(this::detachOrderStateQueue);
        Optional.ofNullable(consumer.getConsumerTag())
            .ifPresent(this::detachOrderStateQueue);

        Thread.sleep(100);

        searchIndexRepository.deleteAll();
        orderEventRepository.deleteAll();
        portfoliosMap.clear();
    }

    void detachOrderStateQueue(String tag) {
        LOGGER.info("Cancelling consumer for consumerTag: {}", tag);
        try {
            rabbitIntegrator.getConsumptionChannel().basicCancel(tag);
        } catch (IOException e) {
            LOGGER.error("Failed to cancel consumerTag: {}", tag);
            throw new RuntimeException(e);
        }
    }

    OrderState awaitOrderState() {
        return await().atMost(Duration.ofSeconds(5)).until(consumerOrderStateQueue::poll, Objects::nonNull);
    }

    OrderState awaitOrderState(Duration duration) {
        return await().atMost(duration).until(consumerOrderStateQueue::poll, Objects::nonNull);
    }

    public void ensureNoMoreMessages(Duration duration) {
        Awaitility.await().atMost(duration).until(() -> consumerOrderStateQueue.poll() == null);
    }

    protected OemsRequest createTestOemsRequest(String orderId) {
        return oemsRequest(orderId, null);
    }

    protected OemsResponse createTestOemsResponse() {
        OemsRequest oemsRequest = createTestOemsRequest(UUID.randomUUID().toString());
        return expectedOemsExecutionReport(oemsRequest, OemsOrderStatus.STATUS_FILLED, 10, ZonedDateTime.now());
    }

    protected OemsResponse createTestOemsResponse(String orderId) {
        OemsRequest oemsRequest = createTestOemsRequest(orderId);
        return expectedOemsExecutionReport(oemsRequest, OemsOrderStatus.STATUS_FILLED, 10, ZonedDateTime.now());
    }

    protected OemsResponse createTestOemsResponse(String orderId, ZonedDateTime timestamp) {
        OemsRequest oemsRequest = oemsRequest(orderId, UUID.randomUUID().toString());
        return expectedOemsExecutionReport(oemsRequest, OemsOrderStatus.STATUS_FILLED, 10, timestamp);
    }

    protected OemsResponse createTestOemsResponse(String orderId, String instrumentId) {
        OemsRequest oemsRequest = oemsRequestWithInstrumentId(orderId, UUID.randomUUID().toString(), instrumentId);
        return expectedOemsExecutionReport(oemsRequest, OemsOrderStatus.STATUS_FILLED, 10, ZonedDateTime.now());
    }

    protected OemsResponse createTestOemsResponse(String orderId, ZonedDateTime timestamp, OemsOrderStatus orderStatus, Integer cumQty) {
        OemsRequest oemsRequest = oemsRequest(orderId, UUID.randomUUID().toString(), orderStatus);
        return expectedOemsExecutionReport(oemsRequest, orderStatus, cumQty, timestamp);
    }

    protected ClientResponse createTestClientResponse() {
        return createTestClientResponse(UUID.randomUUID().toString());
    }

    protected ClientResponse createTestClientResponse(String orderId) {
        ClientRequest request = clientNewOrderRequest(orderId, ZonedDateTime.now());
        return expectedExecutionReportWithTimestamp(1, request, ClientOrderStatus.NEW);
    }

    protected ClientResponse createTestClientResponse(String orderId, ZonedDateTime timestamp) {
        ClientRequest request = clientNewOrderRequest(orderId, timestamp);
        return expectedExecutionReportWithTimestamp(1, request, ClientOrderStatus.NEW);
    }

    protected ClientResponse createTestClientResponse(String orderId, ZonedDateTime timestamp, ClientOrderStatus orderStatus) {
        ClientRequest request = clientNewOrderRequest(orderId, timestamp);
        return expectedExecutionReportWithTimestamp(1, request, orderStatus);
    }

    @TestConfiguration
    public static class TestConfig {

        @Primary
        @Bean("hazelcast")
        HazelcastInstance hazelcastInstance() {
            return new TestHazelcastInstanceFactory().newHazelcastInstance();
        }

        @Bean
        IMap<String, Portfolio> portfoliosMap(HazelcastInstance hazelcastInstance) {
            return PortfolioMapConfig.getMap(hazelcastInstance);
        }

        @Bean
        PortfoliosCacheFacade portfoliosCacheFacade(IMap<String, Portfolio> portfoliosMap) {
            Tracing otlTracingMock = TracingMock.createMock();
            return new PortfoliosCacheFacade(portfoliosMap, otlTracingMock);
        }
    }

}
