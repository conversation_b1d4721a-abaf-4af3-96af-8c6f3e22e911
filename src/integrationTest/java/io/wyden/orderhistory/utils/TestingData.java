package io.wyden.orderhistory.utils;

import com.github.javafaker.Faker;
import io.wyden.cloudutils.rabbitmq.destination.OemsTarget;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.client.ClientExecType;
import io.wyden.published.client.ClientInstrumentType;
import io.wyden.published.client.ClientOrderStatus;
import io.wyden.published.client.ClientOrderType;
import io.wyden.published.client.ClientRequest;
import io.wyden.published.client.ClientRequestType;
import io.wyden.published.client.ClientResponse;
import io.wyden.published.client.ClientResponseType;
import io.wyden.published.client.ClientSide;
import io.wyden.published.client.ClientTIF;
import io.wyden.published.common.Metadata;
import io.wyden.published.oems.OemsExecType;
import io.wyden.published.oems.OemsInstrumentType;
import io.wyden.published.oems.OemsOrderCategory;
import io.wyden.published.oems.OemsOrderStatus;
import io.wyden.published.oems.OemsOrderType;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import io.wyden.published.oems.OemsSide;
import io.wyden.published.oems.OemsTIF;
import io.wyden.published.reporting.OrderStatus;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.testcontainers.shaded.org.apache.commons.lang3.ObjectUtils;

import java.math.BigDecimal;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

public class TestingData {

    private static final Faker FAKER = new Faker();
    public static final String INTEGRATION_TEST_INSTRUMENT_ID = "BTCUSD@FOREX@Test";
    public static final String INTEGRATION_TEST_INSTRUMENT_ID_2 = "ETHUSD@FOREX@Test";
    public static final List<OrderStatus> OPEN_ORDER_STATUSES = List.of(OrderStatus.PENDING_NEW, OrderStatus.NEW, OrderStatus.PARTIALLY_FILLED,
        OrderStatus.PENDING_CANCEL, OrderStatus.PENDING_NEW, OrderStatus.PENDING_REPLACE);
    public static final List<OrderStatus> CLOSED_ORDER_STATUSES = List.of(OrderStatus.FILLED, OrderStatus.REJECTED, OrderStatus.CANCELED,
        OrderStatus.EXPIRED, OrderStatus.REPLACED, OrderStatus.STOPPED, OrderStatus.SUSPENDED);
    public static final String PENDING_NEW = "PENDING_NEW";
    public static final String NEW = "NEW";
    public static final String FILLED = "FILLED";
    public static final String TARGET = "target";
    public static final String PORTFOLIO_A = "Portfolio_A" + "_t'e's't";
    public static final String PORTFOLIO_B = "Portfolio_B";
    public static final String PORTFOLIO_C = "Portfolio_C";
    public static final String VENUE_1 = "Test_1";
    public static final String VENUE_2 = "Test_2";
    public static final String CLIENT_A = "Client_A";
    public static final Integer QUANTITY = 10;
    public static final String CURRENCY = "USD";
    public static final String PRICE = "41000";
    public static final String STOP_PRICE = "39000";
    public static final String ZERO_STRING = "0";
    public static final String ROOT_A = "rootA";
    public static final String ROOT_B = "rootB";
    public static final ClientSide SIDE = ClientSide.BUY;
    public static final String VENUE_ACCOUNT = "OnBitmex-1";
    public static final String CLIENT_ROOT_ORDER_ID = "clientRootOrderId";

    private TestingData() {
        // empty
    }

    public static ClientRequest clientNewOrderRequest(String orderId, ZonedDateTime createdAt) {
        return clientNewOrderRequest(orderId, INTEGRATION_TEST_INSTRUMENT_ID, PORTFOLIO_A, ClientOrderType.MARKET, createdAt);
    }

    public static ClientRequest clientNewOrderRequest(String orderId, int dayOfMonth) {
        ZonedDateTime zonedDateTime = ZonedDateTime.of(2023, 1, dayOfMonth, 12, 0, 0, 0, ZoneId.of("UTC"));
        return clientNewOrderRequest(orderId, INTEGRATION_TEST_INSTRUMENT_ID, PORTFOLIO_A, ClientOrderType.MARKET, zonedDateTime);
    }

    public static ClientRequest clientNewOrderRequest(ClientOrderType orderType, @Nullable ZonedDateTime createdAt) {
        String orderId = "Order-%s".formatted(FAKER.random().hex());
        return clientNewOrderRequest(orderId, INTEGRATION_TEST_INSTRUMENT_ID, PORTFOLIO_A, orderType, createdAt);
    }

    public static ClientRequest clientNewOrderRequest(String instrumentId, ClientOrderType orderType, @Nullable ZonedDateTime createdAt) {
        String orderId = "Order-%s".formatted(FAKER.random().hex());
        return clientNewOrderRequest(orderId, instrumentId, PORTFOLIO_A, orderType, createdAt);
    }

    public static ClientRequest clientNewOrderRequest(String instrumentId, String portfolioId, ClientOrderType orderType, @Nullable ZonedDateTime createdAt) {
        String orderId = "Order-%s".formatted(FAKER.random().hex());
        return clientNewOrderRequest(orderId, instrumentId, portfolioId, orderType, createdAt);
    }

    public static ClientRequest clientNewOrderRequest(String orderId, String instrumentId, String venueAccount, String portfolioId, ClientOrderType orderType, @Nullable ZonedDateTime createdAt) {
        return clientRequest(orderId, null, instrumentId, venueAccount, portfolioId, orderType, ClientRequestType.ORDER_SINGLE, createdAt, QUANTITY);
    }

    public static ClientRequest clientNewOrderRequest(String orderId, String instrumentId, String portfolioId, ClientOrderType orderType, @Nullable ZonedDateTime createdAt) {
        return clientRequest(orderId, null, instrumentId, VENUE_ACCOUNT, portfolioId, orderType, ClientRequestType.ORDER_SINGLE, createdAt, QUANTITY);
    }

    public static ClientRequest clientRequest(ClientRequestType clientRequestType) {
        String orderId = "Order-%s".formatted(FAKER.random().hex());
        return clientRequest(orderId, null, INTEGRATION_TEST_INSTRUMENT_ID, VENUE_ACCOUNT, PORTFOLIO_A, ClientOrderType.MARKET, clientRequestType, ZonedDateTime.now(), QUANTITY);
    }

    public static ClientRequest clientRequest(ClientRequestType clientRequestType, @Nullable String origOrderId, Integer quantity) {
        String orderId = "Order-%s".formatted(FAKER.random().hex());
        return clientRequest(orderId, origOrderId, INTEGRATION_TEST_INSTRUMENT_ID, VENUE_ACCOUNT, PORTFOLIO_A, ClientOrderType.MARKET, clientRequestType, ZonedDateTime.now(), quantity);
    }

    public static ClientRequest clientNewSorOrderRequest(ClientOrderType orderType) {
        String orderId = "Order-%s".formatted(FAKER.random().hex());
        return clientRequest(orderId, null, null, "BTCUSD", ClientInstrumentType.FOREX, VENUE_ACCOUNT, PORTFOLIO_A, orderType, ClientRequestType.ORDER_SINGLE, ZonedDateTime.now(), QUANTITY);
    }

    private static ClientRequest clientRequest(String orderId, @Nullable String origOrderId, @Nullable String instrumentId, @Nullable String venueAccount, String portfolioId,
                                               ClientOrderType orderType, ClientRequestType clientRequestType, @Nullable ZonedDateTime createdAt, Integer quantity) {
        return clientRequest(orderId, origOrderId, instrumentId, null, null, venueAccount, portfolioId, orderType, clientRequestType, createdAt, quantity);
    }

    private static ClientRequest clientRequest(String orderId, @Nullable String origOrderId, @Nullable String instrumentId, @Nullable String symbol,
                                               @Nullable ClientInstrumentType instrumentType, @Nullable String venueAccount, String portfolioId,
                                               ClientOrderType orderType, ClientRequestType clientRequestType, @Nullable ZonedDateTime createdAt, Integer quantity) {
        String clOrderId = "ClientOrder-%s".formatted(FAKER.random().hex());
        Metadata metadata = Metadata.newBuilder()
            .setCreatedAt(DateUtils.toIsoUtcTime(createdAt))
            .setUpdatedAt(DateUtils.toIsoUtcTime(createdAt))
            .build();

        ClientRequest.Builder builder = ClientRequest.newBuilder()
            .setMetadata(metadata)
            .setRequestType(clientRequestType)
            .setOrderId(orderId)
            .setClientId(CLIENT_A)
            .setClOrderId(clOrderId)
            .setPortfolioId(portfolioId)
            .setOrderType(orderType)
            .setSide(SIDE)
            .setQuantity(String.valueOf(quantity))
            .setCurrency(CURRENCY)
            .setTif(ClientTIF.GTC)
            .setCreatedAt(Objects.nonNull(createdAt) ? DateUtils.toIsoUtcTime(createdAt) : DateUtils.toIsoUtcTime(ZonedDateTime.now()));

        if (StringUtils.isNotEmpty(instrumentId)) {
            builder.setInstrumentId(instrumentId);
        }
        if (StringUtils.isNotEmpty(symbol)) {
            builder.setSymbol(symbol);
        }
        if (Objects.nonNull(instrumentType)) {
            builder.setInstrumentType(instrumentType);
        }
        if (orderType == ClientOrderType.LIMIT || orderType == ClientOrderType.STOP_LIMIT) {
            builder.setPrice(PRICE);
        }
        if (orderType == ClientOrderType.STOP) {
            builder.setStopPrice(STOP_PRICE);
        }
        if (Objects.nonNull(origOrderId)) {
            builder.setOrigOrderId(origOrderId);
        }
        if (ObjectUtils.isNotEmpty(venueAccount)) {
            builder.addAllVenueAccounts(List.of(venueAccount));
        }

        return builder.build();
    }

    public static ClientResponse expectedExecutionReportWithTimestamp(int sequenceNumber, ClientRequest newOrderRequest, ClientOrderStatus orderStatus) {
        ZonedDateTime clientTimestamp = Optional.of(newOrderRequest.getCreatedAt())
            .map(DateUtils::isoUtcTimeToZonedDateTime)
            .map(zonedDateTime -> zonedDateTime.plusSeconds(1))
            .orElse(ZonedDateTime.now());
        return executionReport(
            sequenceNumber, newOrderRequest.getOrderId(), newOrderRequest.getOrigOrderId(), newOrderRequest.getClOrderId(),
            orderStatus, ClientExecType.forNumber(orderStatus.getNumber()), newOrderRequest.getVenueAccounts(0), clientTimestamp,
            Integer.valueOf(newOrderRequest.getQuantity()), 0, null).toBuilder()
            .setRequest(newOrderRequest)
            .build();
    }

    public static ClientResponse expectedExecutionReportNew(int sequenceNumber, String orderId, String clOrderId, ClientOrderStatus orderStatus, String venueAccount) {
        return executionReport(sequenceNumber, orderId, null, clOrderId, orderStatus, ClientExecType.CLIENT_EXEC_TYPE_NEW, venueAccount, ZonedDateTime.now(), 0, 0, null);
    }

    public static ClientResponse expectedExecutionReportNew(int sequenceNumber, String orderId, String clOrderId, ClientOrderStatus orderStatus, String venueAccount, String counterPortfolioId) {
        return executionReport(sequenceNumber, orderId, null, clOrderId, orderStatus, ClientExecType.CLIENT_EXEC_TYPE_NEW, venueAccount, ZonedDateTime.now(), 0, 0, counterPortfolioId);
    }

    public static ClientResponse expectedExecutionReportNew(int sequenceNumber, String orderId, String clOrderId, ClientOrderStatus orderStatus, String venueAccount, ZonedDateTime updatedAt) {
        return executionReport(sequenceNumber, orderId, null, clOrderId, orderStatus, ClientExecType.CLIENT_EXEC_TYPE_NEW, venueAccount, updatedAt, 0, 0, null);
    }

    public static ClientResponse expectedExecutionReportNew(int sequenceNumber, String orderId, String clOrderId, ClientOrderStatus orderStatus, String venueAccount, Integer quantity) {
        return executionReport(sequenceNumber, orderId, null, clOrderId, orderStatus, ClientExecType.CLIENT_EXEC_TYPE_NEW, venueAccount, ZonedDateTime.now(), quantity, 0, null);
    }

    public static ClientResponse expectedExecutionReport(int sequenceNumber, String orderId, String clOrderId, ClientOrderStatus orderStatus, ClientExecType execType, String venueAccount, Integer quantity, Integer cumQty) {
        return executionReport(sequenceNumber, orderId, null, clOrderId, orderStatus, execType, venueAccount, ZonedDateTime.now(), quantity, cumQty, null);
    }

    public static ClientResponse expectedExecutionReport(int sequenceNumber, String orderId, String origOrderId, String clOrderId, ClientOrderStatus orderStatus, ClientExecType execType, String venueAccount, Integer quantity, Integer cumQty) {
        return executionReport(sequenceNumber, orderId, origOrderId, clOrderId, orderStatus, execType, venueAccount, ZonedDateTime.now(), quantity, cumQty, null);
    }

    private static ClientResponse executionReport(int sequenceNumber, String orderId, @Nullable String origOrderId, String clOrderId, ClientOrderStatus orderStatus, ClientExecType execType,
                                                  String venueAccount, ZonedDateTime zonedDateTime, Integer quantity, Integer cumQty, @Nullable String counterPortfolioId) {
        ClientResponse.Builder builder = commonClientERBuilder(orderId, clOrderId, venueAccount, zonedDateTime);
        Metadata metadata = Metadata.newBuilder()
            .setCreatedAt(DateUtils.toIsoUtcTime(zonedDateTime))
            .setUpdatedAt(DateUtils.toIsoUtcTime(zonedDateTime))
            .build();

        if (Objects.nonNull(origOrderId)) {
            builder.setOrigOrderId(origOrderId);
        }
        if (Objects.nonNull(counterPortfolioId)) {
            builder.setCounterPortfolioId(counterPortfolioId);
        }

        return builder
            .setMetadata(metadata)
            .setOrderStatus(orderStatus)
            .setExecutionId(UUID.randomUUID().toString())
            .setExecType(execType)
            .setAvgPrice(ZERO_STRING)
            .setLastPrice(ZERO_STRING)
            .setCumQty(String.valueOf(cumQty))
            .setLastQty(ZERO_STRING)
            .setOrderQty(String.valueOf(quantity))
            .setLeavesQty(calculateLeavesQty(quantity, cumQty))
            .setExecutionId(ZERO_STRING)
            .setSequenceNumber(sequenceNumber)
            .build();
    }

    public static OemsResponse expectedOemsExecutionReport(OemsRequest request, OemsOrderStatus orderStatus, Integer cumQty, ZonedDateTime timestamp) {
        return executionReportBuilder(request.getOrderId(), request.getParentOrderId(), orderStatus, OemsExecType.forNumber(orderStatus.getNumber()),
            request.getVenueAccount(), timestamp, Integer.valueOf(request.getQuantity()), cumQty, null)
            .setRequest(request)
            .setInstrumentId(request.getInstrumentId())
            .setInstrumentType(request.getInstrumentType())
            .setPortfolioId(request.getPortfolioId())
            .setClientId(request.getClientId())
            .setSide(request.getSide())
            .setCurrency(request.getCurrency())
            .setBaseCurrency(request.getBaseCurrency())
            .setQuoteCurrency(request.getQuoteCurrency())
            .setOrderCategory(request.getOrderCategory())
            .build();
    }

    public static OemsResponse expectedOemsExecutionReport(String orderId, OemsOrderStatus orderStatus, String venueAccount, Integer quantity, Integer cumQty) {
        return executionReport(orderId, null, orderStatus, OemsExecType.forNumber(orderStatus.getNumber()), venueAccount, ZonedDateTime.now(), quantity, cumQty, null);
    }

    private static OemsResponse executionReport(String orderId, @Nullable String origOrderId, OemsOrderStatus orderStatus, OemsExecType execType, String venueAccount,
                                                ZonedDateTime updatedAt, Integer quantity, Integer cumQty, @Nullable String counterPortfolioId) {
        return executionReportBuilder(orderId, origOrderId, orderStatus, execType, venueAccount, updatedAt, quantity, cumQty, counterPortfolioId).build();
    }

    private static OemsResponse.Builder executionReportBuilder(String orderId, @Nullable String origOrderId, OemsOrderStatus orderStatus, OemsExecType execType, String venueAccount,
                                                ZonedDateTime updatedAt, Integer quantity, Integer cumQty, @Nullable String counterPortfolioId) {
        OemsResponse.Builder builder = commonOemsERBuilder(orderId, venueAccount);
        Metadata metadata = Metadata.newBuilder()
            .setRequestId(String.valueOf(orderStatus.getNumber()))
            .setCreatedAt(DateUtils.toIsoUtcTime(updatedAt))
            .setUpdatedAt(DateUtils.toIsoUtcTime(updatedAt))
            .build();

        if (Objects.nonNull(origOrderId)) {
            builder.setParentOrderId(origOrderId);
        }
        if (Objects.nonNull(counterPortfolioId)) {
            builder.setCounterPortfolioId(counterPortfolioId);
        }

        return builder
            .setMetadata(metadata)
            .setExecutionId(UUID.randomUUID().toString())
            .setExecType(execType)
            .setResponseType(OemsResponse.OemsResponseType.EXECUTION_REPORT)
            .setOrderStatus(orderStatus)
            .setOrderQty(quantity.toString())
            .setCumQty(cumQty.toString())
            .setLeavesQty(calculateLeavesQty(quantity, cumQty));
    }

    private static String calculateLeavesQty(Integer quantity, Integer cumQty) {
        BigDecimal quantityBigDecimal = new BigDecimal(quantity);
        BigDecimal cumQuantityBigDecimal = new BigDecimal(cumQty);
        return quantityBigDecimal.subtract(cumQuantityBigDecimal).toPlainString();
    }

    private static ClientResponse.Builder commonClientERBuilder(String orderId, String clOrderId, String venueAccount, ZonedDateTime zonedDateTime) {
        return ClientResponse.newBuilder()
            .setCreatedAt(DateUtils.toIsoUtcTime(zonedDateTime))
            .setResponseType(ClientResponseType.EXECUTION_REPORT)
            .setOrderId(orderId)
            .setClientId(CLIENT_A)
            .setClOrderId(clOrderId)
            .setSide(ClientSide.BUY)
            .setInstrumentId(INTEGRATION_TEST_INSTRUMENT_ID)
            .setVenueAccount(venueAccount)
            .setPortfolioId(PORTFOLIO_A);
    }

    private static OemsResponse.Builder commonOemsERBuilder(String orderId, String venueAccount) {
        return OemsResponse.newBuilder()
            .setResponseType(OemsResponse.OemsResponseType.EXECUTION_REPORT)
            .setOrderId(orderId)
            .setClientId(CLIENT_A)
            .setSide(OemsSide.BUY)
            .setInstrumentId(INTEGRATION_TEST_INSTRUMENT_ID)
            .setVenueAccount(venueAccount)
            .setPortfolioId(PORTFOLIO_A);
    }

    @NotNull
    public static OemsRequest oemsRequest(String orderId, @Nullable String parentOrderId) {
        return oemsRequest(orderId, parentOrderId, OemsOrderStatus.STATUS_NEW);
    }

    @NotNull
    public static OemsRequest oemsRequest(String orderId, @Nullable String parentOrderId, OemsOrderStatus orderStatus) {
        return oemsRequest(orderId, parentOrderId, CLIENT_ROOT_ORDER_ID, orderStatus, INTEGRATION_TEST_INSTRUMENT_ID);
    }

    @NotNull
    public static OemsRequest oemsRequest(String orderId, @Nullable String parentOrderId, String rootOrderId) {
        return oemsRequest(orderId, parentOrderId, rootOrderId, OemsOrderStatus.STATUS_NEW, INTEGRATION_TEST_INSTRUMENT_ID);
    }

    @NotNull
    public static OemsRequest oemsRequestWithInstrumentId(String orderId, @Nullable String parentOrderId, String instrumentId) {
        return oemsRequest(orderId, parentOrderId, CLIENT_ROOT_ORDER_ID, OemsOrderStatus.STATUS_NEW, instrumentId);
    }

    @NotNull
    public static OemsRequest oemsRequest(String orderId, @Nullable String parentOrderId, String rootOrderId, OemsOrderStatus orderStatus, String instrumentId) {
        Metadata.Builder metadataBuilder = Metadata.newBuilder()
            .setCreatedAt(DateUtils.toIsoUtcTime(ZonedDateTime.now()))
            .setRequestId(String.valueOf(orderStatus.getNumber()))
            .setTarget(TARGET);
        OemsRequest.Builder oemsRequestBuilder = OemsRequest.newBuilder();

        Optional.ofNullable(parentOrderId).ifPresentOrElse(parentId -> {
            metadataBuilder.setSourceType(Metadata.ServiceType.SOR).setTargetType(Metadata.ServiceType.EXTERNAL_VENUE_ACCOUNT);
            oemsRequestBuilder.setParentOrderId(parentId);
            oemsRequestBuilder.setOrderCategory(OemsOrderCategory.SOR_CHILD_ORDER);
        }, () -> {
            metadataBuilder.setSourceType(Metadata.ServiceType.BROKER_DESK).setTargetType(Metadata.ServiceType.SOR);
            oemsRequestBuilder.setOrderCategory(OemsOrderCategory.SOR_ORDER);
        });

        return oemsRequestBuilder
            .setMetadata(metadataBuilder)
            .setClientRootOrderId(CLIENT_ROOT_ORDER_ID)
            .setOrderId(orderId)
            .setRequestType(OemsRequest.OemsRequestType.ORDER_SINGLE)
            .setVenueAccount("testVenue")
            .setInstrumentType(OemsInstrumentType.FOREX)
            .setInstrumentId(instrumentId)
            .setClientId(CLIENT_A)
            .setCurrency("USD")
            .setBaseCurrency("BTC")
            .setQuoteCurrency("USD")
            .setPortfolioId(PORTFOLIO_A)
            .setOrderType(OemsOrderType.MARKET)
            .setSide(OemsSide.BUY)
            .setQuantity(String.valueOf(QUANTITY))
            .setPrice(PRICE)
            .setStopPrice(STOP_PRICE)
            .setTif(OemsTIF.GTC)
            .setRootOrderId(rootOrderId)
            .addAllVenueAccounts(Set.of("Venue-1", "Venue-2"))
            .build();
    }

    @NotNull
    public static OemsRequest oemsRequest(ClientRequest clientRequest) {
        OemsRequest.Builder oemsRequestBuilder = OemsRequest.newBuilder();

        Metadata metadata = clientRequest.getMetadata().toBuilder()
            .setSource("ORDER_HISTORY_TEST")
            .setTarget(resolveTarget(clientRequest))
            .setTargetType(resolveTargetType(clientRequest))
            .setRequestId(UUID.randomUUID().toString())
            .setRequesterId("ORDER_HISTORY_TEST")
            .setCreatedAt(DateUtils.toIsoUtcTime(ZonedDateTime.now()))
            .build();

        return oemsRequestBuilder
            .setMetadata(metadata)
            .setRequestType(OemsRequest.OemsRequestType.ORDER_SINGLE)
            .setOrderId(clientRequest.getOrderId())
            .setRootOrderId(clientRequest.getOrderId())
            .setClientRootOrderId(clientRequest.getClOrderId())
            .setVenueAccount("testVenue")
            .addAllVenueAccounts(Set.of("Venue-1", "Venue-2"))
            .setInstrumentType(OemsInstrumentType.FOREX)
            .setInstrumentId(INTEGRATION_TEST_INSTRUMENT_ID)
            .setClientId(CLIENT_A)
            .setBaseCurrency("BTC")
            .setQuoteCurrency("USD")
            .setPortfolioId(PORTFOLIO_A)
            .setOrderType(OemsOrderType.MARKET)
            .setSide(OemsSide.BUY)
            .setQuantity(String.valueOf(QUANTITY))
            .setPrice(PRICE)
            .setStopPrice(STOP_PRICE)
            .setTif(OemsTIF.GTC)
            .setOrderCategory(resolveOrderCategory(metadata.getTargetType()))
            .build();
    }

    private static String resolveTarget(ClientRequest order) {
        if (isStreetSideSOROrder(order)) {
            return OemsTarget.SOR.getTarget();
        } else {
            return order.getMetadata().getTarget();
        }
    }

    private static Metadata.ServiceType resolveTargetType(ClientRequest clientOrder) {
        if (isStreetSideSOROrder(clientOrder)) {
            return Metadata.ServiceType.SOR;
        } else {
            return Metadata.ServiceType.EXTERNAL_VENUE_ACCOUNT;
        }
    }

    private static OemsOrderCategory resolveOrderCategory(Metadata.ServiceType targetType) {
        return switch (targetType) {
            case BROKER_DESK -> OemsOrderCategory.ORDER_CATEGORY_UNSPECIFIED; // resolved to AGENCY/PRINCIPAL by broker config service later
            case SOR -> OemsOrderCategory.SOR_ORDER;
            case EXTERNAL_VENUE_ACCOUNT -> OemsOrderCategory.DIRECT_MARKET_ACCESS_ORDER;

            case ALGO -> OemsOrderCategory.ORDER_CATEGORY_UNSPECIFIED; // unused
            case CLOB -> OemsOrderCategory.ORDER_CATEGORY_UNSPECIFIED; // it's not possible to route directly to clob (only through agency), unused

            case AUTO_HEDGER -> OemsOrderCategory.AUTO_HEDGING_ORDER;
            case QUOTING_ORDER_SERVICE -> OemsOrderCategory.CLOB_QUOTING_ORDER;
            case TARGET_TYPE_UNSPECIFIED, UNRECOGNIZED -> OemsOrderCategory.ORDER_CATEGORY_UNSPECIFIED;
        };
    }

    public static boolean isStreetSideSOROrder(ClientRequest clientRequest) {
        return StringUtils.isNotEmpty(clientRequest.getSymbol()) && StringUtils.isEmpty(clientRequest.getInstrumentId());
    }
}
