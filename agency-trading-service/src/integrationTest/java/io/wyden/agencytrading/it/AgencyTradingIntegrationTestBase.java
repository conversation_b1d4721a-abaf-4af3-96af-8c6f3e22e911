package io.wyden.agencytrading.it;

import com.hazelcast.config.Config;
import com.hazelcast.core.Hazelcast;
import com.hazelcast.core.HazelcastInstance;
import io.wyden.agencytrading.infrastructure.rabbit.RabbitDestinations;
import io.wyden.agencytrading.service.tracking.InstrumentPrecisionService;
import io.wyden.agencytrading.utils.Collider;
import io.wyden.agencytrading.utils.OrderGateway;
import io.wyden.agencytrading.utils.Sor;
import io.wyden.agencytrading.utils.TestingData;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.destination.OemsTarget;
import io.wyden.published.common.Metadata;
import io.wyden.published.oems.OemsExecType;
import io.wyden.published.oems.OemsOrderStatus;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import io.wyden.published.referencedata.Instrument;
import io.wyden.published.referencedata.TradingConstraints;
import io.wyden.referencedata.client.InstrumentsCacheFacade;
import org.awaitility.Awaitility;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.util.TestPropertyValues;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.testcontainers.containers.RabbitMQContainer;
import org.testcontainers.utility.DockerImageName;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.Optional;
import java.util.UUID;

import static io.wyden.agencytrading.service.oems.outbound.OemsResponseFactory.ORDER_GATEWAY_TARGET;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ContextConfiguration(initializers = AgencyTradingIntegrationTestBase.Initializer.class)
@SpringBootTest(properties = {"spring.main.allow-bean-definition-overriding=true"})
@TestPropertySource(locations = "classpath:integration-test.properties")
@ExtendWith(SpringExtension.class)
public abstract class AgencyTradingIntegrationTestBase {

    public static final String PARENT_ORDER_AGENCY_INSTRUMENT = "LTCBNB@FOREX@Bank";
    public static final String ORDER_BASE_CURRENCY = "LTC";
    public static final String ORDER_QUOTE_CURRENCY = "BNB";
    public static final String ORDER_SYMBOL = "BNB";
    public static final String CHILD_ORDER_AGENCY_INSTRUMENT = "LTCBNB@FOREX@Simulator";
    public static final String CHILD_ORDER_AGENCY_ACCOUNT = "simulator";
    public static final BigDecimal PRICE_INCR_SCALE = new BigDecimal(6);

    public static final RabbitMQContainer RABBIT_MQ = new RabbitMQContainer(DockerImageName.parse("docker.wyden.io/mirror/rabbitmq:3.12-management")
        .asCompatibleSubstituteFor("rabbitmq:management"))
        .withReuse(true);

    static {
        RABBIT_MQ.start();
        Awaitility.setDefaultPollDelay(Duration.ZERO);
        Awaitility.setDefaultPollInterval(Duration.ofMillis(10));
        Awaitility.setDefaultTimeout(Duration.ofMinutes(5));
    }
    @Autowired
    RabbitIntegrator rabbitIntegrator;

    @Autowired
    RabbitDestinations destinations;

    @MockBean
    private InstrumentPrecisionService instrumentPrecisionService;

    @MockBean
    private InstrumentsCacheFacade instrumentsCacheFacade;

    TestingData testingData;
    OrderGateway orderGateway;
    Collider collider;
    Sor sor;

    @BeforeEach
    void setup() {
        testingData = new TestingData();
        orderGateway = new OrderGateway(destinations, rabbitIntegrator);
        collider = new Collider(testingData, destinations, rabbitIntegrator);
        sor = new Sor(testingData, destinations, rabbitIntegrator);

        when(instrumentPrecisionService.getPriceIncr(anyString())).thenReturn(PRICE_INCR_SCALE);
        when(instrumentsCacheFacade.find(PARENT_ORDER_AGENCY_INSTRUMENT)).thenReturn(defaultInstrument());
        when(instrumentsCacheFacade.find(CHILD_ORDER_AGENCY_INSTRUMENT)).thenReturn(defaultInstrument());
    }

    @AfterEach
    void tearDown() {
        orderGateway.cleanup();
        collider.cleanup();
        sor.cleanup();
    }

    public static class Initializer implements ApplicationContextInitializer<ConfigurableApplicationContext> {

        @Override
        public void initialize(@NotNull ConfigurableApplicationContext applicationContext) {
            var values = TestPropertyValues.of(
                "rabbitmq.host=" + RABBIT_MQ.getHost(),
                "rabbitmq.port=" + RABBIT_MQ.getMappedPort(5672),
                "rabbitmq.username=" + RABBIT_MQ.getAdminUsername(),
                "rabbitmq.password=" + RABBIT_MQ.getAdminPassword()
            );

            values.applyTo(applicationContext);
        }
    }

    @TestConfiguration
    static class TestHazelcastConfiguration {

        @Primary
        @Bean("hazelcast")
        HazelcastInstance createHazelcastInstance() {
            Config config = new Config();
            config.setClusterName(UUID.randomUUID().toString());
            return Hazelcast.newHazelcastInstance(config);
        }
    }

    static void assertExecReportFor(OemsResponse response, OemsRequest parentOrder) {
        assertThat(response.getResponseType()).isEqualTo(OemsResponse.OemsResponseType.EXECUTION_REPORT);
        assertThat(response.getOrderId()).isEqualTo(parentOrder.getOrderId());
        assertThat(response.getMetadata().getTarget()).isEqualTo(ORDER_GATEWAY_TARGET);
        assertThat(response.getMetadata().getSource()).isEqualTo(OemsTarget.AGENCY.getTarget());
        assertThat(response.getMetadata().getSourceType()).isEqualTo(Metadata.ServiceType.BROKER_DESK);
        assertThat(response.getPortfolioId()).isEqualTo(parentOrder.getPortfolioId());
        assertThat(response.getSide()).isEqualTo(parentOrder.getSide());
        assertThat(response.getClientId()).isEqualTo(parentOrder.getClientId());
        assertThat(response.getBaseCurrency()).isEqualTo(parentOrder.getBaseCurrency());
        assertThat(response.getQuoteCurrency()).isEqualTo(parentOrder.getQuoteCurrency());
    }

    static void assertCancelRejectFor(OemsResponse response, OemsRequest parentOrder) {
        assertThat(response.getResponseType()).isEqualTo(OemsResponse.OemsResponseType.CANCEL_REJECT);
        assertThat(response.getOrderId()).isEqualTo(parentOrder.getOrderId());
        assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_FILLED);
        assertThat(response.getMetadata().getTarget()).isEqualTo(ORDER_GATEWAY_TARGET);
        assertThat(response.getMetadata().getSource()).isEqualTo(OemsTarget.AGENCY.getTarget());
        assertThat(response.getMetadata().getSourceType()).isEqualTo(Metadata.ServiceType.BROKER_DESK);
    }

    static void assertSimpleChildOrderCancelEmittedFromAgency(OemsRequest childOrderCancel, OemsRequest parentOrder) {
        assertThat(childOrderCancel).satisfies(request -> {
            assertThat(request.getMetadata().getTarget()).isEqualTo(CHILD_ORDER_AGENCY_ACCOUNT);
            assertThat(request.getMetadata().getTargetType()).isEqualTo(Metadata.ServiceType.EXTERNAL_VENUE_ACCOUNT);
            assertThat(request.getVenueAccount()).isEqualTo(parentOrder.getExecutionConfig().getAgencyTradingAccount());
        });
    }

    static void assertSorChildOrderCancelEmittedFromAgency(OemsRequest childOrderCancel) {
        assertThat(childOrderCancel).satisfies(request -> {
            assertThat(request.getMetadata().getTarget()).isEqualTo(OemsTarget.SOR.getTarget());
            assertThat(request.getMetadata().getTargetType()).isEqualTo(Metadata.ServiceType.SOR);
        });
    }

    private static Optional<Instrument> defaultInstrument() {
        return Optional.of(Instrument.newBuilder()
            .setTradingConstraints(TradingConstraints.newBuilder()
                .setQtyIncr("0.0001")
                .build())
            .build());
    }

    static void assertExecReportPendingNewFor(OemsResponse pendingNewResponse, OemsRequest parentOrder) {
        assertThat(pendingNewResponse).satisfies(response -> {
            assertExecReportFor(response, parentOrder);
            assertThat(response.getExecType()).isEqualTo(OemsExecType.PENDING_NEW);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_PENDING_NEW);
        });
    }

}
