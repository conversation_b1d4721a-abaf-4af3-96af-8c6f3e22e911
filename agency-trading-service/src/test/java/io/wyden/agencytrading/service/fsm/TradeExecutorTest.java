package io.wyden.agencytrading.service.fsm;

import io.wyden.agencytrading.model.AgencyTradingOrderState;
import io.wyden.agencytrading.service.tracking.InstrumentPrecisionService;
import io.wyden.agencytrading.service.tracking.OrderFeeProcessor;
import io.wyden.agencytrading.service.tracking.OrderMarkupProcessor;
import io.wyden.published.brokerdesk.PricingConfig;
import io.wyden.published.oems.OemsOrderStatus;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import io.wyden.published.oems.OemsSide;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class TradeExecutorTest {

    public static final String QUANTITY = "100";
    public static final String ZERO = "0";
    public static final String FULL_EXECUTION_AVG_PRICE = "50000.0";

    public static final String PARTIAL_FILL_1_QUANTITY = "20";
    public static final String PARTIAL_FILL_1_AVG_PRICE = "40000.0";
    public static final String PARTIAL_FILL_1_LEAVES_QUANTITY = "80";

    public static final String PARTIAL_FILL_2_QUANTITY = "80";
    public static final String PARTIAL_FILL_2_AVG_PRICE = "60000.0";

    public static final String PARTIAL_FILL_AVG_PRICE = "56000.0"; // (40000*20+60000*80)/100

    private final OrderFeeProcessor orderFeeProcessor = mock(OrderFeeProcessor.class);
    private final InstrumentPrecisionService instrumentPrecisionService = mock(InstrumentPrecisionService.class);
    private final OrderMarkupProcessor orderMarkupProcessor = new OrderMarkupProcessor(instrumentPrecisionService);

    private final TradeExecutor tradeExecutor = new TradeExecutor(orderFeeProcessor, orderMarkupProcessor);

    @BeforeEach
    void setup() {
        when(orderFeeProcessor.calculateMinFeeInPercFeeCurrency(any(), any())).thenReturn(BigDecimal.ZERO);
        when(orderFeeProcessor.calculatePercFeeInPercFeeCurrency(any(), any(), any(), any())).thenReturn(BigDecimal.ZERO);
        when(instrumentPrecisionService.getPriceIncr(anyString())).thenReturn(new BigDecimal(6));
    }

    @Test
    void testFromNewToFilled() {
        OrderState orderState = new OrderState(newUnfilledState());
        OemsResponse report = OemsResponse.newBuilder()
            .setResponseType(OemsResponse.OemsResponseType.EXECUTION_REPORT)
            .setExecutionId("e1")
            .setLastQty(QUANTITY)
            .setLastPrice(FULL_EXECUTION_AVG_PRICE)
            .build();
        tradeExecutor.applyExecution(orderState, report);

        assertThat(orderState)
            .extracting("orderStatus", "avgPrice", "quantity", "filledQuantity", "remainingQuantity")
            .containsExactly(new StatusFilled(), new BigDecimal(FULL_EXECUTION_AVG_PRICE), new BigDecimal(QUANTITY), new BigDecimal(QUANTITY), new BigDecimal(ZERO));

        assertThat(orderState.getExecutions()).hasSize(1);

        assertThat(orderState.getExecutions().get(0))
            .extracting("quantity", "price")
            .containsExactly(QUANTITY, FULL_EXECUTION_AVG_PRICE);
    }

    @Test
    void testFromNewToPartiallyFilledToFilled() {
        OrderState orderState = new OrderState(newUnfilledState());
        OemsResponse report1 = OemsResponse.newBuilder()
            .setResponseType(OemsResponse.OemsResponseType.EXECUTION_REPORT)
            .setExecutionId("e1")
            .setLastQty(PARTIAL_FILL_1_QUANTITY)
            .setLastPrice(PARTIAL_FILL_1_AVG_PRICE)
            .build();
        tradeExecutor.applyExecution(orderState, report1);

        assertThat(orderState)
            .extracting("orderStatus", "avgPrice", "quantity", "filledQuantity", "remainingQuantity")
            .containsExactly(new StatusPartialFill(), new BigDecimal(PARTIAL_FILL_1_AVG_PRICE), new BigDecimal(QUANTITY),
                new BigDecimal(PARTIAL_FILL_1_QUANTITY), new BigDecimal(PARTIAL_FILL_1_LEAVES_QUANTITY));

        OemsResponse report2 = OemsResponse.newBuilder()
            .setResponseType(OemsResponse.OemsResponseType.EXECUTION_REPORT)
            .setExecutionId("e2")
            .setLastQty(PARTIAL_FILL_2_QUANTITY)
            .setLastPrice(PARTIAL_FILL_2_AVG_PRICE)
            .build();
        tradeExecutor.applyExecution(orderState, report2);

        assertThat(orderState)
            .extracting("orderStatus", "avgPrice", "quantity", "filledQuantity", "remainingQuantity")
            .containsExactly(new StatusFilled(), new BigDecimal(PARTIAL_FILL_AVG_PRICE),
                new BigDecimal(QUANTITY), new BigDecimal(QUANTITY), new BigDecimal(ZERO));
    }

    private AgencyTradingOrderState newUnfilledState() {
        return AgencyTradingOrderState.newBuilder()
            .addCurrentStatus(OemsOrderStatus.STATUS_NEW)
            .setAvgPrice("0")
            .setAvgPriceWithMarkup("0")
            .setQuantity(QUANTITY)
            .setRemainingQuantity(QUANTITY)
            .setFilledQuantity(ZERO)
            .setTotalPercFee(ZERO)
            .setTotalFeeCharged(ZERO)
            .setRequest(OemsRequest.newBuilder()
                .setSide(OemsSide.BUY)
                .setPricingConfig(PricingConfig.newBuilder().setMarkup("0").build())
                .build())
            .build();
    }
}
