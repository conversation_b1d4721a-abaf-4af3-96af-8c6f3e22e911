package io.wyden.brokerconfig.domain;

import io.wyden.brokerconfig.application.PortfolioLookupService;
import io.wyden.brokerconfig.domain.referencedata.InstrumentsService;
import io.wyden.brokerconfig.domain.referencedata.VenueAccountService;
import io.wyden.brokerconfig.domain.resolver.ConfigResolvers;
import io.wyden.brokerconfig.domain.resolver.pricing.ExecutionConfigService;
import io.wyden.brokerconfig.domain.resolver.pricing.PricingConfigService;
import io.wyden.brokerconfig.interfaces.rabbit.OemsRequestEmitter;
import io.wyden.brokerconfig.interfaces.rabbit.OemsRequestProcessor;
import io.wyden.brokerconfig.interfaces.rabbit.ProtoUtil;
import io.wyden.brokerconfig.repository.InMemoryPortfolioGroupRepository;
import io.wyden.brokerconfig.repository.InMemoryPortfolioRepository;
import io.wyden.published.brokerdesk.ConfigStatus;
import io.wyden.published.brokerdesk.CurrencyType;
import io.wyden.published.brokerdesk.ExecutionConfig;
import io.wyden.published.brokerdesk.ExecutionMode;
import io.wyden.published.brokerdesk.HedgingConfig;
import io.wyden.published.brokerdesk.InstrumentConfig;
import io.wyden.published.brokerdesk.PortfolioConfig;
import io.wyden.published.brokerdesk.PortfolioGroupConfig;
import io.wyden.published.brokerdesk.PricingConfig;
import io.wyden.published.brokerdesk.SorTarget;
import io.wyden.published.brokerdesk.TernaryBool;
import io.wyden.published.brokerdesk.ThresholdConfig;
import io.wyden.published.brokerdesk.TradingMode;
import io.wyden.published.common.Metadata;
import io.wyden.published.marketdata.InstrumentKey;
import io.wyden.published.oems.OemsInstrumentType;
import io.wyden.published.oems.OemsOrderType;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsSide;
import io.wyden.published.referencedata.AssetClass;
import io.wyden.published.referencedata.BaseInstrument;
import io.wyden.published.referencedata.ForexSpotProperties;
import io.wyden.published.referencedata.Instrument;
import io.wyden.published.referencedata.PortfolioType;
import io.wyden.referencedata.client.InstrumentsCacheFacade;
import io.wyden.referencedata.client.VenueAccountCacheFacade;
import org.junit.jupiter.api.BeforeEach;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Optional;

import static io.wyden.brokerconfig.domain.ConfigFactory.CLIENT_SIDE_QUOTE_CURR;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public abstract class ConfigurationTestBase {

    protected static final String SOR_INSTRUMENT_SYMBOL = "DOGEUSD";

    protected ConfigurationResolver configurationResolver;
    protected PricingConfigService pricingConfigService;
    protected ExecutionConfigService executionConfigService;
    protected VenueAccountService venueAccountService;
    protected InstrumentsService instrumentsService;
    protected InstrumentsCacheFacade instrumentsCacheFacade;
    protected OemsRequestProcessor oemsRequestProcessor;
    protected OemsRequestEmitter oemsRequestEmitter;
    protected ReservationFeeProcessor reservationFeeProcessor;
    protected PortfolioRepository portfolioRepository;
    protected PortfolioGroupRepository portfolioGroupRepository;
    protected PortfolioLookupService portfolioLookupService;

    protected PortfolioGroupConfig.Builder portfolioGroupConfig;
    protected PortfolioConfig.Builder portfolioConfig;
    protected InstrumentConfig.Builder portfolioInstrumentConfig;
    protected OemsRequest.Builder request;
    protected AutoHedgerConfigurationValidator autoHedgerConfigurationValidator;

    @BeforeEach
    void setUp() {
        portfolioRepository = new InMemoryPortfolioRepository();
        portfolioGroupRepository = new InMemoryPortfolioGroupRepository();
        portfolioLookupService = new PortfolioLookupService(portfolioRepository, portfolioGroupRepository);
        instrumentsCacheFacade = mock(InstrumentsCacheFacade.class);

        venueAccountService = new VenueAccountService(mock(VenueAccountCacheFacade.class));
        instrumentsService = new InstrumentsService(instrumentsCacheFacade);

        mockClientSideInstrumentCache();

        pricingConfigService = new PricingConfigService(venueAccountService, instrumentsService, ConfigResolvers.pricingResolvers());
        executionConfigService = new ExecutionConfigService(venueAccountService, instrumentsService, ConfigResolvers.executionResolvers());

        configurationResolver = new ConfigurationResolver(portfolioRepository,
            portfolioGroupRepository,
            pricingConfigService,
            executionConfigService,
            instrumentsCacheFacade
        );

        oemsRequestEmitter = mock(OemsRequestEmitter.class);
        when(oemsRequestEmitter.emit(any(OemsRequest.class)))
            .thenAnswer(invocation -> invocation.getArguments()[0]);
        reservationFeeProcessor = mock(ReservationFeeProcessor.class);
        oemsRequestProcessor = new OemsRequestProcessor(configurationResolver, oemsRequestEmitter, reservationFeeProcessor);

        autoHedgerConfigurationValidator = new AutoHedgerConfigurationValidator(instrumentsCacheFacade);

        setupTestData();
    }

    private void setupTestData() {
        request = OemsRequest.newBuilder()
            .setMetadata(Metadata.newBuilder()
                .setCreatedAt(ZonedDateTime.now().toString())
                .setRequestId("order-A")
                .setSourceType(Metadata.ServiceType.BROKER_DESK)
                .setSource("Agency"))
            .setRequestType(OemsRequest.OemsRequestType.ORDER_SINGLE)
            .setOrderId("order-A")
            .setPortfolioId("Client 1 portfolio")
            .setInstrumentId("BTCUSD@FOREX@Bank")
            .setInstrumentType(OemsInstrumentType.FOREX)
            .setBaseCurrency("BTC")
            .setQuoteCurrency("USD")
            .setSide(OemsSide.BUY)
            .setQuantity(ProtoUtil.map(BigDecimal.valueOf(2.5)))
            .setOrderType(OemsOrderType.LIMIT)
            .setPrice(ProtoUtil.map(BigDecimal.valueOf(12_000)))
            .setConfigStatus(ConfigStatus.CONFIG_REQUIRED);

        portfolioGroupConfig = PortfolioGroupConfig.newBuilder()
            .setId("Portfolio Group 1")
            .setName("Group: crypto clients")
            .setPortfolioType(PortfolioType.VOSTRO)
            .setExecutionConfig(ExecutionConfig.newBuilder()
                .setTradingMode(TradingMode.AGENCY)
                .setCounterPortfolio("e2e-BANK-portfolio")
                .setFixedFee("10")
                .setFixedFeeCurrency(CLIENT_SIDE_QUOTE_CURR)
                .setPercentageFee("0.2")
                .setPercentageFeeCurrency(CLIENT_SIDE_QUOTE_CURR)
                .setPercentageFeeCurrencyType(CurrencyType.SPECIFIC_CURRENCY)
                .setMinFee("5")
                .setMinFeeCurrency(CLIENT_SIDE_QUOTE_CURR)
                .setAgencyTradingAccount("BITMEX-testnet-1")
                .setAgencyTargetInstrumentId("XBTUSD@FOREX@BitMEX")
                .setChargeExchangeFee(TernaryBool.TRUE)
                .setDiscloseTradingVenue(TernaryBool.TRUE)
                .setExecutionMode(ExecutionMode.SIMPLE)
                .addSorTradingAccounts("binance")
                .setSorTarget(SorTarget.newBuilder().build()))
            .setPricingConfig(PricingConfig.newBuilder()
                .setPricingEnabled(true)
                .addPricingSource(InstrumentKey.newBuilder()
                    .setInstrumentId("XBTUSD@FOREX@BitMEX")
                    .setVenueAccount("BITMEX-testnet-1"))
                .setMarkup("0.05"))
            .setHedgingConfig(HedgingConfig.newBuilder()
                .setAutoHedging(TernaryBool.TRUE)
                .setTargetAccountName("e2e-BANK-portfolio")
                .addThreshold(ThresholdConfig.newBuilder()
                    .setAsset("BTC")
                    .setHighThreshold("100")
                    .setLowThreshold("-100")
                    .setTargetExposure("0")
                    .setHedgeInstrument("XBTUSD@FOREX@BitMEX"))
                .addThreshold(ThresholdConfig.newBuilder()
                    .setAsset("ETH")
                    .setHighThreshold("20000")
                    .setLowThreshold("-20000")
                    .setTargetExposure("0")
                    .setHedgeInstrument("ETHUSD@FOREX@BitMEX")))
            .addInstrument(InstrumentConfig.newBuilder()
                .setInstrumentId("BTCUSD@FOREX@Bank")
                .setExecutionConfig(ExecutionConfig.newBuilder()
                    .setTradingMode(TradingMode.AGENCY)
                    .setCounterPortfolio("e2e-BANK-portfolio")
                    .setFixedFee("5")
                    .setFixedFeeCurrency(CLIENT_SIDE_QUOTE_CURR)
                    .setPercentageFee("0.1")
                    .setPercentageFeeCurrency(CLIENT_SIDE_QUOTE_CURR)
                    .setPercentageFeeCurrencyType(CurrencyType.SPECIFIC_CURRENCY)
                    .setMinFee("5")
                    .setMinFeeCurrency(CLIENT_SIDE_QUOTE_CURR)
                    .setAgencyTradingAccount("BITMEX-testnet-1")
                    .setAgencyTargetInstrumentId("XBTUSD@FOREX@BitMEX")
                    .setChargeExchangeFee(TernaryBool.TRUE)
                    .setDiscloseTradingVenue(TernaryBool.TRUE)
                    .setExecutionMode(ExecutionMode.SIMPLE)
                    .addSorTradingAccounts("binance")
                    .setSorTarget(SorTarget.newBuilder().build()))
                .setPricingConfig(PricingConfig.newBuilder()
                    .setPricingEnabled(true)
                    .addPricingSource(InstrumentKey.newBuilder()
                        .setInstrumentId("XBTUSD@FOREX@BitMEX")
                        .setVenueAccount("BITMEX-testnet-1"))
                    .setMarkup("0.06")))
            .addInstrument(InstrumentConfig.newBuilder()
                .setInstrumentId("ETHUSD@FOREX@Bank")
                .setExecutionConfig(ExecutionConfig.newBuilder()
                    .setTradingMode(TradingMode.AGENCY)
                    .setCounterPortfolio("e2e-BANK-portfolio")
                    .setFixedFee("6")
                    .setFixedFeeCurrency(CLIENT_SIDE_QUOTE_CURR)
                    .setPercentageFee("0.16")
                    .setPercentageFeeCurrency(CLIENT_SIDE_QUOTE_CURR)
                    .setPercentageFeeCurrencyType(CurrencyType.SPECIFIC_CURRENCY)
                    .setMinFee("6")
                    .setMinFeeCurrency(CLIENT_SIDE_QUOTE_CURR)
                    .setAgencyTradingAccount("BITMEX-testnet-1")
                    .setAgencyTargetInstrumentId("ETHUSD@FOREX@BitMEX")
                    .setChargeExchangeFee(TernaryBool.TRUE)
                    .setDiscloseTradingVenue(TernaryBool.TRUE)
                    .setExecutionMode(ExecutionMode.SIMPLE))
                .setPricingConfig(PricingConfig.newBuilder()
                    .setPricingEnabled(true)
                    .addPricingSource(InstrumentKey.newBuilder()
                        .setInstrumentId("ETHUSD@FOREX@BitMEX")
                        .setVenueAccount("BITMEX-testnet-1"))
                    .setMarkup("0.07")))
            .addInstrument(InstrumentConfig.newBuilder()
                .setInstrumentId("DOGEUSD@FOREX@Bank")
                .setExecutionConfig(ExecutionConfig.newBuilder()
                    .setTradingMode(TradingMode.AGENCY)
                    .setCounterPortfolio("e2e-BANK-portfolio")
                    .setFixedFee("7")
                    .setFixedFeeCurrency(CLIENT_SIDE_QUOTE_CURR)
                    .setPercentageFee("0.17")
                    .setPercentageFeeCurrency(CLIENT_SIDE_QUOTE_CURR)
                    .setPercentageFeeCurrencyType(CurrencyType.SPECIFIC_CURRENCY)
                    .setMinFee("7")
                    .setMinFeeCurrency(CLIENT_SIDE_QUOTE_CURR)
                    .setAgencyTradingAccount("BITMEX-testnet-1")
                    .setAgencyTargetInstrumentId("DOGEUSD@FOREX@BitMEX")
                    .setChargeExchangeFee(TernaryBool.TRUE)
                    .setDiscloseTradingVenue(TernaryBool.TRUE)
                    .setExecutionMode(ExecutionMode.SIMPLE)
                    .addSorTradingAccounts("binance")
                    .setSorTarget(SorTarget.newBuilder().build()))
                .setPricingConfig(PricingConfig.newBuilder()
                    .setPricingEnabled(true)
                    .addPricingSource(InstrumentKey.newBuilder()
                        .setInstrumentId("DOGEUSD@FOREX@BitMEX")
                        .setVenueAccount("BITMEX-testnet-1"))
                    .setMarkup("0.08")));

        portfolioInstrumentConfig = InstrumentConfig.newBuilder()
            .setInstrumentId("DOGEUSD@FOREX@Bank")
            .setExecutionConfig(ExecutionConfig.newBuilder()
                .setTradingMode(TradingMode.AGENCY)
                .setCounterPortfolio("e2e-BANK-portfolio")
                .setFixedFee("9")
                .setFixedFeeCurrency(CLIENT_SIDE_QUOTE_CURR)
                .setPercentageFee("0.22")
                .setPercentageFeeCurrency(CLIENT_SIDE_QUOTE_CURR)
                .setPercentageFeeCurrencyType(CurrencyType.SPECIFIC_CURRENCY)
                .setMinFee("3")
                .setMinFeeCurrency(CLIENT_SIDE_QUOTE_CURR)
                .setChargeExchangeFee(TernaryBool.TRUE)
                .setDiscloseTradingVenue(TernaryBool.TRUE)
                .setExecutionMode(ExecutionMode.SOR)
                .addSorTradingAccounts("binance")
                .setSorTarget(SorTarget.newBuilder()
                    .setAssetClass(AssetClass.FOREX)
                    .setSymbol(SOR_INSTRUMENT_SYMBOL).build()))
            .setPricingConfig(PricingConfig.newBuilder()
                .setPricingEnabled(true)
                .addPricingSource(InstrumentKey.newBuilder()
                    .setInstrumentId("DOGEUSD@FOREX@BitMEX")
                    .setVenueAccount("BITMEX-testnet-1"))
                .setMarkup("0.12"));

        portfolioConfig = PortfolioConfig.newBuilder()
            .setId("Client 1 portfolio")
            .setName("Portfolio: Client 1")
            .setPortfolioGroupId(portfolioGroupConfig.getId())
            .setExecutionConfig(ExecutionConfig.newBuilder()
                .setTradingMode(TradingMode.AGENCY)
                .setCounterPortfolio("e2e-BANK-portfolio")
                .setFixedFee("11")
                .setFixedFeeCurrency("EUR")
                .setPercentageFee("0.3")
                .setPercentageFeeCurrency("EUR")
                .setPercentageFeeCurrencyType(CurrencyType.SPECIFIC_CURRENCY)
                .setMinFee("6")
                .setMinFeeCurrency("EUR")
                .setAgencyTradingAccount("BITMEX-testnet-1")
                .setAgencyTargetInstrumentId("XBTUSD@FOREX@BitMEX")
                .setChargeExchangeFee(TernaryBool.TRUE)
                .setDiscloseTradingVenue(TernaryBool.TRUE)
                .setExecutionMode(ExecutionMode.SIMPLE)
                .addSorTradingAccounts("binance")
                .setSorTarget(SorTarget.newBuilder().build()))
            .setPricingConfig(PricingConfig.newBuilder()
                .setPricingEnabled(true)
                .addPricingSource(InstrumentKey.newBuilder()
                    .setInstrumentId("XBTUSD@FOREX@BitMEX")
                    .setVenueAccount("BITMEX-testnet-1"))
                .setMarkup("0.09"))
            .setHedgingConfig(HedgingConfig.newBuilder()
                .setAutoHedging(TernaryBool.TRUE)
                .setTargetAccountName("e2e-BANK-portfolio")
                .addThreshold(ThresholdConfig.newBuilder()
                    .setAsset("BTC")
                    .setHighThreshold("120")
                    .setLowThreshold("-120")
                    .setTargetExposure("0")
                    .setHedgeInstrument("XBTUSD@FOREX@BitMEX"))
                .addThreshold(ThresholdConfig.newBuilder()
                    .setAsset("ETH")
                    .setHighThreshold("22000")
                    .setLowThreshold("-22000")
                    .setTargetExposure("0")
                    .setHedgeInstrument("ETHUSD@FOREX@BitMEX")))
            .addInstrument(InstrumentConfig.newBuilder()
                .setInstrumentId("BTCUSD@FOREX@Bank")
                .setExecutionConfig(ExecutionConfig.newBuilder()
                    .setTradingMode(TradingMode.AGENCY)
                    .setCounterPortfolio("e2e-BANK-portfolio")
                    .setFixedFee("8")
                    .setFixedFeeCurrency(CLIENT_SIDE_QUOTE_CURR)
                    .setPercentageFee("0.18")
                    .setPercentageFeeCurrency(CLIENT_SIDE_QUOTE_CURR)
                    .setPercentageFeeCurrencyType(CurrencyType.SPECIFIC_CURRENCY)
                    .setMinFee("8")
                    .setMinFeeCurrency(CLIENT_SIDE_QUOTE_CURR)
                    .setAgencyTradingAccount("BITMEX-testnet-1")
                    .setAgencyTargetInstrumentId("XBTUSD@FOREX@BitMEX")
                    .setChargeExchangeFee(TernaryBool.TRUE)
                    .setDiscloseTradingVenue(TernaryBool.TRUE)
                    .setExecutionMode(ExecutionMode.SIMPLE)
                    .addSorTradingAccounts("binance")
                    .setSorTarget(SorTarget.newBuilder().build()))
                .setPricingConfig(PricingConfig.newBuilder()
                    .setPricingEnabled(true)
                    .addPricingSource(InstrumentKey.newBuilder()
                        .setInstrumentId("XBTUSD@FOREX@BitMEX")
                        .setVenueAccount("BITMEX-testnet-1"))
                    .setMarkup("0.11")))
            .addInstrument(portfolioInstrumentConfig);

        portfolioGroupRepository.save(portfolioGroupConfig.build());
        portfolioRepository.save(portfolioConfig.build());
    }

    protected static void assertThatRequestIsConfiguredWith(OemsRequest request, ExecutionConfig expected) {
        ExecutionConfig executionConfig = request.getExecutionConfig();
        assertThat(executionConfig)
            .isNotNull()
            .isEqualTo(expected);
    }

    protected static void assertThatRequestIsConfiguredWith(OemsRequest request, PricingConfig expected) {
        PricingConfig pricingConfig = request.getPricingConfig();
        assertThat(pricingConfig)
            .isNotNull()
            .isEqualTo(expected);
    }

    private void mockClientSideInstrumentCache() {
        when(instrumentsCacheFacade.find("DOGEUSD@FOREX@Bank")).thenReturn(Optional.of(Instrument.newBuilder()
            .setBaseInstrument(BaseInstrument.newBuilder()
                .setAssetClass(AssetClass.FOREX)
                .setSymbol(SOR_INSTRUMENT_SYMBOL)
                .setQuoteCurrency("USD")
                .build())
            .setForexSpotProperties(ForexSpotProperties.newBuilder()
                .setBaseCurrency("DOGE")
                .build())
            .build()));

        when(instrumentsCacheFacade.find("UNKNOWN@FOREX@Bank")).thenReturn(Optional.of(Instrument.newBuilder()
            .setBaseInstrument(BaseInstrument.newBuilder()
                .setAssetClass(AssetClass.FOREX)
                .setSymbol("UNKNOWN")
                .setQuoteCurrency("KNOWN")
                .build())
            .setForexSpotProperties(ForexSpotProperties.newBuilder()
                .setBaseCurrency("UN")
                .build())
            .build()));

        when(instrumentsCacheFacade.find("BTCUSD@FOREX@Bank")).thenReturn(Optional.of(Instrument.newBuilder()
            .setBaseInstrument(BaseInstrument.newBuilder()
                .setAssetClass(AssetClass.FOREX)
                .setSymbol("BTCUSD")
                .setQuoteCurrency("USD")
                .build())
            .setForexSpotProperties(ForexSpotProperties.newBuilder()
                .setBaseCurrency("BTC")
                .build())
            .build()));

        when(instrumentsCacheFacade.find("USDEUR@FOREX@Bank")).thenReturn(Optional.of(Instrument.newBuilder()
            .setBaseInstrument(BaseInstrument.newBuilder()
                .setAssetClass(AssetClass.FOREX)
                .setSymbol("USDEUR")
                .setQuoteCurrency("EUR")
                .build())
            .setForexSpotProperties(ForexSpotProperties.newBuilder()
                .setBaseCurrency("USD")
                .build())
            .build()));

        when(instrumentsCacheFacade.find("BTCEUR@FOREX@Bank")).thenReturn(Optional.of(Instrument.newBuilder()
            .setBaseInstrument(BaseInstrument.newBuilder()
                .setAssetClass(AssetClass.FOREX)
                .setSymbol("BTCEUR")
                .setQuoteCurrency("EUR")
                .build())
            .setForexSpotProperties(ForexSpotProperties.newBuilder()
                .setBaseCurrency("BTC")
                .build())
            .build()));

        when(instrumentsCacheFacade.find("ADAEUR@FOREX@Bank")).thenReturn(Optional.of(Instrument.newBuilder()
            .setBaseInstrument(BaseInstrument.newBuilder()
                .setAssetClass(AssetClass.FOREX)
                .setSymbol("ADAEUR")
                .setQuoteCurrency("EUR")
                .build())
            .setForexSpotProperties(ForexSpotProperties.newBuilder()
                .setBaseCurrency("ADA")
                .build())
            .build()));
    }
}
