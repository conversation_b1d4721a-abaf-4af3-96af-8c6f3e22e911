plugins {
    id 'idea'
    id 'maven-publish'
    id 'java-library'
    alias dependencyCatalog.plugins.protobuf
    alias dependencyCatalog.plugins.sonarqube
}

def versionPropsFile = file('version.properties')
if (versionPropsFile.canRead()) {
    Properties versionProps = new Properties()
    versionProps.load(new FileInputStream(versionPropsFile))
    def ver = versionProps['VERSION'].toString()
    project.version = ver
} else {
    throw new GradleException("Could not read version.properties!")
}

sonarqube {
    properties {
        property "sonar.projectKey", "audit-server-client"
        property "sonar.projectName", "Audit Server Client"
    }
}

dependencies {
    implementation dependencyCatalog.published.language.oems
    implementation dependencyCatalog.cloud.utils.rabbitmq
    implementation dependencyCatalog.cloud.utils.rabbitmq.destinations
    implementation dependencyCatalog.cloud.utils.telemetry
}

publishing {
    publications {
        mavenJava(MavenPublication) {
            from components.java
        }
    }
    repositories {
        maven {
            name 'nexus-snapshots'
            url 'https://repo.wyden.io/nexus/repository/snapshots/'
            credentials {
                username repository_username
                password repository_password
            }
        }
    }
}
