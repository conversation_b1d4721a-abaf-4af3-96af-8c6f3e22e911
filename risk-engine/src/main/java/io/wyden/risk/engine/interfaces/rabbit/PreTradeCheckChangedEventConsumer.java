package io.wyden.risk.engine.interfaces.rabbit;

import com.rabbitmq.client.AMQP;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.SpanKind;
import io.opentelemetry.api.trace.StatusCode;
import io.opentelemetry.context.Context;
import io.wyden.cloud.utils.spring.util.ExclusiveNameGenerator;
import io.wyden.cloudutils.rabbitmq.ConsumptionResult;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.queue.ExpiringRabbitQueueBuilder;
import io.wyden.cloudutils.rabbitmq.queue.MessageConsumer;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueue;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.cloudutils.telemetry.tracing.otl.RabbitHeadersPropagator;
import io.wyden.published.risk.PreTradeCheckChangedEvent;
import io.wyden.risk.engine.infrastructure.rabbit.RabbitDestinations;
import io.wyden.risk.engine.pretradecheck.configuration.PreTradeCheckConfigurationService;
import jakarta.annotation.PostConstruct;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;

import static io.wyden.cloudutils.rabbitmq.ConsumptionResult.consumed;
import static io.wyden.cloudutils.rabbitmq.ConsumptionResult.failureNonRecoverable;

@Component
public class PreTradeCheckChangedEventConsumer implements MessageConsumer<PreTradeCheckChangedEvent> {
    private static final Logger LOGGER = LoggerFactory.getLogger(PreTradeCheckChangedEventConsumer.class);

    private final Tracing otlTracing;
    private final PreTradeCheckConfigurationService preTradeCheckConfigurationService;
    private final RabbitIntegrator rabbitIntegrator;
    private final RabbitDestinations rabbitDestinations;
    private final ExclusiveNameGenerator queueNameGenerator;
    private final String queueNameTemplate;
    private final String consumerName;

    public PreTradeCheckChangedEventConsumer(Tracing otlTracing,
                                             PreTradeCheckConfigurationService preTradeCheckConfigurationService,
                                             RabbitIntegrator rabbitIntegrator,
                                             RabbitDestinations rabbitDestinations,
                                             ExclusiveNameGenerator queueNameGenerator,
                                             @Value("${rabbitmq.risk-ptc-changed-event-queue-template}") String queueNameTemplate,
                                             @Value("${spring.application.name}") String consumerName) {
        this.otlTracing = otlTracing;
        this.preTradeCheckConfigurationService = preTradeCheckConfigurationService;
        this.rabbitIntegrator = rabbitIntegrator;
        this.rabbitDestinations = rabbitDestinations;
        this.queueNameGenerator = queueNameGenerator;
        this.queueNameTemplate = queueNameTemplate;
        this.consumerName = consumerName;
    }

    @PostConstruct
    public void init() {
        declareQueue();
    }

    @Override
    public ConsumptionResult consume(PreTradeCheckChangedEvent event, AMQP.BasicProperties properties) {
        Context parent = otlTracing.loadContext(RabbitHeadersPropagator.create(properties.getHeaders()), RabbitHeadersPropagator.getter());
        try (var ignored = otlTracing.createBaggage(parent)) {
            try (var ignored2 = otlTracing.createSpan("risk.pretradecheck.changedevent.consume", SpanKind.CONSUMER, parent)) {
                return consumeInner(event, properties.getHeaders());
            }
        }
    }

    private ConsumptionResult consumeInner(PreTradeCheckChangedEvent event, Map<String, Object> headers) {
        try {
            LOGGER.info("Received {}\n{}", event.getClass().getSimpleName(), event);
            preTradeCheckConfigurationService.onChangedEvent(event);
            Span.current().setStatus(StatusCode.OK);
            return consumed();
        } catch (Exception e) {
            LOGGER.error("Exception when processing %s - discarding...".formatted(event.getClass().getSimpleName()), e);
            Span.current().recordException(e);
            Span.current().setStatus(StatusCode.ERROR, "Exception when processing OemsRequest - discarding");
            return failureNonRecoverable();
        }
    }

    private void declareQueue() {
        RabbitQueue<PreTradeCheckChangedEvent> queue = new ExpiringRabbitQueueBuilder<PreTradeCheckChangedEvent>(rabbitIntegrator)
            .setQueueName(queueNameGenerator.getQueueName(queueNameTemplate))
            .setConsumerName(consumerName)
            .declare();

        queue.bindWithRoutingKey(rabbitDestinations.getPtcChangeEventExchange(), StringUtils.EMPTY);
        LOGGER.info("Binding exchange {} and queue {}", rabbitDestinations.getPtcChangeEventExchange().getName(), queue.getName());
        queue.attachConsumer(PreTradeCheckChangedEvent.parser(), this);
    }
}
