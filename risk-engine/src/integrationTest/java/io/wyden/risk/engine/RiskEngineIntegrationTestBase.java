package io.wyden.risk.engine;

import com.google.protobuf.Message;
import com.google.protobuf.Parser;
import com.hazelcast.config.AdvancedNetworkConfig;
import com.hazelcast.config.Config;
import com.hazelcast.config.JoinConfig;
import com.hazelcast.core.Hazelcast;
import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import io.wyden.audit.client.AuditEventsClient;
import io.wyden.cloudutils.rabbitmq.ConsumptionResult;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.queue.MatchingCondition;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueue;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueueBuilder;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.published.referencedata.Instrument;
import io.wyden.referencedata.client.InstrumentsCacheFacade;
import io.wyden.risk.engine.infrastructure.rabbit.RabbitDestinations;
import jakarta.annotation.Nullable;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.extension.ExtendWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.AutoConfigureWebTestClient;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.util.TestPropertyValues;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.testcontainers.containers.RabbitMQContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;
import org.testcontainers.utility.DockerImageName;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static org.mockito.Mockito.mock;

@Testcontainers
@ContextConfiguration(initializers = RiskEngineIntegrationTestBase.Initializer.class)
@SpringBootTest(properties = {"spring.main.allow-bean-definition-overriding=true"})
@TestPropertySource(locations = "classpath:integration-test.properties")
@ExtendWith(SpringExtension.class)
@AutoConfigureWebTestClient
@DirtiesContext
public abstract class RiskEngineIntegrationTestBase {

    private static final Logger LOGGER = LoggerFactory.getLogger(RiskEngineIntegrationTestBase.class);

    @Container
    public static final RabbitMQContainer RABBIT_MQ = new RabbitMQContainer(DockerImageName.parse("docker.wyden.io/mirror/rabbitmq:3.12-management")
        .asCompatibleSubstituteFor("rabbitmq:management"))
        .withReuse(true);

    static {
        RABBIT_MQ.start();
    }

    @Autowired
    RabbitIntegrator rabbitIntegrator;

    @Autowired
    RabbitDestinations destinations;

    protected <T extends Message> QueueWrapper<T> declareQueueAndBindTo(RabbitExchange<T> rabbitExchange, Parser<T> parser, boolean doAck, @Nullable RabbitExchange<T> deadLetterExchange) {
        RabbitQueueBuilder<T> queueBuilder = new RabbitQueueBuilder<>(rabbitIntegrator);
        if (deadLetterExchange != null) {
            queueBuilder.setDeadLetterExchange(deadLetterExchange);
        }
        RabbitQueue<T> queue = queueBuilder
            .setQueueName("risk-it-" + UUID.randomUUID())
            .declare();

        queue.bindWithHeaders(rabbitExchange, MatchingCondition.ALL, Map.of());

        List<T> producedMessages = new ArrayList<>();
        String consumer = queue.attachConsumer(parser, (message, basicProperties) -> {
            LOGGER.debug("Consumed message from test queue connected to: {} \n{}", rabbitExchange.getName(), message);
            producedMessages.add(message);
            if (doAck) {
                return ConsumptionResult.consumed();
            } else {
                return ConsumptionResult.failureNonRecoverable();
            }
        });

        return new QueueWrapper<>(queue, rabbitExchange, producedMessages, consumer);
    }

    public record QueueWrapper<T extends Message>(RabbitQueue<T> queue, RabbitExchange<T> exchange, Collection<T> consumed, String consumer) {

        public void unbind() {
            queue.cancelConsumer(consumer);
            queue.unbindWithHeaders(exchange, MatchingCondition.ALL, Map.of());
        }
    }

    public static class Initializer implements ApplicationContextInitializer<ConfigurableApplicationContext> {

        @Override
        public void initialize(@NotNull ConfigurableApplicationContext applicationContext) {
            var values = TestPropertyValues.of(
                "rabbitmq.host=" + RABBIT_MQ.getHost(),
                "rabbitmq.port=" + RABBIT_MQ.getMappedPort(5672),
                "rabbitmq.username=" + RABBIT_MQ.getAdminUsername(),
                "rabbitmq.password=" + RABBIT_MQ.getAdminPassword()
            );

            values.applyTo(applicationContext);
        }
    }

    @TestConfiguration
    static class TestHazelcastConfiguration {

        @Primary
        @Bean("hazelcast")
        HazelcastInstance createHazelcastInstance() {
            Config config = new Config();
            AdvancedNetworkConfig advancedNetworkConfig = config.getAdvancedNetworkConfig();
            JoinConfig join = advancedNetworkConfig.getJoin();
            join.getMulticastConfig().setEnabled(false);
            join.getAutoDetectionConfig().setEnabled(false);
            join.getKubernetesConfig().setEnabled(false);
            join.getTcpIpConfig().setEnabled(false);
            join.getDiscoveryConfig().setDiscoveryStrategyConfigs(null);
            config.setClusterName(UUID.randomUUID().toString());
            config.getJetConfig().setEnabled(true);
            config.getJetConfig().setBackupCount(0);
            return Hazelcast.newHazelcastInstance(config);
        }

        @Primary
        @Bean
        InstrumentsCacheFacade instrumentsCacheFacade(HazelcastInstance hazelcastInstance) {
            IMap<String, Instrument> instruments = hazelcastInstance.getMap("instruments");
            return new InstrumentsCacheFacade(instruments, mock(Tracing.class));
        }

        @Primary
        @Bean
        AuditEventsClient auditEventsClient(RabbitIntegrator rabbitIntegrator) {
            return new AuditEventsClient(rabbitIntegrator);
        }
    }
}
