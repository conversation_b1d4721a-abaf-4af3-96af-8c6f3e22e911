package io.wyden.rest.management.domain;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Collection;

import static io.wyden.rest.management.common.CollectionUtils.wrapPageSize;

public final class ReservationModel {

    private ReservationModel() {
    }

    public interface Reservation extends Serializable {
        String reservationRef();
    }

    public record DepositReservation(
        String reservationRef,
        TransactionModel.TransactionType transactionType,
        Long dateTime,
        String currency,
        BigDecimal quantity,
        String portfolioId,
        String accountId,
        String feePortfolioId,
        String feeAccountId,
        Collection<ReservationFee> fees
    ) implements Reservation, TransactionModel.WithAdditionalFees<ReservationFee> {
    }

    public record WithdrawalReservation(
        String reservationRef,
        TransactionModel.TransactionType transactionType,
        Long dateTime,
        String currency,
        BigDecimal quantity,
        String portfolioId,
        String accountId,
        String feePortfolioId,
        String feeAccountId,
        Collection<ReservationFee> fees
    ) implements Reservation, TransactionModel.WithAdditionalFees<ReservationFee> {
    }

    public record ClientCashTradeReservation(
        String reservationRef,
        TransactionModel.TransactionType transactionType,
        Long dateTime,
        String currency,
        String baseCurrency,
        BigDecimal quantity,
        BigDecimal price,
        BigDecimal stopPrice,
        String portfolioId,
        String counterPortfolioId,
        Collection<ReservationFee> fees
    ) implements Reservation {
    }

    public record StreetCashTradeReservation(
        String reservationRef,
        TransactionModel.TransactionType transactionType,
        Long dateTime,
        String currency,
        String baseCurrency,
        BigDecimal quantity,
        BigDecimal price,
        BigDecimal stopPrice,
        String portfolioId,
        String accountId,
        Collection<ReservationFee> fees
    ) implements Reservation {
    }

    public record AccountCashTransferReservation(
        String reservationRef,
        TransactionModel.TransactionType transactionType,
        Long dateTime,
        String currency,
        BigDecimal quantity,
        String sourceAccountId,
        String targetAccountId,
        String feeAccountId,
        String feePortfolioId,
        Collection<ReservationFee> fees
    ) implements Reservation, TransactionModel.WithAdditionalFees<ReservationFee> {
    }

    public record PortfolioCashTransferReservation(
        String reservationRef,
        TransactionModel.TransactionType transactionType,
        Long dateTime,
        String currency,
        BigDecimal quantity,
        String sourcePortfolioId,
        String targetPortfolioId,
        String feePortfolioId,
        Collection<ReservationFee> fees
    ) implements Reservation {
    }

    public record ReservationBalance(
        BigDecimal quantity,
        String currency,
        String portfolioId,
        String accountId
    ) implements Serializable {
    }

    public record ReservationSearch(
        String currency,
        String reservationRef,
        String portfolioId,
        String accountId,
        TransactionModel.TransactionType transactionType,
        String clientId,
        Long from,
        Long to,
        TransactionModel.SortingOrder sortingOrder,
        Integer first,
        String after
    ) {
        public ReservationSearch {
            first = wrapPageSize(first);
        }
    }

    @JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "transactionType")
    @JsonSubTypes({
        @JsonSubTypes.Type(value = DepositReservationRequest.class, name = "DEPOSIT"),
        @JsonSubTypes.Type(value = WithdrawalReservationRequest.class, name = "WITHDRAWAL"),
        @JsonSubTypes.Type(value = ClientCashTradeReservationRequest.class, name = "CLIENT_CASH_TRADE"),
        @JsonSubTypes.Type(value = StreetCashTradeReservationRequest.class, name = "STREET_CASH_TRADE"),
        @JsonSubTypes.Type(value = AccountCashTransferReservationRequest.class, name = "ACCOUNT_CASH_TRANSFER"),
        @JsonSubTypes.Type(value = PortfolioCashTransferReservationRequest.class, name = "PORTFOLIO_CASH_TRANSFER"),
    })
    public interface ReservationRequest extends Serializable {
    }

    public record DepositReservationRequest(
        @NotBlank(message = "reservationRef cannot be empty") String reservationRef,
        // apparently @NotNull annotation does not work for transactionType - maybe because it's used for subtype selection
        TransactionModel.TransactionType transactionType,
        @NotNull(message = "dateTime cannot be null") Long dateTime,
        @NotBlank(message = "currency cannot be empty") String currency,
        @NotNull(message = "quantity cannot be null") BigDecimal quantity,
        @NotBlank(message = "portfolioId cannot be empty") String portfolioId,
        @NotBlank(message = "accountId cannot be empty") String accountId,
        String feePortfolioId,
        String feeAccountId,
        @Valid Collection<ReservationFee> fees
    ) implements ReservationRequest, TransactionModel.WithAdditionalFees<ReservationFee> {
    }

    public record WithdrawalReservationRequest(
        @NotBlank(message = "reservationRef cannot be empty") String reservationRef,
        // apparently @NotNull annotation does not work for transactionType - maybe because it's used for subtype selection
        TransactionModel.TransactionType transactionType,
        @NotNull(message = "dateTime cannot be null") Long dateTime,
        @NotBlank(message = "currency cannot be empty") String currency,
        @NotNull(message = "quantity cannot be null") BigDecimal quantity,
        @NotBlank(message = "portfolioId cannot be empty") String portfolioId,
        @NotBlank(message = "accountId cannot be empty") String accountId,
        String feePortfolioId,
        String feeAccountId,
        @Valid Collection<ReservationFee> fees
    ) implements ReservationRequest, TransactionModel.WithAdditionalFees<ReservationFee> {
    }

    public record ClientCashTradeReservationRequest(
        @NotBlank(message = "reservationRef cannot be empty") String reservationRef,
        // apparently @NotNull annotation does not work for transactionType - maybe because it's used for subtype selection
        TransactionModel.TransactionType transactionType,
        @NotNull(message = "dateTime cannot be null") Long dateTime,
        @NotBlank(message = "currency cannot be empty") String currency,
        @NotBlank(message = "baseCurrency cannot be empty") String baseCurrency,
        @NotNull(message = "quantity cannot be null") BigDecimal quantity,
        @NotNull(message = "price cannot be null") BigDecimal price,
        BigDecimal stopPrice,
        @NotBlank(message = "portfolioId cannot be empty") String portfolioId,
        @NotBlank(message = "counterPortfolioId cannot be empty") String counterPortfolioId,
        @Valid Collection<ReservationFee> fees
    ) implements ReservationRequest {
    }

    public record StreetCashTradeReservationRequest(
        @NotBlank(message = "reservationRef cannot be empty") String reservationRef,
        // apparently @NotNull annotation does not work for transactionType - maybe because it's used for subtype selection
        TransactionModel.TransactionType transactionType,
        @NotNull(message = "dateTime cannot be null") Long dateTime,
        @NotBlank(message = "currency cannot be empty") String currency,
        @NotBlank(message = "baseCurrency cannot be empty") String baseCurrency,
        @NotNull(message = "quantity cannot be null") BigDecimal quantity,
        @NotNull(message = "price cannot be null") BigDecimal price,
        BigDecimal stopPrice,
        @NotBlank(message = "portfolioId cannot be empty") String portfolioId,
        @NotBlank(message = "accountId cannot be empty") String accountId,
        @Valid Collection<ReservationFee> fees
    ) implements ReservationRequest {
    }

    public record AccountCashTransferReservationRequest(
        @NotBlank(message = "reservationRef cannot be empty") String reservationRef,
        // apparently @NotNull annotation does not work for transactionType - maybe because it's used for subtype selection
        TransactionModel.TransactionType transactionType,
        @NotNull(message = "dateTime cannot be null") Long dateTime,
        @NotBlank(message = "currency cannot be empty") String currency,
        @NotNull(message = "quantity cannot be null") BigDecimal quantity,
        @NotBlank(message = "sourceAccountId cannot be empty") String sourceAccountId,
        @NotBlank(message = "targetAccountId cannot be empty") String targetAccountId,
        String feeAccountId,
        String feePortfolioId,
        @Valid Collection<ReservationFee> fees
    ) implements ReservationRequest, TransactionModel.WithAdditionalFees<ReservationFee> {
    }

    public record PortfolioCashTransferReservationRequest(
        @NotBlank(message = "reservationRef cannot be empty") String reservationRef,
        // apparently @NotNull annotation does not work for transactionType - maybe because it's used for subtype selection
        TransactionModel.TransactionType transactionType,
        @NotNull(message = "dateTime cannot be null") Long dateTime,
        @NotBlank(message = "currency cannot be empty") String currency,
        @NotNull(message = "quantity cannot be null") BigDecimal quantity,
        @NotBlank(message = "sourcePortfolioId cannot be empty") String sourcePortfolioId,
        @NotBlank(message = "targetPortfolioId cannot be empty") String targetPortfolioId,
        String feePortfolioId,
        @Valid Collection<ReservationFee> fees
    ) implements ReservationRequest {
    }

    public record ReservationFee(
        @NotNull(message = "ReservationFee amount cannot be null") BigDecimal amount,
        @NotBlank(message = "ReservationFee currency cannot be empty") String currency,
        @NotNull(message = "ReservationFee feeType cannot be null") TransactionModel.FeeType feeType) implements TransactionModel.AdditionalFees {
    }
}
