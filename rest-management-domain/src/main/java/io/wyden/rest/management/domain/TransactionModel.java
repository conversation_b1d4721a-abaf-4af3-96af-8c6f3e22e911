package io.wyden.rest.management.domain;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import org.apache.commons.lang3.ObjectUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Collection;

import static io.wyden.rest.management.common.CollectionUtils.wrapPageSize;

public class TransactionModel {

    public enum TransactionType {
        CLIENT_CASH_TRADE,
        STREET_CASH_TRADE,
        ACCOUNT_CASH_TRANSFER,
        PORTFOLIO_CASH_TRANSFER,
        DEPOSIT,
        WITHDRAWAL,
        SETTLEMENT,
        FEE,
        UNSPECIFIED
    }

    public enum FeeType {
        UNSPECIFIED,

        //The executing broker's commission
        EXCHANGE_FEE,

        //The commission charged by the sales desk
        TRANSACTION_FEE,

        //The fixed fee which is charged by the sales desk
        FIXED_FEE
    }

    public enum SortingOrder {
        ASC, DESC
    }

    public record TransactionSearch(
        String currency,
        String portfolioId,
        String accountId,
        String orderId,
        String parentOrderId,
        String rootOrderId,
        String underlyingExecutionId,
        String executionId,
        String rootExecutionId,
        String reservationRef,
        TransactionType transactionType,
        Long from,
        Long to,
        Long settlementFrom,
        Long settlementTo,
        Boolean settled,
        SortingOrder sortingOrder,
        Integer first,
        String after
    ) {
        public TransactionSearch {
            first = wrapPageSize(first);
        }
    }

    public interface AdditionalFees {
        BigDecimal amount();

        String currency();

        FeeType feeType();
    }

    public interface WithAdditionalFees<T extends AdditionalFees> {
        Collection<T> fees();

        String feePortfolioId();

        String feeAccountId();
    }

    public interface Transaction extends Serializable {
    }

    public record ClientCashTrade(
        String id,
        String reservationRef,
        Long dateTime,
        String orderId,
        String parentOrderId,
        String rootOrderId,
        String clientRootOrderId,
        String extOrderId,
        String executionId,
        String venueExecutionId,
        String underlyingExecutionId,
        String rootExecutionId,
        TransactionType transactionType,
        Boolean settled,
        Long settlementDateTime,
        String settlementId,
        String clientSettlementId,
        String baseCurrency,
        String currency,
        BigDecimal quantity,
        BigDecimal leavesQuantity,
        BigDecimal price,
        String portfolioId,
        String counterPortfolioId,
        String description,
        Collection<TransactionFee> fees
    ) implements Transaction {
    }

    public record StreetCashTrade(
        String id,
        String reservationRef,
        Long dateTime,
        String orderId,
        String parentOrderId,
        String rootOrderId,
        String clientRootOrderId,
        String extOrderId,
        String executionId,
        String venueExecutionId,
        String underlyingExecutionId,
        String rootExecutionId,
        TransactionType transactionType,
        Boolean settled,
        Long settlementDateTime,
        String settlementId,
        String clientSettlementId,
        String baseCurrency,
        String currency,
        BigDecimal quantity,
        BigDecimal leavesQuantity,
        BigDecimal price,
        String portfolioId,
        String accountId,
        String description,
        Collection<TransactionFee> fees
    ) implements Transaction {
    }

    public record Deposit(
        String id,
        String reservationRef,
        Long dateTime,
        String executionId,
        String venueExecutionId,
        TransactionType transactionType,
        Boolean settled,
        Long settlementDateTime,
        String currency,
        BigDecimal quantity,
        String portfolioId,
        String accountId,
        String feePortfolioId,
        String feeAccountId,
        String description,
        Collection<TransactionFee> fees
    ) implements Transaction, WithAdditionalFees<TransactionFee> {
    }

    public record Withdrawal(
        String id,
        String reservationRef,
        Long dateTime,
        String executionId,
        String venueExecutionId,
        TransactionType transactionType,
        Boolean settled,
        Long settlementDateTime,
        String currency,
        BigDecimal quantity,
        String portfolioId,
        String accountId,
        String feePortfolioId,
        String feeAccountId,
        String description,
        Collection<TransactionFee> fees
    ) implements Transaction, WithAdditionalFees<TransactionFee> {
    }

    public record PortfolioCashTransfer(
        String id,
        String reservationRef,
        Long dateTime,
        String executionId,
        String venueExecutionId,
        TransactionType transactionType,
        Boolean settled,
        Long settlementDateTime,
        String currency,
        BigDecimal quantity,
        String description,
        String sourcePortfolioId,
        String targetPortfolioId,
        String feePortfolioId,
        Collection<TransactionFee> fees
    ) implements Transaction {
    }

    public record AccountCashTransfer(
        String id,
        String reservationRef,
        Long dateTime,
        String executionId,
        String venueExecutionId,
        TransactionType transactionType,
        Boolean settled,
        Long settlementDateTime,
        String currency,
        BigDecimal quantity,
        String description,
        String sourceAccountId,
        String targetAccountId,
        String feePortfolioId,
        String feeAccountId,
        Collection<TransactionFee> fees
    ) implements Transaction, WithAdditionalFees<TransactionFee> {
    }

    public record Settlement(
        String id,
        String clientSettlementId,
        Long dateTime,
        Long settlementDateTime,
        String description,
        TransactionType transactionType,
        Collection<String> transactionExecutionIds
    ) implements Transaction {
    }

    public record Fee(
        String id,
        String reservationRef,
        Long dateTime,
        String orderId,
        String parentOrderId,
        String rootOrderId,
        String executionId,
        String venueExecutionId,
        String underlyingExecutionId,
        String rootExecutionId,
        TransactionType transactionType,
        Boolean settled,
        Long settlementDateTime,
        String currency,
        BigDecimal quantity,
        String portfolioId,
        String accountId,
        String description
    ) implements Transaction {
    }

    @JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "transactionType")
    @JsonSubTypes({
        @JsonSubTypes.Type(value = DepositRequest.class, name = "DEPOSIT"),
        @JsonSubTypes.Type(value = WithdrawalRequest.class, name = "WITHDRAWAL"),
        @JsonSubTypes.Type(value = ClientCashTradeRequest.class, name = "CLIENT_CASH_TRADE"),
        @JsonSubTypes.Type(value = StreetCashTradeRequest.class, name = "STREET_CASH_TRADE"),
        @JsonSubTypes.Type(value = AccountCashTransferRequest.class, name = "ACCOUNT_CASH_TRANSFER"),
        @JsonSubTypes.Type(value = PortfolioCashTransferRequest.class, name = "PORTFOLIO_CASH_TRANSFER"),
    })
    public interface TransactionRequest extends Serializable {
        String reservationRef();
    }

    public record ClientCashTradeRequest(
        String reservationRef,
        @NotNull(message = "dateTime cannot be null") Long dateTime,
        String orderId,
        String parentOrderId,
        String rootOrderId,
        String clientRootOrderId,
        String extOrderId,
        String executionId,
        String venueExecutionId,
        String underlyingExecutionId,
        String rootExecutionId,
        TransactionType transactionType,
        @NotBlank(message = "baseCurrency cannot be empty") String baseCurrency,
        @NotBlank(message = "currency cannot be empty") String currency,
        @NotNull(message = "quantity cannot be null") BigDecimal quantity,
        BigDecimal leavesQuantity,
        @NotNull(message = "price cannot be null") BigDecimal price,
        @NotBlank(message = "portfolioId cannot be empty") String portfolioId,
        @NotBlank(message = "counterPortfolioId cannot be empty") String counterPortfolioId,
        String description,
        Collection<TransactionFee> fees,
        Boolean settled,
        Long settledDateTime
    ) implements TransactionRequest {
        public ClientCashTradeRequest withPortfolioId(String portfolioId) {
            return new ClientCashTradeRequest(reservationRef, dateTime, orderId, parentOrderId, rootOrderId, clientRootOrderId, extOrderId, executionId, venueExecutionId, underlyingExecutionId, rootExecutionId,
                transactionType, baseCurrency, currency, quantity, leavesQuantity, price, portfolioId, counterPortfolioId, description, fees,
                ObjectUtils.firstNonNull(settled, true), settledDateTime);
        }

        public ClientCashTradeRequest withCounterPortfolioId(String counterPortfolioId) {
            return new ClientCashTradeRequest(reservationRef, dateTime, orderId, parentOrderId, rootOrderId, clientRootOrderId, extOrderId, executionId, venueExecutionId, underlyingExecutionId, rootExecutionId,
                transactionType, baseCurrency, currency, quantity, leavesQuantity, price, portfolioId, counterPortfolioId, description, fees,
                ObjectUtils.firstNonNull(settled, true), settledDateTime);
        }
    }

    public record StreetCashTradeRequest(
        String reservationRef,
        @NotNull(message = "dateTime cannot be null") Long dateTime,
        String orderId,
        String parentOrderId,
        String rootOrderId,
        String clientRootOrderId,
        String extOrderId,
        String executionId,
        String venueExecutionId,
        String underlyingExecutionId,
        String rootExecutionId,
        TransactionType transactionType,
        @NotBlank(message = "baseCurrency cannot be empty") String baseCurrency,
        @NotBlank(message = "currency cannot be empty") String currency,
        @NotNull(message = "quantity cannot be null") BigDecimal quantity,
        BigDecimal leavesQuantity,
        @NotNull(message = "price cannot be null") BigDecimal price,
        @NotBlank(message = "portfolioId cannot be empty") String portfolioId,
        @NotBlank(message = "accountId cannot be empty") String accountId,
        String description,
        Collection<TransactionFee> fees,
        Boolean settled,
        Long settledDateTime
    ) implements TransactionRequest {
        public StreetCashTradeRequest withPortfolioId(String portfolioId) {
            return new StreetCashTradeRequest(reservationRef, dateTime, orderId, parentOrderId, rootOrderId, clientRootOrderId, extOrderId, executionId, venueExecutionId, underlyingExecutionId, rootExecutionId,
                transactionType, baseCurrency, currency, quantity, leavesQuantity, price, portfolioId, accountId, description, fees,
                ObjectUtils.firstNonNull(settled, true), settledDateTime);
        }

        public StreetCashTradeRequest withAccountId(String accountId) {
            return new StreetCashTradeRequest(reservationRef, dateTime, orderId, parentOrderId, rootOrderId, clientRootOrderId, extOrderId, executionId, venueExecutionId, underlyingExecutionId, rootExecutionId,
                transactionType, baseCurrency, currency, quantity, leavesQuantity, price, portfolioId, accountId, description, fees,
                ObjectUtils.firstNonNull(settled, true), settledDateTime);
        }
    }

    public record DepositRequest(
        String reservationRef,
        @NotNull(message = "dateTime cannot be null") Long dateTime,
        String executionId,
        String venueExecutionId,
        TransactionType transactionType,
        @NotBlank(message = "currency cannot be empty") String currency,
        @NotNull(message = "quantity cannot be null") @Positive(message = "quantity has to be positive") BigDecimal quantity,
        @NotBlank(message = "portfolioId cannot be empty") String portfolioId,
        @NotBlank(message = "accountId cannot be empty") String accountId,
        String feePortfolioId,
        String feeAccountId,
        String description,
        Collection<TransactionFee> fees
    ) implements TransactionRequest, WithAdditionalFees<TransactionFee> {
        public DepositRequest withPortfolioId(String portfolioId) {
            return new DepositRequest(reservationRef, dateTime, executionId, venueExecutionId, transactionType, currency, quantity, portfolioId, accountId, feePortfolioId, feeAccountId, description, fees);
        }

        public DepositRequest withAccountId(String accountId) {
            return new DepositRequest(reservationRef, dateTime, executionId, venueExecutionId, transactionType, currency, quantity, portfolioId, accountId, feePortfolioId, feeAccountId, description, fees);
        }

        public DepositRequest withFeePortfolioId(String feePortfolioId) {
            return new DepositRequest(reservationRef, dateTime, executionId, venueExecutionId, transactionType, currency, quantity, portfolioId, accountId, feePortfolioId, feeAccountId, description, fees);
        }

        public DepositRequest withFeeAccountId(String feeAccountId) {
            return new DepositRequest(reservationRef, dateTime, executionId, venueExecutionId, transactionType, currency, quantity, portfolioId, accountId, feePortfolioId, feeAccountId, description, fees);
        }
    }

    public record WithdrawalRequest(
        String reservationRef,
        @NotNull(message = "dateTime cannot be null") Long dateTime,
        String executionId,
        String venueExecutionId,
        TransactionType transactionType,
        @NotBlank(message = "currency cannot be empty") String currency,
        @NotNull(message = "quantity cannot be null") @Positive(message = "quantity has to be positive") BigDecimal quantity,
        @NotBlank(message = "portfolioId cannot be empty") String portfolioId,
        @NotBlank(message = "accountId cannot be empty") String accountId,
        String feePortfolioId,
        String feeAccountId,
        String description,
        Collection<TransactionFee> fees
    ) implements TransactionRequest, WithAdditionalFees<TransactionFee> {
        public WithdrawalRequest withPortfolioId(String portfolioId) {
            return new WithdrawalRequest(reservationRef, dateTime, executionId, venueExecutionId, transactionType, currency, quantity, portfolioId, accountId, feePortfolioId, feeAccountId, description, fees);
        }

        public WithdrawalRequest withAccountId(String accountId) {
            return new WithdrawalRequest(reservationRef, dateTime, executionId, venueExecutionId, transactionType, currency, quantity, portfolioId, accountId, feePortfolioId, feeAccountId, description, fees);
        }

        public WithdrawalRequest withFeePortfolioId(String feePortfolioId) {
            return new WithdrawalRequest(reservationRef, dateTime, executionId, venueExecutionId, transactionType, currency, quantity, portfolioId, accountId, feePortfolioId, feeAccountId, description, fees);
        }

        public WithdrawalRequest withFeeAccountId(String feeAccountId) {
            return new WithdrawalRequest(reservationRef, dateTime, executionId, venueExecutionId, transactionType, currency, quantity, portfolioId, accountId, feePortfolioId, feeAccountId, description, fees);
        }
    }

    public record PortfolioCashTransferRequest(
        String reservationRef,
        @NotNull(message = "dateTime cannot be null") Long dateTime,
        String executionId,
        String venueExecutionId,
        TransactionType transactionType,
        @NotBlank(message = "currency cannot be empty") String currency,
        @NotNull(message = "quantity cannot be null") BigDecimal quantity,
        String description,
        @NotBlank(message = "sourcePortfolioId cannot be empty") String sourcePortfolioId,
        @NotBlank(message = "targetPortfolioId cannot be empty") String targetPortfolioId,
        String feePortfolioId,
        Collection<TransactionFee> fees
    ) implements TransactionRequest {
        public PortfolioCashTransferRequest withSourcePortfolioId(String sourcePortfolioId) {
            return new PortfolioCashTransferRequest(reservationRef, dateTime, executionId, venueExecutionId, transactionType, currency, quantity, description, sourcePortfolioId, targetPortfolioId, feePortfolioId, fees);
        }

        public PortfolioCashTransferRequest withTargetPortfolioId(String targetPortfolioId) {
            return new PortfolioCashTransferRequest(reservationRef, dateTime, executionId, venueExecutionId, transactionType, currency, quantity, description, sourcePortfolioId, targetPortfolioId, feePortfolioId, fees);
        }

        public PortfolioCashTransferRequest withFeePortfolioId(String feePortfolioId) {
            return new PortfolioCashTransferRequest(reservationRef, dateTime, executionId, venueExecutionId, transactionType, currency, quantity, description, sourcePortfolioId, targetPortfolioId, feePortfolioId, fees);
        }
    }

    public record AccountCashTransferRequest(
        String reservationRef,
        @NotNull(message = "dateTime cannot be null") Long dateTime,
        String executionId,
        String venueExecutionId,
        TransactionType transactionType,
        @NotBlank(message = "currency cannot be empty") String currency,
        @NotNull(message = "quantity cannot be null") BigDecimal quantity,
        String description,
        @NotBlank(message = "sourceAccountId cannot be empty") String sourceAccountId,
        @NotBlank(message = "targetAccountId cannot be empty") String targetAccountId,
        String feePortfolioId,
        String feeAccountId,
        Collection<TransactionFee> fees
    ) implements TransactionRequest, WithAdditionalFees<TransactionFee> {
        public AccountCashTransferRequest withSourceAccountId(String sourceAccountId) {
            return new AccountCashTransferRequest(reservationRef, dateTime, executionId, venueExecutionId, transactionType, currency, quantity, description, sourceAccountId, targetAccountId, feePortfolioId, feeAccountId, fees);
        }

        public AccountCashTransferRequest withTargetAccountId(String targetAccountId) {
            return new AccountCashTransferRequest(reservationRef, dateTime, executionId, venueExecutionId, transactionType, currency, quantity, description, sourceAccountId, targetAccountId, feePortfolioId, feeAccountId, fees);
        }

        public AccountCashTransferRequest withFeePortfolioId(String feePortfolioId) {
            return new AccountCashTransferRequest(reservationRef, dateTime, executionId, venueExecutionId, transactionType, currency, quantity, description, sourceAccountId, targetAccountId, feePortfolioId, feeAccountId, fees);
        }

        public AccountCashTransferRequest withFeeAccountId(String feeAccountId) {
            return new AccountCashTransferRequest(reservationRef, dateTime, executionId, venueExecutionId, transactionType, currency, quantity, description, sourceAccountId, targetAccountId, feePortfolioId, feeAccountId, fees);
        }
    }

    public record TransactionFee(
        @NotNull(message = "TransactionFee amount cannot be null") BigDecimal amount,
        @NotBlank(message = "TransactionFee currency cannot be empty") String currency,
        @NotNull(message = "TransactionFee feeType cannot be null") FeeType feeType) implements AdditionalFees {
    }
}
